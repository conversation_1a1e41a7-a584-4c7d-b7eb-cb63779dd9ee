# Asset Type Management System

## Overview

The Asset Type Management System is a comprehensive solution for defining, configuring, and managing different types of assets within the WizeAssets platform. It provides a flexible framework for creating asset type templates with custom fields, forms, depreciation settings, lifecycle stages, and maintenance schedules.

## Key Features

### 🏗️ Asset Type Configuration
- **Custom Fields**: Define type-specific fields with validation rules
- **Depreciation Settings**: Configure depreciation methods and schedules
- **Lifecycle Stages**: Define asset lifecycle with automated transitions
- **Maintenance Schedules**: Set up preventive and predictive maintenance

### 📝 Dynamic Form System
- **Operation-Specific Forms**: Different forms for different operations
- **Form Builder**: Visual form designer with drag-and-drop interface
- **Runtime Engine**: Dynamic form rendering with validation
- **Pre-population**: Auto-fill forms with existing data

### ⚙️ Automation Engines
- **Depreciation Engine**: Automated depreciation calculations
- **Lifecycle Engine**: Automated stage transitions and notifications
- **Maintenance Engine**: Automated task generation and scheduling
- **Workflow Integration**: Trigger external workflows on operations

## Architecture

### Database Schema

The system extends the existing Prisma schema with new models:

```prisma
// Form Definitions
model FormDefinition {
  id          String   @id @default(cuid())
  name        String
  description String?
  sections    String   // JSON
  settings    String   // JSON
  isActive    Boolean  @default(true)
  createdBy   String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  assetTypeForms AssetTypeForm[]
}

// Asset Type Form Associations
model AssetTypeForm {
  id            String   @id @default(cuid())
  assetTypeId   String
  formId        String
  operationType String   // AssetOperationType
  version       Int      @default(1)
  isDefault     Boolean  @default(false)
  isActive      Boolean  @default(true)
  createdBy     String
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  
  assetType AssetType      @relation(fields: [assetTypeId], references: [id], onDelete: Cascade)
  form      FormDefinition @relation(fields: [formId], references: [id], onDelete: Cascade)
  
  @@unique([assetTypeId, operationType, isDefault])
}

// Additional models for operation history, depreciation schedules, etc.
```

### Core Components

#### 1. Form Builder System
- **FormBuilder**: Visual form designer
- **FormRenderer**: Runtime form rendering
- **AssetOperationFormBuilder**: Asset-specific form builder
- **AssetOperationFormRenderer**: Asset operation form renderer

#### 2. Automation Engines
- **DepreciationEngine**: Handles depreciation calculations
- **LifecycleEngine**: Manages asset lifecycle transitions
- **MaintenanceEngine**: Automates maintenance scheduling
- **FormRuntimeEngine**: Processes form submissions

#### 3. Workflow Integration
- **AssetOperationWorkflow**: Orchestrates complete operation workflows
- **External Workflow Triggers**: Integration with workflow engine

## Usage Guide

### Setting Up Asset Types

1. **Create Asset Category**
```typescript
const category = await prisma.assetCategory.create({
  data: {
    name: "IT Equipment",
    description: "Computer hardware and software",
    level: 1,
    path: "/it-equipment",
  },
});
```

2. **Create Asset Type**
```typescript
const assetType = await prisma.assetType.create({
  data: {
    name: "Laptop Computer",
    code: "LAPTOP",
    description: "Portable computers for employees",
    categoryId: category.id,
    icon: "Laptop",
    color: "#3B82F6",
    createdBy: "admin",
  },
});
```

3. **Add Custom Fields**
```typescript
const customField = await prisma.customField.create({
  data: {
    assetTypeId: assetType.id,
    name: "processor",
    label: "Processor",
    type: "text",
    isRequired: true,
    validation: JSON.stringify({
      minLength: 2,
      maxLength: 100,
    }),
  },
});
```

### Configuring Forms

1. **Create Form Definition**
```typescript
const form = await prisma.formDefinition.create({
  data: {
    name: "Laptop Creation Form",
    description: "Form for creating laptop assets",
    sections: JSON.stringify([
      {
        id: "basic-info",
        title: "Basic Information",
        fields: ["name", "serialNumber", "processor"],
      },
    ]),
    settings: JSON.stringify({
      layout: "standard",
      submitButtonText: "Create Laptop",
    }),
    createdBy: "admin",
  },
});
```

2. **Associate Form with Asset Type**
```typescript
const association = await prisma.assetTypeForm.create({
  data: {
    assetTypeId: assetType.id,
    formId: form.id,
    operationType: "asset.create",
    isDefault: true,
    createdBy: "admin",
  },
});
```

### Setting Up Depreciation

```typescript
const depreciationSettings = await prisma.depreciationSettings.create({
  data: {
    assetTypeId: assetType.id,
    method: "double_declining_balance",
    usefulLife: 3,
    usefulLifeUnit: "years",
    salvageValue: 10,
    salvageValueType: "percentage",
    startDate: new Date(),
  },
});
```

### Configuring Lifecycle Stages

```typescript
const stages = [
  {
    name: "Requested",
    code: "REQUESTED",
    order: 1,
    isInitial: true,
    allowedTransitions: ["ORDERED", "CANCELLED"],
  },
  {
    name: "In Use",
    code: "IN_USE",
    order: 4,
    allowedTransitions: ["MAINTENANCE", "DISPOSED"],
  },
];

for (const stage of stages) {
  await prisma.lifecycleStage.create({
    data: {
      ...stage,
      assetTypeId: assetType.id,
      allowedTransitions: JSON.stringify(stage.allowedTransitions),
    },
  });
}
```

### Setting Up Maintenance Schedules

```typescript
const maintenanceSchedule = await prisma.maintenanceSchedule.create({
  data: {
    assetTypeId: assetType.id,
    name: "Software Updates",
    description: "Regular software and security updates",
    type: "preventive",
    frequency: JSON.stringify({ type: "months", interval: 1 }),
    priority: "medium",
    estimatedDuration: 30,
    instructions: "Run all available software updates",
    checklistItems: JSON.stringify([
      "Check for OS updates",
      "Update antivirus software",
    ]),
  },
});
```

## API Endpoints

### Asset Type Forms
- `GET /api/asset-type-forms` - Get form for operation
- `POST /api/asset-type-forms` - Submit form data
- `PUT /api/asset-type-forms` - Associate form with asset type

### Form Definitions
- `GET /api/form-definitions` - Get all forms
- `POST /api/form-definitions` - Create form
- `PUT /api/form-definitions` - Update form
- `DELETE /api/form-definitions` - Delete form

### Asset Operations
- `POST /api/asset-operations` - Execute operation workflow
- `GET /api/asset-operations` - Get operation history

### Depreciation
- `POST /api/depreciation/calculate` - Calculate depreciation
- `GET /api/depreciation/calculate` - Get current book value

### Lifecycle
- `POST /api/lifecycle/transition` - Transition lifecycle stage
- `GET /api/lifecycle/transition` - Get available transitions

### Maintenance
- `GET /api/maintenance/tasks` - Get maintenance tasks
- `POST /api/maintenance/tasks` - Generate tasks
- `PUT /api/maintenance/tasks` - Complete task

## Frontend Components

### Admin Interface
- `AssetTypeDetailPage` - Main asset type management page
- `AssetOperationFormBuilder` - Form builder for operations
- `AssetOperationDashboard` - Operations dashboard

### User Interface
- `AssetOperationFormRenderer` - Form renderer for operations
- `AssetOperationDashboard` - User operations dashboard

## Migration and Setup

### 1. Run Database Migration
```bash
npx prisma db push
```

### 2. Run Setup Script
```bash
npx tsx scripts/migrate-asset-type-system.ts
```

### 3. Seed Sample Data
The migration script will create:
- Sample asset categories
- Sample asset types
- Default form definitions
- Sample depreciation settings
- Default lifecycle stages
- Sample maintenance schedules

## Operation Types

The system supports the following operation types:

1. **asset.create** - Create new assets
2. **asset.update** - Update existing assets
3. **asset.transfer** - Transfer assets between locations
4. **asset.disposal** - Dispose of assets
5. **maintenance.log** - Log maintenance activities
6. **maintenance.schedule** - Schedule maintenance tasks
7. **depreciation.calculate** - Calculate depreciation
8. **lifecycle.transition** - Transition lifecycle stages
9. **inventory.audit** - Conduct inventory audits

## Workflow Integration

The system integrates with the existing workflow engine to:
- Trigger workflows on asset operations
- Execute automated actions
- Send notifications
- Update related systems

## Best Practices

### 1. Asset Type Design
- Keep asset types focused and specific
- Use meaningful codes and names
- Define clear lifecycle stages
- Set up appropriate depreciation methods

### 2. Form Design
- Keep forms simple and focused
- Use clear field labels and descriptions
- Implement proper validation
- Consider user experience

### 3. Maintenance Scheduling
- Set realistic frequencies
- Provide clear instructions
- Use checklists for consistency
- Monitor completion rates

### 4. Lifecycle Management
- Define clear stage transitions
- Set up automated notifications
- Use required fields appropriately
- Monitor stage durations

## Troubleshooting

### Common Issues

1. **Form Not Loading**
   - Check asset type form associations
   - Verify form definition exists
   - Check user permissions

2. **Depreciation Not Calculating**
   - Verify depreciation settings exist
   - Check asset purchase price and date
   - Ensure settings are active

3. **Lifecycle Transition Blocked**
   - Check allowed transitions
   - Verify required fields are filled
   - Check user permissions

4. **Maintenance Tasks Not Generated**
   - Verify maintenance schedules are active
   - Check frequency settings
   - Ensure asset type association exists

## Future Enhancements

- **AI-Powered Maintenance Prediction**
- **Advanced Analytics and Reporting**
- **Mobile App Integration**
- **IoT Sensor Integration**
- **Advanced Workflow Automation**
- **Multi-tenant Support**

## Support

For technical support or questions about the Asset Type Management System, please refer to the main WizeAssets documentation or contact the development team.