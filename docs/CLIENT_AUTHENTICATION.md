# Client Authentication System

This document outlines the comprehensive client authentication system implemented for WizeAssets ERP.

## Overview

The client authentication system provides secure access control for external clients who need to request assets, manage their profiles, and interact with the WizeAssets platform.

## Features

### 🔐 Authentication & Authorization
- **Multi-step Registration**: 3-step client registration process with company information
- **Email Verification**: Required email verification before account activation
- **Role-based Access Control**: Separate client and admin access levels
- **Session Management**: Secure JWT-based session handling
- **Password Security**: Strong password requirements with bcrypt hashing

### 👤 User Management
- **Client Profiles**: Comprehensive client profile management
- **Company Information**: Industry, size, and business details
- **Account Status**: Active, inactive, suspended, pending verification states
- **Preferences**: Notification and dashboard customization options

### 🛡️ Security Features
- **Account Verification**: Email verification required for activation
- **Password Reset**: Secure password reset with token expiration
- **Account Suspension**: Administrative control over account access
- **Two-Factor Authentication**: Ready for 2FA implementation
- **Middleware Protection**: Route-level access control

## Database Schema

### User Model Extensions
```prisma
model User {
  // Basic fields
  id         String   @id @default(cuid())
  name       String
  email      String   @unique
  password   String
  role       String   @default("user") // "client", "admin", "manager"
  status     String   @default("active") // "active", "inactive", "suspended", "pending_verification"
  
  // Client-specific fields
  phone      String?
  address    String?
  company    String?
  jobTitle   String?
  clientId   String?
  
  // Security fields
  emailVerified          DateTime?
  emailVerifyToken       String?   @unique
  passwordResetToken     String?   @unique
  passwordResetExpiresAt DateTime?
  twoFactorEnabled       Boolean   @default(false)
  twoFactorSecret        String?
  
  // Preferences
  preferences Json?
  
  // Relations
  clientProfile  ClientProfile?
  assetRequests  AssetRequest[]
  supportTickets SupportTicket[]
  invoices       Invoice[]
}
```

### Client Profile Model
```prisma
model ClientProfile {
  id                String   @id @default(cuid())
  userId            String   @unique
  companyName       String?
  industry          String?
  companySize       String?
  website           String?
  subscriptionTier  String   @default("basic")
  monthlySpend      Float    @default(0)
  autoApprovalLimit Float    @default(0)
  paymentTerms      String   @default("net_30")
  
  user User @relation(fields: [userId], references: [id])
}
```

## API Endpoints

### Authentication Endpoints
- `POST /api/auth/client-register` - Client registration
- `GET /api/auth/verify-email` - Email verification
- `POST /api/auth/verify-email` - Resend verification email

### Client Profile Endpoints
- `GET /api/client/profile` - Get client profile
- `PUT /api/client/profile` - Update client profile
- `POST /api/client/change-password` - Change password

## Routes & Pages

### Authentication Pages
- `/auth/client-login` - Client login page
- `/auth/client-register` - Client registration page
- `/auth/verify-email-required` - Email verification required
- `/auth/account-suspended` - Account suspended notice
- `/unauthorized` - Unauthorized access page

### Client Dashboard
- `/client` - Main client dashboard
- `/client/profile` - Profile management
- `/client/requests` - Asset requests
- `/client/billing` - Billing and invoices
- `/client/support` - Support tickets

## Middleware Protection

The middleware (`middleware.ts`) provides:
- **Route Protection**: Automatic redirect to login for unauthenticated users
- **Role-based Access**: Different dashboards for different user roles
- **Account Status Checks**: Redirect suspended/inactive accounts
- **Email Verification**: Enforce email verification for clients

## Registration Flow

1. **Step 1: Personal Information**
   - Full name, email, phone, job title
   - Form validation with Zod schema

2. **Step 2: Company Information**
   - Company name, industry, size, website
   - Industry and size selection dropdowns

3. **Step 3: Security & Terms**
   - Password creation with strength requirements
   - Terms and privacy policy acceptance
   - Optional marketing email consent

4. **Account Creation**
   - User and ClientProfile creation in transaction
   - Email verification token generation
   - Welcome email sent (to be implemented)

5. **Email Verification**
   - User must verify email before accessing dashboard
   - Account status changes from "pending_verification" to "active"

## Login Flow

1. **Credential Validation**
   - Email and password verification
   - Account status checks (suspended, inactive)
   - Last active timestamp update

2. **Session Creation**
   - JWT token with user information
   - Role and status included in session

3. **Role-based Redirect**
   - Clients → `/client` dashboard
   - Admins → `/admin` dashboard

## Profile Management

### Personal Information
- Name, email, phone, job title
- Avatar upload (ready for implementation)
- Contact information updates

### Company Information
- Company details and industry
- Subscription tier display
- Spending and approval limits

### Security Settings
- Password change functionality
- Two-factor authentication setup
- Email verification status

### Preferences
- Notification settings
- Dashboard customization
- Marketing email preferences

## Security Considerations

### Password Security
- Minimum 8 characters
- Must contain uppercase, lowercase, and number
- Bcrypt hashing with salt rounds of 12

### Session Security
- JWT tokens with expiration
- Secure HTTP-only cookies (when implemented)
- Session invalidation on logout

### Account Protection
- Email verification required
- Account suspension capabilities
- Password reset with token expiration
- Rate limiting (to be implemented)

## Implementation Status

### ✅ Completed
- Database schema with client models
- Client registration with 3-step form
- Email verification system
- Client login with role-based redirects
- Profile management interface
- Password change functionality
- Middleware protection
- Account status handling

### 🚧 In Progress
- Email sending service integration
- Payment method management
- Two-factor authentication

### 📋 Planned
- Password strength meter
- Account lockout after failed attempts
- Audit logging
- Social login integration
- Mobile app authentication

## Usage Examples

### Client Registration
```typescript
// Register new client
const response = await fetch('/api/auth/client-register', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    name: 'John Doe',
    email: '<EMAIL>',
    password: 'SecurePass123',
    companyName: 'Acme Corp',
    industry: 'Technology',
    // ... other fields
  })
});
```

### Profile Update
```typescript
// Update client profile
const response = await fetch('/api/client/profile', {
  method: 'PUT',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    section: 'personal',
    data: {
      name: 'John Smith',
      phone: '******-123-4567'
    }
  })
});
```

## Environment Variables

Required environment variables:
```env
NEXTAUTH_SECRET=your-secret-key
NEXTAUTH_URL=http://localhost:3000
DATABASE_URL=your-database-url
```

## Testing

To test the client authentication system:

1. **Registration Test**
   - Navigate to `/auth/client-register`
   - Complete the 3-step registration process
   - Verify email verification requirement

2. **Login Test**
   - Navigate to `/auth/client-login`
   - Test with registered credentials
   - Verify redirect to client dashboard

3. **Profile Test**
   - Access `/client/profile`
   - Test profile updates
   - Verify password change functionality

4. **Access Control Test**
   - Try accessing admin routes as client
   - Verify unauthorized redirect
   - Test suspended account handling

## Support

For issues or questions about the client authentication system:
- Check the middleware configuration
- Verify database schema is up to date
- Ensure environment variables are set
- Review NextAuth.js configuration

## Contributing

When extending the authentication system:
1. Update the database schema if needed
2. Add appropriate middleware protection
3. Include proper error handling
4. Update this documentation
5. Add tests for new functionality