Below is a holistic run-through of WizeAssets ERP’s complete UI, its primary screens, and the user flows—both for today’s core asset management/ERP features and for the new Procurement, Leasing, Contract, and Credit-Qualification modules.

---

## 1. Global Layout & Navigation

1. **Top Bar**

   * Logo + “WizeAssets” title
   * Global search (search assets, leases, POs, clients)
   * Notifications (overdue leases, expiring contracts, low stock alerts)
   * User menu (Profile, API Keys, Help, Logout)

2. **Left Sidebar** (collapsible)

   * **Dashboard**
   * **Assets**
   * **Inventory & Procurement**

     * Purchase Orders
     * Vendors
   * **Leasing & Contracts**

     * Leases
     * Contracts
   * **Clients & Qualification**

     * CRM
     * Credit Applications
   * **Maintenance**
   * **Automation**
   * **Financials**
   * **Reports**
   * **Settings**

Each section expands to reveal sub-pages; active section is highlighted.

---

## 2. Dashboard

* **KPI Cards**:

  * Total Assets, Assets in Lease, Assets Available
  * Active Leases, Leases Expiring This Month
  * Low-stock Alerts, Pending POs
  * Approved vs. Pending Credit Applications

* **Charts**:

  * Asset Utilization over Time
  * Monthly Lease Revenue vs. Procurement Spend
  * Credit Approval Rates

* **Quick Actions**:

  * “Create Purchase Order”
  * “New Lease”
  * “Qualify Client”

---

## 3. Assets

### 3.1 Asset List Screen

* Table with columns: Serial #, Model, Location, Status (In-stock / In-lease / Maintenance), Assigned To (Lease or Dept.), Last Maintenance, Value.
* Filters: Category, Location, Status, Custom Fields.
* Actions: View, Edit, QR-print, Export.

### 3.2 Asset Detail Screen

* **Header**: Asset photo, Serial #, Model, Purchase Date, Depreciation Schedule

* **Tabbed UI**:

  1. **Overview** (key specs + last activity log)
  2. **Lease History** (which leases used it, dates)
  3. **Maintenance** (past and scheduled jobs)
  4. **Documents** (manuals, contracts)
  5. **Custom Fields**

* **Action Buttons**:

  * “Create Lease” (pre-fills this asset)
  * “Schedule Maintenance”
  * “Generate QR Code”

---

## 4. Inventory & Procurement

### 4.1 Vendors Screen

* List of vendors, contact info, lead times, rating.

### 4.2 Purchase Orders List

* Table: PO #, Vendor, Order Date, Expected Delivery, Status (Draft, Sent, Received), Total.
* Quick-filter: Drafts, Overdue, Received This Month.

### 4.3 Purchase Order Detail / Editor

* **Header**: PO #, Status badge, Vendor selector, Order & Delivery dates.
* **Line Items**: Model, Qty, Unit Cost, Total.
* **Attachments**: Specs, vendor quotes.
* **Workflow Buttons**: “Save Draft,” “Send to Vendor,” “Mark as Received.”
* **Once Received**: Inventory increases; you’re prompted to enter serial numbers for each unit.

---

## 5. Leasing & Contracts

### 5.1 Leases List

* Table: Lease #, Client, Start/End Dates, Status (Active, Expired, Pending), Total Value.

### 5.2 New Lease Wizard

1. **Step 1 – Client**: Select existing client or “Create New.”
2. **Step 2 – Assets**: Pick by model or serial numbers (inventory-driven availability).
3. **Step 3 – Terms**: Duration, Rate (daily/monthly), Deposit, Billing Schedule.
4. **Step 4 – Contract**:

   * Auto-generate contract from template (editable in-line)
   * Attach additional documents
5. **Step 5 – Review & Sign**: Digital signature integration.

### 5.3 Lease Detail

* **Header**: Lease status timeline (Draft → Active → Expired)
* **Tabs**:

  * **Summary** (dates, amounts, linked assets)
  * **Invoices & Payments**
  * **Documents** (signed contract, addendums)
  * **Communication** (emails, notes)
* **Action Buttons**: Renew, Terminate, Record Return, Send Invoice.

---

## 6. Clients & Qualification

### 6.1 CRM List

* Clients table: Company, Contact, Phone, Email, Industry, Credit Status.

### 6.2 Client Profile

* **Overview**: Contact info, leases, POs, open tickets.
* **Credit Score**: Score badge, last assessment date.
* **Qualification Forms**: Any submitted applications (Pending, Approved, Rejected).

### 6.3 Credit Application Workflow

* **Application Form**: Custom form fields (financials, references) built via Form Builder.
* **Underwriting Dashboard**:

  * Queue of pending apps
  * For each: view documents, scorecards, decision buttons (Approve/Reject/Request More Info).
* **Outcome**: Approved clients get a credit limit; automatically flagged in CRM and Leasing screens.

---

## 7. Maintenance

* **List of Scheduled Jobs**: Filter by date, technician, asset.
* **Job Detail**: Task description, assigned technician, parts needed, labor cost, completion status.
* **Analytics**: Mean Time Between Failures, Maintenance cost by asset type.

---

## 8. Automation

* **Visual Workflow Editor** (ReactFlow): Drag-drop triggers (e.g., “Lease Expiring in 7 days”) → actions (e.g., “Send Renewal Email,” “Create Support Ticket”).
* **Templates**: Auto-reminder, auto-reorder low-stock, contract-expiry notifications.

---

## 9. Financials

* **TCO & Depreciation**: Per asset and roll-up by category.
* **Revenue & Costs Dashboard**: Leasing revenue vs. procurement spend.
* **Invoicing**: List of generated invoices; integrates with payment gateway.
* **Reports**: Profit & Loss, Balance Sheet by period.

---

## 10. Reports

* **Ad-hoc Report Builder**: Choose data sources (assets, leases, POs), filters, groupings, visualizations.
* **Scheduled Reports**: Email PDF/CSV on a cadence.

---

## 11. Settings

* **User & Role Management**: Define purchasing vs. leasing vs. admin roles.
* **Templates**: Contract, Email, PO.
* **Custom Fields**: Add fields on Assets, Clients, POs, Leases.
* **Integrations**: Payment gateways, digital signature providers, external APIs.

---

### Typical User Flow Examples

1. **Onboard New Batch of Laptops**

   * Create PO → Receive → Register serials in Inventory → Assets auto-created.

2. **Lease to a Government Agency**

   * Qualify client via Credit module → Draft Lease → Auto-generate Contract → Client signs → Assets reserved and status flips to “In-lease.”

3. **Handle Lease Expiry**

   * Automation sends 14-day reminder → Technician picks up device (Maintenance tab) → Return recorded → Inventory restocks → Financials logs final invoice and revenue.

4. **Reorder Low-Stock Model**

   * Automation triggers when inventory < threshold → PO draft generated automatically → Purchasing team reviews and sends.

---

By mapping each user role (Purchasing Agent, Leasing Officer, Financial Analyst, Technician) to the screens and flows above, WizeAssets becomes a cohesive platform that takes you seamlessly from “order in” to “order out,” with all the ERP, CRM, and automation glue you need.
