# AI Engine Setup Guide

This guide will help you set up the AI-powered features in the WizAssets ERP system using Google's Gemini 2.0 Flash model.

## Prerequisites

1. **Google AI Studio Account**: You need access to Google AI Studio to get an API key
2. **Node.js**: Ensure you have Node.js 18+ installed
3. **Environment Variables**: Access to modify your `.env.local` file

## Step 1: Get Google AI API Key

1. Go to [Google AI Studio](https://aistudio.google.com/)
2. Sign in with your Google account
3. Click on "Get API Key" in the left sidebar
4. Create a new API key or use an existing one
5. Copy the API key (it starts with `AIza...`)

## Step 2: Configure Environment Variables

Add the following to your `.env.local` file:

```bash
# Google AI Configuration
GOOGLE_GENERATIVE_AI_API_KEY=your_api_key_here
```

Replace `your_api_key_here` with the API key you copied from Google AI Studio.

## Step 3: Install Dependencies

The required dependencies should already be installed. If not, run:

```bash
pnpm install @ai-sdk/google
```

## Step 4: Test the AI Features

1. Start your development server:
   ```bash
   pnpm dev
   ```

2. Navigate to the AI Demo page: `http://localhost:3000/admin/ai-demo`

3. Test the following features:
   - **Asset Analysis**: Analyze asset condition and performance
   - **Maintenance Predictions**: Generate predictive maintenance recommendations
   - **Cost Optimization**: Analyze and optimize asset-related costs
   - **Natural Language Queries**: Ask questions about assets in plain English

## Available AI Features

### 1. Asset Analysis
- Analyzes asset condition, performance, and maintenance needs
- Provides actionable insights and recommendations
- Generates risk assessments

### 2. Maintenance Predictions
- Predicts component failures and maintenance needs
- Estimates costs and downtime
- Provides maintenance scheduling recommendations

### 3. Cost Optimization
- Analyzes asset portfolio for cost-saving opportunities
- Provides ROI calculations and implementation roadmaps
- Identifies optimization strategies

### 4. Natural Language Processing
- Process queries in natural language
- Extract intent and entities from user questions
- Provide contextual responses with data

### 5. AI Assistant
- Interactive chat interface available on all pages
- Access to all AI functions through conversation
- Context-aware responses

### 6. Smart Alerts
- AI-generated alerts based on asset data
- Predictive notifications for maintenance needs
- Automated action recommendations

## API Endpoints

The following API endpoints are available for AI features:

- `POST /api/ai/chat` - AI Assistant chat interface
- `POST /api/ai/analyze` - Asset analysis
- `POST /api/ai/predict` - Maintenance predictions
- `POST /api/ai/optimize` - Cost optimization
- `GET /api/ai/insights` - Get AI insights and alerts
- `POST /api/ai/insights` - Generate insights and alerts

## Usage Examples

### Asset Analysis
```javascript
const response = await fetch('/api/ai/analyze', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    assetId: 'ASSET-001',
    assetData: {
      // Asset data object
    }
  })
});
```

### Maintenance Predictions
```javascript
const response = await fetch('/api/ai/predict', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    assetId: 'ASSET-001',
    historicalData: [
      // Historical maintenance data
    ]
  })
});
```

### Cost Optimization
```javascript
const response = await fetch('/api/ai/optimize', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    assetData: [
      // Array of asset data
    ],
    timeframe: '1 year'
  })
});
```

## React Hooks

Use the `useAIEngine` hook for easy integration:

```javascript
import { useAIEngine } from '@/hooks/use-ai-engine';

function MyComponent() {
  const {
    analyzeAsset,
    generateMaintenancePredictions,
    generateCostOptimization,
    processNLPQuery,
    isLoading,
    error
  } = useAIEngine();

  // Use the AI functions...
}
```

## Components

### AI Assistant
```javascript
import { AIAssistant } from '@/components/ai/ai-assistant';

// Add to any page
<AIAssistant />
```

### AI Insights Dashboard
```javascript
import { AIInsightsDashboard } from '@/components/ai/ai-insights-dashboard';

// Display AI insights
<AIInsightsDashboard />
```

## Troubleshooting

### Common Issues

1. **API Key Not Working**
   - Ensure the API key is correctly set in `.env.local`
   - Verify the API key is active in Google AI Studio
   - Check for any usage limits or restrictions

2. **Rate Limiting**
   - Google AI has rate limits for API calls
   - The system includes caching to reduce API calls
   - Consider upgrading your Google AI plan for higher limits

3. **Model Not Available**
   - Ensure you have access to Gemini 2.0 Flash
   - Check Google AI Studio for model availability
   - Try using `gemini-1.5-flash` as a fallback

4. **Network Issues**
   - Ensure your server can make outbound HTTPS requests
   - Check firewall settings if running in a restricted environment

### Error Messages

- **"API key not configured"**: Add `GOOGLE_GENERATIVE_AI_API_KEY` to your environment
- **"Model not found"**: Check if the model name is correct and available
- **"Rate limit exceeded"**: Wait and retry, or upgrade your API plan
- **"Invalid request"**: Check the request format and required parameters

## Performance Optimization

1. **Caching**: The system caches AI responses for 5 minutes by default
2. **Batch Processing**: Group similar requests when possible
3. **Async Processing**: All AI operations are asynchronous
4. **Error Handling**: Robust error handling with fallbacks

## Security Considerations

1. **API Key Security**: Never expose your API key in client-side code
2. **Input Validation**: All inputs are validated before sending to AI
3. **Rate Limiting**: Implement rate limiting for production use
4. **Data Privacy**: Be mindful of sensitive data sent to AI services

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review the Google AI Studio documentation
3. Check the browser console for error messages
4. Ensure all environment variables are properly set

## Model Information

**Gemini 2.0 Flash**
- Context Length: 1M tokens
- Output Length: 8,192 tokens
- Multimodal: Text and images
- Languages: 100+ languages
- Speed: Optimized for fast responses
- Cost: Pay-per-use pricing

For the latest information on model capabilities and pricing, visit [Google AI Studio](https://aistudio.google.com/).