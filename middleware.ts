import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { verifyToken } from "@/lib/auth";

// Configure runtime to use Node.js
export const runtime = 'nodejs';

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Debug logging
  console.log(`[Middleware] Processing path: ${pathname}`);

  // Allow access to public routes
  if (
    pathname.startsWith("/api/auth") ||
    pathname.startsWith("/_next") ||
    pathname.startsWith("/favicon") ||
    pathname === "/" ||
    pathname === "/login" ||
    pathname === "/register" ||
    pathname === "/simple-login" ||
    pathname === "/test-login" ||
    pathname.startsWith("/auth") ||
    pathname.startsWith("/public") ||
    pathname.startsWith("/terms") ||
    pathname.startsWith("/privacy") ||
    pathname.startsWith("/support") ||
    pathname.startsWith("/forgot-password") ||
    pathname.startsWith("/reset-password") ||
    pathname.startsWith("/client-login") ||
    pathname.startsWith("/client-register") ||
    pathname.startsWith("/verify-email") ||
    pathname.startsWith("/account-suspended")
  ) {
    console.log(`[Middleware] Allowing public path: ${pathname}`);
    return NextResponse.next();
  }

  // Get the auth token from cookies
  const token = request.cookies.get("auth-token")?.value;

  // Check if user is authenticated
  if (!token) {
    console.log(`[Middleware] No token found, redirecting to login from: ${pathname}`);
    const loginUrl = new URL("/login", request.url);
    loginUrl.searchParams.set("from", pathname);
    return NextResponse.redirect(loginUrl);
  }

  // Verify the token
  const user = await verifyToken(token);
  
  if (!user) {
    const loginUrl = new URL("/login", request.url);
    loginUrl.searchParams.set("from", pathname);
    return NextResponse.redirect(loginUrl);
  }

  // Role-based access control
  const userRole = user.role;

  // Admin routes - only accessible by admin and manager roles
  if (pathname.startsWith("/admin")) {
    if (!["admin", "manager"].includes(userRole)) {
      return NextResponse.redirect(new URL("/unauthorized", request.url));
    }
    return NextResponse.next();
  }

  // Client routes - only accessible by client role
  if (pathname.startsWith("/client")) {
    if (userRole !== "client") {
      // Redirect non-clients to appropriate dashboard
      if (["admin", "manager"].includes(userRole)) {
        return NextResponse.redirect(new URL("/admin", request.url));
      }
      return NextResponse.redirect(new URL("/unauthorized", request.url));
    }
    return NextResponse.next();
  }

  // Asset routes - accessible by all authenticated users
  if (pathname.startsWith("/assets") || pathname.startsWith("/requisitions")) {
    return NextResponse.next();
  }

  // Default redirect based on role
  if (pathname === "/dashboard") {
    switch (userRole) {
      case "client":
        return NextResponse.redirect(new URL("/client", request.url));
      case "admin":
      case "manager":
        return NextResponse.redirect(new URL("/admin", request.url));
      default:
        return NextResponse.redirect(new URL("/", request.url));
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api/auth (NextAuth.js API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    "/((?!api/auth|_next/static|_next/image|favicon.ico|public).*)",
  ],
};