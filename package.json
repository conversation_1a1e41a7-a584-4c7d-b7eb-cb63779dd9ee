{"name": "wize-assets-erp", "version": "1.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev -p 3065", "lint": "next lint", "start": "next start -p 3065", "migrate:asset-types": "npx tsx scripts/migrate-asset-type-system.ts", "db:push": "npx prisma db push", "db:generate": "npx prisma generate", "db:seed": "npx tsx scripts/seed/index.ts", "db:seed:dev": "npx tsx scripts/seed/index.ts --environment development --size medium --clean", "db:seed:small": "npx tsx scripts/seed/index.ts --environment development --size small --clean", "db:seed:large": "npx tsx scripts/seed/index.ts --environment development --size large --clean", "db:create-admin": "npx tsx scripts/create-admin-user.ts", "db:admin-sql": "npx tsx scripts/create-admin-user.ts --sql-only"}, "dependencies": {"@ai-sdk/google": "^1.2.22", "@ai-sdk/openai": "^1.3.22", "@ai-sdk/react": "^1.2.12", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^3.9.1", "@prisma/client": "6.10.1", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "latest", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "latest", "@radix-ui/react-progress": "latest", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "latest", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@reactflow/background": "^11.3.14", "@reactflow/controls": "^11.2.13", "@reactflow/core": "^11.11.4", "@reactflow/minimap": "^11.7.14", "@reactflow/node-resizer": "^2.2.14", "@reactflow/node-toolbar": "^1.3.7", "@trigger.dev/sdk": "^3.0.6", "@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/nodemailer": "^6.4.17", "@xyflow/react": "^12.3.2", "ai": "^4.3.16", "appwrite": "^18.1.1", "autoprefixer": "^10.4.20", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "critters": "^0.0.25", "crypto-js": "latest", "dagre": "^0.8.5", "date-fns": "4.1.0", "embla-carousel-react": "8.5.1", "framer-motion": "^12.19.1", "input-otp": "1.4.1", "jose": "^6.0.11", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "lucide-react": "^0.454.0", "next": "14.2.16", "next-auth": "^4.24.11", "next-themes": "^0.4.4", "nextjs-toploader": "^3.8.16", "nodemailer": "^7.0.3", "react": "^18", "react-day-picker": "9.7.0", "react-dom": "^18", "react-dropzone": "^14.3.8", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "reactflow": "^11.11.4", "recharts": "2.15.0", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.6", "zod": "latest", "zustand": "^4.5.5"}, "devDependencies": {"@faker-js/faker": "^9.9.0", "@types/dagre": "^0.7.52", "@types/node": "^22", "@types/react": "^18", "@types/react-dom": "^18", "postcss": "^8.5", "prisma": "^6.10.1", "tailwindcss": "^3.4.17", "typescript": "^5"}}