import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { GradientBackground } from "@/components/landing/gradient-background"
import { HeroAnimation } from "@/components/landing/hero-animation"
import { FeatureCards } from "@/components/landing/feature-cards"
import { PricingSection } from "@/components/landing/pricing-section"
import { TestimonialSlider } from "@/components/landing/testimonial-slider"
import { FloatingAssets } from "@/components/landing/floating-assets"
import { FloatingNav } from "@/components/landing/floating-nav"

export default function HomePage() {
  return (
    <div className="relative">
      <GradientBackground />
      <FloatingNav />

      {/* Hero Section */}
      <section className="relative py-20 md:py-32">
        <div className="container mx-auto px-4 md:px-6">
          <div className="grid gap-12 md:grid-cols-2 md:gap-16 items-center">
            <div>
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight text-white">
                Intelligent Asset Management for Modern Businesses
              </h1>
              <p className="mt-4 text-lg text-gray-400 md:text-xl">
                Track, manage, and optimize your assets with our comprehensive asset management platform. Gain insights,
                reduce costs, and improve efficiency.
              </p>
              <div className="mt-8 flex flex-wrap gap-4">
                <Button asChild size="lg" className="rounded-full">
                  <Link href="/register">Get Started</Link>
                </Button>
                <Button asChild variant="outline" size="lg" className="rounded-full">
                  <Link href="/login">Sign In</Link>
                </Button>
              </div>
            </div>
            <div className="relative h-[300px] md:h-[400px] lg:h-[500px]">
              <HeroAnimation />
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 md:py-24 bg-[#111827]">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white">Powerful Features</h2>
            <p className="mt-4 text-lg text-gray-400">
              Everything you need to manage your assets effectively
            </p>
          </div>
          <FeatureCards />
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-16 md:py-24 bg-[#111827]">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white">What Our Clients Say</h2>
            <p className="mt-4 text-lg text-gray-400">
              Join thousands of satisfied customers who trust WizeAssets
            </p>
          </div>
          <TestimonialSlider />
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-16 md:py-24 bg-[#0f172a]">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white">Simple, Transparent Pricing</h2>
            <p className="mt-4 text-lg text-gray-400">
              Choose the plan that best fits your needs
            </p>
          </div>
          <PricingSection />
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 md:py-24 bg-[#111827]">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white">Ready to Get Started?</h2>
            <p className="mt-4 text-lg text-gray-400">
              Join thousands of businesses already using WizeAssets
            </p>
            <div className="mt-8 flex justify-center gap-4">
              <Button asChild size="lg" className="rounded-full">
                <Link href="/register">Start Free Trial</Link>
              </Button>
              <Button asChild variant="outline" size="lg" className="rounded-full">
                <Link href="/contact">Contact Sales</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Floating Assets Background */}
      <FloatingAssets />
    </div>
  )
}

