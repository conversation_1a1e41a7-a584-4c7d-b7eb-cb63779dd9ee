import { google } from '@ai-sdk/google';
import { streamText, tool } from 'ai';
import { z } from 'zod';
import { NextRequest } from 'next/server';
import { AIEngineService } from '@/lib/advanced-features/ai-engine/services';
import { assetService } from '@/lib/services/asset-service';

// Allow streaming responses up to 30 seconds
export const maxDuration = 30;

export async function POST(req: NextRequest) {
  try {
    const { messages } = await req.json();
    const aiService = AIEngineService.getInstance();

    const result = streamText({
      model: google('gemini-2.0-flash-exp'),
      messages,
      tools: {
        // Asset Management Tools
        analyzeAsset: tool({
          description: 'Analyze an asset for condition, maintenance needs, and performance insights',
          parameters: z.object({
            assetId: z.string().describe('The ID of the asset to analyze'),
            includeHistoricalData: z.boolean().optional().describe('Whether to include historical data in analysis')
          }),
          execute: async ({ assetId, includeHistoricalData }) => {
            try {
              // Mock asset data - replace with actual database query
              const assetData = {
                id: assetId,
                name: 'Sample Asset',
                category: 'IT Equipment',
                purchaseDate: '2023-01-15',
                value: 1500,
                status: 'active',
                location: 'Office Floor 2',
                maintenanceHistory: includeHistoricalData ? [
                  { date: '2023-12-01', type: 'preventive', cost: 150 },
                  { date: '2023-09-15', type: 'corrective', cost: 300 }
                ] : []
              };

              const analysis = await aiService.analyzeAsset(assetId, assetData);
              return {
                success: true,
                analysis,
                message: `Asset ${assetId} analysis completed successfully`
              };
            } catch (error) {
              return {
                success: false,
                error: 'Failed to analyze asset',
                message: `Error analyzing asset ${assetId}: ${error instanceof Error ? error.message : 'Unknown error'}`
              };
            }
          }
        }),

        predictMaintenance: tool({
          description: 'Generate predictive maintenance recommendations for an asset',
          parameters: z.object({
            assetId: z.string().describe('The ID of the asset for maintenance prediction'),
            timeframe: z.string().optional().describe('Prediction timeframe (e.g., "3 months", "1 year")')
          }),
          execute: async ({ assetId, timeframe }) => {
            try {
              // Mock historical data - replace with actual database query
              const historicalData = [
                { date: '2023-12-01', event: 'maintenance', cost: 150, downtime: 2 },
                { date: '2023-09-15', event: 'repair', cost: 300, downtime: 8 },
                { date: '2023-06-10', event: 'inspection', cost: 50, downtime: 0.5 }
              ];

              const prediction = await aiService.generateMaintenancePredictions(assetId, historicalData);
              return {
                success: true,
                prediction,
                timeframe: timeframe || 'default',
                message: `Maintenance predictions generated for asset ${assetId}`
              };
            } catch (error) {
              return {
                success: false,
                error: 'Failed to generate maintenance predictions',
                message: `Error predicting maintenance for asset ${assetId}: ${error instanceof Error ? error.message : 'Unknown error'}`
              };
            }
          }
        }),

        optimizeCosts: tool({
          description: 'Analyze and provide cost optimization recommendations',
          parameters: z.object({
            scope: z.enum(['single_asset', 'category', 'all_assets']).describe('Scope of cost optimization analysis'),
            assetId: z.string().optional().describe('Asset ID if scope is single_asset'),
            category: z.string().optional().describe('Asset category if scope is category'),
            timeframe: z.string().optional().describe('Analysis timeframe')
          }),
          execute: async ({ scope, assetId, category, timeframe }) => {
            try {
              // Mock asset data based on scope - replace with actual database queries
              let assetData = [];
              
              switch (scope) {
                case 'single_asset':
                  assetData = [{
                    id: assetId,
                    name: 'Sample Asset',
                    category: 'IT Equipment',
                    costs: { maintenance: 1200, operation: 800, depreciation: 500 }
                  }];
                  break;
                case 'category':
                  assetData = [
                    { id: '1', name: 'Asset 1', category, costs: { maintenance: 1200, operation: 800, depreciation: 500 } },
                    { id: '2', name: 'Asset 2', category, costs: { maintenance: 900, operation: 600, depreciation: 400 } }
                  ];
                  break;
                case 'all_assets':
                  assetData = [
                    { id: '1', name: 'Asset 1', category: 'IT Equipment', costs: { maintenance: 1200, operation: 800, depreciation: 500 } },
                    { id: '2', name: 'Asset 2', category: 'Machinery', costs: { maintenance: 2500, operation: 1500, depreciation: 1000 } },
                    { id: '3', name: 'Asset 3', category: 'Furniture', costs: { maintenance: 300, operation: 100, depreciation: 200 } }
                  ];
                  break;
              }

              const optimization = await aiService.generateCostOptimization(assetData, timeframe || '1 year');
              return {
                success: true,
                optimization,
                scope,
                message: `Cost optimization analysis completed for ${scope}`
              };
            } catch (error) {
              return {
                success: false,
                error: 'Failed to generate cost optimization',
                message: `Error optimizing costs: ${error instanceof Error ? error.message : 'Unknown error'}`
              };
            }
          }
        }),

        searchAssets: tool({
          description: 'Search for assets based on various criteria',
          parameters: z.object({
            query: z.string().describe('Search query or criteria'),
            category: z.string().optional().describe('Filter by asset category'),
            status: z.string().optional().describe('Filter by asset status'),
            location: z.string().optional().describe('Filter by location'),
            limit: z.number().optional().describe('Maximum number of results to return')
          }),
          execute: async ({ query, category, status, location, limit = 10 }) => {
            try {
              // Use real asset service to search assets
              const filter = {
                search: query,
                category,
                status: status as "active" | "maintenance" | "disposed" | undefined,
                location,
                includeRelations: false,
              };

              const pagination = {
                page: 1,
                limit,
                sortBy: 'createdAt',
                sortOrder: 'desc' as const,
              };

              const result = await assetService.getAssetsWithFilter(filter, pagination);

              // Return simplified asset data for AI
              const assets = result.assets.map(asset => ({
                id: asset.id,
                name: asset.name,
                category: asset.category,
                status: asset.status,
                location: asset.location,
                purchasePrice: asset.purchasePrice,
                purchaseDate: asset.purchaseDate.toISOString(),
                department: asset.department,
              }));

              return {
                success: true,
                assets,
                total: result.total,
                query: { query, category, status, location, limit }
              };
            } catch (error) {
              console.error('Error searching assets:', error);
              return {
                success: false,
                assets: [],
                total: 0,
                error: 'Failed to search assets',
                query: { query, category, status, location, limit }
              };
            }
          }
        }),

        getMaintenanceSchedule: tool({
          description: 'Get maintenance schedule for assets',
          parameters: z.object({
            assetId: z.string().optional().describe('Specific asset ID, or all assets if not provided'),
            timeframe: z.string().optional().describe('Timeframe for schedule (e.g., "next_week", "next_month")')
          }),
          execute: async ({ assetId, timeframe }) => {
            // Mock maintenance schedule - replace with actual database query
            const mockSchedule = [
              { 
                id: '1',
                assetId: assetId || '1', 
                assetName: 'Dell Laptop XPS 15',
                task: 'Software Update', 
                dueDate: '2024-02-15', 
                priority: 'Medium',
                estimatedDuration: '2 hours',
                assignedTo: 'IT Team'
              },
              { 
                id: '2',
                assetId: assetId || '2', 
                assetName: 'Toyota Forklift',
                task: 'Oil Change', 
                dueDate: '2024-02-10', 
                priority: 'High',
                estimatedDuration: '1 hour',
                assignedTo: 'Maintenance Team'
              },
              { 
                id: '3',
                assetId: assetId || '3', 
                assetName: 'HP Printer',
                task: 'Toner Replacement', 
                dueDate: '2024-02-20', 
                priority: 'Low',
                estimatedDuration: '30 minutes',
                assignedTo: 'Office Staff'
              }
            ];

            let filteredSchedule = assetId 
              ? mockSchedule.filter(item => item.assetId === assetId)
              : mockSchedule;

            // Apply timeframe filter if specified
            if (timeframe) {
              const now = new Date();
              const filterDate = new Date();
              
              switch (timeframe) {
                case 'next_week':
                  filterDate.setDate(now.getDate() + 7);
                  break;
                case 'next_month':
                  filterDate.setMonth(now.getMonth() + 1);
                  break;
                default:
                  filterDate.setMonth(now.getMonth() + 3); // Default to 3 months
              }
              
              filteredSchedule = filteredSchedule.filter(item => 
                new Date(item.dueDate) <= filterDate
              );
            }

            return {
              success: true,
              schedule: filteredSchedule,
              total: filteredSchedule.length,
              timeframe: timeframe || 'all'
            };
          }
        }),

        detectAnomalies: tool({
          description: 'Detect anomalies in asset data or performance metrics',
          parameters: z.object({
            entityType: z.enum(['assets', 'maintenance', 'costs', 'utilization']).describe('Type of data to analyze for anomalies'),
            timeframe: z.string().optional().describe('Time period for anomaly detection')
          }),
          execute: async ({ entityType, timeframe }) => {
            try {
              // Mock data for anomaly detection - replace with actual database queries
              const mockData = {
                assets: [
                  { id: '1', metric: 'uptime', value: 0.95, expected: 0.98 },
                  { id: '2', metric: 'uptime', value: 0.75, expected: 0.95 }, // Anomaly
                ],
                maintenance: [
                  { assetId: '1', cost: 150, date: '2024-01-15' },
                  { assetId: '2', cost: 2500, date: '2024-01-20' }, // Anomaly - high cost
                ],
                costs: [
                  { category: 'IT Equipment', monthly: 5000, expected: 4500 },
                  { category: 'Machinery', monthly: 15000, expected: 12000 }, // Anomaly
                ],
                utilization: [
                  { assetId: '1', rate: 0.85, optimal: 0.80 },
                  { assetId: '2', rate: 0.25, optimal: 0.75 }, // Anomaly - underutilized
                ]
              };

              const dataToAnalyze = mockData[entityType] || [];
              const anomalies = await aiService.detectAnomalies(entityType, dataToAnalyze);

              return {
                success: true,
                anomalies,
                entityType,
                timeframe: timeframe || 'last_30_days',
                dataPoints: dataToAnalyze.length
              };
            } catch (error) {
              return {
                success: false,
                error: 'Failed to detect anomalies',
                message: `Error detecting anomalies in ${entityType}: ${error instanceof Error ? error.message : 'Unknown error'}`
              };
            }
          }
        })
      },
      maxSteps: 5,
      temperature: 0.3,
    });

    return result.toDataStreamResponse();
  } catch (error) {
    console.error('AI Chat API Error:', error);
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error', 
        message: error instanceof Error ? error.message : 'Unknown error' 
      }),
      { 
        status: 500, 
        headers: { 'Content-Type': 'application/json' } 
      }
    );
  }
}