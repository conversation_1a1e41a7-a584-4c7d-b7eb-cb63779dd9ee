import { NextRequest, NextResponse } from 'next/server';
import { AIEngineService } from '@/lib/advanced-features/ai-engine/services';

export async function POST(req: NextRequest) {
  try {
    const { assetId, historicalData } = await req.json();

    if (!assetId) {
      return NextResponse.json(
        { error: 'Asset ID is required' },
        { status: 400 }
      );
    }

    const aiService = AIEngineService.getInstance();
    const prediction = await aiService.generateMaintenancePredictions(assetId, historicalData);

    return NextResponse.json({
      success: true,
      prediction,
      message: `Maintenance predictions generated for asset ${assetId}`
    });
  } catch (error) {
    console.error('Maintenance prediction error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to generate maintenance predictions', 
        message: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}