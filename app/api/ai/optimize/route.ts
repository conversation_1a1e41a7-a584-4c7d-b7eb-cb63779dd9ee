import { NextRequest, NextResponse } from 'next/server';
import { AIEngineService } from '@/lib/advanced-features/ai-engine/services';

export async function POST(req: NextRequest) {
  try {
    const { assetData, timeframe = "1 year" } = await req.json();

    if (!assetData || !Array.isArray(assetData)) {
      return NextResponse.json(
        { error: 'Asset data array is required' },
        { status: 400 }
      );
    }

    const aiService = AIEngineService.getInstance();
    const optimization = await aiService.generateCostOptimization(assetData, timeframe);

    return NextResponse.json({
      success: true,
      optimization,
      message: `Cost optimization analysis completed for ${assetData.length} assets`
    });
  } catch (error) {
    console.error('Cost optimization error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to generate cost optimization', 
        message: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}