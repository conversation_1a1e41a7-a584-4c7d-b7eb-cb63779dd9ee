import { NextRequest, NextResponse } from 'next/server';
import { AIEngineService } from '@/lib/advanced-features/ai-engine/services';

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const category = searchParams.get('category');

    const aiService = AIEngineService.getInstance();
    const insights = aiService.getInsights(category || undefined);
    const models = aiService.getModels();
    const alerts = aiService.getAlerts(true);

    return NextResponse.json({
      success: true,
      data: {
        insights,
        models,
        alerts,
        statistics: {
          totalInsights: insights.length,
          criticalInsights: insights.filter(i => i.impact === 'critical').length,
          actionableInsights: insights.filter(i => i.actionable).length,
          avgConfidence: insights.length > 0 
            ? insights.reduce((sum, i) => sum + i.confidence, 0) / insights.length 
            : 0,
          activeModels: models.length,
          activeAlerts: alerts.length
        }
      }
    });
  } catch (error) {
    console.error('Get insights error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to get insights', 
        message: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const { action, ...params } = await req.json();

    const aiService = AIEngineService.getInstance();

    switch (action) {
      case 'detectAnomalies':
        const { entityType, data } = params;
        if (!entityType || !data) {
          return NextResponse.json(
            { error: 'Entity type and data are required for anomaly detection' },
            { status: 400 }
          );
        }
        const anomalies = await aiService.detectAnomalies(entityType, data);
        return NextResponse.json({
          success: true,
          anomalies,
          message: `Anomaly detection completed for ${entityType}`
        });

      case 'generateAlert':
        const { entityId, entityType: alertEntityType, alertData } = params;
        if (!entityId || !alertEntityType || !alertData) {
          return NextResponse.json(
            { error: 'Entity ID, type, and data are required for alert generation' },
            { status: 400 }
          );
        }
        const alert = await aiService.generateSmartAlert(entityId, alertEntityType, alertData);
        return NextResponse.json({
          success: true,
          alert,
          message: `Smart alert generated for ${alertEntityType} ${entityId}`
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action specified' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Insights action error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to process insights action', 
        message: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}