import { NextRequest, NextResponse } from 'next/server';
import { AIEngineService } from '@/lib/advanced-features/ai-engine/services';

export async function POST(req: NextRequest) {
  try {
    const { assetId, assetData } = await req.json();

    if (!assetId || !assetData) {
      return NextResponse.json(
        { error: 'Asset ID and data are required' },
        { status: 400 }
      );
    }

    const aiService = AIEngineService.getInstance();
    const analysis = await aiService.analyzeAsset(assetId, assetData);

    return NextResponse.json({
      success: true,
      analysis,
      message: `Asset ${assetId} analyzed successfully`
    });
  } catch (error) {
    console.error('Asset analysis error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to analyze asset', 
        message: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}