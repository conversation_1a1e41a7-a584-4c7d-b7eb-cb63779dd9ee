import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth-options";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

const updateProfileSchema = z.object({
  section: z.enum(["personal", "company", "preferences"]),
  data: z.record(z.any()),
});

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || session.user.role !== "client") {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get user profile with client profile
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: {
        clientProfile: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Return profile data
    return NextResponse.json({
      id: user.id,
      name: user.name,
      email: user.email,
      phone: user.phone,
      jobTitle: user.jobTitle,
      company: user.company,
      avatarUrl: user.avatarUrl,
      status: user.status,
      emailVerified: user.emailVerified,
      clientProfile: user.clientProfile,
      preferences: user.preferences,
    });

  } catch (error) {
    console.error("Error fetching profile:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || session.user.role !== "client") {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { section, data } = updateProfileSchema.parse(body);

    const userId = session.user.id;

    switch (section) {
      case "personal":
        await prisma.user.update({
          where: { id: userId },
          data: {
            name: data.name,
            phone: data.phone,
            jobTitle: data.jobTitle,
          },
        });
        break;

      case "company":
        // Update client profile
        await prisma.clientProfile.upsert({
          where: { userId },
          update: {
            companyName: data.companyName,
            industry: data.industry,
            companySize: data.companySize,
            website: data.website,
          },
          create: {
            userId,
            companyName: data.companyName,
            industry: data.industry,
            companySize: data.companySize,
            website: data.website,
          },
        });
        break;

      case "preferences":
        await prisma.user.update({
          where: { id: userId },
          data: {
            preferences: data,
          },
        });
        break;

      default:
        return NextResponse.json(
          { error: "Invalid section" },
          { status: 400 }
        );
    }

    return NextResponse.json({
      message: "Profile updated successfully",
    });

  } catch (error) {
    console.error("Error updating profile:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}