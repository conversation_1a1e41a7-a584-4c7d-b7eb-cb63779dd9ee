import { NextRequest, NextResponse } from "next/server";
import { AssetOperationWorkflow } from "@/lib/engines/asset-operation-workflow";
import { AssetOperationType } from "@/lib/types/asset-type-forms";
import prisma from "@/lib/prisma";

// POST /api/asset-operations - Execute asset operation workflow
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      operationType,
      assetId,
      assetTypeId,
      data: formData,
      context,
      skipValidation,
      triggerWorkflows,
    } = body;

    // Validate required fields
    if (!operationType || !assetTypeId || !formData || !context) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Validate operation type
    const validOperations: AssetOperationType[] = [
      "asset.create",
      "asset.update",
      "asset.transfer",
      "asset.disposal",
      "maintenance.log",
      "maintenance.schedule",
      "depreciation.calculate",
      "lifecycle.transition",
      "inventory.audit",
      "requisition.asset",
      "requisition.approve",
      "requisition.fulfill",
    ];

    if (!validOperations.includes(operationType)) {
      return NextResponse.json(
        { error: "Invalid operation type" },
        { status: 400 }
      );
    }

    // Execute the operation workflow
    const result = await AssetOperationWorkflow.executeOperation({
      operationType,
      assetId,
      assetTypeId,
      formData,
      context,
      skipValidation: skipValidation || false,
      triggerWorkflows: triggerWorkflows !== false, // Default to true
    });

    if (!result.success) {
      return NextResponse.json(
        {
          error: "Operation failed",
          errors: result.errors,
          warnings: result.warnings,
        },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      assetId: result.assetId,
      data: result.data,
      warnings: result.warnings,
      triggeredWorkflows: result.triggeredWorkflows,
      notifications: result.notifications,
    });

  } catch (error) {
    console.error("Error executing asset operation:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// GET /api/asset-operations - Get operation history
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const assetId = searchParams.get("assetId");
    const operationType = searchParams.get("operationType");
    const userId = searchParams.get("userId");
    const limit = parseInt(searchParams.get("limit") || "50");
    const offset = parseInt(searchParams.get("offset") || "0");

    const where: any = {};

    if (assetId) {
      where.assetId = assetId;
    }

    if (operationType) {
      where.operationType = operationType;
    }

    if (userId) {
      where.performedBy = userId;
    }

    const operations = await prisma.assetOperationHistory.findMany({
      where,
      orderBy: {
        performedAt: "desc",
      },
      take: limit,
      skip: offset,
      include: {
        asset: {
          select: {
            id: true,
            name: true,
            category: true,
            assetType: {
              select: {
                id: true,
                name: true,
                code: true,
              },
            },
          },
        },
      },
    });

    // Parse JSON fields
    const parsedOperations = operations.map(op => ({
      ...op,
      formData: op.formData ? JSON.parse(op.formData) : null,
    }));

    return NextResponse.json({
      operations: parsedOperations,
      total: operations.length,
      limit,
      offset,
    });

  } catch (error) {
    console.error("Error getting operation history:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}