import { NextRequest, NextResponse } from "next/server";
import { financialService } from "@/lib/services/financial-service";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth-options";

export async function GET(
  request: NextRequest,
  { params }: { params: { assetId: string } }
) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Only admin and manager users can access financial data
    if (!["admin", "manager"].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    const { assetId } = params;
    
    const tcoAnalysis = await financialService.calculateTCO(assetId);
    
    if (!tcoAnalysis) {
      return NextResponse.json(
        { success: false, error: "Asset not found" },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: tcoAnalysis,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error calculating TCO:", error);
    
    const errorMessage = error instanceof Error ? error.message : "Failed to calculate TCO";
    
    return NextResponse.json(
      { 
        success: false,
        error: errorMessage,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
