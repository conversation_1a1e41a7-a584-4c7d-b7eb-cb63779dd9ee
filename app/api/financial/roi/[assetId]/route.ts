import { NextRequest, NextResponse } from "next/server";
import { financialService } from "@/lib/services/financial-service";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth-options";

export async function POST(
  request: NextRequest,
  { params }: { params: { assetId: string } }
) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Only admin and manager users can access financial data
    if (!["admin", "manager"].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    const { assetId } = params;
    const { annualBenefits } = await request.json();
    
    if (!annualBenefits || annualBenefits <= 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: "Annual benefits must be provided and greater than 0" 
        },
        { status: 400 }
      );
    }
    
    const roiAnalysis = await financialService.calculateROI(assetId, annualBenefits);
    
    if (!roiAnalysis) {
      return NextResponse.json(
        { success: false, error: "Asset not found" },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: roiAnalysis,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error calculating ROI:", error);
    
    const errorMessage = error instanceof Error ? error.message : "Failed to calculate ROI";
    
    return NextResponse.json(
      { 
        success: false,
        error: errorMessage,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
