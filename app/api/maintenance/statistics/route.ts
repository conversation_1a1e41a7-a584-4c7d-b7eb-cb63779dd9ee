import { NextRequest, NextResponse } from "next/server";
import { maintenanceService } from "@/lib/services/maintenance-service";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth-options";

export async function GET(request: NextRequest) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const statistics = await maintenanceService.getMaintenanceStatistics();
    
    return NextResponse.json({
      success: true,
      data: statistics,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error fetching maintenance statistics:", error);
    
    const errorMessage = error instanceof Error ? error.message : "Failed to fetch maintenance statistics";
    
    return NextResponse.json(
      { 
        success: false,
        error: errorMessage,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
