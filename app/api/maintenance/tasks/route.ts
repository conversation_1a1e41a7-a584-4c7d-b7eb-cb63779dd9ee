import { NextRequest, NextResponse } from "next/server";
import { MaintenanceEngine } from "@/lib/engines/maintenance-engine";
import prisma from "@/lib/prisma";

// GET /api/maintenance/tasks - Get maintenance tasks
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const assetId = searchParams.get("assetId");
    const assetTypeId = searchParams.get("assetTypeId");
    const status = searchParams.get("status");
    const overdue = searchParams.get("overdue");
    const upcoming = searchParams.get("upcoming");
    
    const where: any = {};
    
    if (assetId) {
      where.assetId = assetId;
    }
    
    if (assetTypeId) {
      where.asset = {
        assetTypeId,
      };
    }
    
    if (status) {
      where.status = status;
    }
    
    if (overdue === "true") {
      where.status = {
        not: "completed",
      };
      where.dueDate = {
        lt: new Date(),
      };
    }
    
    if (upcoming === "true") {
      where.status = "scheduled";
      where.scheduledDate = {
        gt: new Date(),
        lt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // Next 30 days
      };
    }
    
    const tasks = await prisma.maintenanceTask.findMany({
      where,
      include: {
        asset: {
          include: {
            assetType: {
              select: {
                id: true,
                name: true,
                code: true,
              },
            },
          },
        },
      },
      orderBy: {
        scheduledDate: "asc",
      },
    });

    // Transform the data to match expected format for maintenance history
    const transformedTasks = tasks.map(task => ({
      id: task.id,
      type: task.type,
      completedDate: task.completedDate,
      scheduledDate: task.scheduledDate,
      performedBy: task.assignedTo || task.createdBy,
      cost: task.actualCost || task.estimatedCost,
      notes: task.completionNotes || task.description,
      status: task.status,
      asset: task.asset,
    }));
    
    return NextResponse.json({
      tasks: transformedTasks,
      total: tasks.length,
    });
    
  } catch (error) {
    console.error("Error getting maintenance tasks:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/maintenance/tasks - Generate maintenance tasks
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { assetId, assetTypeId, scheduleId } = body;
    
    if (!assetId && !assetTypeId) {
      return NextResponse.json(
        { error: "Either assetId or assetTypeId is required" },
        { status: 400 }
      );
    }
    
    let result;
    
    if (assetTypeId) {
      // Generate tasks for all assets of this type
      result = await MaintenanceEngine.generateTasksForAssetType(assetTypeId);
    } else if (assetId && scheduleId) {
      // Generate tasks for specific asset and schedule
      const asset = await prisma.asset.findUnique({
        where: { id: assetId },
        include: {
          assetType: {
            include: {
              maintenanceSchedules: true,
            },
          },
        },
      });
      
      if (!asset) {
        return NextResponse.json(
          { error: "Asset not found" },
          { status: 404 }
        );
      }
      
      const schedule = asset.assetType?.maintenanceSchedules.find(s => s.id === scheduleId);
      
      if (!schedule) {
        return NextResponse.json(
          { error: "Maintenance schedule not found" },
          { status: 404 }
        );
      }
      
      result = await MaintenanceEngine.generateMaintenanceTasks({
        assetId,
        scheduleId,
        schedule,
      });
    } else {
      return NextResponse.json(
        { error: "Invalid parameters" },
        { status: 400 }
      );
    }
    
    return NextResponse.json(result);
    
  } catch (error) {
    console.error("Error generating maintenance tasks:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// PUT /api/maintenance/tasks - Complete maintenance task
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { taskId, completedBy, completionNotes, actualDuration, actualCost, checklistResults } = body;
    
    if (!taskId || !completedBy) {
      return NextResponse.json(
        { error: "Task ID and completed by are required" },
        { status: 400 }
      );
    }
    
    await MaintenanceEngine.completeMaintenanceTask(taskId, {
      completedBy,
      completionNotes,
      actualDuration,
      actualCost,
      checklistResults,
    });
    
    return NextResponse.json({ success: true });
    
  } catch (error) {
    console.error("Error completing maintenance task:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}