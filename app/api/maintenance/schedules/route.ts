import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";

// GET /api/maintenance/schedules - Get maintenance schedules
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const assetId = searchParams.get("assetId");
    const assetTypeId = searchParams.get("assetTypeId");
    const active = searchParams.get("active");

    const where: any = {};

    if (assetId) {
      // Get schedules for a specific asset (from asset type)
      const asset = await prisma.asset.findUnique({
        where: { id: assetId },
        select: { assetTypeId: true }
      });
      
      if (asset) {
        where.assetTypeId = asset.assetTypeId;
      }
    } else if (assetTypeId) {
      where.assetTypeId = assetTypeId;
    }

    if (active === "true") {
      where.isActive = true;
    } else if (active === "false") {
      where.isActive = false;
    }

    const schedules = await prisma.maintenanceSchedule.findMany({
      where,
      include: {
        assetType: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json({
      schedules,
      total: schedules.length,
    });

  } catch (error) {
    console.error("Error getting maintenance schedules:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/maintenance/schedules - Create maintenance schedule
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      assetTypeId,
      name,
      type,
      description,
      intervalDays,
      estimatedDuration,
      estimatedCost,
      priority,
      checklist,
      isActive,
    } = body;

    // Validate required fields
    if (!assetTypeId || !name || !type || !intervalDays) {
      return NextResponse.json(
        { error: "Missing required fields: assetTypeId, name, type, intervalDays" },
        { status: 400 }
      );
    }

    // Create the maintenance schedule
    const schedule = await prisma.maintenanceSchedule.create({
      data: {
        assetTypeId,
        name,
        type,
        description,
        intervalDays: parseInt(intervalDays),
        estimatedDuration: estimatedDuration ? parseInt(estimatedDuration) : null,
        estimatedCost: estimatedCost ? parseFloat(estimatedCost) : null,
        priority: priority || "medium",
        checklist: checklist ? JSON.stringify(checklist) : null,
        isActive: isActive !== false, // Default to true
        createdBy: "current-user", // This should come from auth context
      },
      include: {
        assetType: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      schedule,
    });

  } catch (error) {
    console.error("Error creating maintenance schedule:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// PUT /api/maintenance/schedules - Update maintenance schedule
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      scheduleId,
      name,
      type,
      description,
      intervalDays,
      estimatedDuration,
      estimatedCost,
      priority,
      checklist,
      isActive,
    } = body;

    // Validate required fields
    if (!scheduleId) {
      return NextResponse.json(
        { error: "Schedule ID is required" },
        { status: 400 }
      );
    }

    // Update the maintenance schedule
    const schedule = await prisma.maintenanceSchedule.update({
      where: { id: scheduleId },
      data: {
        ...(name && { name }),
        ...(type && { type }),
        ...(description !== undefined && { description }),
        ...(intervalDays && { intervalDays: parseInt(intervalDays) }),
        ...(estimatedDuration !== undefined && { 
          estimatedDuration: estimatedDuration ? parseInt(estimatedDuration) : null 
        }),
        ...(estimatedCost !== undefined && { 
          estimatedCost: estimatedCost ? parseFloat(estimatedCost) : null 
        }),
        ...(priority && { priority }),
        ...(checklist !== undefined && { 
          checklist: checklist ? JSON.stringify(checklist) : null 
        }),
        ...(isActive !== undefined && { isActive }),
        updatedAt: new Date(),
      },
      include: {
        assetType: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      schedule,
    });

  } catch (error) {
    console.error("Error updating maintenance schedule:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// DELETE /api/maintenance/schedules - Delete maintenance schedule
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const scheduleId = searchParams.get("scheduleId");

    if (!scheduleId) {
      return NextResponse.json(
        { error: "Schedule ID is required" },
        { status: 400 }
      );
    }

    // Check if schedule has active tasks
    const activeTasks = await prisma.maintenanceTask.count({
      where: {
        scheduleId,
        status: {
          in: ["scheduled", "in_progress"]
        }
      }
    });

    if (activeTasks > 0) {
      return NextResponse.json(
        { error: "Cannot delete schedule with active maintenance tasks" },
        { status: 400 }
      );
    }

    // Delete the maintenance schedule
    await prisma.maintenanceSchedule.delete({
      where: { id: scheduleId }
    });

    return NextResponse.json({
      success: true,
      message: "Maintenance schedule deleted successfully"
    });

  } catch (error) {
    console.error("Error deleting maintenance schedule:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}