import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { FormRuntimeEngine } from "@/lib/engines/form-runtime-engine";
import { AssetOperationType } from "@/lib/types/asset-type-forms";

// GET /api/asset-type-forms - Get form for specific asset type and operation
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const assetTypeId = searchParams.get("assetTypeId");
    const operationType = searchParams.get("operationType") as AssetOperationType;
    const assetId = searchParams.get("assetId");
    const userId = searchParams.get("userId");
    const userRole = searchParams.get("userRole");
    
    if (!assetTypeId || !operationType || !userId || !userRole) {
      return NextResponse.json(
        { error: "Missing required parameters" },
        { status: 400 }
      );
    }
    
    const context = {
      assetId,
      assetTypeId,
      operationType,
      userId,
      userRole,
    };
    
    // Get form definition
    const form = await FormRuntimeEngine.getFormForOperation(
      assetTypeId,
      operationType,
      context
    );
    
    if (!form) {
      return NextResponse.json(
        { error: "No form found for this asset type and operation" },
        { status: 404 }
      );
    }
    
    // Get pre-population data
    const prePopulationData = await FormRuntimeEngine.getPrePopulationData(
      assetId || undefined,
      assetTypeId,
      operationType,
      context
    );
    
    return NextResponse.json({
      form,
      prePopulationData,
    });
    
  } catch (error) {
    console.error("Error getting asset type form:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/asset-type-forms - Submit form data
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { formId, assetTypeId, operationType, data, context } = body;
    
    if (!formId || !assetTypeId || !operationType || !data || !context) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }
    
    // Process form submission
    const result = await FormRuntimeEngine.processFormSubmission({
      formId,
      assetTypeId,
      operationType,
      data,
      context,
    });
    
    if (!result.success) {
      return NextResponse.json(
        { 
          error: "Form submission failed",
          errors: result.errors,
          validationResults: result.validationResults,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: result.data,
      warnings: result.warnings,
    });
    
  } catch (error) {
    console.error("Error processing form submission:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// PUT /api/asset-type-forms - Associate form with asset type and operation
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { assetTypeId, formId, operationType, isDefault, createdBy } = body;
    
    if (!assetTypeId || !formId || !operationType || !createdBy) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }
    
    // If setting as default, unset other defaults for this asset type and operation
    if (isDefault) {
      await prisma.assetTypeForm.updateMany({
        where: {
          assetTypeId,
          operationType,
          isDefault: true,
        },
        data: {
          isDefault: false,
        },
      });
    }
    
    // Create or update association
    const assetTypeForm = await prisma.assetTypeForm.upsert({
      where: {
        assetTypeId_operationType_isDefault: {
          assetTypeId,
          operationType,
          isDefault: isDefault || false,
        },
      },
      update: {
        formId,
        isActive: true,
        updatedAt: new Date(),
      },
      create: {
        assetTypeId,
        formId,
        operationType,
        isDefault: isDefault || false,
        createdBy,
      },
      include: {
        form: true,
        assetType: true,
      },
    });
    
    return NextResponse.json(assetTypeForm);
    
  } catch (error) {
    console.error("Error associating form with asset type:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}