import { NextRequest, NextResponse } from "next/server";
import { DepreciationEngine } from "@/lib/engines/depreciation-engine";
import prisma from "@/lib/prisma";

// POST /api/depreciation/calculate - Calculate depreciation for an asset
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { assetId, recalculate } = body;
    
    if (!assetId) {
      return NextResponse.json(
        { error: "Asset ID is required" },
        { status: 400 }
      );
    }
    
    // Get asset with depreciation settings
    const asset = await prisma.asset.findUnique({
      where: { id: assetId },
      include: {
        assetType: {
          include: {
            depreciationSettings: true,
          },
        },
      },
    });
    
    if (!asset) {
      return NextResponse.json(
        { error: "Asset not found" },
        { status: 404 }
      );
    }
    
    if (!asset.assetType?.depreciationSettings) {
      return NextResponse.json(
        { error: "No depreciation settings found for this asset type" },
        { status: 400 }
      );
    }
    
    // Calculate depreciation
    const result = await DepreciationEngine.calculateDepreciation({
      assetId,
      purchasePrice: asset.purchasePrice,
      purchaseDate: asset.purchaseDate,
      settings: asset.assetType.depreciationSettings,
    });
    
    // Save schedule if requested or if no existing schedule
    if (recalculate) {
      await DepreciationEngine.saveDepreciationSchedule(assetId, result.schedule);
    } else {
      const existingSchedule = await prisma.depreciationSchedule.findFirst({
        where: { assetId },
      });
      
      if (!existingSchedule) {
        await DepreciationEngine.saveDepreciationSchedule(assetId, result.schedule);
      }
    }
    
    return NextResponse.json(result);
    
  } catch (error) {
    console.error("Error calculating depreciation:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// GET /api/depreciation/calculate - Get current book value
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const assetId = searchParams.get("assetId");
    
    if (!assetId) {
      return NextResponse.json(
        { error: "Asset ID is required" },
        { status: 400 }
      );
    }
    
    const bookValue = await DepreciationEngine.getCurrentBookValue(assetId);
    
    return NextResponse.json({ bookValue });
    
  } catch (error) {
    console.error("Error getting book value:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}