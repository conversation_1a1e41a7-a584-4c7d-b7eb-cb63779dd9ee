import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { executeWorkflow } from '@/lib/workflow-engine/executor'

// Handle all HTTP methods for webhooks
export async function GET(
  request: NextRequest,
  { params }: { params: { workflowId: string, webhookId: string } }
) {
  return handleWebhook(request, params, 'GET')
}

export async function POST(
  request: NextRequest,
  { params }: { params: { workflowId: string, webhookId: string } }
) {
  return handleWebhook(request, params, 'POST')
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { workflowId: string, webhookId: string } }
) {
  return handleWebhook(request, params, 'PUT')
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { workflowId: string, webhookId: string } }
) {
  return handleWebhook(request, params, 'DELETE')
}

// Common webhook handler
async function handleWebhook(
  request: NextRequest,
  params: { workflowId: string, webhookId: string },
  method: string
) {
  try {
    const { workflowId, webhookId } = params
    
    // Check if workflow exists
    const workflow = await db.workflow.findUnique({
      where: {
        id: workflowId,
        type: 'asset-automation'
      }
    })
    
    if (!workflow) {
      return NextResponse.json(
        { success: false, error: 'Workflow not found' },
        { status: 404 }
      )
    }
    
    // Find webhook in workflow
    const webhook = (workflow.webhooks || []).find(w => w.id === webhookId)
    
    if (!webhook) {
      return NextResponse.json(
        { success: false, error: 'Webhook not found' },
        { status: 404 }
      )
    }
    
    // Check if webhook method matches
    if (webhook.method !== method) {
      return NextResponse.json(
        { success: false, error: `Method not allowed. Expected ${webhook.method}` },
        { status: 405 }
      )
    }
    
    // Check if webhook is active
    if (webhook.isActive === false) {
      return NextResponse.json(
        { success: false, error: 'Webhook is inactive' },
        { status: 403 }
      )
    }
    
    // Parse request body for POST, PUT methods
    let payload = {}
    if (method === 'POST' || method === 'PUT') {
      const contentType = request.headers.get('content-type') || ''
      
      if (contentType.includes('application/json')) {
        payload = await request.json()
      } else if (contentType.includes('application/x-www-form-urlencoded')) {
        const formData = await request.formData()
        payload = Object.fromEntries(formData.entries())
      }
    }
    
    // Parse query parameters for all methods
    const url = new URL(request.url)
    const queryParams: Record<string, string> = {}
    
    url.searchParams.forEach((value, key) => {
      queryParams[key] = value
    })
    
    // Combine payload and query parameters
    const input = {
      ...payload,
      query: queryParams,
      headers: Object.fromEntries(request.headers.entries()),
      method
    }
    
    // Create execution record
    const executionId = `webhook-exec-${Date.now()}`
    const execution = await db.workflowExecution.create({
      data: {
        id: executionId,
        workflowId,
        status: 'queued',
        input: JSON.stringify(input),
        startedAt: new Date().toISOString(),
        context: JSON.stringify({
          workflowId,
          executionId,
          variables: { input },
          nodeResults: {},
          startTime: new Date().toISOString(),
          webhookResponses: {
            [webhookId]: {
              success: true,
              statusCode: 200,
              data: input
            }
          },
          errors: []
        })
      }
    })
    
    // Execute workflow asynchronously
    executeWorkflow(workflow, execution.id, input)
      .catch(error => {
        console.error(`Error executing workflow ${workflowId} via webhook:`, error)
        // Update execution with error
        db.workflowExecution.update({
          where: { id: executionId },
          data: {
            status: 'failed',
            completedAt: new Date().toISOString(),
            output: JSON.stringify({ success: false, error: error.message }),
            context: JSON.stringify({
              ...JSON.parse(execution.context),
              errors: [{ error: error.message }]
            })
          }
        }).catch(err => {
          console.error(`Error updating execution ${executionId}:`, err)
        })
      })
    
    return NextResponse.json({ 
      success: true, 
      message: 'Webhook received and workflow execution started',
      executionId
    })
  } catch (error: any) {
    console.error(`Error processing webhook:`, error)
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to process webhook' },
      { status: 500 }
    )
  }
}