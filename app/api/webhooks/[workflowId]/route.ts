import { NextRequest, NextResponse } from 'next/server'
import TriggerDevService from '@/lib/advanced-features/automation/trigger-service'

export async function GET(
  request: NextRequest,
  { params }: { params: { workflowId: string } }
) {
  return handleWebhook(request, params.workflowId, 'GET')
}

export async function POST(
  request: NextRequest,
  { params }: { params: { workflowId: string } }
) {
  return handleWebhook(request, params.workflowId, 'POST')
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { workflowId: string } }
) {
  return handleWebhook(request, params.workflowId, 'PUT')
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { workflowId: string } }
) {
  return handleWebhook(request, params.workflowId, 'DELETE')
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { workflowId: string } }
) {
  return handleWebhook(request, params.workflowId, 'PATCH')
}

async function handleWebhook(
  request: NextRequest,
  workflowId: string,
  method: string
) {
  try {
    // Get request data
    let body: any = {}
    if (method !== 'GET') {
      try {
        body = await request.json()
      } catch {
        // Ignore JSON parse errors for non-JSON content
      }
    }

    const headers = Object.fromEntries(request.headers.entries())
    const searchParams = Object.fromEntries(request.nextUrl.searchParams.entries())

    // Prepare webhook payload
    const webhookPayload = {
      method,
      headers,
      query: searchParams,
      body,
      timestamp: new Date().toISOString(),
      url: request.url
    }

    // Execute workflow with webhook data
    const execution = await TriggerDevService.executeWorkflow(workflowId, {
      webhook: webhookPayload,
      trigger: 'webhook'
    })

    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Webhook received and workflow triggered',
      executionId: execution.id,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error(`Webhook error for workflow ${workflowId}:`, error)
    
    return NextResponse.json(
      {
        success: false,
        error: 'Webhook processing failed',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}