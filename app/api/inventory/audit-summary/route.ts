import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";

// GET /api/inventory/audit-summary - Get inventory audit summary
export async function GET(request: NextRequest) {
  try {
    // Get total assets count
    const totalAssets = await prisma.asset.count({
      where: {
        status: {
          not: "disposed"
        }
      }
    });

    // Get audited assets count (assets with recent audit operations)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const auditedAssets = (
      await prisma.assetOperationHistory.findMany({
        where: {
          operationType: "inventory.audit",
          performedAt: {
            gte: thirtyDaysAgo
          }
        },
        distinct: ["assetId"]
      })
    ).length;

    // Get discrepancies count (audit operations with discrepancies noted)
    const discrepancies = await prisma.assetOperationHistory.count({
      where: {
        operationType: "inventory.audit",
        performedAt: {
          gte: thirtyDaysAgo
        },
        formData: {
          contains: '"discrepancies"'
        }
      }
    });

    // Get last audit date
    const lastAudit = await prisma.assetOperationHistory.findFirst({
      where: {
        operationType: "inventory.audit"
      },
      orderBy: {
        performedAt: "desc"
      },
      select: {
        performedAt: true
      }
    });

    const summary = {
      totalAssets,
      auditedAssets,
      discrepancies,
      lastAuditDate: lastAudit?.performedAt || null,
      auditCoverage: totalAssets > 0 ? Math.round((auditedAssets / totalAssets) * 100) : 0
    };

    return NextResponse.json({
      summary
    });

  } catch (error) {
    console.error("Error getting audit summary:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}