import { NextRequest, NextResponse } from "next/server";
import { RequisitionService } from "@/lib/services/requisition-service";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth-options";

// Mark this route as dynamic
export const dynamic = 'force-dynamic';

// GET /api/requisitions/dashboard - Get supply chain dashboard metrics
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Only managers and admins can view dashboard metrics
    if (!["manager", "admin"].includes(session.user.role)) {
      return NextResponse.json({ error: "Insufficient permissions" }, { status: 403 });
    }

    const result = await RequisitionService.getDashboardMetrics();

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({
      metrics: result.metrics,
    });

  } catch (error) {
    console.error("Error getting dashboard metrics:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}