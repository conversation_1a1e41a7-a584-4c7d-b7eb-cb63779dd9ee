import { NextRequest, NextResponse } from "next/server";
import { RequisitionService } from "@/lib/services/requisition-service";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth-options";

// POST /api/requisitions/[id]/fulfill - Fulfill requisition
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Only managers and admins can fulfill requisitions
    if (!["manager", "admin"].includes(session.user.role)) {
      return NextResponse.json({ error: "Insufficient permissions" }, { status: 403 });
    }

    const body = await request.json();
    const {
      method,
      assetIds,
      purchaseOrderId,
      leaseAgreementId,
      deliveryDate,
      notes,
      trackingInfo,
    } = body;

    // Validate required fields
    if (!method) {
      return NextResponse.json(
        { error: "Fulfillment method is required" },
        { status: 400 }
      );
    }

    if (!["inventory", "purchase", "lease"].includes(method)) {
      return NextResponse.json(
        { error: "Invalid fulfillment method" },
        { status: 400 }
      );
    }

    // Validate method-specific requirements
    if (method === "inventory" && (!assetIds || assetIds.length === 0)) {
      return NextResponse.json(
        { error: "Asset IDs are required for inventory fulfillment" },
        { status: 400 }
      );
    }

    if (method === "purchase" && !purchaseOrderId) {
      return NextResponse.json(
        { error: "Purchase Order ID is required for purchase fulfillment" },
        { status: 400 }
      );
    }

    if (method === "lease" && !leaseAgreementId) {
      return NextResponse.json(
        { error: "Lease Agreement ID is required for lease fulfillment" },
        { status: 400 }
      );
    }

    const result = await RequisitionService.fulfillRequisition(params.id, {
      fulfillerId: session.user.id,
      method,
      assetIds,
      purchaseOrderId,
      leaseAgreementId,
      deliveryDate: deliveryDate ? new Date(deliveryDate) : undefined,
      notes,
      trackingInfo,
    });

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      requisition: result.requisition,
      message: "Requisition fulfilled successfully",
    });

  } catch (error) {
    console.error("Error fulfilling requisition:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}