import { NextRequest, NextResponse } from "next/server";
import { RequisitionService } from "@/lib/services/requisition-service";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth-options";

// POST /api/requisitions/[id]/approve - Approve or reject requisition
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Only managers and admins can approve requisitions
    if (!["manager", "admin"].includes(session.user.role)) {
      return NextResponse.json({ error: "Insufficient permissions" }, { status: 403 });
    }

    const body = await request.json();
    const { decision, comments, conditions, alternativeOptions } = body;

    // Validate required fields
    if (!decision || !comments) {
      return NextResponse.json(
        { error: "Missing required fields: decision, comments" },
        { status: 400 }
      );
    }

    if (!["approved", "rejected"].includes(decision)) {
      return NextResponse.json(
        { error: "Decision must be 'approved' or 'rejected'" },
        { status: 400 }
      );
    }

    const result = await RequisitionService.processApproval(params.id, {
      approverId: session.user.id,
      decision,
      comments,
      conditions,
      alternativeOptions,
    });

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      requisition: result.requisition,
      message: `Requisition ${decision} successfully`,
    });

  } catch (error) {
    console.error("Error processing approval:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}