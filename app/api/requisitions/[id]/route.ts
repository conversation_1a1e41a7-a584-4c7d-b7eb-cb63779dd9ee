import { NextRequest, NextResponse } from "next/server";
import { RequisitionService } from "@/lib/services/requisition-service";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth-options";

// GET /api/requisitions/[id] - Get single requisition
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const result = await RequisitionService.getRequisition(params.id);

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: result.error === "Requisition not found" ? 404 : 400 }
      );
    }

    // Check permissions - users can only view their own requisitions unless they're admin/manager
    if (
      session.user.role === "user" &&
      result.requisition!.requestorId !== session.user.id
    ) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    return NextResponse.json({
      requisition: result.requisition,
    });

  } catch (error) {
    console.error("Error getting requisition:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// PUT /api/requisitions/[id] - Update requisition
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // First check if requisition exists and user has permission
    const existingResult = await RequisitionService.getRequisition(params.id);
    if (!existingResult.success) {
      return NextResponse.json(
        { error: existingResult.error },
        { status: existingResult.error === "Requisition not found" ? 404 : 400 }
      );
    }

    const requisition = existingResult.requisition!;

    // Check permissions - users can only update their own pending requisitions
    if (
      session.user.role === "user" &&
      (requisition.requestorId !== session.user.id || requisition.status !== "pending")
    ) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Admins and managers can update any requisition, but only pending ones can be modified
    if (requisition.status !== "pending" && session.user.role !== "admin") {
      return NextResponse.json(
        { error: "Can only update pending requisitions" },
        { status: 400 }
      );
    }

    const body = await request.json();
    const {
      quantity,
      priority,
      justification,
      businessCase,
      location,
      department,
      budgetCode,
      expectedDelivery,
      data,
    } = body;

    const result = await RequisitionService.updateRequisition(params.id, {
      quantity,
      priority,
      justification,
      businessCase,
      location,
      department,
      budgetCode,
      expectedDelivery: expectedDelivery ? new Date(expectedDelivery) : undefined,
      data,
    });

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      requisition: result.requisition,
    });

  } catch (error) {
    console.error("Error updating requisition:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// DELETE /api/requisitions/[id] - Cancel requisition
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { reason } = body;

    if (!reason) {
      return NextResponse.json(
        { error: "Cancellation reason is required" },
        { status: 400 }
      );
    }

    // First check if requisition exists and user has permission
    const existingResult = await RequisitionService.getRequisition(params.id);
    if (!existingResult.success) {
      return NextResponse.json(
        { error: existingResult.error },
        { status: existingResult.error === "Requisition not found" ? 404 : 400 }
      );
    }

    const requisition = existingResult.requisition!;

    // Check permissions - users can only cancel their own requisitions
    if (
      session.user.role === "user" &&
      requisition.requestorId !== session.user.id
    ) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const result = await RequisitionService.cancelRequisition(
      params.id,
      session.user.id,
      reason
    );

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      requisition: result.requisition,
    });

  } catch (error) {
    console.error("Error cancelling requisition:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}