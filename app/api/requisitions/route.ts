import { NextRequest, NextResponse } from "next/server";
import { RequisitionService } from "@/lib/services/requisition-service";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth-options";

// GET /api/requisitions - List requisitions
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    
    // Parse filters
    const filters = {
      status: searchParams.get("status")?.split(",") || undefined,
      priority: searchParams.get("priority")?.split(",") || undefined,
      requestorId: searchParams.get("requestorId") || undefined,
      assetTypeId: searchParams.get("assetTypeId") || undefined,
      department: searchParams.get("department") || undefined,
      search: searchParams.get("search") || undefined,
      dateFrom: searchParams.get("dateFrom") ? new Date(searchParams.get("dateFrom")!) : undefined,
      dateTo: searchParams.get("dateTo") ? new Date(searchParams.get("dateTo")!) : undefined,
    };

    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "50");

    // Apply role-based filtering
    if (session.user.role === "user") {
      filters.requestorId = session.user.id;
    }

    const result = await RequisitionService.listRequisitions(filters, page, limit);

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({
      requisitions: result.requisitions,
      pagination: result.pagination,
    });

  } catch (error) {
    console.error("Error listing requisitions:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/requisitions - Create requisition
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const {
      assetTypeId,
      quantity,
      priority,
      justification,
      businessCase,
      location,
      department,
      budgetCode,
      expectedDelivery,
      data,
    } = body;

    // Validate required fields
    if (!assetTypeId || !quantity || !justification) {
      return NextResponse.json(
        { error: "Missing required fields: assetTypeId, quantity, justification" },
        { status: 400 }
      );
    }

    if (quantity <= 0) {
      return NextResponse.json(
        { error: "Quantity must be greater than 0" },
        { status: 400 }
      );
    }

    const result = await RequisitionService.createRequisition({
      requestorId: session.user.id,
      requestorName: session.user.name || session.user.email,
      assetTypeId,
      quantity,
      priority,
      justification,
      businessCase,
      location,
      department,
      budgetCode,
      expectedDelivery: expectedDelivery ? new Date(expectedDelivery) : undefined,
      data: data || {},
    });

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      requisition: result.requisition,
    }, { status: 201 });

  } catch (error) {
    console.error("Error creating requisition:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}