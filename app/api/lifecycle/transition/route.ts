import { NextRequest, NextResponse } from "next/server";
import { LifecycleEngine } from "@/lib/engines/lifecycle-engine";

// POST /api/lifecycle/transition - Transition asset lifecycle stage
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { assetId, fromStageId, toStageId, userId, reason, data, skipValidation } = body;
    
    if (!assetId || !toStageId || !userId) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }
    
    const result = await LifecycleEngine.transitionAsset({
      assetId,
      fromStageId,
      toStageId,
      userId,
      reason,
      data,
      skipValidation,
    });
    
    if (!result.success) {
      return NextResponse.json(
        { 
          error: "Lifecycle transition failed",
          errors: result.errors,
          warnings: result.warnings,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(result);
    
  } catch (error) {
    console.error("Error transitioning lifecycle:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// GET /api/lifecycle/transition - Get available transitions
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const assetId = searchParams.get("assetId");
    
    if (!assetId) {
      return NextResponse.json(
        { error: "Asset ID is required" },
        { status: 400 }
      );
    }
    
    const availableTransitions = await LifecycleEngine.getAvailableTransitions(assetId);
    
    return NextResponse.json({ availableTransitions });
    
  } catch (error) {
    console.error("Error getting available transitions:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}