import { NextRequest, NextResponse } from "next/server";
import { assetRequestService } from "@/lib/services/asset-request-service";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth-options";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id } = params;
    
    // For client users, only allow accessing their own requests
    const userId = session.user.role === "client" ? session.user.id : undefined;
    
    const assetRequest = await assetRequestService.getAssetRequestById(id, userId);
    
    if (!assetRequest) {
      return NextResponse.json(
        { success: false, error: "Asset request not found" },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: assetRequest,
    });
  } catch (error) {
    console.error("Error fetching asset request:", error);
    
    const errorMessage = error instanceof Error ? error.message : "Failed to fetch asset request";
    
    return NextResponse.json(
      { 
        success: false,
        error: errorMessage 
      },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id } = params;
    const data = await request.json();
    
    // For client users, only allow updating their own requests
    const userId = session.user.role === "client" ? session.user.id : undefined;
    
    const assetRequest = await assetRequestService.updateAssetRequest(id, data, userId);
    
    return NextResponse.json({
      success: true,
      data: assetRequest,
      message: "Asset request updated successfully"
    });
  } catch (error) {
    console.error("Error updating asset request:", error);
    
    let errorMessage = "Failed to update asset request";
    let statusCode = 500;
    
    if (error instanceof Error) {
      errorMessage = error.message;
      
      if (error.message.includes("not found") || error.message.includes("access denied")) {
        statusCode = 404;
      } else if (error.message.includes("Validation failed")) {
        statusCode = 400;
      }
    }
    
    return NextResponse.json(
      { 
        success: false,
        error: errorMessage 
      },
      { status: statusCode }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id } = params;
    
    // For client users, only allow deleting their own requests
    const userId = session.user.role === "client" ? session.user.id : undefined;
    
    await assetRequestService.deleteAssetRequest(id, userId);
    
    return NextResponse.json({
      success: true,
      message: "Asset request deleted successfully"
    });
  } catch (error) {
    console.error("Error deleting asset request:", error);
    
    let errorMessage = "Failed to delete asset request";
    let statusCode = 500;
    
    if (error instanceof Error) {
      errorMessage = error.message;
      
      if (error.message.includes("not found") || error.message.includes("access denied")) {
        statusCode = 404;
      } else if (error.message.includes("Cannot delete")) {
        statusCode = 400;
      }
    }
    
    return NextResponse.json(
      { 
        success: false,
        error: errorMessage 
      },
      { status: statusCode }
    );
  }
}
