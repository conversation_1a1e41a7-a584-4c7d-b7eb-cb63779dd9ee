import { NextRequest, NextResponse } from "next/server";
import { assetRequestService } from "@/lib/services/asset-request-service";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth-options";

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Only admin and manager users can reject requests
    if (!["admin", "manager"].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    const { id } = params;
    const data = await request.json();
    const { rejectionReason } = data;
    
    if (!rejectionReason) {
      return NextResponse.json(
        { 
          success: false,
          error: "Rejection reason is required" 
        },
        { status: 400 }
      );
    }
    
    const assetRequest = await assetRequestService.rejectAssetRequest(id, rejectionReason);
    
    return NextResponse.json({
      success: true,
      data: assetRequest,
      message: "Asset request rejected successfully"
    });
  } catch (error) {
    console.error("Error rejecting asset request:", error);
    
    const errorMessage = error instanceof Error ? error.message : "Failed to reject asset request";
    
    return NextResponse.json(
      { 
        success: false,
        error: errorMessage 
      },
      { status: 500 }
    );
  }
}
