import { NextRequest, NextResponse } from "next/server";
import { assetRequestService } from "@/lib/services/asset-request-service";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth-options";

export async function GET(request: NextRequest) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    // For client users, only show their own statistics
    const userId = session.user.role === "client" ? session.user.id : undefined;
    
    const statistics = await assetRequestService.getAssetRequestStatistics(userId);
    
    return NextResponse.json({
      success: true,
      data: statistics,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error fetching asset request statistics:", error);
    
    const errorMessage = error instanceof Error ? error.message : "Failed to fetch asset request statistics";
    
    return NextResponse.json(
      { 
        success: false,
        error: errorMessage,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
