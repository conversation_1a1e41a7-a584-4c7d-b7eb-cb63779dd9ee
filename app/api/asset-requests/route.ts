import { NextRequest, NextResponse } from "next/server";
import { assetRequestService } from "@/lib/services/asset-request-service";
import { validateAssetRequestFilter, validatePagination } from "@/lib/schemas/api";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth-options";
import { z } from "zod";

export async function GET(request: NextRequest) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const searchParams = request.nextUrl.searchParams;
    
    try {
      const filter = validateAssetRequestFilter(Object.fromEntries(searchParams.entries()));
      const pagination = validatePagination(Object.fromEntries(searchParams.entries()));
      
      // For client users, only show their own requests
      const userId = session.user.role === "client" ? session.user.id : undefined;
      
      const result = await assetRequestService.getAssetRequests(filter, pagination, userId);
      
      return NextResponse.json({
        success: true,
        data: result.requests,
        pagination: {
          page: pagination.page,
          limit: pagination.limit,
          total: result.total,
          totalPages: result.totalPages,
          hasNext: pagination.page < result.totalPages,
          hasPrev: pagination.page > 1,
        },
      });
    } catch (validationError) {
      if (validationError instanceof z.ZodError) {
        return NextResponse.json(
          { 
            success: false,
            error: "Invalid filter parameters",
            details: validationError.errors 
          },
          { status: 400 }
        );
      }
      throw validationError;
    }
  } catch (error) {
    console.error("Error fetching asset requests:", error);
    
    const errorMessage = error instanceof Error ? error.message : "Failed to fetch asset requests";
    
    return NextResponse.json(
      { 
        success: false,
        error: errorMessage 
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const data = await request.json();
    
    // Validate required fields
    if (!data.assetName || !data.justification || !data.location) {
      return NextResponse.json(
        { 
          success: false,
          error: "Missing required fields",
          details: "assetName, justification, and location are required" 
        },
        { status: 400 }
      );
    }
    
    const assetRequest = await assetRequestService.createAssetRequest(data, session.user.id);
    
    return NextResponse.json({
      success: true,
      data: assetRequest,
      message: "Asset request created successfully"
    }, { status: 201 });
  } catch (error) {
    console.error("Error creating asset request:", error);
    
    let errorMessage = "Failed to create asset request";
    let statusCode = 500;
    
    if (error instanceof Error) {
      errorMessage = error.message;
      
      // Check if it's a validation error
      if (error.message.includes("Validation failed")) {
        statusCode = 400;
      }
    }
    
    return NextResponse.json(
      { 
        success: false,
        error: errorMessage 
      },
      { status: statusCode }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const data = await request.json();
    const { id, ...updateData } = data;
    
    if (!id) {
      return NextResponse.json(
        { 
          success: false,
          error: "Missing request ID" 
        },
        { status: 400 }
      );
    }
    
    // For client users, only allow updating their own requests
    const userId = session.user.role === "client" ? session.user.id : undefined;
    
    const assetRequest = await assetRequestService.updateAssetRequest(id, updateData, userId);
    
    return NextResponse.json({
      success: true,
      data: assetRequest,
      message: "Asset request updated successfully"
    });
  } catch (error) {
    console.error("Error updating asset request:", error);
    
    let errorMessage = "Failed to update asset request";
    let statusCode = 500;
    
    if (error instanceof Error) {
      errorMessage = error.message;
      
      if (error.message.includes("not found") || error.message.includes("access denied")) {
        statusCode = 404;
      } else if (error.message.includes("Validation failed")) {
        statusCode = 400;
      }
    }
    
    return NextResponse.json(
      { 
        success: false,
        error: errorMessage 
      },
      { status: statusCode }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");
    
    if (!id) {
      return NextResponse.json(
        { 
          success: false,
          error: "Missing request ID" 
        },
        { status: 400 }
      );
    }
    
    // For client users, only allow deleting their own requests
    const userId = session.user.role === "client" ? session.user.id : undefined;
    
    await assetRequestService.deleteAssetRequest(id, userId);
    
    return NextResponse.json({
      success: true,
      message: "Asset request deleted successfully"
    });
  } catch (error) {
    console.error("Error deleting asset request:", error);
    
    let errorMessage = "Failed to delete asset request";
    let statusCode = 500;
    
    if (error instanceof Error) {
      errorMessage = error.message;
      
      if (error.message.includes("not found") || error.message.includes("access denied")) {
        statusCode = 404;
      } else if (error.message.includes("Cannot delete")) {
        statusCode = 400;
      }
    }
    
    return NextResponse.json(
      { 
        success: false,
        error: errorMessage 
      },
      { status: statusCode }
    );
  }
}
