import { NextRequest, NextResponse } from "next/server";
import { AssetTypeTemplateService } from "@/lib/templates/asset-type-templates";
import { AssetTypeDbService } from "@/lib/modules/asset-types/db-service";
import prisma from "@/lib/prisma";

const assetTypeService = AssetTypeDbService.getInstance();

// GET /api/asset-type-templates - Get all templates or filter by category
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get("category");
    const tags = searchParams.get("tags")?.split(",");

    let templates;

    if (category) {
      templates = AssetTypeTemplateService.getTemplatesByCategory(category);
    } else if (tags) {
      templates = AssetTypeTemplateService.searchTemplatesByTags(tags);
    } else {
      templates = AssetTypeTemplateService.getTemplates();
    }

    return NextResponse.json({
      templates,
      total: templates.length,
    });

  } catch (error) {
    console.error("Error getting asset type templates:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/asset-type-templates - Create asset type from template
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { templateId, customizations, createForms = true } = body;

    if (!templateId) {
      return NextResponse.json(
        { error: "Template ID is required" },
        { status: 400 }
      );
    }

    const template = AssetTypeTemplateService.getTemplate(templateId);
    if (!template) {
      return NextResponse.json(
        { error: "Template not found" },
        { status: 404 }
      );
    }

    // Validate template
    const validation = AssetTypeTemplateService.validateTemplate(template);
    if (!validation.isValid) {
      return NextResponse.json(
        {
          error: "Template validation failed",
          errors: validation.errors,
          warnings: validation.warnings,
        },
        { status: 400 }
      );
    }

    // Get or create category
    let category = await prisma.assetCategory.findFirst({
      where: { name: template.category },
    });

    if (!category) {
      category = await prisma.assetCategory.create({
        data: {
          name: template.category,
          description: `Category for ${template.category} assets`,
          level: 1,
          path: `/${template.category.toLowerCase().replace(/\s+/g, "-")}`,
          isActive: true,
        },
      });
    }

    // Create asset type
    const assetTypeData = {
      ...template.assetType,
      ...customizations,
      category,
      customFields: template.customFields,
      lifecycleStages: template.lifecycleStages,
      maintenanceSchedules: template.maintenanceSchedules,
      depreciationSettings: template.depreciationSettings,
      icon: template.icon,
      color: template.color,
      tags: template.tags,
      createdBy: "system", // This should come from authentication
    };

    const createdAssetType = await assetTypeService.createAssetType(assetTypeData);

    // Create form definitions if requested
    const createdForms = [];
    if (createForms && template.formTemplates) {
      for (const formTemplate of template.formTemplates) {
        const form = await prisma.formDefinition.create({
          data: {
            name: `${createdAssetType.name} - ${formTemplate.operationType}`,
            description: `Form for ${formTemplate.operationType} operation on ${createdAssetType.name}`,
            sections: JSON.stringify(formTemplate.sections),
            settings: JSON.stringify(formTemplate.settings),
            isActive: true,
            createdBy: "system",
          },
        });

        // Associate form with asset type
        await prisma.assetTypeForm.create({
          data: {
            assetTypeId: createdAssetType.id,
            formId: form.id,
            operationType: formTemplate.operationType,
            isDefault: true,
            isActive: true,
            createdBy: "system",
          },
        });

        createdForms.push({
          id: form.id,
          name: form.name,
          operationType: formTemplate.operationType,
        });
      }
    }

    return NextResponse.json({
      success: true,
      assetType: createdAssetType,
      forms: createdForms,
      warnings: validation.warnings,
    });

  } catch (error) {
    console.error("Error creating asset type from template:", error);
    return NextResponse.json(
      { 
        error: "Failed to create asset type from template",
        details: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}