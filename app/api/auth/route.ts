import { NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { apiResponse, apiError, withAuth } from '@/lib/utils';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';

// User registration
export async function POST(req: Request) {
  try {
    const { name, email, password, department } = await req.json();

    // Validate input
    if (!name || !email || !password) {
      return apiError('Missing required fields');
    }

    // Check if user exists
    const existingUser = await db.user.findUnique({
      where: { email }
    });

    if (existingUser) {
      return apiError('User already exists');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create user
    const user = await db.user.create({
      data: {
        name,
        email,
        password: hashedPassword,
        department
      }
    });

    // Generate JWT token
    const token = jwt.sign(
      { userId: user.id, email: user.email },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '24h' }
    );

    return apiResponse({ user, token });
  } catch (error) {
    console.error('Registration error:', error);
    return apiError('Failed to register user');
  }
}

// User login
export async function PUT(req: Request) {
  try {
    const { email, password } = await req.json();

    // Validate input
    if (!email || !password) {
      return apiError('Missing required fields');
    }

    // Find user
    const user = await db.user.findUnique({
      where: { email }
    });

    if (!user) {
      return apiError('Invalid credentials');
    }

    // Verify password
    const isValid = await bcrypt.compare(password, user.password);

    if (!isValid) {
      return apiError('Invalid credentials');
    }

    // Generate JWT token
    const token = jwt.sign(
      { userId: user.id, email: user.email },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '24h' }
    );

    return apiResponse({ user, token });
  } catch (error) {
    console.error('Login error:', error);
    return apiError('Failed to login');
  }
}

// Get current user
export const GET = withAuth(async (req: Request, session: any) => {
  try {
    const user = await db.user.findUnique({
      where: { id: session.user.id },
      select: {
        id: true,
        name: true,
        email: true,
        department: true,
        role: true,
        status: true,
        joinDate: true,
        lastActive: true,
        avatarUrl: true
      }
    });

    return apiResponse({ user });
  } catch (error) {
    console.error('Get user error:', error);
    return apiError('Failed to get user');
  }
});

// Update user
export const PATCH = withAuth(async (req: Request, session: any) => {
  try {
    const { name, department, avatarUrl } = await req.json();

    const user = await db.user.update({
      where: { id: session.user.id },
      data: {
        name,
        department,
        avatarUrl
      },
      select: {
        id: true,
        name: true,
        email: true,
        department: true,
        role: true,
        status: true,
        joinDate: true,
        lastActive: true,
        avatarUrl: true
      }
    });

    return apiResponse({ user });
  } catch (error) {
    console.error('Update user error:', error);
    return apiError('Failed to update user');
  }
});

// Delete user
export const DELETE = withAuth(async (req: Request, session: any) => {
  try {
    await db.user.delete({
      where: { id: session.user.id }
    });

    return apiResponse({ message: 'User deleted successfully' });
  } catch (error) {
    console.error('Delete user error:', error);
    return apiError('Failed to delete user');
  }
}); 