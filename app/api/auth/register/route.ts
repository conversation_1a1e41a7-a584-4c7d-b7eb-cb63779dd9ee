import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { db } from '@/lib/db';
import { setAuthCookie, signToken } from '@/lib/auth';
import { hashPassword } from '@/lib/utils';

// Mark this route as dynamic since it uses cookies
export const dynamic = 'force-dynamic';

// Registration request schema
const registerSchema = z.object({
  name: z.string().min(2, { message: 'Name must be at least 2 characters' }),
  email: z.string().email({ message: 'Please enter a valid email address' }),
  password: z.string().min(8, { message: 'Password must be at least 8 characters' }),
  department: z.string().optional(),
});

export async function POST(request: NextRequest) {
  try {
    // Parse request body against schema
    const body = await request.json();
    const result = registerSchema.safeParse(body);

    if (!result.success) {
      return NextResponse.json(
        { error: 'Invalid request', details: result.error.format() },
        { status: 400 }
      );
    }

    const { name, email, password, department } = result.data;

    // Check if the user already exists
    const existingUser = await db.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: 'User with this email already exists' },
        { status: 409 }
      );
    }

    // Hash the password using our utility function
    const hashedPassword = await hashPassword(password);

    // Create the user (with default role "user")
    const user = await db.user.create({
      data: {
        name,
        email,
        password: hashedPassword,
        department,
        role: 'user', // Default role
      },
    });

    // Generate a JWT token
    const token = await signToken({
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role,
    });

    // Set the auth cookie
    await setAuthCookie(token);

    // Return the user data (without sensitive information)
    return NextResponse.json({
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
      },
    });
  } catch (error) {
    console.error('Registration error:', error);
    return NextResponse.json(
      { error: 'Registration failed' },
      { status: 500 }
    );
  }
} 