import { NextRequest, NextResponse } from "next/server";
import { hash } from "bcryptjs";
import { prisma } from "@/lib/prisma";
import { z } from "zod";
import crypto from "crypto";

const registerSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  phone: z.string().min(10, "Phone number must be at least 10 characters"),
  jobTitle: z.string().min(2, "Job title is required"),
  password: z.string()
    .min(8, "Password must be at least 8 characters")
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, "Password must contain at least one uppercase letter, one lowercase letter, and one number"),
  companyName: z.string().min(2, "Company name is required"),
  industry: z.string().min(1, "Industry is required"),
  companySize: z.string().min(1, "Company size is required"),
  website: z.string().url("Invalid website URL").optional().nullable(),
  marketingEmails: z.boolean().default(false),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate the request body
    const validatedData = registerSchema.parse(body);

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: validatedData.email },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: "A user with this email already exists" },
        { status: 400 }
      );
    }

    // Hash the password
    const hashedPassword = await hash(validatedData.password, 12);

    // Generate email verification token
    const emailVerifyToken = crypto.randomBytes(32).toString("hex");

    // Generate client ID
    const clientId = `CLIENT-${Date.now()}-${Math.random().toString(36).substr(2, 9).toUpperCase()}`;

    // Create user and client profile in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create the user
      const user = await tx.user.create({
        data: {
          name: validatedData.name,
          email: validatedData.email,
          password: hashedPassword,
          phone: validatedData.phone,
          jobTitle: validatedData.jobTitle,
          company: validatedData.companyName,
          role: "client",
          status: "pending_verification",
          clientId: clientId,
          emailVerifyToken: emailVerifyToken,
          preferences: {
            marketingEmails: validatedData.marketingEmails,
            notifications: {
              email: true,
              requestUpdates: true,
              billingAlerts: true,
              maintenanceNotices: true,
            },
            dashboard: {
              theme: "light",
              language: "en",
              timezone: "UTC",
            },
          },
        },
      });

      // Create the client profile
      const clientProfile = await tx.clientProfile.create({
        data: {
          userId: user.id,
          companyName: validatedData.companyName,
          industry: validatedData.industry,
          companySize: validatedData.companySize,
          website: validatedData.website,
          subscriptionTier: "basic",
          monthlySpend: 0,
          autoApprovalLimit: 500, // Default auto-approval limit for new clients
          paymentTerms: "net_30",
        },
      });

      return { user, clientProfile };
    });

    // TODO: Send email verification email
    // await sendVerificationEmail(validatedData.email, emailVerifyToken);

    // Return success response (without sensitive data)
    return NextResponse.json({
      message: "Account created successfully. Please check your email to verify your account.",
      user: {
        id: result.user.id,
        name: result.user.name,
        email: result.user.email,
        role: result.user.role,
        status: result.user.status,
        clientId: result.user.clientId,
      },
    });

  } catch (error) {
    console.error("Client registration error:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// Helper function to send verification email (to be implemented)
async function sendVerificationEmail(email: string, token: string) {
  // TODO: Implement email sending logic
  // This could use services like SendGrid, AWS SES, or Nodemailer
  console.log(`Verification email would be sent to ${email} with token ${token}`);
  
  // Example implementation:
  /*
  const verificationUrl = `${process.env.NEXTAUTH_URL}/auth/verify-email?token=${token}`;
  
  await emailService.send({
    to: email,
    subject: "Verify your WizeAssets account",
    template: "email-verification",
    data: {
      verificationUrl,
      email,
    },
  });
  */
}