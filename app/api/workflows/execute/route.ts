import { NextRequest, NextResponse } from 'next/server'
import TriggerDevService from '@/lib/advanced-features/automation/trigger-service'

export async function POST(request: NextRequest) {
  try {
    const { workflowId, input = {} } = await request.json()

    if (!workflowId) {
      return NextResponse.json(
        { error: 'Workflow ID is required' },
        { status: 400 }
      )
    }

    // Execute workflow via Trigger.dev
    const execution = await TriggerDevService.executeWorkflow(workflowId, input)

    return NextResponse.json({
      success: true,
      executionId: execution.id,
      status: execution.status,
      startedAt: execution.startedAt
    })

  } catch (error) {
    console.error('Error executing workflow:', error)
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}