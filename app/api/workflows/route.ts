import { NextRequest, NextResponse } from 'next/server'
import { FlowWorkflowDefinition } from '@/lib/advanced-features/automation/types'
import TriggerDevService from '@/lib/advanced-features/automation/trigger-service'

// In a real app, this would be a database
const workflows = new Map<string, FlowWorkflowDefinition>()

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const workflowId = searchParams.get('id')

    if (workflowId) {
      const workflow = workflows.get(workflowId)
      if (!workflow) {
        return NextResponse.json(
          { error: 'Workflow not found' },
          { status: 404 }
        )
      }
      return NextResponse.json(workflow)
    }

    // Return all workflows
    const allWorkflows = Array.from(workflows.values())
    return NextResponse.json(allWorkflows)

  } catch (error) {
    console.error('Error fetching workflows:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const workflow: FlowWorkflowDefinition = await request.json()

    // Validate workflow
    if (!workflow.id || !workflow.name) {
      return NextResponse.json(
        { error: 'Workflow ID and name are required' },
        { status: 400 }
      )
    }

    // Save workflow
    workflows.set(workflow.id, {
      ...workflow,
      createdAt: workflow.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString()
    })

    // Deploy to Trigger.dev if there are trigger nodes
    const triggerNodes = workflow.nodes.filter(node => 
      node.type === 'trigger' || node.type === 'webhook'
    )

    if (triggerNodes.length > 0) {
      try {
        const deploymentId = await TriggerDevService.deployWorkflow(workflow)
        console.log(`Workflow deployed to Trigger.dev: ${deploymentId}`)
      } catch (deployError) {
        console.error('Failed to deploy to Trigger.dev:', deployError)
        // Continue with saving - deployment is optional
      }
    }

    return NextResponse.json(workflow, { status: 201 })

  } catch (error) {
    console.error('Error creating workflow:', error)
    return NextResponse.json(
      { error: 'Failed to create workflow' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const workflow: FlowWorkflowDefinition = await request.json()

    if (!workflow.id) {
      return NextResponse.json(
        { error: 'Workflow ID is required' },
        { status: 400 }
      )
    }

    const existingWorkflow = workflows.get(workflow.id)
    if (!existingWorkflow) {
      return NextResponse.json(
        { error: 'Workflow not found' },
        { status: 404 }
      )
    }

    // Update workflow
    const updatedWorkflow = {
      ...workflow,
      updatedAt: new Date().toISOString()
    }

    workflows.set(workflow.id, updatedWorkflow)

    // Redeploy to Trigger.dev if needed
    const triggerNodes = workflow.nodes.filter(node => 
      node.type === 'trigger' || node.type === 'webhook'
    )

    if (triggerNodes.length > 0) {
      try {
        const deploymentId = await TriggerDevService.deployWorkflow(updatedWorkflow)
        console.log(`Workflow redeployed to Trigger.dev: ${deploymentId}`)
      } catch (deployError) {
        console.error('Failed to redeploy to Trigger.dev:', deployError)
      }
    }

    return NextResponse.json(updatedWorkflow)

  } catch (error) {
    console.error('Error updating workflow:', error)
    return NextResponse.json(
      { error: 'Failed to update workflow' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const workflowId = searchParams.get('id')

    if (!workflowId) {
      return NextResponse.json(
        { error: 'Workflow ID is required' },
        { status: 400 }
      )
    }

    const workflow = workflows.get(workflowId)
    if (!workflow) {
      return NextResponse.json(
        { error: 'Workflow not found' },
        { status: 404 }
      )
    }

    // Delete workflow
    workflows.delete(workflowId)

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('Error deleting workflow:', error)
    return NextResponse.json(
      { error: 'Failed to delete workflow' },
      { status: 500 }
    )
  }
}