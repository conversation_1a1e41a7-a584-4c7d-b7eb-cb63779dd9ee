import { NextRequest, NextResponse } from "next/server";
import { assetService } from "@/lib/services/asset-service";
import { validateAssetFilter, validatePagination } from "@/lib/schemas/api";
import { z } from "zod";

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;

    // Check if this is a request for comprehensive filtering
    const useFilter = searchParams.has("page") || searchParams.has("limit") ||
                     searchParams.has("sortBy") || searchParams.has("dateFrom") ||
                     searchParams.has("priceMin");

    if (useFilter) {
      // Use comprehensive filtering with pagination
      try {
        const filter = validateAssetFilter(Object.fromEntries(searchParams.entries()));
        const pagination = validatePagination(Object.fromEntries(searchParams.entries()));

        const result = await assetService.getAssetsWithFilter(filter, pagination);

        return NextResponse.json({
          success: true,
          data: result.assets,
          pagination: {
            page: pagination.page,
            limit: pagination.limit,
            total: result.total,
            totalPages: result.totalPages,
            hasNext: pagination.page < result.totalPages,
            hasPrev: pagination.page > 1,
          },
        });
      } catch (validationError) {
        if (validationError instanceof z.ZodError) {
          return NextResponse.json(
            {
              success: false,
              error: "Invalid filter parameters",
              details: validationError.errors
            },
            { status: 400 }
          );
        }
        throw validationError;
      }
    }

    // Legacy simple filtering for backward compatibility
    const category = searchParams.get("category");
    const status = searchParams.get("status");
    const department = searchParams.get("department");
    const location = searchParams.get("location");
    const search = searchParams.get("search");
    const includeRelations = searchParams.get("includeRelations") === "true";

    let assets;

    if (search) {
      assets = await assetService.searchAssets(search);
    } else if (category) {
      assets = await assetService.getAssetsByCategory(category);
    } else if (status) {
      assets = await assetService.getAssetsByStatus(status);
    } else if (department) {
      assets = await assetService.getAssetsByDepartment(department);
    } else if (location) {
      assets = await assetService.getAssetsByLocation(location);
    } else {
      assets = await assetService.getAllAssets(includeRelations);
    }

    return NextResponse.json({
      success: true,
      data: assets,
    });
  } catch (error) {
    console.error("Error fetching assets:", error);

    const errorMessage = error instanceof Error ? error.message : "Failed to fetch assets";

    return NextResponse.json(
      {
        success: false,
        error: errorMessage
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();

    // Validate required fields
    if (!data.name || !data.category || !data.location || !data.purchasePrice) {
      return NextResponse.json(
        {
          success: false,
          error: "Missing required fields",
          details: "name, category, location, and purchasePrice are required"
        },
        { status: 400 }
      );
    }

    // Transform the data to match schema requirements
    const transformedData = {
      ...data,
      purchaseDate: data.purchaseDate ? new Date(data.purchaseDate) : new Date(),
      purchasePrice: Number(data.purchasePrice),
      serialNumber: data.serialNumber || null,
      department: data.department || null,
      assetImages: data.assetImages || [],
      status: data.status || "active",
    };

    const asset = await assetService.createAsset(transformedData);

    return NextResponse.json({
      success: true,
      data: asset,
      message: "Asset created successfully"
    }, { status: 201 });
  } catch (error) {
    console.error("Error creating asset:", error);

    let errorMessage = "Failed to create asset";
    let statusCode = 500;

    if (error instanceof Error) {
      errorMessage = error.message;

      // Check if it's a validation error
      if (error.message.includes("Validation failed")) {
        statusCode = 400;
      }
    }

    return NextResponse.json(
      {
        success: false,
        error: errorMessage
      },
      { status: statusCode }
    );
  }
}