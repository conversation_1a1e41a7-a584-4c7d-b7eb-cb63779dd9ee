import { NextRequest, NextResponse } from "next/server";
import { assetService } from "@/lib/services/asset-service";

interface Params {
  params: {
    id: string;
  };
}

export async function GET(request: NextRequest, { params }: Params) {
  try {
    const { id } = params;
    const searchParams = request.nextUrl.searchParams;
    const includeRelations = searchParams.get("includeRelations") === "true";
    
    const asset = await assetService.getAssetById(id, includeRelations);
    
    if (!asset) {
      return NextResponse.json(
        { error: "Asset not found" },
        { status: 404 }
      );
    }
    
    return NextResponse.json(asset);
  } catch (error) {
    console.error(`Error fetching asset ${params.id}:`, error);
    return NextResponse.json(
      { error: "Failed to fetch asset" },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest, { params }: Params) {
  try {
    const { id } = params;
    const data = await request.json();
    
    // Transform the data to match schema requirements
    const transformedData = {
      ...data,
      ...(data.purchaseDate && { purchaseDate: new Date(data.purchaseDate) }),
      ...(data.purchasePrice && { purchasePrice: Number(data.purchasePrice) }),
      ...(data.serialNumber !== undefined && { serialNumber: data.serialNumber || null }),
      ...(data.department !== undefined && { department: data.department || null }),
      ...(data.assetImages !== undefined && { assetImages: data.assetImages || [] }),
    };
    
    const asset = await assetService.updateAsset(id, transformedData);
    
    return NextResponse.json(asset);
  } catch (error) {
    console.error(`Error updating asset ${params.id}:`, error);
    return NextResponse.json(
      { error: "Failed to update asset" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest, { params }: Params) {
  try {
    const { id } = params;
    
    await assetService.deleteAsset(id);
    
    return NextResponse.json(
      { message: "Asset deleted successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error(`Error deleting asset ${params.id}:`, error);
    return NextResponse.json(
      { error: "Failed to delete asset" },
      { status: 500 }
    );
  }
}