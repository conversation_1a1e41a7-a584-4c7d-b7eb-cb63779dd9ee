import { NextRequest, NextResponse } from "next/server";
import { assetService } from "@/lib/services/asset-service";

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const includeValueTrends = searchParams.get("includeValueTrends") === "true";
    const trendMonths = parseInt(searchParams.get("trendMonths") || "12");

    // Get basic statistics
    const statistics = await assetService.getAssetStatistics();

    // Optionally include value trends
    let valueTrends = null;
    if (includeValueTrends) {
      valueTrends = await assetService.getAssetValueTrends(trendMonths);
    }

    // Get assets needing maintenance
    const assetsNeedingMaintenance = await assetService.getAssetsNeedingMaintenance();

    const response = {
      success: true,
      data: {
        ...statistics,
        assetsNeedingMaintenanceCount: assetsNeedingMaintenance.length,
        ...(valueTrends && { valueTrends }),
      },
      timestamp: new Date().toISOString(),
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching asset statistics:", error);

    const errorMessage = error instanceof Error ? error.message : "Failed to fetch asset statistics";

    return NextResponse.json(
      {
        success: false,
        error: errorMessage,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}