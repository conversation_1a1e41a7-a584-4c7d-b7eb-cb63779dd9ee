import { NextRequest, NextResponse } from "next/server"
import { AssetTypeDbService } from "@/lib/modules/asset-types/db-service"

const assetTypeService = AssetTypeDbService.getInstance()

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get("category") || undefined
    const isActive = searchParams.get("isActive") ? searchParams.get("isActive") === "true" : undefined
    const search = searchParams.get("search") || undefined

    const filters = { category, isActive, search }
    const assetTypes = await assetTypeService.getAssetTypes(filters)
    
    return NextResponse.json(assetTypes)
  } catch (error) {
    console.error("Failed to fetch asset types:", error)
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const assetType = await assetTypeService.createAssetType(body)
    
    return NextResponse.json(assetType, { status: 201 })
  } catch (error) {
    console.error("Failed to create asset type:", error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Internal Server Error" },
      { status: 400 }
    )
  }
}