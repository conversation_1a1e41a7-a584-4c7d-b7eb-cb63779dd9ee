import { NextRequest, NextResponse } from "next/server"
import { AssetTypeDbService } from "@/lib/modules/asset-types/db-service"

const assetTypeService = AssetTypeDbService.getInstance()

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const parentId = searchParams.get("parentId") || undefined
    
    const categories = parentId 
      ? await assetTypeService.getCategories(parentId)
      : await assetTypeService.getAllCategories()
    
    return NextResponse.json(categories)
  } catch (error) {
    console.error("Failed to fetch categories:", error)
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const category = await assetTypeService.createCategory(body)
    
    return NextResponse.json(category, { status: 201 })
  } catch (error) {
    console.error("Failed to create category:", error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Internal Server Error" },
      { status: 400 }
    )
  }
}