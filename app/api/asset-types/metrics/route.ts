import { NextResponse } from "next/server"
import { AssetTypeDbService } from "@/lib/modules/asset-types/db-service"

const assetTypeService = AssetTypeDbService.getInstance()

export async function GET() {
  try {
    const metrics = await assetTypeService.getMetrics()
    return NextResponse.json(metrics)
  } catch (error) {
    console.error("Failed to fetch metrics:", error)
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    )
  }
}