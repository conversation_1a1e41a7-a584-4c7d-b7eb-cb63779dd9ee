import { NextRequest, NextResponse } from "next/server"
import { AssetTypeDbService } from "@/lib/modules/asset-types/db-service"

const assetTypeService = AssetTypeDbService.getInstance()

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string; fieldId: string } }
) {
  try {
    const body = await request.json()
    const customField = await assetTypeService.updateCustomField(params.id, params.fieldId, body)
    
    if (!customField) {
      return NextResponse.json(
        { error: "Asset type or custom field not found" },
        { status: 404 }
      )
    }
    
    return NextResponse.json(customField)
  } catch (error) {
    console.error("Failed to update custom field:", error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Internal Server Error" },
      { status: 400 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string; fieldId: string } }
) {
  try {
    const success = await assetTypeService.removeCustomField(params.id, params.fieldId)
    
    if (!success) {
      return NextResponse.json(
        { error: "Asset type or custom field not found" },
        { status: 404 }
      )
    }
    
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Failed to remove custom field:", error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Internal Server Error" },
      { status: 400 }
    )
  }
}