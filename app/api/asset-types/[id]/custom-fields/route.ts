import { NextRequest, NextResponse } from "next/server"
import { AssetTypeDbService } from "@/lib/modules/asset-types/db-service"

const assetTypeService = AssetTypeDbService.getInstance()

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const customField = await assetTypeService.addCustomField(params.id, body)
    
    if (!customField) {
      return NextResponse.json(
        { error: "Asset type not found" },
        { status: 404 }
      )
    }
    
    return NextResponse.json(customField, { status: 201 })
  } catch (error) {
    console.error("Failed to add custom field:", error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Internal Server Error" },
      { status: 400 }
    )
  }
}