import { NextRequest, NextResponse } from "next/server"
import { AssetTypeDbService } from "@/lib/modules/asset-types/db-service"

const assetTypeService = AssetTypeDbService.getInstance()

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const assetType = await assetTypeService.getAssetType(params.id)
    
    if (!assetType) {
      return NextResponse.json(
        { error: "Asset type not found" },
        { status: 404 }
      )
    }
    
    return NextResponse.json(assetType)
  } catch (error) {
    console.error("Failed to fetch asset type:", error)
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const assetType = await assetTypeService.updateAssetType(params.id, body)
    
    if (!assetType) {
      return NextResponse.json(
        { error: "Asset type not found" },
        { status: 404 }
      )
    }
    
    return NextResponse.json(assetType)
  } catch (error) {
    console.error("Failed to update asset type:", error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Internal Server Error" },
      { status: 400 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const success = await assetTypeService.deleteAssetType(params.id)
    
    if (!success) {
      return NextResponse.json(
        { error: "Asset type not found or could not be deleted" },
        { status: 404 }
      )
    }
    
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Failed to delete asset type:", error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Internal Server Error" },
      { status: 400 }
    )
  }
}