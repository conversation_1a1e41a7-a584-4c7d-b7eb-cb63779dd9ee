import { NextRequest, NextResponse } from 'next/server'
import { AssetNodeType } from '@/lib/advanced-features/automation/asset-node-types'
import { db } from '@/lib/db'

// POST /api/asset-automation/functions/[type]
export async function POST(
  request: NextRequest,
  { params }: { params: { type: AssetNodeType } }
) {
  try {
    const { type } = params
    const body = await request.json()
    const { input = {} } = body
    
    // Execute the appropriate asset function based on type
    let result
    
    switch (type) {
      case 'assetCreate':
        result = await executeAssetCreate(input)
        break
      case 'assetUpdate':
        result = await executeAssetUpdate(input)
        break
      case 'assetQuery':
        result = await executeAssetQuery(input)
        break
      case 'assetDepreciation':
        result = await executeAssetDepreciation(input)
        break
      case 'assetMaintenance':
        result = await executeAssetMaintenance(input)
        break
      case 'assetTransfer':
        result = await executeAssetTransfer(input)
        break
      case 'assetDisposal':
        result = await executeAssetDisposal(input)
        break
      case 'inventoryCheck':
        result = await executeInventoryCheck(input)
        break
      case 'purchaseOrder':
        result = await executePurchaseOrder(input)
        break
      case 'invoiceProcess':
        result = await executeInvoiceProcess(input)
        break
      case 'approvalRequest':
        result = await executeApprovalRequest(input)
        break
      case 'notifyStakeholders':
        result = await executeNotifyStakeholders(input)
        break
      case 'generateReport':
        result = await executeGenerateReport(input)
        break
      default:
        return NextResponse.json(
          { success: false, error: `Unknown function type: ${type}` },
          { status: 400 }
        )
    }
    
    return NextResponse.json({ 
      success: true, 
      result 
    })
  } catch (error: any) {
    console.error(`Error executing asset function ${params.type}:`, error)
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Failed to execute asset function' 
      },
      { status: 500 }
    )
  }
}

// Asset function implementations

async function executeAssetCreate(input: any) {
  const { assetName, category, purchaseDate, purchasePrice, location } = input
  
  // Validate required fields
  if (!assetName || !category || !purchaseDate || !purchasePrice) {
    throw new Error('Missing required asset information')
  }
  
  // Create asset in database
  const asset = await db.asset.create({
    data: {
      name: assetName,
      category,
      purchaseDate,
      purchasePrice,
      location: location || 'Unknown',
      status: 'active',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  })
  
  return {
    success: true,
    assetId: asset.id,
    message: `Asset "${assetName}" created successfully`
  }
}

async function executeAssetUpdate(input: any) {
  const { assetId, updates } = input
  
  // Validate required fields
  if (!assetId || !updates) {
    throw new Error('Missing asset ID or update data')
  }
  
  // Check if asset exists
  const asset = await db.asset.findUnique({
    where: { id: assetId }
  })
  
  if (!asset) {
    throw new Error(`Asset with ID ${assetId} not found`)
  }
  
  // Update asset in database
  const updatedAsset = await db.asset.update({
    where: { id: assetId },
    data: {
      ...updates,
      updatedAt: new Date().toISOString()
    }
  })
  
  return {
    success: true,
    assetId: updatedAsset.id,
    message: `Asset "${assetId}" updated successfully`
  }
}

async function executeAssetQuery(input: any) {
  const { category, location, status, purchaseDateRange, minValue, maxValue } = input
  
  // Build query filters
  const filters: any = {}
  
  if (category) filters.category = category
  if (location) filters.location = location
  if (status) filters.status = status
  
  if (purchaseDateRange) {
    if (purchaseDateRange.start) {
      filters.purchaseDate = {
        ...(filters.purchaseDate || {}),
        gte: purchaseDateRange.start
      }
    }
    if (purchaseDateRange.end) {
      filters.purchaseDate = {
        ...(filters.purchaseDate || {}),
        lte: purchaseDateRange.end
      }
    }
  }
  
  if (minValue || maxValue) {
    filters.purchasePrice = {}
    if (minValue) filters.purchasePrice.gte = minValue
    if (maxValue) filters.purchasePrice.lte = maxValue
  }
  
  // Query assets from database
  const assets = await db.asset.findMany({
    where: filters,
    orderBy: {
      createdAt: 'desc'
    }
  })
  
  return {
    success: true,
    filters,
    results: assets,
    count: assets.length
  }
}

async function executeAssetDepreciation(input: any) {
  const { assetId, method, usefulLife, salvageValue } = input
  
  // Validate required fields
  if (!assetId || !method || !usefulLife) {
    throw new Error('Missing required depreciation parameters')
  }
  
  // Check if asset exists
  const asset = await db.asset.findUnique({
    where: { id: assetId }
  })
  
  if (!asset) {
    throw new Error(`Asset with ID ${assetId} not found`)
  }
  
  // Calculate depreciation
  const initialValue = asset.purchasePrice
  const depreciableValue = initialValue - (salvageValue || 0)
  const depreciationSchedule = []
  
  let currentBookValue = initialValue
  
  for (let year = 1; year <= usefulLife; year++) {
    let depreciationAmount
    
    if (method === 'straight-line') {
      depreciationAmount = depreciableValue / usefulLife
    } else if (method === 'double-declining') {
      const rate = 2 / usefulLife
      depreciationAmount = currentBookValue * rate
    } else {
      throw new Error(`Unsupported depreciation method: ${method}`)
    }
    
    currentBookValue -= depreciationAmount
    
    depreciationSchedule.push({
      year,
      amount: depreciationAmount,
      bookValue: currentBookValue
    })
  }
  
  // Create depreciation record in database
  const depreciation = await db.assetDepreciation.create({
    data: {
      assetId,
      method,
      usefulLife,
      salvageValue: salvageValue || 0,
      initialValue,
      schedule: JSON.stringify(depreciationSchedule),
      createdAt: new Date().toISOString()
    }
  })
  
  return {
    success: true,
    assetId,
    depreciationId: depreciation.id,
    depreciationSchedule,
    method,
    totalDepreciation: depreciableValue
  }
}

async function executeAssetMaintenance(input: any) {
  const { assetId, maintenanceType, scheduledDate, assignedTo, notes } = input
  
  // Validate required fields
  if (!assetId || !maintenanceType || !scheduledDate) {
    throw new Error('Missing required maintenance information')
  }
  
  // Check if asset exists
  const asset = await db.asset.findUnique({
    where: { id: assetId }
  })
  
  if (!asset) {
    throw new Error(`Asset with ID ${assetId} not found`)
  }
  
  // Create maintenance record in database
  const maintenance = await db.assetMaintenance.create({
    data: {
      assetId,
      type: maintenanceType,
      scheduledDate,
      assignedTo: assignedTo || null,
      notes: notes || null,
      status: 'scheduled',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  })
  
  return {
    success: true,
    maintenanceId: maintenance.id,
    assetId,
    scheduledDate,
    message: `Maintenance scheduled for asset "${assetId}"`
  }
}

async function executeAssetTransfer(input: any) {
  const { assetId, newLocation, newDepartment, transferDate, reason } = input
  
  // Validate required fields
  if (!assetId || (!newLocation && !newDepartment)) {
    throw new Error('Missing asset ID or transfer destination')
  }
  
  // Check if asset exists
  const asset = await db.asset.findUnique({
    where: { id: assetId }
  })
  
  if (!asset) {
    throw new Error(`Asset with ID ${assetId} not found`)
  }
  
  const previousLocation = asset.location
  const previousDepartment = asset.department
  
  // Update asset in database
  const updatedAsset = await db.asset.update({
    where: { id: assetId },
    data: {
      location: newLocation || asset.location,
      department: newDepartment || asset.department,
      updatedAt: new Date().toISOString()
    }
  })
  
  // Create transfer record in database
  const transfer = await db.assetTransfer.create({
    data: {
      assetId,
      previousLocation,
      newLocation: newLocation || previousLocation,
      previousDepartment: previousDepartment || null,
      newDepartment: newDepartment || previousDepartment || null,
      transferDate: transferDate || new Date().toISOString(),
      reason: reason || null,
      createdAt: new Date().toISOString()
    }
  })
  
  return {
    success: true,
    assetId,
    transferId: transfer.id,
    previousLocation,
    newLocation: newLocation || 'Same',
    previousDepartment: previousDepartment || 'None',
    newDepartment: newDepartment || 'Same',
    message: `Asset "${assetId}" transferred successfully`
  }
}

async function executeAssetDisposal(input: any) {
  const { assetId, disposalMethod, disposalDate, salePrice, reason } = input
  
  // Validate required fields
  if (!assetId || !disposalMethod || !disposalDate) {
    throw new Error('Missing required disposal information')
  }
  
  // Check if asset exists
  const asset = await db.asset.findUnique({
    where: { id: assetId }
  })
  
  if (!asset) {
    throw new Error(`Asset with ID ${assetId} not found`)
  }
  
  // Get current book value (could be calculated from depreciation records)
  const bookValue = await calculateCurrentBookValue(assetId)
  
  // Update asset status in database
  await db.asset.update({
    where: { id: assetId },
    data: {
      status: 'disposed',
      updatedAt: new Date().toISOString()
    }
  })
  
  // Create disposal record in database
  const disposal = await db.assetDisposal.create({
    data: {
      assetId,
      method: disposalMethod,
      disposalDate,
      bookValue,
      salePrice: salePrice || 0,
      gainLoss: (salePrice || 0) - bookValue,
      reason: reason || null,
      createdAt: new Date().toISOString()
    }
  })
  
  return {
    success: true,
    assetId,
    disposalId: disposal.id,
    disposalMethod,
    bookValue,
    salePrice: salePrice || 0,
    gainLoss: (salePrice || 0) - bookValue,
    message: `Asset "${assetId}" disposed successfully`
  }
}

async function executeInventoryCheck(input: any) {
  const { location, department, checkDate, performedBy } = input
  
  // Validate required fields
  if (!location && !department) {
    throw new Error('Must specify location or department for inventory check')
  }
  
  // Build query filters
  const filters: any = {}
  
  if (location) filters.location = location
  if (department) filters.department = department
  
  // Query assets from database
  const assets = await db.asset.findMany({
    where: filters
  })
  
  // Simulate some missing assets for demonstration
  const assetsChecked = assets.length
  const assetsFound = assetsChecked - 2
  const missingAssets = assets.slice(0, 2).map(asset => ({
    id: asset.id,
    name: asset.name
  }))
  
  // Create inventory check record in database
  const inventoryCheck = await db.inventoryCheck.create({
    data: {
      location: location || 'All',
      department: department || 'All',
      checkDate: checkDate || new Date().toISOString(),
      performedBy: performedBy || 'System',
      assetsChecked,
      assetsFound,
      assetsMissing: missingAssets.length,
      missingAssets: JSON.stringify(missingAssets),
      createdAt: new Date().toISOString()
    }
  })
  
  return {
    success: true,
    inventoryCheckId: inventoryCheck.id,
    location: location || 'All',
    department: department || 'All',
    assetsChecked,
    assetsFound,
    assetsMissing: missingAssets.length,
    missingAssets,
    message: `Inventory check completed with ${missingAssets.length} discrepancies`
  }
}

async function executePurchaseOrder(input: any) {
  const { supplier, items, requestedBy, deliveryDate, shippingAddress } = input
  
  // Validate required fields
  if (!supplier || !items || items.length === 0) {
    throw new Error('Missing supplier or items for purchase order')
  }
  
  // Calculate total
  const total = items.reduce((sum: number, item: any) => sum + (item.quantity * item.unitPrice), 0)
  
  // Create purchase order in database
  const purchaseOrder = await db.purchaseOrder.create({
    data: {
      supplier,
      items: JSON.stringify(items),
      total,
      requestedBy: requestedBy || 'System',
      deliveryDate: deliveryDate || null,
      shippingAddress: shippingAddress || null,
      status: 'pending_approval',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  })
  
  return {
    success: true,
    purchaseOrderId: purchaseOrder.id,
    supplier,
    items,
    total,
    status: 'pending_approval',
    message: `Purchase order created for ${items.length} items totaling ${total}`
  }
}

async function executeInvoiceProcess(input: any) {
  const { invoiceNumber, supplier, amount, purchaseOrderId, items, dueDate } = input
  
  // Validate required fields
  if (!invoiceNumber || !supplier || !amount) {
    throw new Error('Missing required invoice information')
  }
  
  // Create invoice in database
  const invoice = await db.invoice.create({
    data: {
      invoiceNumber,
      supplier,
      amount,
      purchaseOrderId: purchaseOrderId || null,
      items: items ? JSON.stringify(items) : null,
      dueDate: dueDate || null,
      status: 'pending_approval',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  })
  
  return {
    success: true,
    invoiceId: invoice.id,
    invoiceNumber,
    supplier,
    amount,
    status: 'pending_approval',
    message: `Invoice ${invoiceNumber} processed successfully`
  }
}

async function executeApprovalRequest(input: any) {
  const { requestType, requestedBy, approver, items, justification, urgency } = input
  
  // Validate required fields
  if (!requestType || !approver) {
    throw new Error('Missing request type or approver')
  }
  
  // Create approval request in database
  const approvalRequest = await db.approvalRequest.create({
    data: {
      type: requestType,
      requestedBy: requestedBy || 'System',
      approver,
      items: items ? JSON.stringify(items) : null,
      justification: justification || null,
      urgency: urgency || 'normal',
      status: 'pending',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  })
  
  return {
    success: true,
    approvalRequestId: approvalRequest.id,
    requestType,
    approver,
    status: 'pending',
    message: `Approval request sent to ${approver}`
  }
}

async function executeNotifyStakeholders(input: any) {
  const { recipients, subject, message, priority, notificationType } = input
  
  // Validate required fields
  if (!recipients || recipients.length === 0 || !subject || !message) {
    throw new Error('Missing recipients, subject, or message')
  }
  
  // Create notification records in database
  const notifications = await Promise.all(
    recipients.map((recipient: string) => 
      db.notification.create({
        data: {
          recipient,
          subject,
          message,
          priority: priority || 'normal',
          type: notificationType || 'system',
          status: 'sent',
          createdAt: new Date().toISOString()
        }
      })
    )
  )
  
  return {
    success: true,
    notificationId: `batch-${Date.now()}`,
    recipients,
    deliveredTo: recipients.length,
    message: `Notification sent to ${recipients.length} recipients`
  }
}

async function executeGenerateReport(input: any) {
  const { reportType, parameters, format, delivery } = input
  
  // Validate required fields
  if (!reportType) {
    throw new Error('Missing report type')
  }
  
  // Define report types and their descriptions
  const reportTypes: Record<string, string> = {
    'asset-inventory': 'Current inventory of all assets',
    'depreciation': 'Asset depreciation schedule',
    'maintenance-history': 'Asset maintenance history',
    'acquisition': 'Asset acquisition report',
    'disposal': 'Asset disposal report',
    'valuation': 'Current asset valuation'
  }
  
  // Create report record in database
  const report = await db.report.create({
    data: {
      type: reportType,
      name: reportTypes[reportType] || reportType,
      parameters: parameters ? JSON.stringify(parameters) : null,
      format: format || 'PDF',
      delivery: delivery || 'download',
      status: 'completed',
      url: `https://example.com/reports/${reportType}-${Date.now()}.${format || 'pdf'}`,
      createdAt: new Date().toISOString()
    }
  })
  
  return {
    success: true,
    reportId: report.id,
    reportType,
    reportName: reportTypes[reportType] || reportType,
    format: format || 'PDF',
    url: report.url,
    message: `${reportTypes[reportType] || reportType} report generated successfully`
  }
}

// Helper function to calculate current book value
async function calculateCurrentBookValue(assetId: string): Promise<number> {
  // Get asset
  const asset = await db.asset.findUnique({
    where: { id: assetId }
  })
  
  if (!asset) {
    throw new Error(`Asset with ID ${assetId} not found`)
  }
  
  // Get latest depreciation record
  const depreciation = await db.assetDepreciation.findFirst({
    where: { assetId },
    orderBy: { createdAt: 'desc' }
  })
  
  if (!depreciation) {
    // If no depreciation record, return purchase price
    return asset.purchasePrice
  }
  
  // Parse depreciation schedule
  const schedule = JSON.parse(depreciation.schedule)
  
  // Get current year in the schedule
  const purchaseDate = new Date(asset.purchaseDate)
  const currentDate = new Date()
  const yearsSincePurchase = currentDate.getFullYear() - purchaseDate.getFullYear()
  
  // Find the appropriate book value based on years since purchase
  const yearEntry = schedule.find((entry: any) => entry.year === yearsSincePurchase)
  
  if (yearEntry) {
    return yearEntry.bookValue
  } else if (yearsSincePurchase > depreciation.usefulLife) {
    // If beyond useful life, return salvage value
    return depreciation.salvageValue
  } else {
    // If year not found but within useful life, return purchase price
    return asset.purchasePrice
  }
}