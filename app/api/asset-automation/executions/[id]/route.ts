import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'

// GET /api/asset-automation/executions/[id]
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    
    // Get execution from database
    const execution = await db.workflowExecution.findUnique({
      where: { id }
    })
    
    if (!execution) {
      return NextResponse.json(
        { success: false, error: 'Execution not found' },
        { status: 404 }
      )
    }
    
    // Transform execution to include parsed JSON fields
    const transformedExecution = {
      id: execution.id,
      jobId: `workflow-${execution.workflowId}`,
      status: execution.status,
      startedAt: execution.startedAt,
      completedAt: execution.completedAt,
      output: execution.output ? JSON.parse(execution.output) : null,
      context: execution.context ? JSON.parse(execution.context) : {}
    }
    
    return NextResponse.json({ 
      success: true, 
      execution: transformedExecution 
    })
  } catch (error) {
    console.error(`Error fetching execution ${params.id}:`, error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch execution' },
      { status: 500 }
    )
  }
}