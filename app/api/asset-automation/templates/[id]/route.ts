import { NextRequest, NextResponse } from 'next/server'
import { assetWorkflowTemplates } from '@/lib/advanced-features/automation/asset-node-types'
import { FlowWorkflowDefinition } from '@/lib/advanced-features/automation/types'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const templateId = params.id
    
    // Find the template by ID
    const template = assetWorkflowTemplates.find(t => t.id === templateId)
    
    if (!template) {
      return NextResponse.json(
        { error: `Template with ID ${templateId} not found` },
        { status: 404 }
      )
    }
    
    // Convert template to a partial workflow definition
    const workflowTemplate: Partial<FlowWorkflowDefinition> = {
      // Create nodes with proper IDs
      nodes: template.nodes.map((node, index) => ({
        id: `node_${index + 1}`,
        type: node.type as any,
        position: node.position,
        data: {
          label: node.type,
          config: {}
        }
      })),
      
      // Create edges with proper IDs and references
      edges: template.edges.map((edge, index) => {
        // Find source and target node indices
        const sourceIndex = template.nodes.findIndex(n => n.type === edge.source)
        const targetIndex = template.nodes.findIndex(n => n.type === edge.target)
        
        return {
          id: `edge_${index + 1}`,
          source: `node_${sourceIndex + 1}`,
          target: `node_${targetIndex + 1}`,
          type: 'custom',
          animated: true,
          sourceHandle: edge.sourceHandle,
          targetHandle: edge.targetHandle
        }
      }),
      
      // Add other required workflow properties
      viewport: { x: 0, y: 0, zoom: 1 },
      variables: [],
      webhooks: [],
      triggers: []
    }
    
    return NextResponse.json({ template: workflowTemplate })
  } catch (error: any) {
    console.error('Error fetching workflow template:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to fetch workflow template' },
      { status: 500 }
    )
  }
}