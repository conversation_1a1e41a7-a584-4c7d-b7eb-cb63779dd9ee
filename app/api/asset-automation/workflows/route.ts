import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { z } from 'zod'

// Schema for workflow validation
const workflowSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, "Workflow name is required"),
  description: z.string().optional(),
  nodes: z.array(z.any()),
  edges: z.array(z.any()),
  webhooks: z.array(z.any()).optional(),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),
  createdBy: z.string().optional(),
  isActive: z.boolean().optional(),
  tags: z.array(z.string()).optional()
})

// GET /api/asset-automation/workflows
export async function GET(request: NextRequest) {
  try {
    // Get all workflows from the database
    const workflows = await db.workflow.findMany({
      where: {
        type: 'asset-automation'
      },
      orderBy: {
        updatedAt: 'desc'
      }
    })
    
    return NextResponse.json({ 
      success: true, 
      workflows 
    })
  } catch (error) {
    console.error('Error fetching workflows:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch workflows' },
      { status: 500 }
    )
  }
}

// POST /api/asset-automation/workflows
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { workflow } = body
    
    // Validate workflow data
    const validatedData = workflowSchema.parse(workflow)
    
    // Create workflow in database
    const newWorkflow = await db.workflow.create({
      data: {
        ...validatedData,
        type: 'asset-automation',
        id: validatedData.id || `workflow-${Date.now()}`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        isActive: validatedData.isActive ?? true,
        webhooks: validatedData.webhooks || [],
        tags: validatedData.tags || []
      }
    })
    
    return NextResponse.json({ 
      success: true, 
      workflow: newWorkflow 
    })
  } catch (error) {
    console.error('Error creating workflow:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { success: false, error: 'Failed to create workflow' },
      { status: 500 }
    )
  }
}