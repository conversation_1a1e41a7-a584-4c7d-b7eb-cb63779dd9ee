import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { z } from 'zod'

// Schema for workflow validation
const workflowSchema = z.object({
  id: z.string(),
  name: z.string().min(1, "Workflow name is required"),
  description: z.string().optional(),
  nodes: z.array(z.any()),
  edges: z.array(z.any()),
  webhooks: z.array(z.any()).optional(),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),
  createdBy: z.string().optional(),
  isActive: z.boolean().optional(),
  tags: z.array(z.string()).optional()
})

// GET /api/asset-automation/workflows/[id]
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    
    // Get workflow from database
    const workflow = await db.workflow.findUnique({
      where: {
        id,
        type: 'asset-automation'
      }
    })
    
    if (!workflow) {
      return NextResponse.json(
        { success: false, error: 'Workflow not found' },
        { status: 404 }
      )
    }
    
    return NextResponse.json({ 
      success: true, 
      workflow 
    })
  } catch (error) {
    console.error(`Error fetching workflow ${params.id}:`, error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch workflow' },
      { status: 500 }
    )
  }
}

// PUT /api/asset-automation/workflows/[id]
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const body = await request.json()
    const { workflow } = body
    
    // Check if workflow exists
    const existingWorkflow = await db.workflow.findUnique({
      where: {
        id,
        type: 'asset-automation'
      }
    })
    
    if (!existingWorkflow) {
      return NextResponse.json(
        { success: false, error: 'Workflow not found' },
        { status: 404 }
      )
    }
    
    // Validate workflow data
    const validatedData = workflowSchema.parse({
      ...workflow,
      id // Ensure ID matches the URL parameter
    })
    
    // Update workflow in database
    const updatedWorkflow = await db.workflow.update({
      where: { id },
      data: {
        ...validatedData,
        updatedAt: new Date().toISOString()
      }
    })
    
    return NextResponse.json({ 
      success: true, 
      workflow: updatedWorkflow 
    })
  } catch (error) {
    console.error(`Error updating workflow ${params.id}:`, error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { success: false, error: 'Failed to update workflow' },
      { status: 500 }
    )
  }
}

// DELETE /api/asset-automation/workflows/[id]
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    
    // Check if workflow exists
    const existingWorkflow = await db.workflow.findUnique({
      where: {
        id,
        type: 'asset-automation'
      }
    })
    
    if (!existingWorkflow) {
      return NextResponse.json(
        { success: false, error: 'Workflow not found' },
        { status: 404 }
      )
    }
    
    // Delete workflow from database
    await db.workflow.delete({
      where: { id }
    })
    
    // Also delete related executions
    await db.workflowExecution.deleteMany({
      where: { workflowId: id }
    })
    
    return NextResponse.json({ 
      success: true 
    })
  } catch (error) {
    console.error(`Error deleting workflow ${params.id}:`, error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete workflow' },
      { status: 500 }
    )
  }
}