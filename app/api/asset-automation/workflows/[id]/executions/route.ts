import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'

// GET /api/asset-automation/workflows/[id]/executions
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    
    // Check if workflow exists
    const workflow = await db.workflow.findUnique({
      where: {
        id,
        type: 'asset-automation'
      }
    })
    
    if (!workflow) {
      return NextResponse.json(
        { success: false, error: 'Workflow not found' },
        { status: 404 }
      )
    }
    
    // Get executions from database
    const executions = await db.workflowExecution.findMany({
      where: {
        workflowId: id
      },
      orderBy: {
        startedAt: 'desc'
      },
      take: 50 // Limit to last 50 executions
    })
    
    // Transform executions to include parsed JSON fields
    const transformedExecutions = executions.map(execution => ({
      id: execution.id,
      workflowId: execution.workflowId,
      status: execution.status,
      startedAt: execution.startedAt,
      completedAt: execution.completedAt,
      output: execution.output ? JSON.parse(execution.output) : null,
      context: execution.context ? JSON.parse(execution.context) : {}
    }))
    
    return NextResponse.json({ 
      success: true, 
      executions: transformedExecutions 
    })
  } catch (error) {
    console.error(`Error fetching executions for workflow ${params.id}:`, error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch executions' },
      { status: 500 }
    )
  }
}