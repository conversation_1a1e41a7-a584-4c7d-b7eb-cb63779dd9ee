import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'

// GET /api/asset-automation/workflows/[id]/analytics
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    
    // Check if workflow exists
    const workflow = await db.workflow.findUnique({
      where: {
        id,
        type: 'asset-automation'
      }
    })
    
    if (!workflow) {
      return NextResponse.json(
        { success: false, error: 'Workflow not found' },
        { status: 404 }
      )
    }
    
    // Get executions from database
    const executions = await db.workflowExecution.findMany({
      where: {
        workflowId: id
      },
      orderBy: {
        startedAt: 'desc'
      }
    })
    
    // Calculate analytics
    const totalExecutions = executions.length
    const successfulExecutions = executions.filter(e => e.status === 'completed').length
    const failedExecutions = executions.filter(e => e.status === 'failed').length
    
    // Calculate average execution time
    let totalExecutionTime = 0
    let executionsWithTime = 0
    
    executions.forEach(execution => {
      if (execution.startedAt && execution.completedAt) {
        const startTime = new Date(execution.startedAt).getTime()
        const endTime = new Date(execution.completedAt).getTime()
        const duration = endTime - startTime
        
        if (duration > 0) {
          totalExecutionTime += duration
          executionsWithTime++
        }
      }
    })
    
    const averageExecutionTime = executionsWithTime > 0 
      ? totalExecutionTime / executionsWithTime 
      : 0
    
    // Calculate execution trend (last 30 days)
    const now = new Date()
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    
    const dailyExecutions = Array(30).fill(0)
    const dailySuccessRate = Array(30).fill(0)
    
    executions.forEach(execution => {
      const executionDate = new Date(execution.startedAt)
      if (executionDate >= thirtyDaysAgo) {
        const dayIndex = 29 - Math.floor((now.getTime() - executionDate.getTime()) / (24 * 60 * 60 * 1000))
        if (dayIndex >= 0 && dayIndex < 30) {
          dailyExecutions[dayIndex]++
          if (execution.status === 'completed') {
            dailySuccessRate[dayIndex]++
          }
        }
      }
    })
    
    // Calculate success rate for each day
    for (let i = 0; i < 30; i++) {
      if (dailyExecutions[i] > 0) {
        dailySuccessRate[i] = (dailySuccessRate[i] / dailyExecutions[i]) * 100
      }
    }
    
    // Generate dates for the last 30 days
    const dates = Array(30).fill(0).map((_, index) => {
      const date = new Date(now.getTime() - (29 - index) * 24 * 60 * 60 * 1000)
      return date.toISOString().split('T')[0]
    })
    
    // Prepare analytics data
    const analytics = {
      totalExecutions,
      successfulExecutions,
      failedExecutions,
      successRate: totalExecutions > 0 ? (successfulExecutions / totalExecutions) * 100 : 0,
      averageExecutionTime,
      trend: {
        dates,
        executions: dailyExecutions,
        successRate: dailySuccessRate
      },
      lastExecuted: executions.length > 0 ? executions[0].startedAt : null,
      nodePerformance: calculateNodePerformance(executions)
    }
    
    return NextResponse.json({ 
      success: true, 
      analytics 
    })
  } catch (error) {
    console.error(`Error fetching analytics for workflow ${params.id}:`, error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch analytics' },
      { status: 500 }
    )
  }
}

// Helper function to calculate node performance
function calculateNodePerformance(executions: any[]) {
  const nodePerformance: Record<string, { 
    totalExecutions: number, 
    successfulExecutions: number, 
    failedExecutions: number,
    averageExecutionTime: number
  }> = {}
  
  executions.forEach(execution => {
    if (!execution.context) return
    
    let context
    try {
      context = typeof execution.context === 'string' 
        ? JSON.parse(execution.context) 
        : execution.context
    } catch (error) {
      console.error('Error parsing execution context:', error)
      return
    }
    
    const nodeResults = context.nodeResults || {}
    
    Object.entries(nodeResults).forEach(([nodeId, result]: [string, any]) => {
      if (!nodePerformance[nodeId]) {
        nodePerformance[nodeId] = {
          totalExecutions: 0,
          successfulExecutions: 0,
          failedExecutions: 0,
          averageExecutionTime: 0
        }
      }
      
      nodePerformance[nodeId].totalExecutions++
      
      if (result.error) {
        nodePerformance[nodeId].failedExecutions++
      } else {
        nodePerformance[nodeId].successfulExecutions++
      }
      
      // Calculate execution time if available
      if (result.startTime && result.endTime) {
        const startTime = new Date(result.startTime).getTime()
        const endTime = new Date(result.endTime).getTime()
        const duration = endTime - startTime
        
        if (duration > 0) {
          const currentTotal = nodePerformance[nodeId].averageExecutionTime * 
            (nodePerformance[nodeId].totalExecutions - 1)
          
          nodePerformance[nodeId].averageExecutionTime = 
            (currentTotal + duration) / nodePerformance[nodeId].totalExecutions
        }
      }
    })
  })
  
  return nodePerformance
}