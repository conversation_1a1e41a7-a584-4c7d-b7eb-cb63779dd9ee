import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { executeWorkflow } from '@/lib/workflow-engine/executor'

// POST /api/asset-automation/workflows/[id]/execute
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const body = await request.json()
    const { input = {} } = body
    
    // Check if workflow exists
    const workflow = await db.workflow.findUnique({
      where: {
        id,
        type: 'asset-automation'
      }
    })
    
    if (!workflow) {
      return NextResponse.json(
        { success: false, error: 'Workflow not found' },
        { status: 404 }
      )
    }
    
    // Create execution record
    const executionId = `exec-${Date.now()}`
    const execution = await db.workflowExecution.create({
      data: {
        id: executionId,
        workflowId: id,
        status: 'queued',
        input: JSON.stringify(input),
        startedAt: new Date().toISOString(),
        context: JSON.stringify({
          workflowId: id,
          executionId,
          variables: { input },
          nodeResults: {},
          startTime: new Date().toISOString(),
          webhookResponses: {},
          errors: []
        })
      }
    })
    
    // Execute workflow asynchronously
    executeWorkflow(workflow, execution.id, input)
      .catch(error => {
        console.error(`Error executing workflow ${id}:`, error)
        // Update execution with error
        db.workflowExecution.update({
          where: { id: executionId },
          data: {
            status: 'failed',
            completedAt: new Date().toISOString(),
            output: JSON.stringify({ success: false, error: error.message }),
            context: JSON.stringify({
              ...JSON.parse(execution.context),
              errors: [{ error: error.message }]
            })
          }
        }).catch(err => {
          console.error(`Error updating execution ${executionId}:`, err)
        })
      })
    
    return NextResponse.json({ 
      success: true, 
      executionId 
    })
  } catch (error) {
    console.error(`Error executing workflow ${params.id}:`, error)
    return NextResponse.json(
      { success: false, error: 'Failed to execute workflow' },
      { status: 500 }
    )
  }
}