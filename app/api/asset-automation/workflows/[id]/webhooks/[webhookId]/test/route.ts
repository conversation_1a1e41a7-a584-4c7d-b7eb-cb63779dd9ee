import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'

// POST /api/asset-automation/workflows/[id]/webhooks/[webhookId]/test
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string, webhookId: string } }
) {
  try {
    const { id, webhookId } = params
    const payload = await request.json()
    
    // Check if workflow exists
    const workflow = await db.workflow.findUnique({
      where: {
        id,
        type: 'asset-automation'
      }
    })
    
    if (!workflow) {
      return NextResponse.json(
        { success: false, error: 'Workflow not found' },
        { status: 404 }
      )
    }
    
    // Find webhook in workflow
    const webhook = (workflow.webhooks || []).find(w => w.id === webhookId)
    
    if (!webhook) {
      return NextResponse.json(
        { success: false, error: 'Webhook not found' },
        { status: 404 }
      )
    }
    
    // Create a test execution
    const executionId = `test-exec-${Date.now()}`
    const execution = await db.workflowExecution.create({
      data: {
        id: executionId,
        workflowId: id,
        status: 'completed',
        input: JSON.stringify(payload),
        startedAt: new Date().toISOString(),
        completedAt: new Date().toISOString(),
        output: JSON.stringify({ success: true, message: 'Webhook test executed successfully' }),
        context: JSON.stringify({
          workflowId: id,
          executionId,
          variables: { input: payload },
          nodeResults: {},
          startTime: new Date().toISOString(),
          endTime: new Date().toISOString(),
          webhookResponses: {
            [webhookId]: {
              success: true,
              statusCode: 200,
              data: { message: 'Webhook test executed successfully' }
            }
          },
          errors: []
        })
      }
    })
    
    return NextResponse.json({ 
      success: true,
      message: 'Webhook test executed successfully',
      executionId
    })
  } catch (error) {
    console.error(`Error testing webhook ${params.webhookId}:`, error)
    return NextResponse.json(
      { success: false, error: 'Failed to test webhook' },
      { status: 500 }
    )
  }
}