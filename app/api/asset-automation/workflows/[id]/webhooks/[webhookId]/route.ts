import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { z } from 'zod'

// Schema for webhook validation
const webhookSchema = z.object({
  id: z.string(),
  name: z.string().min(1, "Webhook name is required"),
  description: z.string().optional(),
  method: z.enum(['GET', 'POST', 'PUT', 'DELETE']),
  headers: z.record(z.string()).optional(),
  authentication: z.object({
    type: z.enum(['none', 'basic', 'bearer', 'api_key']),
    username: z.string().optional(),
    password: z.string().optional(),
    token: z.string().optional(),
    key: z.string().optional(),
    value: z.string().optional(),
    in: z.enum(['header', 'query']).optional()
  }).optional(),
  isActive: z.boolean().optional()
})

// GET /api/asset-automation/workflows/[id]/webhooks/[webhookId]
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string, webhookId: string } }
) {
  try {
    const { id, webhookId } = params
    
    // Check if workflow exists
    const workflow = await db.workflow.findUnique({
      where: {
        id,
        type: 'asset-automation'
      }
    })
    
    if (!workflow) {
      return NextResponse.json(
        { success: false, error: 'Workflow not found' },
        { status: 404 }
      )
    }
    
    // Find webhook in workflow
    const webhook = (workflow.webhooks || []).find(w => w.id === webhookId)
    
    if (!webhook) {
      return NextResponse.json(
        { success: false, error: 'Webhook not found' },
        { status: 404 }
      )
    }
    
    return NextResponse.json({ 
      success: true, 
      webhook 
    })
  } catch (error) {
    console.error(`Error fetching webhook ${params.webhookId}:`, error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch webhook' },
      { status: 500 }
    )
  }
}

// PUT /api/asset-automation/workflows/[id]/webhooks/[webhookId]
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string, webhookId: string } }
) {
  try {
    const { id, webhookId } = params
    const body = await request.json()
    const { webhook } = body
    
    // Check if workflow exists
    const workflow = await db.workflow.findUnique({
      where: {
        id,
        type: 'asset-automation'
      }
    })
    
    if (!workflow) {
      return NextResponse.json(
        { success: false, error: 'Workflow not found' },
        { status: 404 }
      )
    }
    
    // Find webhook index in workflow
    const webhooks = workflow.webhooks || []
    const webhookIndex = webhooks.findIndex(w => w.id === webhookId)
    
    if (webhookIndex === -1) {
      return NextResponse.json(
        { success: false, error: 'Webhook not found' },
        { status: 404 }
      )
    }
    
    // Validate webhook data
    const validatedData = webhookSchema.parse({
      ...webhook,
      id: webhookId // Ensure ID matches the URL parameter
    })
    
    // Update webhook
    const updatedWebhook = {
      ...webhooks[webhookIndex],
      ...validatedData,
      updatedAt: new Date().toISOString()
    }
    
    // Update webhooks array
    webhooks[webhookIndex] = updatedWebhook
    
    // Update workflow in database
    await db.workflow.update({
      where: { id },
      data: {
        webhooks,
        updatedAt: new Date().toISOString()
      }
    })
    
    return NextResponse.json({ 
      success: true, 
      webhook: updatedWebhook 
    })
  } catch (error) {
    console.error(`Error updating webhook ${params.webhookId}:`, error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { success: false, error: 'Failed to update webhook' },
      { status: 500 }
    )
  }
}

// DELETE /api/asset-automation/workflows/[id]/webhooks/[webhookId]
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string, webhookId: string } }
) {
  try {
    const { id, webhookId } = params
    
    // Check if workflow exists
    const workflow = await db.workflow.findUnique({
      where: {
        id,
        type: 'asset-automation'
      }
    })
    
    if (!workflow) {
      return NextResponse.json(
        { success: false, error: 'Workflow not found' },
        { status: 404 }
      )
    }
    
    // Filter out webhook from webhooks array
    const webhooks = (workflow.webhooks || []).filter(w => w.id !== webhookId)
    
    // Update workflow in database
    await db.workflow.update({
      where: { id },
      data: {
        webhooks,
        updatedAt: new Date().toISOString()
      }
    })
    
    return NextResponse.json({ 
      success: true 
    })
  } catch (error) {
    console.error(`Error deleting webhook ${params.webhookId}:`, error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete webhook' },
      { status: 500 }
    )
  }
}