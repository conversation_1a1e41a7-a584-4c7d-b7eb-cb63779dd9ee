import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { z } from 'zod'

// Schema for webhook validation
const webhookSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, "Webhook name is required"),
  description: z.string().optional(),
  method: z.enum(['GET', 'POST', 'PUT', 'DELETE']),
  headers: z.record(z.string()).optional(),
  authentication: z.object({
    type: z.enum(['none', 'basic', 'bearer', 'api_key']),
    username: z.string().optional(),
    password: z.string().optional(),
    token: z.string().optional(),
    key: z.string().optional(),
    value: z.string().optional(),
    in: z.enum(['header', 'query']).optional()
  }).optional(),
  isActive: z.boolean().optional()
})

// GET /api/asset-automation/workflows/[id]/webhooks
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    
    // Check if workflow exists
    const workflow = await db.workflow.findUnique({
      where: {
        id,
        type: 'asset-automation'
      }
    })
    
    if (!workflow) {
      return NextResponse.json(
        { success: false, error: 'Workflow not found' },
        { status: 404 }
      )
    }
    
    // Get webhooks from workflow
    const webhooks = workflow.webhooks || []
    
    return NextResponse.json({ 
      success: true, 
      webhooks 
    })
  } catch (error) {
    console.error(`Error fetching webhooks for workflow ${params.id}:`, error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch webhooks' },
      { status: 500 }
    )
  }
}

// POST /api/asset-automation/workflows/[id]/webhooks
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const body = await request.json()
    const { webhook } = body
    
    // Check if workflow exists
    const workflow = await db.workflow.findUnique({
      where: {
        id,
        type: 'asset-automation'
      }
    })
    
    if (!workflow) {
      return NextResponse.json(
        { success: false, error: 'Workflow not found' },
        { status: 404 }
      )
    }
    
    // Validate webhook data
    const validatedData = webhookSchema.parse(webhook)
    
    // Create webhook
    const newWebhook = {
      ...validatedData,
      id: validatedData.id || `webhook-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isActive: validatedData.isActive ?? true,
      url: `/api/webhooks/${id}/${validatedData.id || `webhook-${Date.now()}`}`
    }
    
    // Add webhook to workflow
    const webhooks = [...(workflow.webhooks || []), newWebhook]
    
    // Update workflow in database
    const updatedWorkflow = await db.workflow.update({
      where: { id },
      data: {
        webhooks,
        updatedAt: new Date().toISOString()
      }
    })
    
    return NextResponse.json({ 
      success: true, 
      webhook: newWebhook 
    })
  } catch (error) {
    console.error(`Error creating webhook for workflow ${params.id}:`, error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { success: false, error: 'Failed to create webhook' },
      { status: 500 }
    )
  }
}