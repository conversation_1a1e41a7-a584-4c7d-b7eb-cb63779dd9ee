import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { FormDefinition } from "@/components/form-builder";

// GET /api/form-definitions - Get all form definitions
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const isActive = searchParams.get("isActive");
    const createdBy = searchParams.get("createdBy");
    
    const where: any = {};
    
    if (isActive !== null) {
      where.isActive = isActive === "true";
    }
    
    if (createdBy) {
      where.createdBy = createdBy;
    }
    
    const forms = await prisma.formDefinition.findMany({
      where,
      orderBy: {
        createdAt: "desc",
      },
      include: {
        assetTypeForms: {
          include: {
            assetType: {
              select: {
                id: true,
                name: true,
                code: true,
              },
            },
          },
        },
      },
    });
    
    // Parse JSON fields
    const parsedForms = forms.map(form => ({
      ...form,
      sections: JSON.parse(form.sections),
      settings: JSON.parse(form.settings),
    }));
    
    return NextResponse.json(parsedForms);
    
  } catch (error) {
    console.error("Error getting form definitions:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/form-definitions - Create new form definition
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, description, sections, settings, createdBy } = body;
    
    if (!name || !sections || !settings || !createdBy) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }
    
    const form = await prisma.formDefinition.create({
      data: {
        name,
        description,
        sections: JSON.stringify(sections),
        settings: JSON.stringify(settings),
        createdBy,
      },
    });
    
    return NextResponse.json({
      ...form,
      sections: JSON.parse(form.sections),
      settings: JSON.parse(form.settings),
    });
    
  } catch (error) {
    console.error("Error creating form definition:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// PUT /api/form-definitions - Update form definition
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, name, description, sections, settings } = body;
    
    if (!id) {
      return NextResponse.json(
        { error: "Form ID is required" },
        { status: 400 }
      );
    }
    
    const updateData: any = {
      updatedAt: new Date(),
    };
    
    if (name) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (sections) updateData.sections = JSON.stringify(sections);
    if (settings) updateData.settings = JSON.stringify(settings);
    
    const form = await prisma.formDefinition.update({
      where: { id },
      data: updateData,
    });
    
    return NextResponse.json({
      ...form,
      sections: JSON.parse(form.sections),
      settings: JSON.parse(form.settings),
    });
    
  } catch (error) {
    console.error("Error updating form definition:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// DELETE /api/form-definitions - Delete form definition
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");
    
    if (!id) {
      return NextResponse.json(
        { error: "Form ID is required" },
        { status: 400 }
      );
    }
    
    // Check if form is being used
    const usage = await prisma.assetTypeForm.findFirst({
      where: { formId: id },
    });
    
    if (usage) {
      return NextResponse.json(
        { error: "Cannot delete form that is currently in use" },
        { status: 400 }
      );
    }
    
    await prisma.formDefinition.delete({
      where: { id },
    });
    
    return NextResponse.json({ success: true });
    
  } catch (error) {
    console.error("Error deleting form definition:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}