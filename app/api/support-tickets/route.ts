import { NextRequest, NextResponse } from "next/server";
import { supportTicketService } from "@/lib/services/support-ticket-service";
import { validateSupportTicketFilter, validatePagination } from "@/lib/schemas/api";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth-options";
import { z } from "zod";

export async function GET(request: NextRequest) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const searchParams = request.nextUrl.searchParams;
    
    try {
      const filter = validateSupportTicketFilter(Object.fromEntries(searchParams.entries()));
      const pagination = validatePagination(Object.fromEntries(searchParams.entries()));
      
      // For client users, only show their own tickets
      const userId = session.user.role === "client" ? session.user.id : undefined;
      
      const result = await supportTicketService.getSupportTickets(filter, pagination, userId);
      
      return NextResponse.json({
        success: true,
        data: result.tickets,
        pagination: {
          page: pagination.page,
          limit: pagination.limit,
          total: result.total,
          totalPages: result.totalPages,
          hasNext: pagination.page < result.totalPages,
          hasPrev: pagination.page > 1,
        },
      });
    } catch (validationError) {
      if (validationError instanceof z.ZodError) {
        return NextResponse.json(
          { 
            success: false,
            error: "Invalid filter parameters",
            details: validationError.errors 
          },
          { status: 400 }
        );
      }
      throw validationError;
    }
  } catch (error) {
    console.error("Error fetching support tickets:", error);
    
    const errorMessage = error instanceof Error ? error.message : "Failed to fetch support tickets";
    
    return NextResponse.json(
      { 
        success: false,
        error: errorMessage 
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const data = await request.json();
    
    // Validate required fields
    if (!data.subject || !data.description || !data.category) {
      return NextResponse.json(
        { 
          success: false,
          error: "Missing required fields",
          details: "subject, description, and category are required" 
        },
        { status: 400 }
      );
    }
    
    const supportTicket = await supportTicketService.createSupportTicket(data, session.user.id);
    
    return NextResponse.json({
      success: true,
      data: supportTicket,
      message: "Support ticket created successfully"
    }, { status: 201 });
  } catch (error) {
    console.error("Error creating support ticket:", error);
    
    let errorMessage = "Failed to create support ticket";
    let statusCode = 500;
    
    if (error instanceof Error) {
      errorMessage = error.message;
      
      // Check if it's a validation error
      if (error.message.includes("Validation failed")) {
        statusCode = 400;
      }
    }
    
    return NextResponse.json(
      { 
        success: false,
        error: errorMessage 
      },
      { status: statusCode }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const data = await request.json();
    const { id, ...updateData } = data;
    
    if (!id) {
      return NextResponse.json(
        { 
          success: false,
          error: "Missing ticket ID" 
        },
        { status: 400 }
      );
    }
    
    // For client users, only allow updating their own tickets
    const userId = session.user.role === "client" ? session.user.id : undefined;
    
    const supportTicket = await supportTicketService.updateSupportTicket(id, updateData, userId);
    
    return NextResponse.json({
      success: true,
      data: supportTicket,
      message: "Support ticket updated successfully"
    });
  } catch (error) {
    console.error("Error updating support ticket:", error);
    
    let errorMessage = "Failed to update support ticket";
    let statusCode = 500;
    
    if (error instanceof Error) {
      errorMessage = error.message;
      
      if (error.message.includes("not found") || error.message.includes("access denied")) {
        statusCode = 404;
      } else if (error.message.includes("Validation failed")) {
        statusCode = 400;
      }
    }
    
    return NextResponse.json(
      { 
        success: false,
        error: errorMessage 
      },
      { status: statusCode }
    );
  }
}
