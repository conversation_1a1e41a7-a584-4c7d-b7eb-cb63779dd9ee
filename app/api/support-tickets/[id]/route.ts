import { NextRequest, NextResponse } from "next/server";
import { supportTicketService } from "@/lib/services/support-ticket-service";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth-options";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id } = params;
    
    // For client users, only allow accessing their own tickets
    const userId = session.user.role === "client" ? session.user.id : undefined;
    
    const supportTicket = await supportTicketService.getSupportTicketById(id, userId);
    
    if (!supportTicket) {
      return NextResponse.json(
        { success: false, error: "Support ticket not found" },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: supportTicket,
    });
  } catch (error) {
    console.error("Error fetching support ticket:", error);
    
    const errorMessage = error instanceof Error ? error.message : "Failed to fetch support ticket";
    
    return NextResponse.json(
      { 
        success: false,
        error: errorMessage 
      },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id } = params;
    const data = await request.json();
    
    // For client users, only allow updating their own tickets
    const userId = session.user.role === "client" ? session.user.id : undefined;
    
    const supportTicket = await supportTicketService.updateSupportTicket(id, data, userId);
    
    return NextResponse.json({
      success: true,
      data: supportTicket,
      message: "Support ticket updated successfully"
    });
  } catch (error) {
    console.error("Error updating support ticket:", error);
    
    let errorMessage = "Failed to update support ticket";
    let statusCode = 500;
    
    if (error instanceof Error) {
      errorMessage = error.message;
      
      if (error.message.includes("not found") || error.message.includes("access denied")) {
        statusCode = 404;
      } else if (error.message.includes("Validation failed")) {
        statusCode = 400;
      }
    }
    
    return NextResponse.json(
      { 
        success: false,
        error: errorMessage 
      },
      { status: statusCode }
    );
  }
}
