import { NextRequest, NextResponse } from "next/server";
import { supportTicketService } from "@/lib/services/support-ticket-service";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth-options";

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id: ticketId } = params;
    const data = await request.json();
    
    // Validate required fields
    if (!data.content) {
      return NextResponse.json(
        { 
          success: false,
          error: "Message content is required" 
        },
        { status: 400 }
      );
    }
    
    // For client users, only allow adding messages to their own tickets
    const userId = session.user.role === "client" ? session.user.id : undefined;
    
    const message = await supportTicketService.addMessage(
      ticketId, 
      data, 
      session.user.id, 
      session.user.name
    );
    
    return NextResponse.json({
      success: true,
      data: message,
      message: "Message added successfully"
    }, { status: 201 });
  } catch (error) {
    console.error("Error adding message:", error);
    
    let errorMessage = "Failed to add message";
    let statusCode = 500;
    
    if (error instanceof Error) {
      errorMessage = error.message;
      
      if (error.message.includes("not found") || error.message.includes("access denied")) {
        statusCode = 404;
      } else if (error.message.includes("Validation failed")) {
        statusCode = 400;
      }
    }
    
    return NextResponse.json(
      { 
        success: false,
        error: errorMessage 
      },
      { status: statusCode }
    );
  }
}
