"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { 
  ArrowLeft, 
  Package, 
  User, 
  Calendar, 
  MapPin, 
  Building, 
  DollarSign,
  Clock,
  CheckCircle,
  XCircle,
  Truck,
  FileText,
  AlertCircle,
  Edit,
  Trash2
} from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { useSession } from "next-auth/react";

interface Requisition {
  id: string;
  requestorId: string;
  requestorName: string;
  assetType: {
    id: string;
    name: string;
    code: string;
    icon?: string;
    color?: string;
  };
  quantity: number;
  priority: string;
  status: string;
  location?: string;
  department?: string;
  budgetCode?: string;
  justification?: string;
  businessCase?: string;
  expectedDelivery?: string;
  createdAt: string;
  updatedAt: string;
  approvedAt?: string;
  approvedBy?: string;
  rejectedAt?: string;
  rejectedBy?: string;
  rejectionReason?: string;
  fulfilledAt?: string;
  fulfilledBy?: string;
  data: Record<string, any>;
  approvalHistory?: any[];
  fulfillmentData?: any;
  allocations?: any[];
  purchaseOrders?: any[];
  leaseAgreements?: any[];
}

const statusConfig = {
  pending: { label: "Pending", color: "bg-yellow-100 text-yellow-800", icon: Clock },
  approved: { label: "Approved", color: "bg-blue-100 text-blue-800", icon: CheckCircle },
  rejected: { label: "Rejected", color: "bg-red-100 text-red-800", icon: XCircle },
  fulfilled: { label: "Fulfilled", color: "bg-green-100 text-green-800", icon: CheckCircle },
  cancelled: { label: "Cancelled", color: "bg-gray-100 text-gray-800", icon: XCircle },
  partially_fulfilled: { label: "Partially Fulfilled", color: "bg-orange-100 text-orange-800", icon: Package },
};

const priorityConfig = {
  low: { label: "Low", color: "bg-gray-100 text-gray-800" },
  normal: { label: "Normal", color: "bg-blue-100 text-blue-800" },
  high: { label: "High", color: "bg-orange-100 text-orange-800" },
  critical: { label: "Critical", color: "bg-red-100 text-red-800" },
};

export default function RequisitionDetailsPage() {
  const router = useRouter();
  const params = useParams();
  const { data: session } = useSession();
  const requisitionId = params.id as string;

  const [requisition, setRequisition] = useState<Requisition | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [showApprovalForm, setShowApprovalForm] = useState(false);
  const [approvalComments, setApprovalComments] = useState("");

  useEffect(() => {
    if (requisitionId) {
      loadRequisition();
    }
  }, [requisitionId]);

  const loadRequisition = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/requisitions/${requisitionId}`);
      
      if (!response.ok) {
        throw new Error("Failed to load requisition");
      }

      const data = await response.json();
      setRequisition(data.requisition);
    } catch (error) {
      console.error("Error loading requisition:", error);
      toast({
        title: "Error",
        description: "Failed to load requisition. Please try again.",
        variant: "destructive",
      });
      router.back();
    } finally {
      setIsLoading(false);
    }
  };

  const handleApproval = async (decision: "approved" | "rejected") => {
    if (!approvalComments.trim()) {
      toast({
        title: "Error",
        description: "Please provide comments for your decision.",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsProcessing(true);
      
      const response = await fetch(`/api/requisitions/${requisitionId}/approve`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          decision,
          comments: approvalComments,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to process approval");
      }

      const result = await response.json();
      toast({
        title: "Success",
        description: result.message,
      });

      setShowApprovalForm(false);
      setApprovalComments("");
      await loadRequisition();
    } catch (error) {
      console.error("Error processing approval:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to process approval.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleCancel = async () => {
    const reason = prompt("Please provide a reason for cancellation:");
    if (!reason) return;

    try {
      setIsProcessing(true);
      
      const response = await fetch(`/api/requisitions/${requisitionId}`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ reason }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to cancel requisition");
      }

      toast({
        title: "Success",
        description: "Requisition cancelled successfully.",
      });

      await loadRequisition();
    } catch (error) {
      console.error("Error cancelling requisition:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to cancel requisition.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;
    return (
      <Badge className={config.color}>
        <Icon className="h-3 w-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.normal;
    return (
      <Badge variant="outline" className={config.color}>
        {config.label}
      </Badge>
    );
  };

  const canApprove = session?.user?.role && ["manager", "admin"].includes(session.user.role) && requisition?.status === "pending";
  const canEdit = session?.user?.id === requisition?.requestorId && requisition?.status === "pending";
  const canCancel = (session?.user?.id === requisition?.requestorId || session?.user?.role === "admin") && 
                   requisition?.status && ["pending", "approved"].includes(requisition.status);

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-2">Loading...</span>
        </div>
      </div>
    );
  }

  if (!requisition) {
    return (
      <div className="container mx-auto py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Requisition not found. Please check the ID and try again.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Requisition Details</h1>
            <p className="text-muted-foreground">
              Request ID: {requisition.id}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {getStatusBadge(requisition.status)}
          {getPriorityBadge(requisition.priority)}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center gap-2">
        {canApprove && (
          <Button onClick={() => setShowApprovalForm(true)}>
            <CheckCircle className="h-4 w-4 mr-2" />
            Review & Approve
          </Button>
        )}
        {canEdit && (
          <Button variant="outline">
            <Edit className="h-4 w-4 mr-2" />
            Edit Request
          </Button>
        )}
        {canCancel && (
          <Button variant="destructive" onClick={handleCancel} disabled={isProcessing}>
            <Trash2 className="h-4 w-4 mr-2" />
            Cancel Request
          </Button>
        )}
      </div>

      {/* Approval Form */}
      {showApprovalForm && (
        <Card>
          <CardHeader>
            <CardTitle>Review Requisition</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Comments *</label>
              <Textarea
                placeholder="Provide your decision rationale..."
                value={approvalComments}
                onChange={(e) => setApprovalComments(e.target.value)}
                rows={3}
              />
            </div>
            <div className="flex items-center gap-2">
              <Button 
                onClick={() => handleApproval("approved")}
                disabled={isProcessing}
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Approve
              </Button>
              <Button 
                variant="destructive"
                onClick={() => handleApproval("rejected")}
                disabled={isProcessing}
              >
                <XCircle className="h-4 w-4 mr-2" />
                Reject
              </Button>
              <Button 
                variant="outline"
                onClick={() => setShowApprovalForm(false)}
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Asset Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Asset Request
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <div 
                    className="p-3 rounded-lg"
                    style={{ backgroundColor: requisition.assetType.color + "20" }}
                  >
                    <Package 
                      className="h-6 w-6" 
                      style={{ color: requisition.assetType.color }}
                    />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold">{requisition.assetType.name}</h3>
                    <p className="text-sm text-muted-foreground">
                      Code: {requisition.assetType.code}
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Quantity</p>
                    <p className="text-2xl font-bold">{requisition.quantity}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Priority</p>
                    <div className="mt-1">
                      {getPriorityBadge(requisition.priority)}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Request Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Request Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {requisition.justification && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground mb-2">Justification</p>
                  <p className="text-sm">{requisition.justification}</p>
                </div>
              )}

              {requisition.businessCase && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground mb-2">Business Case</p>
                  <p className="text-sm">{requisition.businessCase}</p>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {requisition.location && (
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">Location</p>
                      <p className="text-sm text-muted-foreground">{requisition.location}</p>
                    </div>
                  </div>
                )}

                {requisition.department && (
                  <div className="flex items-center gap-2">
                    <Building className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">Department</p>
                      <p className="text-sm text-muted-foreground">{requisition.department}</p>
                    </div>
                  </div>
                )}

                {requisition.budgetCode && (
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">Budget Code</p>
                      <p className="text-sm text-muted-foreground">{requisition.budgetCode}</p>
                    </div>
                  </div>
                )}

                {requisition.expectedDelivery && (
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">Expected Delivery</p>
                      <p className="text-sm text-muted-foreground">
                        {new Date(requisition.expectedDelivery).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Approval History */}
          {requisition.approvalHistory && requisition.approvalHistory.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5" />
                  Approval History
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {requisition.approvalHistory.map((approval: any, index: number) => (
                    <div key={index} className="flex items-start gap-3 p-3 border rounded-lg">
                      <div className={`p-1 rounded-full ${
                        approval.decision === "approved" ? "bg-green-100" : "bg-red-100"
                      }`}>
                        {approval.decision === "approved" ? (
                          <CheckCircle className="h-4 w-4 text-green-600" />
                        ) : (
                          <XCircle className="h-4 w-4 text-red-600" />
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <p className="font-medium capitalize">{approval.decision}</p>
                          <p className="text-sm text-muted-foreground">
                            {new Date(approval.timestamp).toLocaleString()}
                          </p>
                        </div>
                        <p className="text-sm text-muted-foreground mt-1">
                          {approval.comments}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Request Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Request Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Requestor</p>
                <p className="font-medium">{requisition.requestorName}</p>
              </div>

              <Separator />

              <div>
                <p className="text-sm font-medium text-muted-foreground">Created</p>
                <p className="text-sm">{new Date(requisition.createdAt).toLocaleString()}</p>
              </div>

              <div>
                <p className="text-sm font-medium text-muted-foreground">Last Updated</p>
                <p className="text-sm">{new Date(requisition.updatedAt).toLocaleString()}</p>
              </div>

              {requisition.approvedAt && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Approved</p>
                  <p className="text-sm">{new Date(requisition.approvedAt).toLocaleString()}</p>
                </div>
              )}

              {requisition.fulfilledAt && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Fulfilled</p>
                  <p className="text-sm">{new Date(requisition.fulfilledAt).toLocaleString()}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Fulfillment Status */}
          {requisition.status !== "pending" && requisition.status !== "rejected" && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Truck className="h-5 w-5" />
                  Fulfillment Status
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {requisition.allocations && requisition.allocations.length > 0 && (
                    <div>
                      <p className="text-sm font-medium mb-2">Allocated Assets</p>
                      <div className="space-y-2">
                        {requisition.allocations.map((allocation: any) => (
                          <div key={allocation.id} className="text-sm p-2 bg-muted rounded">
                            <p className="font-medium">{allocation.asset.name}</p>
                            <p className="text-muted-foreground">
                              Serial: {allocation.asset.serialNumber || "N/A"}
                            </p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {requisition.purchaseOrders && requisition.purchaseOrders.length > 0 && (
                    <div>
                      <p className="text-sm font-medium mb-2">Purchase Orders</p>
                      <div className="space-y-2">
                        {requisition.purchaseOrders.map((po: any) => (
                          <div key={po.id} className="text-sm p-2 bg-muted rounded">
                            <p className="font-medium">PO #{po.purchaseOrder.id.slice(-8)}</p>
                            <p className="text-muted-foreground">
                              Supplier: {po.purchaseOrder.supplier}
                            </p>
                            <p className="text-muted-foreground">
                              Status: {po.purchaseOrder.status}
                            </p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}