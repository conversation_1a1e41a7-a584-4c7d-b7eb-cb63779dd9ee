"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AssetOperationFormRenderer } from "@/components/form-builder/asset-operation-form-renderer";
import { FormContext } from "@/lib/types/asset-type-forms";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ArrowLeft, ShoppingCart, Package, Info } from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { useSession } from "next-auth/react";

interface AssetType {
  id: string;
  name: string;
  code: string;
  icon?: string;
  color?: string;
  description?: string;
}

export default function NewRequisitionPage() {
  const router = useRouter();
  const session = useSession();
  const [assetTypes, setAssetTypes] = useState<AssetType[]>([]);
  const [selectedAssetTypeId, setSelectedAssetTypeId] = useState<string>("");
  const [isLoadingAssetTypes, setIsLoadingAssetTypes] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    loadAssetTypes();
  }, []);

  const loadAssetTypes = async () => {
    try {
      setIsLoadingAssetTypes(true);
      const response = await fetch("/api/asset-types");
      
      if (!response.ok) {
        throw new Error("Failed to load asset types");
      }

      const data = await response.json();
      setAssetTypes(data.assetTypes || []);
    } catch (error) {
      console.error("Error loading asset types:", error);
      toast({
        title: "Error",
        description: "Failed to load asset types. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoadingAssetTypes(false);
    }
  };

  const handleSubmit = async (formData: Record<string, any>) => {
    try {
      setIsSubmitting(true);

      const response = await fetch("/api/asset-operations", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          operationType: "requisition.asset",
          assetTypeId: selectedAssetTypeId,
          data: {
            ...formData,
            assetTypeId: selectedAssetTypeId,
            requestorName: session?.data?.user?.name || session?.data?.user?.email,
          },
          context: {
            assetTypeId: selectedAssetTypeId,
            operationType: "requisition.asset",
            userId: session?.data?.user?.id || "unknown",
            userRole: session?.data?.user?.role || "user",
          },
        }),
      });

      if (response.ok) {
        const result = await response.json();
        toast({
          title: "Success",
          description: "Asset requisition submitted successfully!",
        });
        
        // Redirect to requisition details or list
        router.push("/requisitions");
      } else {
        const error = await response.json();
        throw new Error(error.message || "Failed to submit requisition");
      }
    } catch (error) {
      console.error("Error submitting requisition:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to submit requisition. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSaveDraft = async (formData: Record<string, any>) => {
    try {
      // Save as draft logic
      const draftKey = `requisition-draft-${selectedAssetTypeId}`;
      localStorage.setItem(draftKey, JSON.stringify(formData));
      toast({
        title: "Draft Saved",
        description: "Your requisition has been saved as a draft.",
      });
    } catch (error) {
      console.error("Error saving draft:", error);
      toast({
        title: "Error",
        description: "Failed to save draft.",
        variant: "destructive",
      });
    }
  };

  const handleCancel = () => {
    router.back();
  };

  const selectedAssetType = assetTypes.find(at => at.id === selectedAssetTypeId);

  if (!session) {
    return (
      <div className="container mx-auto py-8">
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            Please log in to submit asset requisitions.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const formContext: FormContext = {
    assetTypeId: selectedAssetTypeId,
    operationType: "requisition.asset",
    userId: session.data?.user?.id || "unknown",
    userRole: session.data?.user?.role || "user",
  };

  return (
    <div className="container mx-auto py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold">New Asset Requisition</h1>
            <p className="text-muted-foreground">
              Submit a request for new assets for your department
            </p>
          </div>
        </div>
      </div>

      {/* Asset Type Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Select Asset Type
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Asset Type *</label>
              <Select 
                value={selectedAssetTypeId} 
                onValueChange={setSelectedAssetTypeId}
                disabled={isLoadingAssetTypes}
              >
                <SelectTrigger>
                  <SelectValue placeholder={isLoadingAssetTypes ? "Loading..." : "Select an asset type"} />
                </SelectTrigger>
                <SelectContent>
                  {assetTypes.map((assetType) => (
                    <SelectItem key={assetType.id} value={assetType.id}>
                      <div className="flex items-center gap-2">
                        <div 
                          className="w-3 h-3 rounded-full" 
                          style={{ backgroundColor: assetType.color || "#6B7280" }}
                        />
                        <span>{assetType.name}</span>
                        <span className="text-muted-foreground">({assetType.code})</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {selectedAssetType && (
              <div className="p-4 bg-muted rounded-lg">
                <div className="flex items-center gap-3">
                  <div 
                    className="p-2 rounded-lg"
                    style={{ backgroundColor: selectedAssetType.color + "20" }}
                  >
                    <Package 
                      className="h-5 w-5" 
                      style={{ color: selectedAssetType.color }}
                    />
                  </div>
                  <div>
                    <h3 className="font-medium">{selectedAssetType.name}</h3>
                    <p className="text-sm text-muted-foreground">
                      {selectedAssetType.description || `Asset type: ${selectedAssetType.code}`}
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Requisition Form */}
      {selectedAssetTypeId && (
        <div className="space-y-6">
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              Please provide detailed information about your asset request. 
              All requisitions require approval before fulfillment.
            </AlertDescription>
          </Alert>

          <AssetOperationFormRenderer
            assetTypeId={selectedAssetTypeId}
            operationType="requisition.asset"
            context={formContext}
            onSubmit={handleSubmit}
            onSaveDraft={handleSaveDraft}
            onCancel={handleCancel}
          />
        </div>
      )}

      {/* Instructions */}
      {!selectedAssetTypeId && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ShoppingCart className="h-5 w-5" />
              How to Submit a Requisition
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-sm font-medium">
                  1
                </div>
                <div>
                  <h4 className="font-medium">Select Asset Type</h4>
                  <p className="text-sm text-muted-foreground">
                    Choose the type of asset you need from the dropdown above.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-sm font-medium">
                  2
                </div>
                <div>
                  <h4 className="font-medium">Fill Out Details</h4>
                  <p className="text-sm text-muted-foreground">
                    Provide quantity, justification, location, and other required information.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-sm font-medium">
                  3
                </div>
                <div>
                  <h4 className="font-medium">Submit for Approval</h4>
                  <p className="text-sm text-muted-foreground">
                    Your request will be reviewed by management and you'll be notified of the decision.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-sm font-medium">
                  4
                </div>
                <div>
                  <h4 className="font-medium">Track Progress</h4>
                  <p className="text-sm text-muted-foreground">
                    Monitor your requisition status and receive updates on fulfillment.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}