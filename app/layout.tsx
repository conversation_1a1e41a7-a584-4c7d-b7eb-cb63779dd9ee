import { <PERSON><PERSON><PERSON>eader } from "@/components/layout/app-header";
import type React from "react"
import type { Metadata } from "next"
import { outfit, montserrat, jetbrains<PERSON>ono, manrope } from "@/lib/fonts"
import "./globals.css"
//import "./index.css"
import { AppSidebar } from "@/components/app-sidebar"
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar"
import { ErrorBoundary } from "@/components/ui/error-boundary"
import { OfflineIndicator } from "@/components/ui/offline-indicator"
import { Toaster } from "@/components/ui/toaster"
import { ThemeProvider } from "@/components/theme-provider"
import NextTopLoader from "nextjs-toploader"
import { AuthProvider } from "@/contexts/auth-context";

export const metadata: Metadata = {
  title: "WizeAssets - Enterprise Asset Management",
  description: "Advanced ERP platform for comprehensive asset management",
  keywords: "asset management, ERP, enterprise, maintenance, financial management",
  authors: [{ name: "WizeAssets Team" }],
  robots: "index, follow",
  openGraph: {
    title: "WizeAssets - Enterprise Asset Management",
    description: "Advanced ERP platform for comprehensive asset management",
    type: "website",
    locale: "en_US",
  },
  generator: 'v0.dev'
}

export const viewport = "width=device-width, initial-scale=1"

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <head>
        <meta name="theme-color" content="#2563eb" />
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
      </head>
      <body className={`${outfit.variable} ${montserrat.variable} ${jetbrainsMono.variable} ${manrope.variable}`}>
        <NextTopLoader
          showSpinner={false}
          color="hsl(var(--primary))"
          height={3.5}
          crawlSpeed={200}
          easing="ease"
          shadow="0 0 10px hsl(var(--foreground) / 0.1)"
        />
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <AuthProvider>
          {children}
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
