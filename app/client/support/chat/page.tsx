"use client";

import React, { useState, useEffect, useRef } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  MessageSquare, 
  Send, 
  Paperclip, 
  Phone,
  Video,
  MoreVertical,
  Smile,
  User,
  Bot,
  Clock,
  CheckCircle2
} from "lucide-react";
import { useSession } from "next-auth/react";
import { toast } from "@/components/ui/use-toast";

interface ChatMessage {
  id: string;
  content: string;
  sender: string;
  senderType: "client" | "support" | "bot";
  timestamp: string;
  status?: "sent" | "delivered" | "read";
  attachments?: string[];
}

interface SupportAgent {
  id: string;
  name: string;
  avatar?: string;
  status: "online" | "away" | "busy";
  title: string;
}

const mockAgent: SupportAgent = {
  id: "agent-1",
  name: "<PERSON>",
  avatar: "/avatars/sarah.jpg",
  status: "online",
  title: "Senior Support Specialist",
};

export default function LiveChatPage() {
  const { data: session } = useSession();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState("");
  const [isConnected, setIsConnected] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [agent, setAgent] = useState<SupportAgent | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Simulate connecting to chat
    const connectTimer = setTimeout(() => {
      setIsConnected(true);
      setAgent(mockAgent);
      
      // Add welcome message
      const welcomeMessage: ChatMessage = {
        id: "welcome",
        content: `Hi ${session?.user?.name || "there"}! I'm Sarah, your support specialist. How can I help you today?`,
        sender: mockAgent.name,
        senderType: "support",
        timestamp: new Date().toISOString(),
        status: "delivered",
      };
      
      setMessages([welcomeMessage]);
    }, 2000);

    return () => clearTimeout(connectTimer);
  }, [session]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const handleSendMessage = async () => {
    if (!newMessage.trim() || !isConnected) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      content: newMessage,
      sender: session?.user?.name || "You",
      senderType: "client",
      timestamp: new Date().toISOString(),
      status: "sent",
    };

    setMessages(prev => [...prev, userMessage]);
    setNewMessage("");

    // Simulate typing indicator
    setIsTyping(true);

    // Simulate agent response
    setTimeout(() => {
      setIsTyping(false);
      
      const responses = [
        "I understand your concern. Let me look into this for you.",
        "That's a great question! I can definitely help you with that.",
        "I see what you mean. Let me check our system for more information.",
        "Thank you for providing those details. I'll investigate this right away.",
        "I can help you resolve this issue. Let me walk you through the process.",
      ];
      
      const randomResponse = responses[Math.floor(Math.random() * responses.length)];
      
      const agentMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: randomResponse,
        sender: agent?.name || "Support Agent",
        senderType: "support",
        timestamp: new Date().toISOString(),
        status: "delivered",
      };

      setMessages(prev => [...prev, agentMessage]);
    }, 1500 + Math.random() * 2000);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case "sent":
        return <Clock className="h-3 w-3 text-muted-foreground" />;
      case "delivered":
        return <CheckCircle2 className="h-3 w-3 text-muted-foreground" />;
      case "read":
        return <CheckCircle2 className="h-3 w-3 text-blue-500" />;
      default:
        return null;
    }
  };

  const getSenderAvatar = (message: ChatMessage) => {
    if (message.senderType === "client") {
      return (
        <Avatar className="h-8 w-8">
          <AvatarFallback>
            {session?.user?.name?.charAt(0) || "U"}
          </AvatarFallback>
        </Avatar>
      );
    } else if (message.senderType === "support") {
      return (
        <Avatar className="h-8 w-8">
          <AvatarImage src={agent?.avatar} />
          <AvatarFallback>
            {agent?.name?.charAt(0) || "S"}
          </AvatarFallback>
        </Avatar>
      );
    } else {
      return (
        <Avatar className="h-8 w-8">
          <AvatarFallback>
            <Bot className="h-4 w-4" />
          </AvatarFallback>
        </Avatar>
      );
    }
  };

  return (
    <div className="container mx-auto py-8">
      <div className="max-w-4xl mx-auto">
        <Card className="h-[600px] flex flex-col">
          {/* Chat Header */}
          <CardHeader className="border-b">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <MessageSquare className="h-6 w-6" />
                <div>
                  <CardTitle className="text-lg">Live Support Chat</CardTitle>
                  {isConnected && agent ? (
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                        <span>{agent.name}</span>
                      </div>
                      <span>•</span>
                      <span>{agent.title}</span>
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground">
                      Connecting to support...
                    </p>
                  )}
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" disabled={!isConnected}>
                  <Phone className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="sm" disabled={!isConnected}>
                  <Video className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="sm">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardHeader>

          {/* Chat Messages */}
          <CardContent className="flex-1 overflow-y-auto p-4 space-y-4">
            {!isConnected ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                  <p className="text-muted-foreground">Connecting you to a support agent...</p>
                </div>
              </div>
            ) : (
              <>
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex gap-3 ${
                      message.senderType === "client" ? "flex-row-reverse" : ""
                    }`}
                  >
                    {getSenderAvatar(message)}
                    <div
                      className={`max-w-[70%] ${
                        message.senderType === "client" ? "text-right" : ""
                      }`}
                    >
                      <div
                        className={`p-3 rounded-lg ${
                          message.senderType === "client"
                            ? "bg-primary text-primary-foreground"
                            : message.senderType === "support"
                            ? "bg-muted"
                            : "bg-blue-50 border border-blue-200"
                        }`}
                      >
                        <p className="text-sm">{message.content}</p>
                      </div>
                      <div
                        className={`flex items-center gap-1 mt-1 text-xs text-muted-foreground ${
                          message.senderType === "client" ? "justify-end" : ""
                        }`}
                      >
                        <span>
                          {new Date(message.timestamp).toLocaleTimeString([], {
                            hour: "2-digit",
                            minute: "2-digit",
                          })}
                        </span>
                        {message.senderType === "client" && getStatusIcon(message.status)}
                      </div>
                    </div>
                  </div>
                ))}

                {isTyping && (
                  <div className="flex gap-3">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={agent?.avatar} />
                      <AvatarFallback>
                        {agent?.name?.charAt(0) || "S"}
                      </AvatarFallback>
                    </Avatar>
                    <div className="bg-muted p-3 rounded-lg">
                      <div className="flex gap-1">
                        <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: "0.1s" }}></div>
                        <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: "0.2s" }}></div>
                      </div>
                    </div>
                  </div>
                )}

                <div ref={messagesEndRef} />
              </>
            )}
          </CardContent>

          {/* Chat Input */}
          <div className="border-t p-4">
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" disabled={!isConnected}>
                <Paperclip className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm" disabled={!isConnected}>
                <Smile className="h-4 w-4" />
              </Button>
              <div className="flex-1">
                <Input
                  placeholder={
                    isConnected 
                      ? "Type your message..." 
                      : "Please wait while we connect you..."
                  }
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  disabled={!isConnected}
                />
              </div>
              <Button 
                onClick={handleSendMessage}
                disabled={!isConnected || !newMessage.trim()}
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
            
            {isConnected && (
              <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
                <span>Press Enter to send, Shift+Enter for new line</span>
                <div className="flex items-center gap-1">
                  <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                  <span>Support agent online</span>
                </div>
              </div>
            )}
          </div>
        </Card>

        {/* Quick Actions */}
        <div className="mt-6 grid gap-4 md:grid-cols-3">
          <Card>
            <CardContent className="p-4">
              <h3 className="font-medium mb-2">Common Questions</h3>
              <div className="space-y-2">
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="w-full justify-start text-left h-auto p-2"
                  onClick={() => setNewMessage("How do I submit an asset request?")}
                  disabled={!isConnected}
                >
                  How do I submit an asset request?
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="w-full justify-start text-left h-auto p-2"
                  onClick={() => setNewMessage("What's the status of my request?")}
                  disabled={!isConnected}
                >
                  What's the status of my request?
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="w-full justify-start text-left h-auto p-2"
                  onClick={() => setNewMessage("How do I update my payment method?")}
                  disabled={!isConnected}
                >
                  How do I update my payment method?
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <h3 className="font-medium mb-2">Support Hours</h3>
              <div className="space-y-1 text-sm text-muted-foreground">
                <div className="flex justify-between">
                  <span>Monday - Friday:</span>
                  <span>8:00 AM - 8:00 PM</span>
                </div>
                <div className="flex justify-between">
                  <span>Saturday:</span>
                  <span>9:00 AM - 5:00 PM</span>
                </div>
                <div className="flex justify-between">
                  <span>Sunday:</span>
                  <span>Closed</span>
                </div>
              </div>
              <Badge variant="outline" className="mt-2">
                Currently Open
              </Badge>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <h3 className="font-medium mb-2">Need More Help?</h3>
              <div className="space-y-2">
                <Button variant="outline" size="sm" className="w-full">
                  Create Support Ticket
                </Button>
                <Button variant="outline" size="sm" className="w-full">
                  Browse Help Center
                </Button>
                <Button variant="outline" size="sm" className="w-full">
                  Schedule a Call
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}