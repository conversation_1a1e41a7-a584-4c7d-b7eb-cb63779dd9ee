"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { 
  MessageSquare, 
  Plus, 
  Search,
  Clock,
  CheckCircle,
  AlertCircle,
  User,
  Calendar,
  Tag,
  Send,
  Paperclip
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { toast } from "@/components/ui/use-toast";
import { useSupportTickets } from "@/hooks/use-support-tickets";
import type { SupportTicket as SupportTicketType } from "@/hooks/use-support-tickets";

// Using imported types from hook
type SupportTicket = SupportTicketType;

const statusConfig = {
  open: { label: "Open", color: "bg-blue-100 text-blue-800", icon: Clock },
  in_progress: { label: "In Progress", color: "bg-yellow-100 text-yellow-800", icon: Clock },
  waiting_response: { label: "Waiting Response", color: "bg-orange-100 text-orange-800", icon: AlertCircle },
  resolved: { label: "Resolved", color: "bg-green-100 text-green-800", icon: CheckCircle },
  closed: { label: "Closed", color: "bg-gray-100 text-gray-800", icon: CheckCircle },
};

const priorityConfig = {
  low: { label: "Low", color: "bg-gray-100 text-gray-800" },
  normal: { label: "Normal", color: "bg-blue-100 text-blue-800" },
  high: { label: "High", color: "bg-orange-100 text-orange-800" },
  critical: { label: "Critical", color: "bg-red-100 text-red-800" },
};

const categoryOptions = [
  "Asset Request",
  "Technical Issue",
  "Billing Question",
  "Account Access",
  "Feature Request",
  "Bug Report",
  "General Inquiry",
];

export default function SupportTicketsPage() {
  const router = useRouter();
  const { data: session } = useSession();
  // Use the support tickets hook
  const {
    tickets,
    loading: isLoading,
    error,
    fetchTickets,
    fetchTicketById,
    createTicket,
    updateTicket,
    addMessage,
  } = useSupportTickets();

  const [filteredTickets, setFilteredTickets] = useState<SupportTicket[]>([]);
  const [selectedTicket, setSelectedTicket] = useState<SupportTicket | null>(null);
  const [showNewTicketForm, setShowNewTicketForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [newMessage, setNewMessage] = useState("");

  // New ticket form data
  const [newTicketData, setNewTicketData] = useState({
    subject: "",
    description: "",
    category: "",
    priority: "normal",
  });

  // Show error toast if there's an error from the hook
  useEffect(() => {
    if (error) {
      toast({
        title: "Error",
        description: error,
        variant: "destructive",
      });
    }
  }, [error]);

  useEffect(() => {
    filterTickets();
  }, [tickets, searchTerm, statusFilter]);

  const loadTickets = async () => {
    try {
      setIsLoading(true);
      
      // Mock data - replace with actual API call
      const mockTickets: SupportTicket[] = [
        {
          id: "1",
          ticketNumber: "TICK-2024-001",
          subject: "Unable to access asset request form",
          description: "I'm getting an error when trying to submit a new asset request. The form seems to freeze after clicking submit.",
          status: "in_progress",
          priority: "high",
          category: "Technical Issue",
          createdAt: "2024-01-15T10:30:00Z",
          updatedAt: "2024-01-15T14:20:00Z",
          assignedTo: "Sarah Johnson",
          messages: [
            {
              id: "1",
              content: "I'm getting an error when trying to submit a new asset request. The form seems to freeze after clicking submit.",
              sender: session?.user?.name || "You",
              senderType: "client",
              timestamp: "2024-01-15T10:30:00Z",
            },
            {
              id: "2",
              content: "Thank you for reporting this issue. I've escalated this to our technical team. Can you please tell me which browser you're using and if you've tried clearing your cache?",
              sender: "Sarah Johnson",
              senderType: "support",
              timestamp: "2024-01-15T14:20:00Z",
            },
          ],
        },
        {
          id: "2",
          ticketNumber: "TICK-2024-002",
          subject: "Question about billing cycle",
          description: "I need clarification on when my monthly subscription renews and how to update my payment method.",
          status: "waiting_response",
          priority: "normal",
          category: "Billing Question",
          createdAt: "2024-01-12T09:15:00Z",
          updatedAt: "2024-01-13T16:45:00Z",
          assignedTo: "Mike Chen",
          messages: [
            {
              id: "1",
              content: "I need clarification on when my monthly subscription renews and how to update my payment method.",
              sender: session?.user?.name || "You",
              senderType: "client",
              timestamp: "2024-01-12T09:15:00Z",
            },
            {
              id: "2",
              content: "Your subscription renews on the 15th of each month. You can update your payment method in the billing section of your account. Would you like me to walk you through the process?",
              sender: "Mike Chen",
              senderType: "support",
              timestamp: "2024-01-13T16:45:00Z",
            },
          ],
        },
        {
          id: "3",
          ticketNumber: "TICK-2024-003",
          subject: "Asset delivery confirmation",
          description: "I received my laptop but it doesn't match the specifications I requested. Can we arrange a replacement?",
          status: "resolved",
          priority: "high",
          category: "Asset Request",
          createdAt: "2024-01-10T11:20:00Z",
          updatedAt: "2024-01-14T10:30:00Z",
          assignedTo: "Lisa Wang",
          messages: [
            {
              id: "1",
              content: "I received my laptop but it doesn't match the specifications I requested. Can we arrange a replacement?",
              sender: session?.user?.name || "You",
              senderType: "client",
              timestamp: "2024-01-10T11:20:00Z",
            },
            {
              id: "2",
              content: "I apologize for the mix-up. I've arranged for a replacement laptop with the correct specifications. It will be delivered within 2 business days. Please keep the current laptop until the replacement arrives.",
              sender: "Lisa Wang",
              senderType: "support",
              timestamp: "2024-01-14T10:30:00Z",
            },
          ],
        },
      ];

      // Fetch support tickets from API
      const response = await fetch('/api/support-tickets?page=1&limit=50&sortBy=createdAt&sortOrder=desc');

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch support tickets');
      }

      // Data is now loaded via the hook
    } catch (error) {
      console.error("Error loading tickets:", error);
      toast({
        title: "Error",
        description: "Failed to load support tickets. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const filterTickets = () => {
    let filtered = tickets;

    if (searchTerm) {
      filtered = filtered.filter(ticket =>
        ticket.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
        ticket.ticketNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        ticket.category.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter !== "all") {
      filtered = filtered.filter(ticket => ticket.status === statusFilter);
    }

    setFilteredTickets(filtered);
  };

  const handleCreateTicket = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newTicketData.subject.trim() || !newTicketData.description.trim()) {
      toast({
        title: "Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    try {
      const newTicket = await createTicket({
        subject: newTicketData.subject,
        description: newTicketData.description,
        category: newTicketData.category,
        priority: newTicketData.priority as any,
      });

      setNewTicketData({ subject: "", description: "", category: "", priority: "normal" });
      setShowNewTicketForm(false);

      toast({
        title: "Success",
        description: `Support ticket ${newTicket.ticketNumber} has been created.`,
      });
    } catch (error) {
      console.error("Error creating ticket:", error);
      toast({
        title: "Error",
        description: "Failed to create support ticket. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleSendMessage = async (ticketId: string) => {
    if (!newMessage.trim()) return;

    try {
      await addMessage(ticketId, newMessage);
      setNewMessage("");

      // Refresh the selected ticket to show the new message
      const updatedTicket = await fetchTicketById(ticketId);
      if (updatedTicket) {
        setSelectedTicket(updatedTicket);
      }

      toast({
        title: "Message Sent",
        description: "Your message has been added to the ticket.",
      });
    } catch (error) {
      console.error("Error sending message:", error);
      toast({
        title: "Error",
        description: "Failed to send message. Please try again.",
        variant: "destructive",
      });
    }
  };

  const getStatusBadge = (status: string) => {
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.open;
    const Icon = config.icon;
    return (
      <Badge className={config.color}>
        <Icon className="h-3 w-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.normal;
    return (
      <Badge variant="outline" className={config.color}>
        {config.label}
      </Badge>
    );
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-2">Loading support tickets...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Support Tickets</h1>
          <p className="text-muted-foreground">
            Get help with your account and asset requests
          </p>
        </div>
        <Button onClick={() => setShowNewTicketForm(true)}>
          <Plus className="h-4 w-4 mr-2" />
          New Ticket
        </Button>
      </div>

      {/* New Ticket Form */}
      {showNewTicketForm && (
        <Card>
          <CardHeader>
            <CardTitle>Create New Support Ticket</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleCreateTicket} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="subject">Subject *</Label>
                  <Input
                    id="subject"
                    placeholder="Brief description of your issue"
                    value={newTicketData.subject}
                    onChange={(e) => setNewTicketData(prev => ({ ...prev, subject: e.target.value }))}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="category">Category *</Label>
                  <Select 
                    value={newTicketData.category} 
                    onValueChange={(value) => setNewTicketData(prev => ({ ...prev, category: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categoryOptions.map((category) => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="priority">Priority</Label>
                <Select 
                  value={newTicketData.priority} 
                  onValueChange={(value) => setNewTicketData(prev => ({ ...prev, priority: value }))}
                >
                  <SelectTrigger className="w-40">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="normal">Normal</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="critical">Critical</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description *</Label>
                <Textarea
                  id="description"
                  placeholder="Provide detailed information about your issue..."
                  value={newTicketData.description}
                  onChange={(e) => setNewTicketData(prev => ({ ...prev, description: e.target.value }))}
                  rows={4}
                  required
                />
              </div>

              <div className="flex items-center gap-2">
                <Button type="submit">
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Create Ticket
                </Button>
                <Button 
                  type="button" 
                  variant="outline"
                  onClick={() => setShowNewTicketForm(false)}
                >
                  Cancel
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Tickets List */}
        <div className="lg:col-span-2 space-y-6">
          {/* Filters */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-4">
                <div className="flex-1 max-w-md">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search tickets..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-8"
                    />
                  </div>
                </div>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-40">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="open">Open</SelectItem>
                    <SelectItem value="in_progress">In Progress</SelectItem>
                    <SelectItem value="waiting_response">Waiting Response</SelectItem>
                    <SelectItem value="resolved">Resolved</SelectItem>
                    <SelectItem value="closed">Closed</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Tickets */}
          <div className="space-y-4">
            {filteredTickets.map((ticket) => (
              <Card 
                key={ticket.id} 
                className={`cursor-pointer transition-colors ${
                  selectedTicket?.id === ticket.id ? "ring-2 ring-primary" : ""
                }`}
                onClick={() => setSelectedTicket(ticket)}
              >
                <CardContent className="p-4">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <h3 className="font-medium">{ticket.subject}</h3>
                      {getStatusBadge(ticket.status)}
                      {getPriorityBadge(ticket.priority)}
                    </div>
                    <span className="text-sm text-muted-foreground">
                      {ticket.ticketNumber}
                    </span>
                  </div>
                  
                  <div className="flex items-center gap-4 text-sm text-muted-foreground mb-2">
                    <div className="flex items-center gap-1">
                      <Tag className="h-3 w-3" />
                      {ticket.category}
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {new Date(ticket.createdAt).toLocaleDateString()}
                    </div>
                    {ticket.assignedTo && (
                      <div className="flex items-center gap-1">
                        <User className="h-3 w-3" />
                        {ticket.assignedTo}
                      </div>
                    )}
                  </div>
                  
                  <p className="text-sm text-muted-foreground line-clamp-2">
                    {ticket.description}
                  </p>
                  
                  <div className="flex items-center justify-between mt-3">
                    <span className="text-xs text-muted-foreground">
                      {ticket.messages?.length || 0} message{(ticket.messages?.length || 0) !== 1 ? 's' : ''}
                    </span>
                    <span className="text-xs text-muted-foreground">
                      Updated {new Date(ticket.updatedAt).toLocaleDateString()}
                    </span>
                  </div>
                </CardContent>
              </Card>
            ))}
            
            {filteredTickets.length === 0 && (
              <Card>
                <CardContent className="p-8 text-center">
                  <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No Tickets Found</h3>
                  <p className="text-muted-foreground">
                    {searchTerm || statusFilter !== "all" 
                      ? "No tickets match your current filters."
                      : "You don't have any support tickets yet."
                    }
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        {/* Ticket Details */}
        <div className="space-y-6">
          {selectedTicket ? (
            <>
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">{selectedTicket.subject}</CardTitle>
                  <div className="flex items-center gap-2">
                    {getStatusBadge(selectedTicket.status)}
                    {getPriorityBadge(selectedTicket.priority)}
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="text-sm space-y-2">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Ticket:</span>
                      <span className="font-mono">{selectedTicket.ticketNumber}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Category:</span>
                      <span>{selectedTicket.category}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Created:</span>
                      <span>{new Date(selectedTicket.createdAt).toLocaleDateString()}</span>
                    </div>
                    {selectedTicket.assignedTo && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Assigned to:</span>
                        <span>{selectedTicket.assignedTo}</span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Messages</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4 max-h-96 overflow-y-auto">
                    {selectedTicket.messages?.map((message) => (
                      <div 
                        key={message.id} 
                        className={`p-3 rounded-lg ${
                          message.senderType === "client" 
                            ? "bg-primary/10 ml-4" 
                            : "bg-muted mr-4"
                        }`}
                      >
                        <div className="flex items-center justify-between mb-2">
                          <span className="font-medium text-sm">{message.sender}</span>
                          <span className="text-xs text-muted-foreground">
                            {new Date(message.createdAt).toLocaleString()}
                          </span>
                        </div>
                        <p className="text-sm">{message.content}</p>
                      </div>
                    ))}
                  </div>
                  
                  {selectedTicket.status !== "closed" && selectedTicket.status !== "resolved" && (
                    <div className="mt-4 space-y-2">
                      <Textarea
                        placeholder="Type your message..."
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        rows={3}
                      />
                      <div className="flex items-center gap-2">
                        <Button 
                          size="sm"
                          onClick={() => handleSendMessage(selectedTicket.id)}
                          disabled={!newMessage.trim()}
                        >
                          <Send className="h-4 w-4 mr-1" />
                          Send
                        </Button>
                        <Button variant="outline" size="sm">
                          <Paperclip className="h-4 w-4 mr-1" />
                          Attach
                        </Button>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Select a Ticket</h3>
                <p className="text-muted-foreground">
                  Choose a ticket from the list to view details and messages.
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}