"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Package, 
  ShoppingCart, 
  Clock, 
  CheckCircle, 
  CreditCard,
  TrendingUp,
  AlertCircle,
  Calendar,
  DollarSign,
  Truck,
  MessageSquare,
  HelpCircle,
  Plus
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { toast } from "@/components/ui/use-toast";

interface DashboardMetrics {
  totalAssets: number;
  activeRequests: number;
  pendingPayments: number;
  openTickets: number;
  monthlySpend: number;
  requestsThisMonth: number;
}

interface RecentActivity {
  id: string;
  type: "request" | "payment" | "delivery" | "support";
  title: string;
  description: string;
  timestamp: string;
  status: string;
}

const statusConfig = {
  pending: { label: "Pending", color: "bg-yellow-100 text-yellow-800" },
  approved: { label: "Approved", color: "bg-blue-100 text-blue-800" },
  delivered: { label: "Delivered", color: "bg-green-100 text-green-800" },
  paid: { label: "Paid", color: "bg-green-100 text-green-800" },
  overdue: { label: "Overdue", color: "bg-red-100 text-red-800" },
  open: { label: "Open", color: "bg-blue-100 text-blue-800" },
  resolved: { label: "Resolved", color: "bg-green-100 text-green-800" },
};

export default function ClientDashboard() {
  const router = useRouter();
  const { data: session } = useSession();
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      
      // Mock data for now - replace with actual API calls
      const mockMetrics: DashboardMetrics = {
        totalAssets: 12,
        activeRequests: 3,
        pendingPayments: 2,
        openTickets: 1,
        monthlySpend: 2450.00,
        requestsThisMonth: 5,
      };

      const mockActivity: RecentActivity[] = [
        {
          id: "1",
          type: "request",
          title: "Laptop Request Approved",
          description: "Your MacBook Pro request has been approved and is being processed.",
          timestamp: "2 hours ago",
          status: "approved",
        },
        {
          id: "2",
          type: "delivery",
          title: "Monitor Delivered",
          description: "Dell UltraSharp monitor has been delivered to your office.",
          timestamp: "1 day ago",
          status: "delivered",
        },
        {
          id: "3",
          type: "payment",
          title: "Payment Processed",
          description: "Monthly subscription payment of $299.99 has been processed.",
          timestamp: "3 days ago",
          status: "paid",
        },
        {
          id: "4",
          type: "support",
          title: "Support Ticket Updated",
          description: "Your keyboard replacement ticket has been updated.",
          timestamp: "5 days ago",
          status: "open",
        },
      ];

      setMetrics(mockMetrics);
      setRecentActivity(mockActivity);
    } catch (error) {
      console.error("Error loading dashboard data:", error);
      toast({
        title: "Error",
        description: "Failed to load dashboard data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    return (
      <Badge className={config.color}>
        {config.label}
      </Badge>
    );
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case "request":
        return <ShoppingCart className="h-4 w-4" />;
      case "payment":
        return <CreditCard className="h-4 w-4" />;
      case "delivery":
        return <Truck className="h-4 w-4" />;
      case "support":
        return <MessageSquare className="h-4 w-4" />;
      default:
        return <Package className="h-4 w-4" />;
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-2">Loading dashboard...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      {/* Welcome Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Welcome back, {session?.user?.name}!</h1>
          <p className="text-muted-foreground">
            Here's what's happening with your assets and requests
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button onClick={() => router.push("/client/requests/new")}>
            <Plus className="h-4 w-4 mr-2" />
            New Request
          </Button>
          <Button variant="outline" onClick={() => router.push("/client/support/contact")}>
            <HelpCircle className="h-4 w-4 mr-2" />
            Get Help
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      {metrics && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">My Assets</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.totalAssets}</div>
              <p className="text-xs text-muted-foreground">
                Active assets assigned to you
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Requests</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.activeRequests}</div>
              <p className="text-xs text-muted-foreground">
                Requests in progress
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Payments</CardTitle>
              <CreditCard className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.pendingPayments}</div>
              <p className="text-xs text-muted-foreground">
                Invoices awaiting payment
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Monthly Spend</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${metrics.monthlySpend.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                This month's total
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Quick Actions
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button 
              variant="outline" 
              className="w-full justify-start"
              onClick={() => router.push("/client/requests/new")}
            >
              <ShoppingCart className="h-4 w-4 mr-2" />
              Request New Asset
            </Button>
            
            <Button 
              variant="outline" 
              className="w-full justify-start"
              onClick={() => router.push("/client/assets")}
            >
              <Package className="h-4 w-4 mr-2" />
              View My Assets
            </Button>
            
            <Button 
              variant="outline" 
              className="w-full justify-start"
              onClick={() => router.push("/client/billing/invoices")}
            >
              <CreditCard className="h-4 w-4 mr-2" />
              Pay Invoices
            </Button>
            
            <Button 
              variant="outline" 
              className="w-full justify-start"
              onClick={() => router.push("/client/support/tickets")}
            >
              <MessageSquare className="h-4 w-4 mr-2" />
              Create Support Ticket
            </Button>
          </CardContent>
        </Card>

        {/* Request Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Request Progress
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>MacBook Pro Request</span>
                <Badge className="bg-blue-100 text-blue-800">Processing</Badge>
              </div>
              <Progress value={75} className="h-2" />
              <p className="text-xs text-muted-foreground">Expected delivery: 3-5 days</p>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Office Chair Request</span>
                <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>
              </div>
              <Progress value={25} className="h-2" />
              <p className="text-xs text-muted-foreground">Awaiting approval</p>
            </div>
            
            <Button 
              variant="outline" 
              size="sm" 
              className="w-full"
              onClick={() => router.push("/client/requests")}
            >
              View All Requests
            </Button>
          </CardContent>
        </Card>

        {/* Support Summary */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              Support & Help
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm">Open Tickets</span>
              <Badge variant="outline">{metrics?.openTickets || 0}</Badge>
            </div>
            
            <div className="space-y-2">
              <Button 
                variant="outline" 
                size="sm" 
                className="w-full justify-start"
                onClick={() => router.push("/client/support/chat")}
              >
                <MessageSquare className="h-4 w-4 mr-2" />
                Live Chat Support
              </Button>
              
              <Button 
                variant="outline" 
                size="sm" 
                className="w-full justify-start"
                onClick={() => router.push("/client/help")}
              >
                <HelpCircle className="h-4 w-4 mr-2" />
                Help Center
              </Button>
            </div>
            
            <div className="text-xs text-muted-foreground">
              Average response time: 2 hours
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Recent Activity
          </CardTitle>
        </CardHeader>
        <CardContent>
          {recentActivity.length > 0 ? (
            <div className="space-y-4">
              {recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-start gap-4 p-4 border rounded-lg">
                  <div className="p-2 bg-muted rounded-lg">
                    {getActivityIcon(activity.type)}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <h4 className="font-medium">{activity.title}</h4>
                      {getStatusBadge(activity.status)}
                    </div>
                    <p className="text-sm text-muted-foreground mb-2">
                      {activity.description}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {activity.timestamp}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Recent Activity</h3>
              <p className="text-muted-foreground mb-4">
                Your recent activity will appear here.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}