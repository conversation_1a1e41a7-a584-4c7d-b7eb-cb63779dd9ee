"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Package, 
  ArrowLeft, 
  ShoppingCart, 
  Calendar,
  MapPin,
  Building,
  DollarSign,
  AlertCircle,
  CheckCircle,
  Clock
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { toast } from "@/components/ui/use-toast";

interface AssetType {
  id: string;
  name: string;
  code: string;
  category: string;
  description?: string;
  icon?: string;
  color?: string;
  estimatedCost?: number;
  availableQuantity?: number;
}

const priorityOptions = [
  { value: "low", label: "Low", description: "Standard delivery (7-14 days)" },
  { value: "normal", label: "Normal", description: "Priority delivery (3-7 days)" },
  { value: "high", label: "High", description: "Express delivery (1-3 days)" },
  { value: "critical", label: "Critical", description: "Emergency delivery (same day)" },
];

export default function NewClientRequestPage() {
  const router = useRouter();
  const { data: session } = useSession();
  const [assetTypes, setAssetTypes] = useState<AssetType[]>([]);
  const [selectedAssetType, setSelectedAssetType] = useState<AssetType | null>(null);
  const [isLoadingAssetTypes, setIsLoadingAssetTypes] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Form data
  const [formData, setFormData] = useState({
    assetTypeId: "",
    quantity: 1,
    priority: "normal",
    justification: "",
    businessCase: "",
    location: "",
    department: "",
    budgetCode: "",
    expectedDelivery: "",
    specifications: "",
  });

  useEffect(() => {
    loadAssetTypes();
  }, []);

  const loadAssetTypes = async () => {
    try {
      setIsLoadingAssetTypes(true);
      
      // Mock data for now - replace with actual API call
      const mockAssetTypes: AssetType[] = [
        {
          id: "1",
          name: "MacBook Pro",
          code: "MBP-16",
          category: "Laptops",
          description: "16-inch MacBook Pro with M3 chip",
          estimatedCost: 2499,
          availableQuantity: 5,
        },
        {
          id: "2",
          name: "Dell UltraSharp Monitor",
          code: "DELL-U27",
          category: "Monitors",
          description: "27-inch 4K USB-C monitor",
          estimatedCost: 599,
          availableQuantity: 12,
        },
        {
          id: "3",
          name: "Herman Miller Chair",
          code: "HM-AERON",
          category: "Furniture",
          description: "Ergonomic office chair",
          estimatedCost: 1395,
          availableQuantity: 3,
        },
        {
          id: "4",
          name: "iPhone 15 Pro",
          code: "IP15-PRO",
          category: "Mobile Devices",
          description: "Latest iPhone with 256GB storage",
          estimatedCost: 999,
          availableQuantity: 8,
        },
      ];

      setAssetTypes(mockAssetTypes);
    } catch (error) {
      console.error("Error loading asset types:", error);
      toast({
        title: "Error",
        description: "Failed to load asset types. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoadingAssetTypes(false);
    }
  };

  const handleAssetTypeSelect = (assetTypeId: string) => {
    const assetType = assetTypes.find(at => at.id === assetTypeId);
    setSelectedAssetType(assetType || null);
    setFormData(prev => ({ ...prev, assetTypeId }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.assetTypeId || !formData.justification.trim()) {
      toast({
        title: "Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSubmitting(true);

      const requestData = {
        ...formData,
        requestorId: session?.user?.id,
        requestorName: session?.user?.name || session?.user?.email,
        expectedDelivery: formData.expectedDelivery ? new Date(formData.expectedDelivery) : null,
      };

      // Mock API call - replace with actual endpoint
      console.log("Submitting request:", requestData);
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      toast({
        title: "Success",
        description: "Your asset request has been submitted successfully!",
      });

      router.push("/client/requests");
    } catch (error) {
      console.error("Error submitting request:", error);
      toast({
        title: "Error",
        description: "Failed to submit request. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const estimatedTotal = selectedAssetType ? selectedAssetType.estimatedCost * formData.quantity : 0;

  return (
    <div className="container mx-auto py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" onClick={() => router.back()}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <div>
          <h1 className="text-3xl font-bold">New Asset Request</h1>
          <p className="text-muted-foreground">
            Request new assets for your work or department
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Main Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Asset Selection */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package className="h-5 w-5" />
                  Asset Selection
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="assetType">Asset Type *</Label>
                  <Select 
                    value={formData.assetTypeId} 
                    onValueChange={handleAssetTypeSelect}
                    disabled={isLoadingAssetTypes}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select an asset type" />
                    </SelectTrigger>
                    <SelectContent>
                      {assetTypes.map((assetType) => (
                        <SelectItem key={assetType.id} value={assetType.id}>
                          <div className="flex items-center justify-between w-full">
                            <div>
                              <div className="font-medium">{assetType.name}</div>
                              <div className="text-sm text-muted-foreground">
                                {assetType.category} • ${assetType.estimatedCost?.toLocaleString()}
                              </div>
                            </div>
                            <Badge variant="outline">
                              {assetType.availableQuantity} available
                            </Badge>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {selectedAssetType && (
                  <Alert>
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>
                      <strong>{selectedAssetType.name}</strong> - {selectedAssetType.description}
                      <br />
                      Estimated cost: ${selectedAssetType.estimatedCost?.toLocaleString()} each
                    </AlertDescription>
                  </Alert>
                )}

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="quantity">Quantity *</Label>
                    <Input
                      id="quantity"
                      type="number"
                      min="1"
                      max={selectedAssetType?.availableQuantity || 100}
                      value={formData.quantity}
                      onChange={(e) => setFormData(prev => ({ 
                        ...prev, 
                        quantity: parseInt(e.target.value) || 1 
                      }))}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="priority">Priority *</Label>
                    <Select 
                      value={formData.priority} 
                      onValueChange={(value) => setFormData(prev => ({ ...prev, priority: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {priorityOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            <div>
                              <div className="font-medium">{option.label}</div>
                              <div className="text-sm text-muted-foreground">
                                {option.description}
                              </div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Request Details */}
            <Card>
              <CardHeader>
                <CardTitle>Request Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="justification">Justification *</Label>
                  <Textarea
                    id="justification"
                    placeholder="Explain why you need this asset..."
                    value={formData.justification}
                    onChange={(e) => setFormData(prev => ({ ...prev, justification: e.target.value }))}
                    required
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="businessCase">Business Case</Label>
                  <Textarea
                    id="businessCase"
                    placeholder="Provide additional business justification (optional)..."
                    value={formData.businessCase}
                    onChange={(e) => setFormData(prev => ({ ...prev, businessCase: e.target.value }))}
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="specifications">Special Requirements</Label>
                  <Textarea
                    id="specifications"
                    placeholder="Any specific configurations or requirements..."
                    value={formData.specifications}
                    onChange={(e) => setFormData(prev => ({ ...prev, specifications: e.target.value }))}
                    rows={2}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Location & Delivery */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  Location & Delivery
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="location">Delivery Location *</Label>
                    <Input
                      id="location"
                      placeholder="Building, floor, room..."
                      value={formData.location}
                      onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="department">Department</Label>
                    <Input
                      id="department"
                      placeholder="Your department"
                      value={formData.department}
                      onChange={(e) => setFormData(prev => ({ ...prev, department: e.target.value }))}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="budgetCode">Budget Code</Label>
                    <Input
                      id="budgetCode"
                      placeholder="Budget/cost center code"
                      value={formData.budgetCode}
                      onChange={(e) => setFormData(prev => ({ ...prev, budgetCode: e.target.value }))}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="expectedDelivery">Expected Delivery</Label>
                    <Input
                      id="expectedDelivery"
                      type="date"
                      value={formData.expectedDelivery}
                      onChange={(e) => setFormData(prev => ({ ...prev, expectedDelivery: e.target.value }))}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Request Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ShoppingCart className="h-5 w-5" />
                  Request Summary
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {selectedAssetType ? (
                  <>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm">Asset:</span>
                        <span className="text-sm font-medium">{selectedAssetType.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">Quantity:</span>
                        <span className="text-sm font-medium">{formData.quantity}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">Unit Cost:</span>
                        <span className="text-sm font-medium">
                          ${selectedAssetType.estimatedCost?.toLocaleString()}
                        </span>
                      </div>
                      <div className="border-t pt-2">
                        <div className="flex justify-between">
                          <span className="font-medium">Estimated Total:</span>
                          <span className="font-bold text-lg">
                            ${estimatedTotal.toLocaleString()}
                          </span>
                        </div>
                      </div>
                    </div>

                    <Alert>
                      <Clock className="h-4 w-4" />
                      <AlertDescription>
                        Expected processing time: 2-5 business days
                      </AlertDescription>
                    </Alert>
                  </>
                ) : (
                  <p className="text-sm text-muted-foreground">
                    Select an asset type to see the summary
                  </p>
                )}
              </CardContent>
            </Card>

            {/* Help & Support */}
            <Card>
              <CardHeader>
                <CardTitle>Need Help?</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="w-full justify-start"
                  type="button"
                  onClick={() => router.push("/client/help")}
                >
                  <AlertCircle className="h-4 w-4 mr-2" />
                  Request Guidelines
                </Button>
                
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="w-full justify-start"
                  type="button"
                  onClick={() => router.push("/client/support/chat")}
                >
                  <Package className="h-4 w-4 mr-2" />
                  Chat with Support
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Submit Actions */}
        <div className="flex items-center justify-end gap-4 pt-6 border-t">
          <Button 
            type="button" 
            variant="outline" 
            onClick={() => router.back()}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Submitting...
              </>
            ) : (
              <>
                <ShoppingCart className="h-4 w-4 mr-2" />
                Submit Request
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}