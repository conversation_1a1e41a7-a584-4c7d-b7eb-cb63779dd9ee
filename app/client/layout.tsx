"use client";

import React, { useEffect } from "react";
import { ClientSidebar } from "@/components/client/client-sidebar";
import { ClientHeader } from "@/components/client/client-header";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";

export default function ClientLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === "loading") return; // Still loading

    if (!session) {
      router.push("/auth/client-login");
      return;
    }

    // Check if user is a client
    if (session.user.role !== "client") {
      router.push("/unauthorized");
      return;
    }

    // Check if account is active
    if (session.user.status === "suspended" || session.user.status === "inactive") {
      router.push("/auth/account-suspended");
      return;
    }

    // Check if email is verified
    if (session.user.status === "pending_verification") {
      router.push("/auth/verify-email-required");
      return;
    }
  }, [session, status, router]);

  if (status === "loading") {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!session || session.user.role !== "client") {
    return null; // Will redirect via useEffect
  }

  return (
    <SidebarProvider>
      <ClientSidebar />
      <SidebarInset>
        <ClientHeader />
        <main className="flex-1 overflow-auto">
          {children}
        </main>
      </SidebarInset>
    </SidebarProvider>
  );
}