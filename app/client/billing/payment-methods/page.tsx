"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  CreditCard, 
  Plus, 
  Edit, 
  Trash2,
  Shield,
  CheckCircle,
  AlertCircle,
  Calendar,
  Lock,
  ArrowLeft
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { toast } from "@/components/ui/use-toast";

interface PaymentMethod {
  id: string;
  type: "credit_card" | "debit_card" | "bank_account" | "paypal";
  last4: string;
  brand?: string;
  expiryMonth?: number;
  expiryYear?: number;
  holderName: string;
  isDefault: boolean;
  isExpired: boolean;
  createdAt: string;
}

const cardBrands = {
  visa: { name: "Visa", color: "bg-blue-600" },
  mastercard: { name: "Mastercard", color: "bg-red-600" },
  amex: { name: "American Express", color: "bg-green-600" },
  discover: { name: "Discover", color: "bg-orange-600" },
};

export default function PaymentMethodsPage() {
  const router = useRouter();
  const { data: session } = useSession();
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingMethod, setEditingMethod] = useState<PaymentMethod | null>(null);

  // Form data for new payment method
  const [formData, setFormData] = useState({
    type: "credit_card",
    cardNumber: "",
    expiryMonth: "",
    expiryYear: "",
    cvv: "",
    holderName: "",
    billingAddress: {
      street: "",
      city: "",
      state: "",
      zipCode: "",
      country: "US",
    },
  });

  useEffect(() => {
    loadPaymentMethods();
  }, []);

  const loadPaymentMethods = async () => {
    try {
      setIsLoading(true);
      
      // Mock data - replace with actual API call
      const mockPaymentMethods: PaymentMethod[] = [
        {
          id: "1",
          type: "credit_card",
          last4: "4242",
          brand: "visa",
          expiryMonth: 12,
          expiryYear: 2025,
          holderName: "John Doe",
          isDefault: true,
          isExpired: false,
          createdAt: "2024-01-15T10:30:00Z",
        },
        {
          id: "2",
          type: "credit_card",
          last4: "5555",
          brand: "mastercard",
          expiryMonth: 8,
          expiryYear: 2024,
          holderName: "John Doe",
          isDefault: false,
          isExpired: true,
          createdAt: "2023-06-20T14:20:00Z",
        },
      ];

      setPaymentMethods(mockPaymentMethods);
    } catch (error) {
      console.error("Error loading payment methods:", error);
      toast({
        title: "Error",
        description: "Failed to load payment methods. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddPaymentMethod = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.cardNumber || !formData.expiryMonth || !formData.expiryYear || !formData.cvv || !formData.holderName) {
      toast({
        title: "Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    try {
      // Mock payment method creation
      const newPaymentMethod: PaymentMethod = {
        id: Date.now().toString(),
        type: formData.type as any,
        last4: formData.cardNumber.slice(-4),
        brand: detectCardBrand(formData.cardNumber),
        expiryMonth: parseInt(formData.expiryMonth),
        expiryYear: parseInt(formData.expiryYear),
        holderName: formData.holderName,
        isDefault: paymentMethods.length === 0, // First card becomes default
        isExpired: false,
        createdAt: new Date().toISOString(),
      };

      setPaymentMethods(prev => [...prev, newPaymentMethod]);
      setFormData({
        type: "credit_card",
        cardNumber: "",
        expiryMonth: "",
        expiryYear: "",
        cvv: "",
        holderName: "",
        billingAddress: {
          street: "",
          city: "",
          state: "",
          zipCode: "",
          country: "US",
        },
      });
      setShowAddForm(false);
      
      toast({
        title: "Success",
        description: "Payment method added successfully.",
      });
    } catch (error) {
      console.error("Error adding payment method:", error);
      toast({
        title: "Error",
        description: "Failed to add payment method. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleSetDefault = async (methodId: string) => {
    try {
      setPaymentMethods(prev => prev.map(method => ({
        ...method,
        isDefault: method.id === methodId,
      })));
      
      toast({
        title: "Success",
        description: "Default payment method updated.",
      });
    } catch (error) {
      console.error("Error setting default payment method:", error);
      toast({
        title: "Error",
        description: "Failed to update default payment method.",
        variant: "destructive",
      });
    }
  };

  const handleDeletePaymentMethod = async (methodId: string) => {
    try {
      const methodToDelete = paymentMethods.find(m => m.id === methodId);
      
      if (methodToDelete?.isDefault && paymentMethods.length > 1) {
        toast({
          title: "Error",
          description: "Cannot delete the default payment method. Please set another method as default first.",
          variant: "destructive",
        });
        return;
      }

      setPaymentMethods(prev => prev.filter(method => method.id !== methodId));
      
      toast({
        title: "Success",
        description: "Payment method deleted successfully.",
      });
    } catch (error) {
      console.error("Error deleting payment method:", error);
      toast({
        title: "Error",
        description: "Failed to delete payment method.",
        variant: "destructive",
      });
    }
  };

  const detectCardBrand = (cardNumber: string): string => {
    const number = cardNumber.replace(/\s/g, "");
    if (number.startsWith("4")) return "visa";
    if (number.startsWith("5") || number.startsWith("2")) return "mastercard";
    if (number.startsWith("3")) return "amex";
    if (number.startsWith("6")) return "discover";
    return "visa"; // default
  };

  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, "").replace(/[^0-9]/gi, "");
    const matches = v.match(/\d{4,16}/g);
    const match = matches && matches[0] || "";
    const parts = [];
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }
    if (parts.length) {
      return parts.join(" ");
    } else {
      return v;
    }
  };

  const getCardIcon = (brand?: string) => {
    if (!brand) return <CreditCard className="h-6 w-6" />;
    
    const brandInfo = cardBrands[brand as keyof typeof cardBrands];
    return (
      <div className={`w-10 h-6 rounded ${brandInfo?.color || "bg-gray-600"} flex items-center justify-center`}>
        <span className="text-white text-xs font-bold">
          {brandInfo?.name.slice(0, 4).toUpperCase() || "CARD"}
        </span>
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-2">Loading payment methods...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" onClick={() => router.back()}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Payment Methods</h1>
          <p className="text-muted-foreground">
            Manage your payment methods and billing information
          </p>
        </div>
      </div>

      {/* Security Notice */}
      <Alert>
        <Shield className="h-4 w-4" />
        <AlertDescription>
          Your payment information is encrypted and secure. We use industry-standard security measures to protect your data.
        </AlertDescription>
      </Alert>

      {/* Payment Methods List */}
      <div className="grid gap-4">
        {paymentMethods.map((method) => (
          <Card key={method.id} className={method.isDefault ? "ring-2 ring-primary" : ""}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  {getCardIcon(method.brand)}
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">
                        •••• •••• •••• {method.last4}
                      </span>
                      {method.isDefault && (
                        <Badge variant="secondary">Default</Badge>
                      )}
                      {method.isExpired && (
                        <Badge variant="destructive">Expired</Badge>
                      )}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {method.holderName}
                      {method.expiryMonth && method.expiryYear && (
                        <span> • Expires {method.expiryMonth.toString().padStart(2, '0')}/{method.expiryYear}</span>
                      )}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  {!method.isDefault && (
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleSetDefault(method.id)}
                    >
                      Set as Default
                    </Button>
                  )}
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => setEditingMethod(method)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => handleDeletePaymentMethod(method.id)}
                    disabled={method.isDefault && paymentMethods.length === 1}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}

        {paymentMethods.length === 0 && (
          <Card>
            <CardContent className="p-8 text-center">
              <CreditCard className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Payment Methods</h3>
              <p className="text-muted-foreground mb-4">
                Add a payment method to start making purchases and paying invoices.
              </p>
              <Button onClick={() => setShowAddForm(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Add Payment Method
              </Button>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Add Payment Method Button */}
      {paymentMethods.length > 0 && !showAddForm && (
        <Button onClick={() => setShowAddForm(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Add Payment Method
        </Button>
      )}

      {/* Add Payment Method Form */}
      {showAddForm && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lock className="h-5 w-5" />
              Add New Payment Method
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleAddPaymentMethod} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="holderName">Cardholder Name *</Label>
                  <Input
                    id="holderName"
                    placeholder="John Doe"
                    value={formData.holderName}
                    onChange={(e) => setFormData(prev => ({ ...prev, holderName: e.target.value }))}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="cardNumber">Card Number *</Label>
                  <Input
                    id="cardNumber"
                    placeholder="1234 5678 9012 3456"
                    value={formData.cardNumber}
                    onChange={(e) => setFormData(prev => ({ 
                      ...prev, 
                      cardNumber: formatCardNumber(e.target.value) 
                    }))}
                    maxLength={19}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="expiryMonth">Expiry Month *</Label>
                  <Select 
                    value={formData.expiryMonth} 
                    onValueChange={(value) => setFormData(prev => ({ ...prev, expiryMonth: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Month" />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.from({ length: 12 }, (_, i) => i + 1).map((month) => (
                        <SelectItem key={month} value={month.toString()}>
                          {month.toString().padStart(2, '0')} - {new Date(0, month - 1).toLocaleString('default', { month: 'long' })}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="expiryYear">Expiry Year *</Label>
                  <Select 
                    value={formData.expiryYear} 
                    onValueChange={(value) => setFormData(prev => ({ ...prev, expiryYear: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Year" />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.from({ length: 10 }, (_, i) => new Date().getFullYear() + i).map((year) => (
                        <SelectItem key={year} value={year.toString()}>
                          {year}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="cvv">CVV *</Label>
                  <Input
                    id="cvv"
                    placeholder="123"
                    value={formData.cvv}
                    onChange={(e) => setFormData(prev => ({ 
                      ...prev, 
                      cvv: e.target.value.replace(/\D/g, '').slice(0, 4) 
                    }))}
                    maxLength={4}
                    required
                  />
                </div>
              </div>

              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  Your card information is encrypted and processed securely. We never store your full card number or CVV.
                </AlertDescription>
              </Alert>

              <div className="flex items-center gap-2">
                <Button type="submit">
                  <CreditCard className="h-4 w-4 mr-2" />
                  Add Payment Method
                </Button>
                <Button 
                  type="button" 
                  variant="outline"
                  onClick={() => setShowAddForm(false)}
                >
                  Cancel
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}
    </div>
  );
}