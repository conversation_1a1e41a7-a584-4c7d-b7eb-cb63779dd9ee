"use client";

import React, { useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { 
  Search, 
  HelpCircle, 
  FileText, 
  Video, 
  MessageSquare,
  Phone,
  Mail,
  Clock,
  ChevronRight,
  Star,
  ThumbsUp,
  ThumbsDown,
  BookOpen,
  Lightbulb,
  Settings,
  CreditCard,
  Package,
  Truck
} from "lucide-react";
import { useRouter } from "next/navigation";

interface HelpArticle {
  id: string;
  title: string;
  description: string;
  category: string;
  readTime: number;
  rating: number;
  helpful: number;
  type: "article" | "video" | "guide";
  popular?: boolean;
}

interface HelpCategory {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  articleCount: number;
  color: string;
}

const helpCategories: HelpCategory[] = [
  {
    id: "getting-started",
    name: "Getting Started",
    description: "Learn the basics of using WizeAssets",
    icon: Lightbulb,
    articleCount: 12,
    color: "bg-blue-100 text-blue-800",
  },
  {
    id: "asset-requests",
    name: "Asset Requests",
    description: "How to request and manage assets",
    icon: Package,
    articleCount: 18,
    color: "bg-green-100 text-green-800",
  },
  {
    id: "billing-payments",
    name: "Billing & Payments",
    description: "Manage your billing and payment methods",
    icon: CreditCard,
    articleCount: 8,
    color: "bg-purple-100 text-purple-800",
  },
  {
    id: "delivery-tracking",
    name: "Delivery & Tracking",
    description: "Track your asset deliveries",
    icon: Truck,
    articleCount: 6,
    color: "bg-orange-100 text-orange-800",
  },
  {
    id: "account-settings",
    name: "Account Settings",
    description: "Manage your account and preferences",
    icon: Settings,
    articleCount: 10,
    color: "bg-gray-100 text-gray-800",
  },
  {
    id: "troubleshooting",
    name: "Troubleshooting",
    description: "Common issues and solutions",
    icon: HelpCircle,
    articleCount: 15,
    color: "bg-red-100 text-red-800",
  },
];

const popularArticles: HelpArticle[] = [
  {
    id: "1",
    title: "How to Submit Your First Asset Request",
    description: "Step-by-step guide to requesting assets through the portal",
    category: "Asset Requests",
    readTime: 5,
    rating: 4.8,
    helpful: 156,
    type: "guide",
    popular: true,
  },
  {
    id: "2",
    title: "Understanding Your Invoice",
    description: "Learn how to read and understand your monthly invoices",
    category: "Billing & Payments",
    readTime: 3,
    rating: 4.6,
    helpful: 89,
    type: "article",
    popular: true,
  },
  {
    id: "3",
    title: "Tracking Your Asset Delivery",
    description: "How to track the status of your asset requests and deliveries",
    category: "Delivery & Tracking",
    readTime: 4,
    rating: 4.7,
    helpful: 124,
    type: "video",
    popular: true,
  },
  {
    id: "4",
    title: "Setting Up Payment Methods",
    description: "Add and manage your payment methods securely",
    category: "Billing & Payments",
    readTime: 6,
    rating: 4.5,
    helpful: 78,
    type: "guide",
  },
  {
    id: "5",
    title: "Managing Your Asset Inventory",
    description: "View and manage all your assigned assets",
    category: "Asset Requests",
    readTime: 7,
    rating: 4.4,
    helpful: 92,
    type: "article",
  },
  {
    id: "6",
    title: "Troubleshooting Login Issues",
    description: "Common solutions for login and access problems",
    category: "Troubleshooting",
    readTime: 4,
    rating: 4.3,
    helpful: 67,
    type: "article",
  },
];

export default function HelpCenterPage() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  const filteredArticles = popularArticles.filter(article => {
    const matchesSearch = searchTerm === "" || 
      article.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      article.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = selectedCategory === null || 
      article.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "video":
        return <Video className="h-4 w-4" />;
      case "guide":
        return <BookOpen className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const handleArticleVote = (articleId: string, helpful: boolean) => {
    // Mock implementation - would update article helpfulness
    console.log(`Article ${articleId} voted as ${helpful ? 'helpful' : 'not helpful'}`);
  };

  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold">Help Center</h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          Find answers to your questions and learn how to make the most of WizeAssets
        </p>
        
        {/* Search */}
        <div className="max-w-md mx-auto">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search for help articles..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 h-12"
            />
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => router.push("/client/support/chat")}>
          <CardContent className="p-6 text-center">
            <MessageSquare className="h-12 w-12 text-blue-600 mx-auto mb-4" />
            <h3 className="font-semibold mb-2">Live Chat</h3>
            <p className="text-sm text-muted-foreground">
              Get instant help from our support team
            </p>
            <Badge variant="outline" className="mt-2">
              Average response: 2 min
            </Badge>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => router.push("/client/support/tickets")}>
          <CardContent className="p-6 text-center">
            <Mail className="h-12 w-12 text-green-600 mx-auto mb-4" />
            <h3 className="font-semibold mb-2">Create Ticket</h3>
            <p className="text-sm text-muted-foreground">
              Submit a detailed support request
            </p>
            <Badge variant="outline" className="mt-2">
              Response within 24h
            </Badge>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-6 text-center">
            <Phone className="h-12 w-12 text-purple-600 mx-auto mb-4" />
            <h3 className="font-semibold mb-2">Schedule Call</h3>
            <p className="text-sm text-muted-foreground">
              Book a call with our specialists
            </p>
            <Badge variant="outline" className="mt-2">
              Available Mon-Fri
            </Badge>
          </CardContent>
        </Card>
      </div>

      {/* Categories */}
      <div>
        <h2 className="text-2xl font-bold mb-6">Browse by Category</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {helpCategories.map((category) => {
            const Icon = category.icon;
            return (
              <Card 
                key={category.id} 
                className="cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => setSelectedCategory(category.name)}
              >
                <CardContent className="p-6">
                  <div className="flex items-start gap-4">
                    <div className={`p-3 rounded-lg ${category.color}`}>
                      <Icon className="h-6 w-6" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold mb-2">{category.name}</h3>
                      <p className="text-sm text-muted-foreground mb-3">
                        {category.description}
                      </p>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">
                          {category.articleCount} articles
                        </span>
                        <ChevronRight className="h-4 w-4 text-muted-foreground" />
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>

      {/* Popular Articles */}
      <div>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold">
            {selectedCategory ? `${selectedCategory} Articles` : "Popular Articles"}
          </h2>
          {selectedCategory && (
            <Button variant="outline" onClick={() => setSelectedCategory(null)}>
              View All Categories
            </Button>
          )}
        </div>
        
        <div className="grid gap-4 md:grid-cols-2">
          {filteredArticles.map((article) => (
            <Card key={article.id} className="cursor-pointer hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <div className="p-2 bg-muted rounded-lg">
                    {getTypeIcon(article.type)}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-start justify-between mb-2">
                      <h3 className="font-semibold line-clamp-2">{article.title}</h3>
                      {article.popular && (
                        <Badge variant="secondary" className="ml-2">
                          <Star className="h-3 w-3 mr-1" />
                          Popular
                        </Badge>
                      )}
                    </div>
                    
                    <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                      {article.description}
                    </p>
                    
                    <div className="flex items-center justify-between text-sm text-muted-foreground">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {article.readTime} min read
                        </div>
                        <div className="flex items-center gap-1">
                          <Star className="h-3 w-3 fill-current text-yellow-500" />
                          {article.rating}
                        </div>
                      </div>
                      <Badge variant="outline">{article.category}</Badge>
                    </div>
                    
                    <div className="flex items-center justify-between mt-4 pt-4 border-t">
                      <span className="text-xs text-muted-foreground">
                        {article.helpful} people found this helpful
                      </span>
                      <div className="flex items-center gap-2">
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleArticleVote(article.id, true);
                          }}
                        >
                          <ThumbsUp className="h-3 w-3" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleArticleVote(article.id, false);
                          }}
                        >
                          <ThumbsDown className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
        
        {filteredArticles.length === 0 && (
          <Card>
            <CardContent className="p-8 text-center">
              <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Articles Found</h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm 
                  ? `No articles match "${searchTerm}". Try different keywords or browse categories.`
                  : "No articles found in this category."
                }
              </p>
              <div className="flex items-center justify-center gap-2">
                <Button variant="outline" onClick={() => setSearchTerm("")}>
                  Clear Search
                </Button>
                <Button onClick={() => router.push("/client/support/chat")}>
                  Ask Support
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Contact Support */}
      <Card>
        <CardContent className="p-8 text-center">
          <h3 className="text-xl font-semibold mb-4">Still Need Help?</h3>
          <p className="text-muted-foreground mb-6">
            Can't find what you're looking for? Our support team is here to help.
          </p>
          <div className="flex items-center justify-center gap-4">
            <Button onClick={() => router.push("/client/support/chat")}>
              <MessageSquare className="h-4 w-4 mr-2" />
              Start Live Chat
            </Button>
            <Button variant="outline" onClick={() => router.push("/client/support/tickets")}>
              <Mail className="h-4 w-4 mr-2" />
              Create Ticket
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}