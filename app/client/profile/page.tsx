"use client";

import React, { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  User, 
  Building, 
  Mail, 
  Phone, 
  Globe, 
  Shield, 
  Bell, 
  CreditCard,
  Save,
  Upload,
  CheckCircle,
  AlertCircle
} from "lucide-react";
import { toast } from "@/components/ui/use-toast";

interface UserProfile {
  id: string;
  name: string;
  email: string;
  phone?: string;
  jobTitle?: string;
  company?: string;
  avatarUrl?: string;
  status: string;
  emailVerified?: Date | null;
  clientProfile?: {
    companyName?: string;
    industry?: string;
    companySize?: string;
    website?: string;
    subscriptionTier: string;
    monthlySpend: number;
    autoApprovalLimit: number;
  };
  preferences?: {
    marketingEmails?: boolean;
    notifications?: {
      email?: boolean;
      requestUpdates?: boolean;
      billingAlerts?: boolean;
      maintenanceNotices?: boolean;
    };
    dashboard?: {
      theme?: string;
      language?: string;
      timezone?: string;
    };
  };
}

export default function ClientProfilePage() {
  const { data: session, update } = useSession();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [activeTab, setActiveTab] = useState("personal");

  useEffect(() => {
    loadProfile();
  }, []);

  const loadProfile = async () => {
    try {
      setIsLoading(true);
      
      // Mock data - replace with actual API call
      const mockProfile: UserProfile = {
        id: session?.user?.id || "1",
        name: session?.user?.name || "John Doe",
        email: session?.user?.email || "<EMAIL>",
        phone: "+****************",
        jobTitle: "IT Manager",
        company: "Acme Corporation",
        status: session?.user?.status || "active",
        emailVerified: new Date(),
        clientProfile: {
          companyName: "Acme Corporation",
          industry: "Technology",
          companySize: "51-200",
          website: "https://www.acme.com",
          subscriptionTier: "professional",
          monthlySpend: 2500,
          autoApprovalLimit: 1000,
        },
        preferences: {
          marketingEmails: true,
          notifications: {
            email: true,
            requestUpdates: true,
            billingAlerts: true,
            maintenanceNotices: false,
          },
          dashboard: {
            theme: "light",
            language: "en",
            timezone: "America/New_York",
          },
        },
      };

      setProfile(mockProfile);
    } catch (error) {
      console.error("Error loading profile:", error);
      toast({
        title: "Error",
        description: "Failed to load profile. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async (section: string) => {
    try {
      setIsSaving(true);
      
      // Mock API call - replace with actual endpoint
      console.log("Saving profile section:", section, profile);
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast({
        title: "Profile Updated",
        description: "Your profile has been saved successfully.",
      });

      // Update session if personal info changed
      if (section === "personal") {
        await update({
          name: profile?.name,
          email: profile?.email,
        });
      }
    } catch (error) {
      console.error("Error saving profile:", error);
      toast({
        title: "Error",
        description: "Failed to save profile. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleInputChange = (field: string, value: any, section?: string) => {
    setProfile(prev => {
      if (!prev) return null;
      
      if (section) {
        return {
          ...prev,
          [section]: {
            ...prev[section as keyof UserProfile],
            [field]: value,
          },
        };
      }
      
      return {
        ...prev,
        [field]: value,
      };
    });
  };

  const getSubscriptionBadge = (tier: string) => {
    const config = {
      basic: { label: "Basic", color: "bg-gray-100 text-gray-800" },
      professional: { label: "Professional", color: "bg-blue-100 text-blue-800" },
      enterprise: { label: "Enterprise", color: "bg-purple-100 text-purple-800" },
    };
    
    const { label, color } = config[tier as keyof typeof config] || config.basic;
    return <Badge className={color}>{label}</Badge>;
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-2">Loading profile...</span>
        </div>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="container mx-auto py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Failed to load profile. Please refresh the page.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Profile Settings</h1>
          <p className="text-muted-foreground">
            Manage your account information and preferences
          </p>
        </div>
        <div className="flex items-center gap-2">
          {profile.emailVerified ? (
            <Badge className="bg-green-100 text-green-800">
              <CheckCircle className="h-3 w-3 mr-1" />
              Verified
            </Badge>
          ) : (
            <Badge variant="destructive">
              <AlertCircle className="h-3 w-3 mr-1" />
              Unverified
            </Badge>
          )}
          {getSubscriptionBadge(profile.clientProfile?.subscriptionTier || "basic")}
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="personal">Personal</TabsTrigger>
          <TabsTrigger value="company">Company</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="preferences">Preferences</TabsTrigger>
        </TabsList>

        {/* Personal Information */}
        <TabsContent value="personal">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Personal Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center gap-6">
                <Avatar className="h-20 w-20">
                  <AvatarImage src={profile.avatarUrl} />
                  <AvatarFallback className="text-lg">
                    {profile.name.split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <Button variant="outline" size="sm">
                    <Upload className="h-4 w-4 mr-2" />
                    Upload Photo
                  </Button>
                  <p className="text-sm text-muted-foreground mt-2">
                    JPG, PNG or GIF. Max size 2MB.
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Full Name</Label>
                  <Input
                    id="name"
                    value={profile.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email Address</Label>
                  <Input
                    id="email"
                    type="email"
                    value={profile.email}
                    onChange={(e) => handleInputChange("email", e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone">Phone Number</Label>
                  <Input
                    id="phone"
                    value={profile.phone || ""}
                    onChange={(e) => handleInputChange("phone", e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="jobTitle">Job Title</Label>
                  <Input
                    id="jobTitle"
                    value={profile.jobTitle || ""}
                    onChange={(e) => handleInputChange("jobTitle", e.target.value)}
                  />
                </div>
              </div>

              <div className="flex justify-end">
                <Button onClick={() => handleSave("personal")} disabled={isSaving}>
                  <Save className="h-4 w-4 mr-2" />
                  {isSaving ? "Saving..." : "Save Changes"}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Company Information */}
        <TabsContent value="company">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5" />
                Company Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="companyName">Company Name</Label>
                  <Input
                    id="companyName"
                    value={profile.clientProfile?.companyName || ""}
                    onChange={(e) => handleInputChange("companyName", e.target.value, "clientProfile")}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="industry">Industry</Label>
                  <Select
                    value={profile.clientProfile?.industry || ""}
                    onValueChange={(value) => handleInputChange("industry", value, "clientProfile")}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select industry" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="technology">Technology</SelectItem>
                      <SelectItem value="healthcare">Healthcare</SelectItem>
                      <SelectItem value="finance">Finance</SelectItem>
                      <SelectItem value="manufacturing">Manufacturing</SelectItem>
                      <SelectItem value="retail">Retail</SelectItem>
                      <SelectItem value="education">Education</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="companySize">Company Size</Label>
                  <Select
                    value={profile.clientProfile?.companySize || ""}
                    onValueChange={(value) => handleInputChange("companySize", value, "clientProfile")}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select size" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1-10">1-10 employees</SelectItem>
                      <SelectItem value="11-50">11-50 employees</SelectItem>
                      <SelectItem value="51-200">51-200 employees</SelectItem>
                      <SelectItem value="201-500">201-500 employees</SelectItem>
                      <SelectItem value="500+">500+ employees</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="website">Website</Label>
                  <Input
                    id="website"
                    value={profile.clientProfile?.website || ""}
                    onChange={(e) => handleInputChange("website", e.target.value, "clientProfile")}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-muted rounded-lg">
                <div>
                  <Label className="text-sm font-medium">Subscription Tier</Label>
                  <p className="text-lg font-semibold">
                    {profile.clientProfile?.subscriptionTier || "Basic"}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Monthly Spend</Label>
                  <p className="text-lg font-semibold">
                    ${profile.clientProfile?.monthlySpend?.toLocaleString() || "0"}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Auto-Approval Limit</Label>
                  <p className="text-lg font-semibold">
                    ${profile.clientProfile?.autoApprovalLimit?.toLocaleString() || "0"}
                  </p>
                </div>
              </div>

              <div className="flex justify-end">
                <Button onClick={() => handleSave("company")} disabled={isSaving}>
                  <Save className="h-4 w-4 mr-2" />
                  {isSaving ? "Saving..." : "Save Changes"}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Security */}
        <TabsContent value="security">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Security Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h4 className="font-medium">Password</h4>
                    <p className="text-sm text-muted-foreground">
                      Last changed 30 days ago
                    </p>
                  </div>
                  <Button variant="outline">Change Password</Button>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h4 className="font-medium">Two-Factor Authentication</h4>
                    <p className="text-sm text-muted-foreground">
                      Add an extra layer of security to your account
                    </p>
                  </div>
                  <Button variant="outline">Enable 2FA</Button>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h4 className="font-medium">Email Verification</h4>
                    <p className="text-sm text-muted-foreground">
                      {profile.emailVerified ? "Email verified" : "Email not verified"}
                    </p>
                  </div>
                  {!profile.emailVerified && (
                    <Button variant="outline">Verify Email</Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Preferences */}
        <TabsContent value="preferences">
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bell className="h-5 w-5" />
                  Notification Preferences
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label>Email Notifications</Label>
                    <p className="text-sm text-muted-foreground">
                      Receive notifications via email
                    </p>
                  </div>
                  <Switch
                    checked={profile.preferences?.notifications?.email || false}
                    onCheckedChange={(checked) => 
                      handleInputChange("notifications", 
                        { ...profile.preferences?.notifications, email: checked }, 
                        "preferences"
                      )
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label>Request Updates</Label>
                    <p className="text-sm text-muted-foreground">
                      Get notified about asset request status changes
                    </p>
                  </div>
                  <Switch
                    checked={profile.preferences?.notifications?.requestUpdates || false}
                    onCheckedChange={(checked) => 
                      handleInputChange("notifications", 
                        { ...profile.preferences?.notifications, requestUpdates: checked }, 
                        "preferences"
                      )
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label>Billing Alerts</Label>
                    <p className="text-sm text-muted-foreground">
                      Receive alerts about invoices and payments
                    </p>
                  </div>
                  <Switch
                    checked={profile.preferences?.notifications?.billingAlerts || false}
                    onCheckedChange={(checked) => 
                      handleInputChange("notifications", 
                        { ...profile.preferences?.notifications, billingAlerts: checked }, 
                        "preferences"
                      )
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label>Marketing Emails</Label>
                    <p className="text-sm text-muted-foreground">
                      Receive product updates and promotional content
                    </p>
                  </div>
                  <Switch
                    checked={profile.preferences?.marketingEmails || false}
                    onCheckedChange={(checked) => 
                      handleInputChange("marketingEmails", checked, "preferences")
                    }
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Dashboard Preferences</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Theme</Label>
                    <Select
                      value={profile.preferences?.dashboard?.theme || "light"}
                      onValueChange={(value) => 
                        handleInputChange("dashboard", 
                          { ...profile.preferences?.dashboard, theme: value }, 
                          "preferences"
                        )
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="light">Light</SelectItem>
                        <SelectItem value="dark">Dark</SelectItem>
                        <SelectItem value="system">System</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Language</Label>
                    <Select
                      value={profile.preferences?.dashboard?.language || "en"}
                      onValueChange={(value) => 
                        handleInputChange("dashboard", 
                          { ...profile.preferences?.dashboard, language: value }, 
                          "preferences"
                        )
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="en">English</SelectItem>
                        <SelectItem value="es">Spanish</SelectItem>
                        <SelectItem value="fr">French</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="flex justify-end">
              <Button onClick={() => handleSave("preferences")} disabled={isSaving}>
                <Save className="h-4 w-4 mr-2" />
                {isSaving ? "Saving..." : "Save Preferences"}
              </Button>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}