@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --sidebar: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 221.2 83.2% 53.3%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 220 14.3% 95.9%;
    --sidebar-accent-foreground: 220.9 39.3% 11%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 221.2 83.2% 53.3%;
    --font-sans: Outfit, sans-serif;
    --font-serif: Montserrat, sans-serif;
    --font-mono: JetBrains Mono, monospace;
    --radius: 0.475rem;
    --shadow-2xs: 0px 1px 3px 0px hsl(0 0% 10.1961% / 0.05);
    --shadow-xs: 0px 1px 3px 0px hsl(0 0% 10.1961% / 0.05);
    --shadow-sm: 0px 1px 3px 0px hsl(0 0% 10.1961% / 0.1),
      0px 1px 2px -1px hsl(0 0% 10.1961% / 0.1);
    --shadow: 0px 1px 3px 0px hsl(0 0% 10.1961% / 0.1),
      0px 1px 2px -1px hsl(0 0% 10.1961% / 0.1);
    --shadow-md: 0px 1px 3px 0px hsl(0 0% 10.1961% / 0.1),
      0px 2px 4px -1px hsl(0 0% 10.1961% / 0.1);
    --shadow-lg: 0px 1px 3px 0px hsl(0 0% 10.1961% / 0.1),
      0px 4px 6px -1px hsl(0 0% 10.1961% / 0.1);
    --shadow-xl: 0px 1px 3px 0px hsl(0 0% 10.1961% / 0.1),
      0px 8px 10px -1px hsl(0 0% 10.1961% / 0.1);
    --shadow-2xl: 0px 1px 3px 0px hsl(0 0% 10.1961% / 0.25);
    --tracking-normal: -0.025em;
    --spacing: 0.23rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 217.2 91.2% 59.8%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar: 222.2 84% 4.9%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 217.2 91.2% 59.8%;
    --sidebar-primary-foreground: 222.2 84% 4.9%;
    --sidebar-accent: 217.2 32.6% 17.5%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217.2 32.6% 17.5%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    --font-sans: Outfit, sans-serif;
    --font-serif: Montserrat, sans-serif;
    --font-mono: JetBrains Mono, monospace;
    --radius: 0.475rem;
    --shadow-2xs: 0px 1px 3px 0px hsl(0 0% 10.1961% / 0.05);
    --shadow-xs: 0px 1px 3px 0px hsl(0 0% 10.1961% / 0.05);
    --shadow-sm: 0px 1px 3px 0px hsl(0 0% 10.1961% / 0.1),
      0px 1px 2px -1px hsl(0 0% 10.1961% / 0.1);
    --shadow: 0px 1px 3px 0px hsl(0 0% 10.1961% / 0.1),
      0px 1px 2px -1px hsl(0 0% 10.1961% / 0.1);
    --shadow-md: 0px 1px 3px 0px hsl(0 0% 10.1961% / 0.1),
      0px 2px 4px -1px hsl(0 0% 10.1961% / 0.1);
    --shadow-lg: 0px 1px 3px 0px hsl(0 0% 10.1961% / 0.1),
      0px 4px 6px -1px hsl(0 0% 10.1961% / 0.1);
    --shadow-xl: 0px 1px 3px 0px hsl(0 0% 10.1961% / 0.1),
      0px 8px 10px -1px hsl(0 0% 10.1961% / 0.1);
    --shadow-2xl: 0px 1px 3px 0px hsl(0 0% 10.1961% / 0.25);
  }

  body {
    letter-spacing: var(--tracking-normal);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-heading font-semibold;
  }
  /* Remove underlines from all elements */
  a,
  button,
  [role="button"],
  input,
  select,
  textarea {
    text-decoration: none !important;
  }
  /* Remove underline from focus states */
  *:focus {
    text-decoration: none !important;
  }
  /* Remove underline from hover states */
  *:hover {
    text-decoration: none !important;
  }
}
