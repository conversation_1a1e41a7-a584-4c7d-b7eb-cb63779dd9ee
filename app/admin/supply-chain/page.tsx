"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { 
  ShoppingCart, 
  Clock, 
  CheckCircle, 
  XCircle, 
  TrendingUp, 
  TrendingDown,
  Package,
  Truck,
  DollarSign,
  Calendar,
  Users,
  AlertTriangle,
  BarChart3
} from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";

interface SupplyChainMetrics {
  totalRequisitions: number;
  pendingRequisitions: number;
  approvedRequisitions: number;
  fulfilledRequisitions: number;
  rejectedRequisitions: number;
  fulfillmentRate: number;
  approvalRate: number;
  avgProcessingTime: number;
}

interface RecentRequisition {
  id: string;
  requestorName: string;
  assetType: {
    name: string;
    code: string;
  };
  quantity: number;
  status: string;
  priority: string;
  createdAt: string;
}

const statusConfig = {
  pending: { label: "Pending", color: "bg-yellow-100 text-yellow-800", icon: Clock },
  approved: { label: "Approved", color: "bg-blue-100 text-blue-800", icon: CheckCircle },
  rejected: { label: "Rejected", color: "bg-red-100 text-red-800", icon: XCircle },
  fulfilled: { label: "Fulfilled", color: "bg-green-100 text-green-800", icon: CheckCircle },
  cancelled: { label: "Cancelled", color: "bg-gray-100 text-gray-800", icon: XCircle },
  partially_fulfilled: { label: "Partially Fulfilled", color: "bg-orange-100 text-orange-800", icon: Package },
};

const priorityConfig = {
  low: { label: "Low", color: "bg-gray-100 text-gray-800" },
  normal: { label: "Normal", color: "bg-blue-100 text-blue-800" },
  high: { label: "High", color: "bg-orange-100 text-orange-800" },
  critical: { label: "Critical", color: "bg-red-100 text-red-800" },
};

export default function SupplyChainDashboard() {
  const router = useRouter();
  const session = useSession();
  const [metrics, setMetrics] = useState<SupplyChainMetrics | null>(null);
  const [recentRequisitions, setRecentRequisitions] = useState<RecentRequisition[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      
      const [metricsResponse, requisitionsResponse] = await Promise.all([
        fetch("/api/requisitions/dashboard"),
        fetch("/api/requisitions?limit=10")
      ]);

      if (metricsResponse.ok) {
        const metricsData = await metricsResponse.json();
        setMetrics(metricsData.metrics);
      }

      if (requisitionsResponse.ok) {
        const requisitionsData = await requisitionsResponse.json();
        setRecentRequisitions(requisitionsData.requisitions || []);
      }

    } catch (error) {
      console.error("Error loading dashboard data:", error);
      toast({
        title: "Error",
        description: "Failed to load dashboard data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;
    return (
      <Badge className={config.color}>
        <Icon className="h-3 w-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.normal;
    return (
      <Badge variant="outline" className={config.color}>
        {config.label}
      </Badge>
    );
  };

  if (!session?.data?.user || !["manager", "admin"].includes(session.data.user.role)) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <AlertTriangle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Access Restricted</h3>
              <p className="text-muted-foreground">
                You need manager or admin privileges to access the supply chain dashboard.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-2">Loading dashboard...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Supply Chain Dashboard</h1>
          <p className="text-muted-foreground">
            Monitor and manage asset requisitions and fulfillment
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button onClick={() => router.push("/requisitions")}>
            <ShoppingCart className="h-4 w-4 mr-2" />
            View All Requisitions
          </Button>
          <Button onClick={() => router.push("/requisitions/new")}>
            <Package className="h-4 w-4 mr-2" />
            New Requisition
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      {metrics && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Requisitions</CardTitle>
              <ShoppingCart className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.totalRequisitions}</div>
              <p className="text-xs text-muted-foreground">
                All time requests
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Approval</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.pendingRequisitions}</div>
              <p className="text-xs text-muted-foreground">
                Awaiting review
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Fulfillment Rate</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.fulfillmentRate}%</div>
              <Progress value={metrics.fulfillmentRate} className="mt-2" />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Processing Time</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.avgProcessingTime} days</div>
              <p className="text-xs text-muted-foreground">
                From request to fulfillment
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* Status Breakdown */}
        {metrics && (
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Requisition Status Breakdown
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-yellow-600" />
                    <span className="text-sm font-medium">Pending</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-mono">{metrics.pendingRequisitions}</span>
                    <div className="w-24 bg-muted rounded-full h-2">
                      <div 
                        className="bg-yellow-600 h-2 rounded-full" 
                        style={{ 
                          width: `${metrics.totalRequisitions > 0 ? (metrics.pendingRequisitions / metrics.totalRequisitions) * 100 : 0}%` 
                        }}
                      />
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium">Approved</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-mono">{metrics.approvedRequisitions}</span>
                    <div className="w-24 bg-muted rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full" 
                        style={{ 
                          width: `${metrics.totalRequisitions > 0 ? (metrics.approvedRequisitions / metrics.totalRequisitions) * 100 : 0}%` 
                        }}
                      />
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="text-sm font-medium">Fulfilled</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-mono">{metrics.fulfilledRequisitions}</span>
                    <div className="w-24 bg-muted rounded-full h-2">
                      <div 
                        className="bg-green-600 h-2 rounded-full" 
                        style={{ 
                          width: `${metrics.totalRequisitions > 0 ? (metrics.fulfilledRequisitions / metrics.totalRequisitions) * 100 : 0}%` 
                        }}
                      />
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <XCircle className="h-4 w-4 text-red-600" />
                    <span className="text-sm font-medium">Rejected</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-mono">{metrics.rejectedRequisitions}</span>
                    <div className="w-24 bg-muted rounded-full h-2">
                      <div 
                        className="bg-red-600 h-2 rounded-full" 
                        style={{ 
                          width: `${metrics.totalRequisitions > 0 ? (metrics.rejectedRequisitions / metrics.totalRequisitions) * 100 : 0}%` 
                        }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Truck className="h-5 w-5" />
              Quick Actions
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button 
              variant="outline" 
              className="w-full justify-start"
              onClick={() => router.push("/requisitions?status=pending")}
            >
              <Clock className="h-4 w-4 mr-2" />
              Review Pending ({metrics?.pendingRequisitions || 0})
            </Button>
            
            <Button 
              variant="outline" 
              className="w-full justify-start"
              onClick={() => router.push("/requisitions?status=approved")}
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              Process Approved ({metrics?.approvedRequisitions || 0})
            </Button>
            
            <Button 
              variant="outline" 
              className="w-full justify-start"
              onClick={() => router.push("/purchase-orders")}
            >
              <DollarSign className="h-4 w-4 mr-2" />
              Manage Purchase Orders
            </Button>
            
            <Button 
              variant="outline" 
              className="w-full justify-start"
              onClick={() => router.push("/assets")}
            >
              <Package className="h-4 w-4 mr-2" />
              View Asset Inventory
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Recent Requisitions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ShoppingCart className="h-5 w-5" />
            Recent Requisitions
          </CardTitle>
        </CardHeader>
        <CardContent>
          {recentRequisitions.length > 0 ? (
            <div className="space-y-4">
              {recentRequisitions.map((requisition) => (
                <div 
                  key={requisition.id} 
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 cursor-pointer"
                  onClick={() => router.push(`/requisitions/${requisition.id}`)}
                >
                  <div className="flex items-center gap-4">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Package className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <p className="font-medium">{requisition.assetType.name}</p>
                        <span className="text-sm text-muted-foreground">
                          x{requisition.quantity}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Requested by {requisition.requestorName}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {new Date(requisition.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {getPriorityBadge(requisition.priority)}
                    {getStatusBadge(requisition.status)}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <ShoppingCart className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Recent Requisitions</h3>
              <p className="text-muted-foreground mb-4">
                There are no recent asset requisitions to display.
              </p>
              <Button onClick={() => router.push("/requisitions/new")}>
                <Package className="h-4 w-4 mr-2" />
                Create First Requisition
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}