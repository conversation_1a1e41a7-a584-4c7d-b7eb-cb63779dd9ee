"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import {
  TrendingUp,
  TrendingDown,
  Package,
  Wrench,
  CheckCircle,
  Clock,
  DollarSign,
  Users,
  BarChart3,
  Brain,
  Zap,
  RefreshCw,
  AlertTriangle,
} from "lucide-react"
import { useAdminHeader } from "@/hooks/use-admin-header"
import { getDashboardHeaderConfig } from "@/lib/utils/admin-header-configs"
import { useDashboardData } from "@/hooks/use-dashboard-data"
import { toast } from "@/components/ui/use-toast"

export default function Dashboard() {
  // Set up the header for this page
  useAdminHeader(getDashboardHeaderConfig);

  // Fetch real dashboard data
  const { dashboardData, loading, error, refreshData } = useDashboardData();

  // Show error toast if there's an error
  if (error) {
    toast({
      title: "Error",
      description: error,
      variant: "destructive",
    });
  }

  // Format numbers for display
  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  const formatCurrency = (num: number): string => {
    if (num >= 1000000) {
      return `$${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `$${(num / 1000).toFixed(1)}K`;
    }
    return `$${num.toFixed(0)}`;
  };

  // Create metrics array from real data
  const metrics = dashboardData ? [
    {
      title: "Total Assets",
      value: formatNumber(dashboardData.metrics.totalAssets),
      change: `${dashboardData.trends.assetsGrowth > 0 ? '+' : ''}${dashboardData.trends.assetsGrowth.toFixed(1)}%`,
      trend: dashboardData.trends.assetsGrowth > 0 ? "up" : "down",
      icon: Package,
      description: "Active assets in system",
    },
    {
      title: "Maintenance Tasks",
      value: formatNumber(dashboardData.metrics.pendingMaintenanceTasks),
      change: `${dashboardData.trends.maintenanceReduction > 0 ? '+' : ''}${dashboardData.trends.maintenanceReduction.toFixed(1)}%`,
      trend: dashboardData.trends.maintenanceReduction < 0 ? "up" : "down", // Negative is good for maintenance
      icon: Wrench,
      description: "Pending maintenance",
    },
    {
      title: "System Health",
      value: `${dashboardData.metrics.systemHealth.toFixed(1)}%`,
      change: `${dashboardData.trends.systemHealthImprovement > 0 ? '+' : ''}${dashboardData.trends.systemHealthImprovement.toFixed(1)}%`,
      trend: dashboardData.trends.systemHealthImprovement > 0 ? "up" : "down",
      icon: CheckCircle,
      description: "Overall system status",
    },
    {
      title: "Cost Savings",
      value: formatCurrency(dashboardData.metrics.costSavings),
      change: `${dashboardData.trends.costSavingsIncrease > 0 ? '+' : ''}${dashboardData.trends.costSavingsIncrease.toFixed(1)}%`,
      trend: dashboardData.trends.costSavingsIncrease > 0 ? "up" : "down",
      icon: DollarSign,
      description: "Monthly savings",
    },
  ] : [];

  const recentActivities = dashboardData?.recentActivities || [];

  return (
    <div className="space-y-6">
      {/* Header with refresh button */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-muted-foreground">
            Overview of your asset management system
          </p>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={refreshData}
          disabled={loading}
          className="flex items-center gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Metrics Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {loading ? (
          // Loading skeletons
          Array.from({ length: 4 }).map((_, index) => (
            <Card key={index} className="relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10" />
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-4" />
              </CardHeader>
              <CardContent className="relative">
                <Skeleton className="h-8 w-16 mb-2" />
                <Skeleton className="h-3 w-20 mb-1" />
                <Skeleton className="h-3 w-32" />
              </CardContent>
            </Card>
          ))
        ) : error ? (
          // Error state
          <Card className="col-span-full">
            <CardContent className="flex items-center justify-center py-8">
              <div className="text-center">
                <AlertTriangle className="h-8 w-8 text-destructive mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">Failed to load dashboard data</p>
                <Button variant="outline" size="sm" onClick={refreshData} className="mt-2">
                  Try Again
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          // Real metrics
          metrics.map((metric, index) => (
            <Card key={index} className="relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10" />
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative">
                <CardTitle className="text-sm font-medium">{metric.title}</CardTitle>
                <metric.icon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent className="relative">
                <div className="text-2xl font-bold">{metric.value}</div>
                <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                  <span className={`flex items-center ${metric.trend === "up" ? "text-green-600" : "text-red-600"}`}>
                    {metric.trend === "up" ? (
                      <TrendingUp className="h-3 w-3 mr-1" />
                    ) : (
                      <TrendingDown className="h-3 w-3 mr-1" />
                    )}
                    {metric.change}
                  </span>
                  <span>from last month</span>
                </div>
                <p className="text-xs text-muted-foreground mt-1">{metric.description}</p>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Main Content Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* System Status */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              System Status
            </CardTitle>
            <CardDescription>Real-time monitoring of critical systems</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">HVAC Systems</span>
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  Operational
                </Badge>
              </div>
              <Progress value={94} className="h-2" />
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Security Systems</span>
                <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                  Maintenance
                </Badge>
              </div>
              <Progress value={78} className="h-2" />
            </div>
            {loading ? (
              // Loading skeletons
              Array.from({ length: 2 }).map((_, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-6 w-20" />
                  </div>
                  <Skeleton className="h-2 w-full" />
                </div>
              ))
            ) : dashboardData ? (
              <>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">System Health</span>
                    <Badge variant="secondary" className={
                      dashboardData.metrics.systemHealth > 90
                        ? "bg-green-100 text-green-800"
                        : dashboardData.metrics.systemHealth > 70
                          ? "bg-yellow-100 text-yellow-800"
                          : "bg-red-100 text-red-800"
                    }>
                      {dashboardData.metrics.systemHealth > 90 ? 'Excellent' :
                       dashboardData.metrics.systemHealth > 70 ? 'Good' : 'Needs Attention'}
                    </Badge>
                  </div>
                  <Progress value={dashboardData.metrics.systemHealth} className="h-2" />
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Maintenance Compliance</span>
                    <Badge variant="secondary" className={
                      dashboardData.metrics.overdueTasks === 0
                        ? "bg-green-100 text-green-800"
                        : dashboardData.metrics.overdueTasks < 5
                          ? "bg-yellow-100 text-yellow-800"
                          : "bg-red-100 text-red-800"
                    }>
                      {dashboardData.metrics.overdueTasks === 0 ? 'On Track' :
                       dashboardData.metrics.overdueTasks < 5 ? 'Minor Issues' : 'Attention Required'}
                    </Badge>
                  </div>
                  <Progress
                    value={dashboardData.metrics.pendingMaintenanceTasks > 0
                      ? Math.max(0, 100 - (dashboardData.metrics.overdueTasks / dashboardData.metrics.pendingMaintenanceTasks) * 100)
                      : 100
                    }
                    className="h-2"
                  />
                </div>
              </>
            ) : (
              <div className="text-center py-4">
                <AlertTriangle className="h-6 w-6 text-muted-foreground mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">Unable to load system data</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-blue-500" />
              Quick Actions
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button variant="outline" className="w-full justify-start">
              <Package className="h-4 w-4 mr-2" />
              Add New Asset
            </Button>
            <Button variant="outline" className="w-full justify-start">
              <Wrench className="h-4 w-4 mr-2" />
              Schedule Maintenance
            </Button>
            <Button variant="outline" className="w-full justify-start">
              <Users className="h-4 w-4 mr-2" />
              Assign Technician
            </Button>
            <Button variant="outline" className="w-full justify-start">
              <BarChart3 className="h-4 w-4 mr-2" />
              Generate Report
            </Button>
            <Button variant="outline" className="w-full justify-start" onClick={() => window.location.href = '/admin/supply-chain'}>
              <Package className="h-4 w-4 mr-2" />
              Supply Chain Dashboard
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activities */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5 text-orange-500" />
            Recent Activities
          </CardTitle>
          <CardDescription>Latest updates and system events</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {loading ? (
              // Loading skeletons for activities
              Array.from({ length: 4 }).map((_, index) => (
                <div key={index} className="flex items-center space-x-4 p-3 rounded-lg border">
                  <Skeleton className="h-2 w-2 rounded-full" />
                  <div className="flex-1 space-y-2">
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-3 w-1/2" />
                  </div>
                  <Skeleton className="h-6 w-16" />
                </div>
              ))
            ) : recentActivities.length === 0 ? (
              // Empty state
              <div className="text-center py-8">
                <Clock className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">No recent activities</p>
              </div>
            ) : (
              // Real activities
              recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-center space-x-4 p-3 rounded-lg border">
                  <div
                    className={`h-2 w-2 rounded-full ${
                      activity.status === "critical"
                        ? "bg-red-500"
                        : activity.status === "completed"
                          ? "bg-green-500"
                          : activity.status === "pending"
                            ? "bg-yellow-500"
                            : "bg-blue-500"
                    }`}
                  />
                  <div className="flex-1 space-y-1">
                    <p className="text-sm font-medium leading-none">{activity.message}</p>
                    <p className="text-xs text-muted-foreground">{activity.time}</p>
                  </div>
                  <Badge
                    variant={
                      activity.status === "critical"
                        ? "destructive"
                        : activity.status === "completed"
                          ? "secondary"
                          : "outline"
                    }
                  >
                    {activity.status}
                  </Badge>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
