"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { AssetOperationFormRenderer } from "@/components/form-builder/asset-operation-form-renderer";
import { FormContext } from "@/lib/types/asset-type-forms";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ArrowLeft, Trash2, Package, DollarSign, Calendar, AlertTriangle } from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { getStatusBadge } from "@/lib/utils/asset-status";

interface Asset {
  id: string;
  name: string;
  code: string;
  status: string;
  purchasePrice: number;
  purchaseDate: string;
  currentValue?: number;
  assetType: {
    id: string;
    name: string;
    code: string;
  };
}

export default function AssetDisposalPage() {
  const router = useRouter();
  const params = useParams();
  const assetId = params.id as string;

  const [asset, setAsset] = useState<Asset | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (assetId) {
      loadAsset();
    }
  }, [assetId]);

  const loadAsset = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/assets/${assetId}`);
      if (response.ok) {
        const data = await response.json();
        setAsset(data.asset);
      } else {
        throw new Error("Failed to load asset");
      }
    } catch (error) {
      console.error("Error loading asset:", error);
      toast({
        title: "Error",
        description: "Failed to load asset. Please try again.",
        variant: "destructive",
      });
      router.back();
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (formData: Record<string, any>) => {
    try {
      setIsSubmitting(true);

      const response = await fetch("/api/asset-operations", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          operationType: "asset.disposal",
          assetTypeId: asset?.assetType.id,
          data: {
            ...formData,
            assetId: assetId,
            originalPurchasePrice: asset?.purchasePrice,
            originalPurchaseDate: asset?.purchaseDate,
            currentValue: asset?.currentValue,
          },
          context: {
            assetId: assetId,
            assetTypeId: asset?.assetType.id,
            operationType: "asset.disposal",
            userId: "current-user", // This should come from auth context
            userRole: "admin", // This should come from auth context
          },
        }),
      });

      if (response.ok) {
        const result = await response.json();
        toast({
          title: "Success",
          description: "Asset disposal request submitted successfully!",
        });
        
        // Redirect back to asset detail page or assets list
        router.push(`/assets/${assetId}`);
      } else {
        const error = await response.json();
        throw new Error(error.message || "Failed to submit disposal request");
      }
    } catch (error) {
      console.error("Error submitting disposal request:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to submit disposal request. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    router.back();
  };



  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-2">Loading asset...</span>
        </div>
      </div>
    );
  }

  if (!asset) {
    return (
      <div className="container mx-auto py-8">
        <Alert variant="destructive">
          <AlertDescription>
            Asset not found. Please check the asset ID and try again.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const formContext: FormContext = {
    assetId: assetId,
    assetTypeId: asset.assetType.id,
    operationType: "asset.disposal",
    userId: "current-user", // This should come from auth context
    userRole: "admin", // This should come from auth context
  };

  return (
    <div className="container mx-auto py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-red-600">Dispose Asset</h1>
            <p className="text-muted-foreground">
              Submit a disposal request for this asset
            </p>
          </div>
        </div>
      </div>

      {/* Warning Alert */}
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          <strong>Warning:</strong> Asset disposal is a permanent action. Once disposed, 
          the asset will be marked as disposed and removed from active inventory. 
          Please ensure all information is accurate before proceeding.
        </AlertDescription>
      </Alert>

      {/* Asset Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Asset Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">Asset Name</p>
              <p className="font-medium">{asset.name}</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">Asset Code</p>
              <p className="font-mono text-sm">{asset.code}</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">Asset Type</p>
              <p>{asset.assetType.name}</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">Status</p>
              {getStatusBadge(asset.status)}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Financial Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Financial Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <DollarSign className="h-4 w-4 text-blue-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Purchase Price</p>
                <p className="font-medium">${asset.purchasePrice?.toLocaleString() || "N/A"}</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Calendar className="h-4 w-4 text-green-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Purchase Date</p>
                <p className="font-medium">
                  {asset.purchaseDate ? new Date(asset.purchaseDate).toLocaleDateString() : "N/A"}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <DollarSign className="h-4 w-4 text-purple-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Current Value</p>
                <p className="font-medium">${asset.currentValue?.toLocaleString() || "N/A"}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Disposal Form */}
      <AssetOperationFormRenderer
        assetTypeId={asset.assetType.id}
        operationType="asset.disposal"
        context={formContext}
        onSubmit={handleSubmit}
        onCancel={handleCancel}
      />
    </div>
  );
}