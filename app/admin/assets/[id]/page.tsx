"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  ArrowLeft, 
  Package, 
  Edit, 
  ArrowRightLeft, 
  Trash2, 
  Wrench, 
  ClipboardCheck,
  Calendar,
  DollarSign,
  MapPin,
  User,
  FileText,
  History,
  Settings,
  TrendingDown
} from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { getStatusBadge } from "@/lib/utils/asset-status";
import { getOperationTypeBadge } from "@/lib/utils/operation-types";

interface Asset {
  id: string;
  name: string;
  code: string;
  status: string;
  location: string;
  department: string;
  assignedTo?: string;
  purchasePrice: number;
  purchaseDate: string;
  currentValue?: number;
  description?: string;
  serialNumber?: string;
  assetType: {
    id: string;
    name: string;
    code: string;
    category: {
      name: string;
    };
  };
  customFields?: Record<string, any>;
}

interface OperationHistory {
  id: string;
  operationType: string;
  performedBy: string;
  performedAt: string;
  status: string;
  notes?: string;
}

export default function AssetDetailPage() {
  const router = useRouter();
  const params = useParams();
  const assetId = params.id as string;

  const [asset, setAsset] = useState<Asset | null>(null);
  const [operationHistory, setOperationHistory] = useState<OperationHistory[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("overview");

  useEffect(() => {
    if (assetId) {
      loadAsset();
      loadOperationHistory();
    }
  }, [assetId]);

  const loadAsset = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/assets/${assetId}`);
      if (response.ok) {
        const data = await response.json();
        setAsset(data.asset);
      } else {
        throw new Error("Failed to load asset");
      }
    } catch (error) {
      console.error("Error loading asset:", error);
      toast({
        title: "Error",
        description: "Failed to load asset. Please try again.",
        variant: "destructive",
      });
      router.back();
    } finally {
      setIsLoading(false);
    }
  };

  const loadOperationHistory = async () => {
    try {
      const response = await fetch(`/api/asset-operations?assetId=${assetId}`);
      if (response.ok) {
        const data = await response.json();
        setOperationHistory(data.operations || []);
      }
    } catch (error) {
      console.error("Error loading operation history:", error);
    }
  };





  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-2">Loading asset...</span>
        </div>
      </div>
    );
  }

  if (!asset) {
    return (
      <div className="container mx-auto py-8">
        <Alert variant="destructive">
          <AlertDescription>
            Asset not found. Please check the asset ID and try again.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold">{asset.name}</h1>
            <p className="text-muted-foreground">
              {asset.assetType.name} • {asset.code}
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          {getStatusBadge(asset.status)}
          <Badge variant="outline">{asset.assetType.category.name}</Badge>
        </div>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Asset Operations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => router.push(`/assets/${assetId}/update`)}
              disabled={asset.status === "disposed"}
            >
              <Edit className="h-4 w-4 mr-2" />
              Update Asset
            </Button>
            
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => router.push(`/assets/${assetId}/transfer`)}
              disabled={asset.status === "disposed"}
            >
              <ArrowRightLeft className="h-4 w-4 mr-2" />
              Transfer
            </Button>
            
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => router.push(`/assets/${assetId}/maintenance/log`)}
              disabled={asset.status === "disposed"}
            >
              <Wrench className="h-4 w-4 mr-2" />
              Log Maintenance
            </Button>
            
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => router.push(`/assets/${assetId}/maintenance/schedule`)}
              disabled={asset.status === "disposed"}
            >
              <Calendar className="h-4 w-4 mr-2" />
              Schedule Maintenance
            </Button>
            
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => router.push(`/inventory/check?assetId=${assetId}`)}
              disabled={asset.status === "disposed"}
            >
              <ClipboardCheck className="h-4 w-4 mr-2" />
              Audit
            </Button>
            
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => router.push(`/assets/${assetId}/dispose`)}
              disabled={asset.status === "disposed"}
              className="text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Dispose
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Asset Details Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="details">Details</TabsTrigger>
          <TabsTrigger value="operations">Operations</TabsTrigger>
          <TabsTrigger value="financial">Financial</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package className="h-5 w-5" />
                  Basic Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Asset Name</p>
                    <p className="font-medium">{asset.name}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Asset Code</p>
                    <p className="font-mono text-sm">{asset.code}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Asset Type</p>
                    <p>{asset.assetType.name}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Category</p>
                    <p>{asset.assetType.category.name}</p>
                  </div>
                  {asset.serialNumber && (
                    <div className="col-span-2">
                      <p className="text-sm font-medium text-muted-foreground">Serial Number</p>
                      <p className="font-mono text-sm">{asset.serialNumber}</p>
                    </div>
                  )}
                </div>
                {asset.description && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Description</p>
                    <p className="text-sm">{asset.description}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  Location & Assignment
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <MapPin className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Location</p>
                      <p className="font-medium">{asset.location || "Not assigned"}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <Package className="h-4 w-4 text-green-600" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Department</p>
                      <p className="font-medium">{asset.department || "Not assigned"}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-purple-100 rounded-lg">
                      <User className="h-4 w-4 text-purple-600" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Assigned To</p>
                      <p className="font-medium">{asset.assignedTo || "Not assigned"}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Custom Fields */}
          {asset.customFields && Object.keys(asset.customFields).length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Custom Fields
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {Object.entries(asset.customFields).map(([key, value]) => (
                    <div key={key}>
                      <p className="text-sm font-medium text-muted-foreground capitalize">
                        {key.replace(/([A-Z])/g, ' $1').trim()}
                      </p>
                      <p className="font-medium">{String(value)}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="details" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Detailed Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Purchase Date</p>
                    <p className="font-medium">
                      {asset.purchaseDate ? new Date(asset.purchaseDate).toLocaleDateString() : "N/A"}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Purchase Price</p>
                    <p className="font-medium">${asset.purchasePrice?.toLocaleString() || "N/A"}</p>
                  </div>
                  {asset.currentValue && (
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Current Value</p>
                      <p className="font-medium">${asset.currentValue.toLocaleString()}</p>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="operations" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <History className="h-5 w-5" />
                Operation History
              </CardTitle>
            </CardHeader>
            <CardContent>
              {operationHistory.length > 0 ? (
                <div className="space-y-4">
                  {operationHistory.map((operation) => (
                    <div key={operation.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="p-2 bg-gray-100 rounded-lg">
                          <History className="h-4 w-4 text-gray-600" />
                        </div>
                        <div>
                          <div className="flex items-center gap-2 mb-1">
                            {getOperationTypeBadge(operation.operationType)}
                            <span className="text-sm text-muted-foreground">
                              {new Date(operation.performedAt).toLocaleDateString()}
                            </span>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            Performed by: {operation.performedBy}
                          </p>
                          {operation.notes && (
                            <p className="text-sm mt-1">{operation.notes}</p>
                          )}
                        </div>
                      </div>
                      <Badge variant={operation.status === "completed" ? "default" : "secondary"}>
                        {operation.status}
                      </Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <History className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">No Operations Yet</h3>
                  <p className="text-muted-foreground">
                    No operations have been performed on this asset.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="financial" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5" />
                  Financial Summary
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Purchase Price</span>
                    <span className="font-medium">${asset.purchasePrice?.toLocaleString() || "N/A"}</span>
                  </div>
                  {asset.currentValue && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Current Value</span>
                      <span className="font-medium">${asset.currentValue.toLocaleString()}</span>
                    </div>
                  )}
                  {asset.currentValue && asset.purchasePrice && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Depreciation</span>
                      <span className="font-medium text-red-600">
                        ${(asset.purchasePrice - asset.currentValue).toLocaleString()}
                      </span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingDown className="h-5 w-5" />
                  Depreciation
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-4">
                  <p className="text-muted-foreground">
                    Depreciation calculation will be available once configured for this asset type.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}