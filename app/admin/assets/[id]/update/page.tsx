"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { AssetOperationFormRenderer } from "@/components/form-builder/asset-operation-form-renderer";
import { FormContext } from "@/lib/types/asset-type-forms";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ArrowLeft, Edit, Package, Calendar, DollarSign, MapPin, User, Info } from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { getStatusBadge } from "@/lib/utils/asset-status";

interface Asset {
  id: string;
  name: string;
  code: string;
  status: string;
  location: string;
  department: string;
  assignedTo?: string;
  purchasePrice: number;
  purchaseDate: string;
  currentValue?: number;
  description?: string;
  serialNumber?: string;
  assetType: {
    id: string;
    name: string;
    code: string;
    category: {
      name: string;
    };
  };
  customFields?: Record<string, any>;
}

export default function AssetUpdatePage() {
  const router = useRouter();
  const params = useParams();
  const assetId = params.id as string;

  const [asset, setAsset] = useState<Asset | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (assetId) {
      loadAsset();
    }
  }, [assetId]);

  const loadAsset = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/assets/${assetId}`);
      if (response.ok) {
        const data = await response.json();
        setAsset(data.asset);
      } else {
        throw new Error("Failed to load asset");
      }
    } catch (error) {
      console.error("Error loading asset:", error);
      toast({
        title: "Error",
        description: "Failed to load asset. Please try again.",
        variant: "destructive",
      });
      router.back();
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (formData: Record<string, any>) => {
    try {
      setIsSubmitting(true);

      const response = await fetch("/api/asset-operations", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          operationType: "asset.update",
          assetTypeId: asset?.assetType.id,
          data: {
            ...formData,
            assetId: assetId,
            // Include current values for comparison
            currentName: asset?.name,
            currentLocation: asset?.location,
            currentDepartment: asset?.department,
            currentAssignedTo: asset?.assignedTo,
            currentStatus: asset?.status,
          },
          context: {
            assetId: assetId,
            assetTypeId: asset?.assetType.id,
            operationType: "asset.update",
            userId: "current-user", // This should come from auth context
            userRole: "admin", // This should come from auth context
          },
        }),
      });

      if (response.ok) {
        const result = await response.json();
        toast({
          title: "Success",
          description: "Asset updated successfully!",
        });
        
        // Redirect back to asset detail page
        router.push(`/assets/${assetId}`);
      } else {
        const error = await response.json();
        throw new Error(error.message || "Failed to update asset");
      }
    } catch (error) {
      console.error("Error updating asset:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update asset. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSaveDraft = async (formData: Record<string, any>) => {
    try {
      // Save as draft logic
      localStorage.setItem(`asset-update-draft-${assetId}`, JSON.stringify(formData));
      toast({
        title: "Draft Saved",
        description: "Your changes have been saved as a draft.",
      });
    } catch (error) {
      console.error("Error saving draft:", error);
      toast({
        title: "Error",
        description: "Failed to save draft.",
        variant: "destructive",
      });
    }
  };

  const handleCancel = () => {
    router.back();
  };



  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-2">Loading asset...</span>
        </div>
      </div>
    );
  }

  if (!asset) {
    return (
      <div className="container mx-auto py-8">
        <Alert variant="destructive">
          <AlertDescription>
            Asset not found. Please check the asset ID and try again.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (asset.status === "disposed") {
    return (
      <div className="container mx-auto py-8">
        <Alert variant="destructive">
          <AlertDescription>
            This asset has been disposed and cannot be updated.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const formContext: FormContext = {
    assetId: assetId,
    assetTypeId: asset.assetType.id,
    operationType: "asset.update",
    userId: "current-user", // This should come from auth context
    userRole: "admin", // This should come from auth context
  };

  // Prepare initial form data from current asset values
  const initialFormData = {
    name: asset.name,
    description: asset.description,
    location: asset.location,
    department: asset.department,
    assignedTo: asset.assignedTo,
    status: asset.status,
    serialNumber: asset.serialNumber,
    purchasePrice: asset.purchasePrice,
    purchaseDate: asset.purchaseDate,
    currentValue: asset.currentValue,
    ...asset.customFields,
  };

  return (
    <div className="container mx-auto py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Update Asset</h1>
            <p className="text-muted-foreground">
              Modify asset information and properties
            </p>
          </div>
        </div>
      </div>

      {/* Asset Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Current Asset Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">Asset Name</p>
              <p className="font-medium">{asset.name}</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">Asset Code</p>
              <p className="font-mono text-sm">{asset.code}</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">Asset Type</p>
              <p>{asset.assetType.name}</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">Status</p>
              {getStatusBadge(asset.status)}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Current Assignment */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            Current Assignment
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <MapPin className="h-4 w-4 text-blue-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Location</p>
                <p className="font-medium">{asset.location || "Not assigned"}</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Package className="h-4 w-4 text-green-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Department</p>
                <p className="font-medium">{asset.department || "Not assigned"}</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <User className="h-4 w-4 text-purple-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Assigned To</p>
                <p className="font-medium">{asset.assignedTo || "Not assigned"}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Update Notice */}
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          <strong>Note:</strong> Updating asset information will create an audit trail entry. 
          Changes to critical fields may trigger automated workflows and notifications.
        </AlertDescription>
      </Alert>

      {/* Update Form */}
      <AssetOperationFormRenderer
        assetTypeId={asset.assetType.id}
        operationType="asset.update"
        context={formContext}
        initialData={initialFormData}
        onSubmit={handleSubmit}
        onSaveDraft={handleSaveDraft}
        onCancel={handleCancel}
      />
    </div>
  );
}