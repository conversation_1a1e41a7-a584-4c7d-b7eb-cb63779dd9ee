"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { AssetOperationFormRenderer } from "@/components/form-builder/asset-operation-form-renderer";
import { FormContext } from "@/lib/types/asset-type-forms";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ArrowLeft, Calendar, Package, Clock, User, AlertTriangle, CheckCircle } from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { getStatusBadge } from "@/lib/utils/asset-status";
import { getMaintenanceTypeBadge } from "@/lib/utils/maintenance-types";

interface Asset {
  id: string;
  name: string;
  code: string;
  status: string;
  location: string;
  assetType: {
    id: string;
    name: string;
    code: string;
  };
}

interface MaintenanceSchedule {
  id: string;
  name: string;
  type: string;
  frequency: string;
  intervalDays: number;
  description?: string;
  isActive: boolean;
}

export default function MaintenanceSchedulePage() {
  const router = useRouter();
  const params = useParams();
  const assetId = params.id as string;

  const [asset, setAsset] = useState<Asset | null>(null);
  const [schedules, setSchedules] = useState<MaintenanceSchedule[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (assetId) {
      loadAsset();
      loadMaintenanceSchedules();
    }
  }, [assetId]);

  const loadAsset = async () => {
    try {
      const response = await fetch(`/api/assets/${assetId}`);
      if (response.ok) {
        const data = await response.json();
        setAsset(data.asset);
      } else {
        throw new Error("Failed to load asset");
      }
    } catch (error) {
      console.error("Error loading asset:", error);
      toast({
        title: "Error",
        description: "Failed to load asset. Please try again.",
        variant: "destructive",
      });
      router.back();
    }
  };

  const loadMaintenanceSchedules = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/maintenance/schedules?assetId=${assetId}`);
      if (response.ok) {
        const data = await response.json();
        setSchedules(data.schedules || []);
      }
    } catch (error) {
      console.error("Error loading maintenance schedules:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (formData: Record<string, any>) => {
    try {
      setIsSubmitting(true);

      const response = await fetch("/api/asset-operations", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          operationType: "maintenance.schedule",
          assetTypeId: asset?.assetType.id,
          data: {
            ...formData,
            assetId: assetId,
            assetName: asset?.name,
            assetCode: asset?.code,
            location: asset?.location,
          },
          context: {
            assetId: assetId,
            assetTypeId: asset?.assetType.id,
            operationType: "maintenance.schedule",
            userId: "current-user", // This should come from auth context
            userRole: "admin", // This should come from auth context
          },
        }),
      });

      if (response.ok) {
        const result = await response.json();
        toast({
          title: "Success",
          description: "Maintenance scheduled successfully!",
        });
        
        // Refresh schedules and redirect
        await loadMaintenanceSchedules();
        router.push(`/assets/${assetId}`);
      } else {
        const error = await response.json();
        throw new Error(error.message || "Failed to schedule maintenance");
      }
    } catch (error) {
      console.error("Error scheduling maintenance:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to schedule maintenance. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSaveDraft = async (formData: Record<string, any>) => {
    try {
      // Save as draft logic
      localStorage.setItem(`maintenance-schedule-draft-${assetId}`, JSON.stringify(formData));
      toast({
        title: "Draft Saved",
        description: "Your maintenance schedule has been saved as a draft.",
      });
    } catch (error) {
      console.error("Error saving draft:", error);
      toast({
        title: "Error",
        description: "Failed to save draft.",
        variant: "destructive",
      });
    }
  };

  const handleCancel = () => {
    router.back();
  };





  const getFrequencyText = (intervalDays: number) => {
    if (intervalDays === 1) return "Daily";
    if (intervalDays === 7) return "Weekly";
    if (intervalDays === 30) return "Monthly";
    if (intervalDays === 90) return "Quarterly";
    if (intervalDays === 365) return "Annually";
    return `Every ${intervalDays} days`;
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-2">Loading...</span>
        </div>
      </div>
    );
  }

  if (!asset) {
    return (
      <div className="container mx-auto py-8">
        <Alert variant="destructive">
          <AlertDescription>
            Asset not found. Please check the asset ID and try again.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const formContext: FormContext = {
    assetId: assetId,
    assetTypeId: asset.assetType.id,
    operationType: "maintenance.schedule",
    userId: "current-user", // This should come from auth context
    userRole: "admin", // This should come from auth context
  };

  return (
    <div className="container mx-auto py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Schedule Maintenance</h1>
            <p className="text-muted-foreground">
              Create maintenance schedules for this asset
            </p>
          </div>
        </div>
      </div>

      {/* Asset Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Asset Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">Asset Name</p>
              <p className="font-medium">{asset.name}</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">Asset Code</p>
              <p className="font-mono text-sm">{asset.code}</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">Asset Type</p>
              <p>{asset.assetType.name}</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">Status</p>
              {getStatusBadge(asset.status)}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Existing Schedules */}
      {schedules.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Existing Maintenance Schedules
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {schedules.map((schedule) => (
                <div key={schedule.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-4">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Calendar className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <p className="font-medium">{schedule.name}</p>
                        {getMaintenanceTypeBadge(schedule.type)}
                        {schedule.isActive ? (
                          <Badge className="bg-green-100 text-green-800">
                            <CheckCircle className="w-3 h-3 mr-1" />
                            Active
                          </Badge>
                        ) : (
                          <Badge className="bg-gray-100 text-gray-800">
                            <Clock className="w-3 h-3 mr-1" />
                            Inactive
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {getFrequencyText(schedule.intervalDays)} • {schedule.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Warning for Disposed Assets */}
      {asset.status === "disposed" && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>Warning:</strong> This asset has been disposed. 
            Maintenance scheduling is not available for disposed assets.
          </AlertDescription>
        </Alert>
      )}

      {/* Schedule Form */}
      {asset.status !== "disposed" && (
        <AssetOperationFormRenderer
          assetTypeId={asset.assetType.id}
          operationType="maintenance.schedule"
          context={formContext}
          onSubmit={handleSubmit}
          onSaveDraft={handleSaveDraft}
          onCancel={handleCancel}
        />
      )}
    </div>
  );
}