"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Checkbox } from "@/components/ui/checkbox"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import {
  Plus,
  Search,
  Download,
  Edit,
  Trash2,
  MoreHorizontal,
  Package,
  DollarSign,
  Calendar,
  MapPin,
  QrCode,
  FileText,
  Upload,
  Filter,
  SortAsc,
  Eye,
  Copy,
  Share,
  History,
  CheckCircle,
  Clock,
  Zap,
  Loader2,
  AlertCircle,
  Layers,
} from "lucide-react"
import { useAdminHeader } from "@/hooks/use-admin-header";
import { getAssetManagementHeaderConfig } from "@/lib/utils/admin-header-configs";
import { getStatusBadge } from "@/lib/utils/asset-status";

import { useAssets } from "@/hooks/use-assets";
import { Asset } from "@prisma/client";
import { toast } from "@/components/ui/use-toast";
import { AIAssistant } from "@/components/ai/ai-assistant";
import { AIInsightsDashboard } from "@/components/ai/ai-insights-dashboard";
import { AssetOperationsSummary } from "@/components/dashboard/asset-operations-summary";
import { AssetBreakdownCharts } from "@/components/dashboard/asset-breakdown-charts";

const categories = ["All", "IT Equipment", "Machinery", "Furniture", "Security", "Vehicles"]
const statuses = ["All", "active", "maintenance", "disposed"]
const conditions = ["All", "Excellent", "Good", "Fair", "Poor"]

export default function AssetsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All")
  const [selectedStatus, setSelectedStatus] = useState("All")
  const [selectedCondition, setSelectedCondition] = useState("All")
  const [selectedAssets, setSelectedAssets] = useState<string[]>([])
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)

  const [viewMode, setViewMode] = useState<"table" | "grid">("table")
  const [sortBy, setSortBy] = useState("name")
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc")

  const { 
    assets, 
    isLoading, 
    error, 
    fetchAssets, 
    deleteAsset
  } = useAssets();

  // Set up the header for this page
  // Set up the header for this page
  useAdminHeader({
    title: "Asset Management",
    breadcrumbs: [
      { label: "Admin", url: "/admin" },
      { label: "Assets", url: "/admin/assets" },
    ],
    variant: 'management',
    actions: [
      <Button variant="outline" key="import">
        <Upload className="mr-2 h-4 w-4" />
        Import
      </Button>,
      <Button variant="outline" key="export">
        <Download className="mr-2 h-4 w-4" />
        Export
      </Button>,
      <Link href="/admin/assets/new" key="add-asset">
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Add Asset
        </Button>
      </Link>,
    ],
  });  useEffect(() => {
    // Apply filters when they change
    const filters: any = {};
    if (selectedCategory !== "All") filters.category = selectedCategory;
    if (selectedStatus !== "All") filters.status = selectedStatus;
    if (searchTerm) filters.search = searchTerm;
    
    fetchAssets(filters);
  }, [fetchAssets, selectedCategory, selectedStatus, searchTerm]);



  const handleDeleteAsset = async (id: string) => {
    if (window.confirm("Are you sure you want to delete this asset?")) {
      const success = await deleteAsset(id);
      if (success) {
        toast({
          title: "Asset Deleted",
          description: "The asset has been successfully deleted.",
        });
        setSelectedAssets((prev) => prev.filter((assetId) => assetId !== id));
      } else {
        toast({
          title: "Error",
          description: "Failed to delete the asset. Please try again.",
          variant: "destructive",
        });
      }
    }
  };

  const filteredAssets = assets || [];



  const getConditionBadge = (condition: string) => {
    switch (condition) {
      case "Excellent":
        return (
          <Badge variant="default" className="bg-green-500">
            Excellent
          </Badge>
        )
      case "Good":
        return (
          <Badge variant="default" className="bg-blue-500">
            Good
          </Badge>
        )
      case "Fair":
        return (
          <Badge variant="default" className="bg-yellow-500">
            Fair
          </Badge>
        )
      case "Poor":
        return <Badge variant="destructive">Poor</Badge>
      default:
        return <Badge variant="secondary">{condition}</Badge>
    }
  }

  const handleSelectAsset = (assetId: string) => {
    setSelectedAssets((prev) => (prev.includes(assetId) ? prev.filter((id) => id !== assetId) : [...prev, assetId]))
  }

  const handleSelectAll = () => {
    setSelectedAssets(selectedAssets.length === filteredAssets.length ? [] : filteredAssets.map((asset) => asset.id))
  }

  if (error) {
    return (
      <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col items-center justify-center text-center p-6">
              <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
              <h3 className="text-xl font-semibold mb-2">Error Loading Assets</h3>
              <p className="text-muted-foreground mb-4">{error}</p>
              <Button onClick={() => fetchAssets()}>
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
      {/* The AppHeader is now managed by Zustand and rendered in app/layout.tsx */}

      <Tabs defaultValue="list" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="list">Asset List</TabsTrigger>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="ai-insights">AI Insights</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <AssetOperationsSummary />

          {/* Asset Categories Breakdown */}
          <AssetBreakdownCharts assets={filteredAssets} isLoading={isLoading} />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Asset Value Trends</CardTitle>
                <CardDescription>Depreciation and value changes over time</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg">
                  <div className="text-center">
                    <DollarSign className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">Interactive Value Chart</p>
                    <p className="text-sm text-muted-foreground">Asset depreciation over time</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Utilization Metrics</CardTitle>
                <CardDescription>Asset usage and efficiency analysis</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg">
                  <div className="text-center">
                    <Zap className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">Utilization Dashboard</p>
                    <p className="text-sm text-muted-foreground">Real-time usage metrics</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="list" className="space-y-6">
          {/* Enhanced Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Advanced Filters</span>
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm">
                    <Filter className="mr-2 h-4 w-4" />
                    More Filters
                  </Button>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" size="sm">
                        <SortAsc className="mr-2 h-4 w-4" />
                        Sort
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-56">
                      <div className="space-y-2">
                        <Label>Sort by</Label>
                        <Select value={sortBy} onValueChange={setSortBy}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="name">Name</SelectItem>
                            <SelectItem value="purchasePrice">Value</SelectItem>
                            <SelectItem value="purchaseDate">Purchase Date</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search assets, serial numbers, or locations..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-8"
                    />
                  </div>
                </div>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    {statuses.map((status) => (
                      <SelectItem key={status} value={status}>
                        {status === "active" ? "Active" : 
                         status === "maintenance" ? "Maintenance" : 
                         status === "disposed" ? "Disposed" : status}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Bulk Actions */}
          {selectedAssets.length > 0 && (
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">
                    {selectedAssets.length} asset{selectedAssets.length > 1 ? "s" : ""} selected
                  </span>
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm">
                      <Edit className="mr-2 h-4 w-4" />
                      Bulk Edit
                    </Button>
                    <Button variant="outline" size="sm">
                      <Download className="mr-2 h-4 w-4" />
                      Export Selected
                    </Button>
                    <Button variant="outline" size="sm">
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete Selected
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Enhanced Assets Table */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Assets ({filteredAssets.length})</CardTitle>
                  <CardDescription>Comprehensive asset inventory with detailed information</CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setViewMode(viewMode === "table" ? "grid" : "table")}
                  >
                    {viewMode === "table" ? "Grid View" : "Table View"}
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex justify-center items-center h-40">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              ) : filteredAssets.length === 0 ? (
                <div className="flex flex-col items-center justify-center text-center p-6">
                  <Package className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-xl font-semibold mb-2">No Assets Found</h3>
                  <p className="text-muted-foreground mb-4">Try adjusting your filters or add a new asset.</p>
                  <Button onClick={() => setIsAddDialogOpen(true)}>
                    <Plus className="mr-2 h-4 w-4" />
                    Add Asset
                  </Button>
                </div>
              ) : (
                <ScrollArea className="h-[600px]">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-12">
                          <Checkbox
                            checked={selectedAssets.length === filteredAssets.length && filteredAssets.length > 0}
                            onCheckedChange={handleSelectAll}
                          />
                        </TableHead>
                        <TableHead>Asset</TableHead>
                        <TableHead>Category</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Location</TableHead>
                        <TableHead>Purchase Date</TableHead>
                        <TableHead>Value</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredAssets.map((asset) => (
                        <TableRow key={asset.id} className="hover:bg-muted/50">
                          <TableCell>
                            <Checkbox
                              checked={selectedAssets.includes(asset.id)}
                              onCheckedChange={() => handleSelectAsset(asset.id)}
                            />
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-3">
                              <div className="w-10 h-10 bg-muted rounded-lg flex items-center justify-center">
                                <Package className="h-5 w-5 text-muted-foreground" />
                              </div>
                              <div>
                                <p className="font-medium">{asset.name}</p>
                                <p className="text-sm text-muted-foreground">{asset.id}</p>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">{asset.category}</Badge>
                          </TableCell>
                          <TableCell>{getStatusBadge(asset.status)}</TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <MapPin className="mr-1 h-3 w-3 text-muted-foreground" />
                              <span className="text-sm">{asset.location}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <span className="text-sm">{asset.purchaseDate instanceof Date ? asset.purchaseDate.toLocaleDateString() : asset.purchaseDate}</span>
                          </TableCell>
                          <TableCell>
                            <div>
                              <p className="font-medium">${asset.purchasePrice.toLocaleString()}</p>
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <span className="sr-only">Open menu</span>
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end" className="w-56">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <Link href={`/assets/${asset.id}`} passHref>
                                  <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                                    <Eye className="mr-2 h-4 w-4" />
                                    View Details
                                  </DropdownMenuItem>
                                </Link>
                                <Link href={`/assets/${asset.id}/update`} passHref>
                                  <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                                    <Edit className="mr-2 h-4 w-4" />
                                    Edit Asset
                                  </DropdownMenuItem>
                                </Link>
                                <DropdownMenuItem>
                                  <QrCode className="mr-2 h-4 w-4" />
                                  Generate QR Code
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>
                                  <Calendar className="mr-2 h-4 w-4" />
                                  Schedule Maintenance
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Share className="mr-2 h-4 w-4" />
                                  Transfer Asset
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem 
                                  className="text-red-600"
                                  onClick={() => handleDeleteAsset(asset.id)}
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Delete Asset
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </ScrollArea>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="ai-insights" className="space-y-6">
          <AIInsightsDashboard />
        </TabsContent>
      </Tabs>

      {/* AI Assistant - Always available */}
      <AIAssistant />
    </div>
  )
}