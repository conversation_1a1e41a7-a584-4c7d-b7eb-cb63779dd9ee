"use client";

import React, { useState, useEffect, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { AssetOperationFormRenderer } from "@/components/form-builder/asset-operation-form-renderer";
import { FormContext } from "@/lib/types/asset-type-forms";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ArrowLeft, Plus, Info } from "lucide-react";
import { toast } from "@/components/ui/use-toast";

interface AssetType {
  id: string;
  name: string;
  code: string;
  description: string;
  category: {
    name: string;
  };
}

function NewAssetPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [assetTypes, setAssetTypes] = useState<AssetType[]>([]);
  const [selectedAssetTypeId, setSelectedAssetTypeId] = useState<string>("");
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get asset type from URL params if provided
  const assetTypeIdFromUrl = searchParams.get("assetTypeId");

  useEffect(() => {
    loadAssetTypes();
  }, []);

  useEffect(() => {
    if (assetTypeIdFromUrl && assetTypes.length > 0) {
      const assetType = assetTypes.find(at => at.id === assetTypeIdFromUrl);
      if (assetType) {
        setSelectedAssetTypeId(assetTypeIdFromUrl);
      }
    }
  }, [assetTypeIdFromUrl, assetTypes]);

  const loadAssetTypes = async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/asset-types");
      if (response.ok) {
        const data = await response.json();
        setAssetTypes(data.assetTypes || []);
      } else {
        throw new Error("Failed to load asset types");
      }
    } catch (error) {
      console.error("Error loading asset types:", error);
      toast({
        title: "Error",
        description: "Failed to load asset types. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (formData: Record<string, any>) => {
    try {
      setIsSubmitting(true);

      const response = await fetch("/api/asset-operations", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          operationType: "asset.create",
          assetTypeId: selectedAssetTypeId,
          data: formData,
          context: {
            assetTypeId: selectedAssetTypeId,
            operationType: "asset.create",
            userId: "current-user", // This should come from auth context
            userRole: "admin", // This should come from auth context
          },
        }),
      });

      if (response.ok) {
        const result = await response.json();
        toast({
          title: "Success",
          description: "Asset created successfully!",
        });
        
        // Redirect to the new asset's detail page
        if (result.asset?.id) {
          router.push(`/assets/${result.asset.id}`);
        } else {
          router.push("/admin/assets");
        }
      } else {
        const error = await response.json();
        throw new Error(error.message || "Failed to create asset");
      }
    } catch (error) {
      console.error("Error creating asset:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create asset. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSaveDraft = async (formData: Record<string, any>) => {
    try {
      // Save as draft logic - could store in localStorage or send to API
      localStorage.setItem(`asset-draft-${selectedAssetTypeId}`, JSON.stringify(formData));
      toast({
        title: "Draft Saved",
        description: "Your progress has been saved as a draft.",
      });
    } catch (error) {
      console.error("Error saving draft:", error);
      toast({
        title: "Error",
        description: "Failed to save draft.",
        variant: "destructive",
      });
    }
  };

  const handleCancel = () => {
    router.back();
  };

  const formContext: FormContext = {
    assetTypeId: selectedAssetTypeId,
    operationType: "asset.create",
    userId: "current-user", // This should come from auth context
    userRole: "admin", // This should come from auth context
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-2">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Create New Asset</h1>
            <p className="text-muted-foreground">
              Add a new asset to your inventory using dynamic forms
            </p>
          </div>
        </div>
      </div>

      {/* Asset Type Selection */}
      {!selectedAssetTypeId && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Plus className="h-5 w-5" />
              Select Asset Type
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="assetType">Asset Type</Label>
              <Select value={selectedAssetTypeId} onValueChange={setSelectedAssetTypeId}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose an asset type to continue" />
                </SelectTrigger>
                <SelectContent>
                  {assetTypes.map((assetType) => (
                    <SelectItem key={assetType.id} value={assetType.id}>
                      <div className="flex flex-col">
                        <span className="font-medium">{assetType.name}</span>
                        <span className="text-sm text-muted-foreground">
                          {assetType.category.name} • {assetType.code}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {assetTypes.length === 0 && (
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  No asset types are available. Please create asset types first in the admin panel.
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      )}

      {/* Asset Creation Form */}
      {selectedAssetTypeId && (
        <AssetOperationFormRenderer
          assetTypeId={selectedAssetTypeId}
          operationType="asset.create"
          context={formContext}
          onSubmit={handleSubmit}
          onSaveDraft={handleSaveDraft}
          onCancel={handleCancel}
        />
      )}
    </div>
  );
}

export default function NewAssetPage() {
  return (
    <Suspense fallback={
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-2">Loading...</span>
        </div>
      </div>
    }>
      <NewAssetPageContent />
    </Suspense>
  );
}
