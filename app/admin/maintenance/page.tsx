"use client"

import { useState } from "react"
import { useAdminHeader } from "@/hooks/use-admin-header"
import { getMaintenanceHeaderConfig } from "@/lib/utils/admin-header-configs"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Plus,
  AlertTriangle,
  Clock,
  Wrench,
  DollarSign,
  Calendar,
  CheckCircle,
  XCircle,
  User,
  MapPin,
  FileText,
  Timer,
  TrendingUp,
  Activity,
  Zap,
  Target,
  BarChart3,
} from "lucide-react"
import { getStatusBadge } from "@/lib/utils/asset-status";
import { getMaintenanceStatusBadge } from "@/lib/utils/maintenance-types";

// Enhanced maintenance data
const maintenanceTasks = [
  {
    id: "MNT-001",
    assetId: "AST-002",
    assetName: "Toyota Forklift Model X",
    type: "Preventive",
    priority: "High",
    status: "Scheduled",
    scheduledDate: "2024-01-15",
    assignedTo: "Mike Johnson",
    assigneeAvatar: "/placeholder.svg?height=32&width=32",
    estimatedCost: 500,
    actualCost: 0,
    estimatedDuration: 4,
    actualDuration: 0,
    description: "Regular oil change and filter replacement",
    location: "Warehouse A, Bay 3",
    completionPercentage: 0,
    lastUpdated: "2024-01-10",
    checklist: [
      { item: "Check oil level", completed: false },
      { item: "Replace oil filter", completed: false },
      { item: "Replace air filter", completed: false },
      { item: "Inspect hydraulic system", completed: false },
    ],
    notes: [],
    attachments: 2,
  },
  {
    id: "MNT-002",
    assetId: "AST-005",
    assetName: "Security Camera System Pro",
    type: "Inspection",
    priority: "Medium",
    status: "In Progress",
    scheduledDate: "2024-01-12",
    assignedTo: "David Park",
    assigneeAvatar: "/placeholder.svg?height=32&width=32",
    estimatedCost: 200,
    actualCost: 150,
    estimatedDuration: 2,
    actualDuration: 1.5,
    description: "Quarterly security system inspection and calibration",
    location: "Building Perimeter",
    completionPercentage: 75,
    lastUpdated: "2024-01-12",
    checklist: [
      { item: "Test camera functionality", completed: true },
      { item: "Check recording quality", completed: true },
      { item: "Calibrate motion sensors", completed: true },
      { item: "Update firmware", completed: false },
    ],
    notes: ["All cameras operational", "Motion detection working properly"],
    attachments: 5,
  },
  {
    id: "MNT-003",
    assetId: "AST-001",
    assetName: "Dell Laptop XPS 15",
    type: "Repair",
    priority: "Low",
    status: "Completed",
    scheduledDate: "2024-01-10",
    assignedTo: "Sarah Wilson",
    assigneeAvatar: "/placeholder.svg?height=32&width=32",
    estimatedCost: 150,
    actualCost: 125,
    estimatedDuration: 1,
    actualDuration: 0.5,
    description: "Screen replacement and system update",
    location: "IT Department",
    completionPercentage: 100,
    lastUpdated: "2024-01-10",
    checklist: [
      { item: "Replace screen", completed: true },
      { item: "Update operating system", completed: true },
      { item: "Install security patches", completed: true },
      { item: "Test functionality", completed: true },
    ],
    notes: ["Screen replaced successfully", "All updates installed", "User satisfied with repair"],
    attachments: 3,
  },
  {
    id: "MNT-004",
    assetId: "AST-004",
    assetName: "HP LaserJet Pro 4000",
    type: "Preventive",
    priority: "Medium",
    status: "Overdue",
    scheduledDate: "2024-01-08",
    assignedTo: "Robert Chen",
    assigneeAvatar: "/placeholder.svg?height=32&width=32",
    estimatedCost: 75,
    actualCost: 0,
    estimatedDuration: 1,
    actualDuration: 0,
    description: "Toner replacement and cleaning",
    location: "Office Floor 1",
    completionPercentage: 0,
    lastUpdated: "2024-01-08",
    checklist: [
      { item: "Replace toner cartridge", completed: false },
      { item: "Clean print heads", completed: false },
      { item: "Check paper feed", completed: false },
      { item: "Test print quality", completed: false },
    ],
    notes: [],
    attachments: 1,
  },
]

const maintenanceStats = {
  totalTasks: maintenanceTasks.length,
  scheduled: maintenanceTasks.filter((task) => task.status === "Scheduled").length,
  inProgress: maintenanceTasks.filter((task) => task.status === "In Progress").length,
  overdue: maintenanceTasks.filter((task) => task.status === "Overdue").length,
  completed: maintenanceTasks.filter((task) => task.status === "Completed").length,
  totalCost: maintenanceTasks.reduce((sum, task) => sum + task.actualCost, 0),
  avgCompletionTime: 2.5,
  efficiency: 87,
  costSavings: 15000,
}

const technicians = [
  {
    name: "Mike Johnson",
    avatar: "/placeholder.svg?height=32&width=32",
    activeTasks: 3,
    completedTasks: 15,
    rating: 4.8,
  },
  {
    name: "Sarah Wilson",
    avatar: "/placeholder.svg?height=32&width=32",
    activeTasks: 2,
    completedTasks: 22,
    rating: 4.9,
  },
  {
    name: "David Park",
    avatar: "/placeholder.svg?height=32&width=32",
    activeTasks: 4,
    completedTasks: 18,
    rating: 4.7,
  },
  {
    name: "Robert Chen",
    avatar: "/placeholder.svg?height=32&width=32",
    activeTasks: 1,
    completedTasks: 12,
    rating: 4.6,
  },
]

export default function MaintenancePage() {
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [selectedTask, setSelectedTask] = useState<string | null>(null)

  // Set up the header for this page
  useAdminHeader(getMaintenanceHeaderConfig)



  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "High":
        return <Badge variant="destructive">High</Badge>
      case "Medium":
        return <Badge variant="default">Medium</Badge>
      case "Low":
        return <Badge variant="secondary">Low</Badge>
      default:
        return <Badge variant="secondary">{priority}</Badge>
    }
  }

  return (
    <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>Schedule Maintenance Task</DialogTitle>
                <DialogDescription>
                  Create a comprehensive maintenance task with detailed specifications.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="asset">Asset *</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select asset" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="ast-001">Dell Laptop XPS 15</SelectItem>
                        <SelectItem value="ast-002">Toyota Forklift Model X</SelectItem>
                        <SelectItem value="ast-003">Executive Conference Table</SelectItem>
                        <SelectItem value="ast-004">HP LaserJet Pro 4000</SelectItem>
                        <SelectItem value="ast-005">Security Camera System Pro</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="type">Maintenance Type *</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="preventive">Preventive</SelectItem>
                        <SelectItem value="repair">Repair</SelectItem>
                        <SelectItem value="inspection">Inspection</SelectItem>
                        <SelectItem value="emergency">Emergency</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="priority">Priority *</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select priority" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="low">Low</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="technician">Assign Technician</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select technician" />
                      </SelectTrigger>
                      <SelectContent>
                        {technicians.map((tech) => (
                          <SelectItem key={tech.name} value={tech.name.toLowerCase().replace(" ", "-")}>
                            {tech.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="date">Scheduled Date *</Label>
                    <Input id="date" type="date" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="duration">Estimated Duration (hours)</Label>
                    <Input id="duration" type="number" step="0.5" placeholder="2.0" />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="cost">Estimated Cost</Label>
                    <Input id="cost" type="number" placeholder="0.00" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="location">Location</Label>
                    <Input id="location" placeholder="Asset location" />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">Description *</Label>
                  <Textarea id="description" placeholder="Detailed description of maintenance task..." />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="checklist">Maintenance Checklist</Label>
                  <Textarea id="checklist" placeholder="Enter checklist items (one per line)..." />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit" onClick={() => setIsAddDialogOpen(false)}>
                  Schedule Task
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="scheduled">Scheduled</TabsTrigger>
          <TabsTrigger value="calendar">Calendar</TabsTrigger>
          <TabsTrigger value="technicians">Technicians</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Enhanced Stats Cards */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card className="relative overflow-hidden">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Tasks</CardTitle>
                <Wrench className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{maintenanceStats.totalTasks}</div>
                <p className="text-xs text-muted-foreground">All maintenance tasks</p>
                <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-purple-500" />
              </CardContent>
            </Card>

            <Card className="relative overflow-hidden">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Overdue Tasks</CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">{maintenanceStats.overdue}</div>
                <p className="text-xs text-muted-foreground">Require immediate attention</p>
                <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-red-500 to-orange-500" />
              </CardContent>
            </Card>

            <Card className="relative overflow-hidden">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Efficiency Score</CardTitle>
                <Target className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{maintenanceStats.efficiency}%</div>
                <Progress value={maintenanceStats.efficiency} className="mt-2" />
                <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-green-500 to-emerald-500" />
              </CardContent>
            </Card>

            <Card className="relative overflow-hidden">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Cost Savings</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  ${maintenanceStats.costSavings.toLocaleString()}
                </div>
                <p className="text-xs text-muted-foreground">This quarter</p>
                <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-cyan-500 to-blue-500" />
              </CardContent>
            </Card>
          </div>

          {/* Task Status Overview */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Scheduled</CardTitle>
                <Calendar className="h-4 w-4 text-blue-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">{maintenanceStats.scheduled}</div>
                <p className="text-xs text-muted-foreground">Upcoming tasks</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">In Progress</CardTitle>
                <Clock className="h-4 w-4 text-yellow-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">{maintenanceStats.inProgress}</div>
                <p className="text-xs text-muted-foreground">Currently active</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Completed</CardTitle>
                <CheckCircle className="h-4 w-4 text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{maintenanceStats.completed}</div>
                <p className="text-xs text-muted-foreground">This month</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Avg Time</CardTitle>
                <Timer className="h-4 w-4 text-purple-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-purple-600">{maintenanceStats.avgCompletionTime}h</div>
                <p className="text-xs text-muted-foreground">Per task</p>
              </CardContent>
            </Card>
          </div>

          {/* Recent Tasks and Priority Alerts */}
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Recent Maintenance Tasks</CardTitle>
                <CardDescription>Latest maintenance activities and their status</CardDescription>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[400px]">
                  <div className="space-y-4">
                    {maintenanceTasks.slice(0, 4).map((task) => (
                      <div
                        key={task.id}
                        className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                      >
                        <div className="flex items-center space-x-4">
                          <Avatar>
                            <AvatarImage src={task.assigneeAvatar || "/placeholder.svg"} />
                            <AvatarFallback>
                              {task.assignedTo
                                .split(" ")
                                .map((n) => n[0])
                                .join("")}
                            </AvatarFallback>
                          </Avatar>
                          <div className="space-y-1">
                            <p className="font-medium">{task.assetName}</p>
                            <p className="text-sm text-muted-foreground">{task.description}</p>
                            <div className="flex items-center space-x-2">
                              {getStatusBadge(task.status)}
                              {getPriorityBadge(task.priority)}
                            </div>
                            <div className="flex items-center text-xs text-muted-foreground">
                              <MapPin className="mr-1 h-3 w-3" />
                              {task.location}
                            </div>
                          </div>
                        </div>
                        <div className="text-right space-y-1">
                          <p className="text-sm font-medium">{task.scheduledDate}</p>
                          <p className="text-sm text-muted-foreground">${task.estimatedCost}</p>
                          {task.completionPercentage > 0 && (
                            <div className="w-20">
                              <Progress value={task.completionPercentage} className="h-1" />
                              <p className="text-xs text-muted-foreground mt-1">{task.completionPercentage}%</p>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Priority Alerts</CardTitle>
                <CardDescription>High-priority maintenance requiring attention</CardDescription>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[400px]">
                  <div className="space-y-4">
                    {maintenanceTasks
                      .filter((task) => task.priority === "High" || task.status === "Overdue")
                      .map((task) => (
                        <div key={task.id} className="space-y-3 p-4 border rounded-lg bg-red-50 border-red-200">
                          <div className="flex items-center justify-between">
                            <p className="text-sm font-medium">{task.assetName}</p>
                            <div className="flex items-center space-x-2">
                              {getPriorityBadge(task.priority)}
                              {getStatusBadge(task.status)}
                            </div>
                          </div>
                          <div className="space-y-2">
                            <div className="flex items-center text-sm text-muted-foreground">
                              <Calendar className="mr-2 h-3 w-3" />
                              Due: {task.scheduledDate}
                            </div>
                            <div className="flex items-center text-sm text-muted-foreground">
                              <User className="mr-2 h-3 w-3" />
                              {task.assignedTo}
                            </div>
                            <div className="flex items-center justify-between text-sm">
                              <span className="flex items-center text-muted-foreground">
                                <Timer className="mr-1 h-3 w-3" />
                                {task.estimatedDuration}h estimated
                              </span>
                              <span className="font-medium">${task.estimatedCost}</span>
                            </div>
                          </div>
                          <Button
                            size="sm"
                            className="w-full"
                            variant={task.status === "Overdue" ? "destructive" : "default"}
                          >
                            {task.status === "Overdue" ? "Urgent Action Required" : "Start Task"}
                          </Button>
                        </div>
                      ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="scheduled" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Scheduled Maintenance Tasks</CardTitle>
              <CardDescription>All upcoming and current maintenance tasks with detailed tracking</CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[600px]">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Task ID</TableHead>
                      <TableHead>Asset</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Priority</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Technician</TableHead>
                      <TableHead>Progress</TableHead>
                      <TableHead>Due Date</TableHead>
                      <TableHead>Cost</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {maintenanceTasks
                      .filter((task) => task.status !== "Completed")
                      .map((task) => (
                        <TableRow key={task.id} className="hover:bg-muted/50">
                          <TableCell className="font-medium">{task.id}</TableCell>
                          <TableCell>
                            <div>
                              <p className="font-medium">{task.assetName}</p>
                              <p className="text-sm text-muted-foreground">{task.location}</p>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">{task.type}</Badge>
                          </TableCell>
                          <TableCell>{getPriorityBadge(task.priority)}</TableCell>
                          <TableCell>{getStatusBadge(task.status)}</TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Avatar className="h-6 w-6">
                                <AvatarImage src={task.assigneeAvatar || "/placeholder.svg"} />
                                <AvatarFallback className="text-xs">
                                  {task.assignedTo
                                    .split(" ")
                                    .map((n) => n[0])
                                    .join("")}
                                </AvatarFallback>
                              </Avatar>
                              <span className="text-sm">{task.assignedTo}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              <Progress value={task.completionPercentage} className="w-16" />
                              <p className="text-xs text-muted-foreground">{task.completionPercentage}%</p>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div>
                              <p className="text-sm">{task.scheduledDate}</p>
                              <p className="text-xs text-muted-foreground">{task.estimatedDuration}h</p>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div>
                              <p className="text-sm font-medium">${task.estimatedCost}</p>
                              {task.actualCost > 0 && (
                                <p className="text-xs text-muted-foreground">Actual: ${task.actualCost}</p>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="calendar" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Maintenance Calendar</CardTitle>
              <CardDescription>Visual timeline of all scheduled maintenance activities</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[500px] flex items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg">
                <div className="text-center">
                  <Calendar className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                  <p className="text-lg text-muted-foreground mb-2">Interactive Maintenance Calendar</p>
                  <p className="text-sm text-muted-foreground">Drag and drop scheduling with resource management</p>
                  <div className="mt-4 space-y-2">
                    <p className="text-xs text-muted-foreground">• Visual task scheduling</p>
                    <p className="text-xs text-muted-foreground">• Resource conflict detection</p>
                    <p className="text-xs text-muted-foreground">• Automated notifications</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="technicians" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Technician Performance</CardTitle>
                <CardDescription>Individual technician metrics and workload</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {technicians.map((tech) => (
                    <div key={tech.name} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <Avatar>
                          <AvatarImage src={tech.avatar || "/placeholder.svg"} />
                          <AvatarFallback>
                            {tech.name
                              .split(" ")
                              .map((n) => n[0])
                              .join("")}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium">{tech.name}</p>
                          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                            <span>Active: {tech.activeTasks}</span>
                            <span>Completed: {tech.completedTasks}</span>
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="flex items-center space-x-1">
                          <span className="text-sm font-medium">{tech.rating}</span>
                          <span className="text-yellow-500">★</span>
                        </div>
                        <p className="text-xs text-muted-foreground">Rating</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Workload Distribution</CardTitle>
                <CardDescription>Current task allocation across technicians</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {technicians.map((tech) => {
                    const totalTasks = tech.activeTasks + tech.completedTasks
                    const workloadPercentage = (tech.activeTasks / 5) * 100 // Assuming max 5 active tasks
                    return (
                      <div key={tech.name} className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>{tech.name}</span>
                          <span>{tech.activeTasks}/5 active tasks</span>
                        </div>
                        <Progress value={workloadPercentage} className="h-2" />
                        <p className="text-xs text-muted-foreground">
                          {workloadPercentage > 80
                            ? "High workload"
                            : workloadPercentage > 50
                              ? "Moderate workload"
                              : "Available"}
                        </p>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">MTTR</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">2.3h</div>
                <p className="text-xs text-muted-foreground">Mean Time To Repair</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">MTBF</CardTitle>
                <Zap className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">45d</div>
                <p className="text-xs text-muted-foreground">Mean Time Between Failures</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">First-Time Fix</CardTitle>
                <Target className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">92%</div>
                <p className="text-xs text-muted-foreground">Success rate</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Planned vs Unplanned</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">75:25</div>
                <p className="text-xs text-muted-foreground">Maintenance ratio</p>
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Maintenance Trends</CardTitle>
                <CardDescription>Historical maintenance patterns and forecasting</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg">
                  <div className="text-center">
                    <TrendingUp className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">Maintenance Analytics Dashboard</p>
                    <p className="text-sm text-muted-foreground">Predictive maintenance insights</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Cost Analysis</CardTitle>
                <CardDescription>Maintenance cost breakdown and optimization</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg">
                  <div className="text-center">
                    <DollarSign className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">Cost Optimization Dashboard</p>
                    <p className="text-sm text-muted-foreground">ROI and budget analysis</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
