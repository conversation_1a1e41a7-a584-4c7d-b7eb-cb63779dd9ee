"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Progress } from "@/components/ui/progress"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Calculator,
  Calendar,
  ChevronDown,
  Download,
  FileText,
  Filter,
  BarChart,
  PieChart,
  Settings,
  Sliders,
  TrendingDown,
  TrendingUp,
  Upload,
  DollarSign,
  Clock,
  AlertTriangle,
  CheckCircle,
  HelpCircle,
  Info,
  Printer,
} from "lucide-react"
import { useAppHeaderStore } from "@/store/app-header-store"
import { financialService } from "@/lib/modules/financial/services"
import { depreciationService } from "@/lib/modules/financial/depreciation-service"
import type { FinancialAsset } from "@/lib/modules/financial/types"
import type {
  DepreciationMethod,
  DepreciationSchedule,
  DepreciationEntry,
  DepreciationComparison,
  DepreciationForecast,
} from "@/lib/modules/financial/depreciation-types"

export default function DepreciationPage() {
  const [assets, setAssets] = useState<FinancialAsset[]>([])
  const [selectedAsset, setSelectedAsset] = useState<FinancialAsset | null>(null)
  const [depreciationMethod, setDepreciationMethod] = useState<DepreciationMethod>("straight-line")
  const [depreciationSchedule, setDepreciationSchedule] = useState<DepreciationSchedule | null>(null)
  const [methodComparison, setMethodComparison] = useState<DepreciationComparison | null>(null)
  const [depreciationForecast, setDepreciationForecast] = useState<DepreciationForecast[]>([])
  const [loading, setLoading] = useState(true)
  const [startDate, setStartDate] = useState<string>(new Date().toISOString().split("T")[0])
  const [salvageValue, setSalvageValue] = useState<number>(0)
  const [usefulLife, setUsefulLife] = useState<number>(5)
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false)
  const [taxRate, setTaxRate] = useState<number>(0.21) // 21% corporate tax rate
  const [discountRate, setDiscountRate] = useState<number>(0.08) // 8% discount rate

  useEffect(() => {
    const { setHeaderContent, resetHeaderContent } = useAppHeaderStore.getState()
    
    setHeaderContent({
      title: "Asset Depreciation",
      actions: [
        <Button variant="outline" key="export">
          <Download className="mr-2 h-4 w-4" />
          Export
        </Button>,
        <Button variant="outline" key="print">
          <Printer className="mr-2 h-4 w-4" />
          Print
        </Button>,
        <Button key="settings">
          <Settings className="mr-2 h-4 w-4" />
          Settings
        </Button>,
      ],
    })

    return () => {
      resetHeaderContent()
    }
  }, [])  
  
  useEffect(() => {
    const loadData = async () => {
      try {
        const assetsData = financialService.getAssets()
        setAssets(assetsData)

        if (assetsData.length > 0) {
          setSelectedAsset(assetsData[0])
          setSalvageValue(assetsData[0].salvageValue)
          setUsefulLife(assetsData[0].usefulLife)
          setDepreciationMethod(assetsData[0].depreciationMethod)
        }
      } catch (error) {
        console.error("Error loading assets:", error)
      } finally {
        setLoading(false)
      }
    }

    loadData()
  }, [])

  useEffect(() => {
    if (selectedAsset) {
      calculateDepreciation()
      compareDepreciationMethods()
      generateForecast()
    }
  }, [selectedAsset, depreciationMethod, startDate, salvageValue, usefulLife])

  const handleAssetSelect = (asset: FinancialAsset) => {
    setSelectedAsset(asset)
    setSalvageValue(asset.salvageValue)
    setUsefulLife(asset.usefulLife)
    setDepreciationMethod(asset.depreciationMethod)
  }

  const calculateDepreciation = () => {
    if (!selectedAsset) return

    const assetWithCustomValues = {
      ...selectedAsset,
      salvageValue,
      usefulLife,
    }

    let schedule: DepreciationSchedule

    switch (depreciationMethod) {
      case "straight-line":
        schedule = depreciationService.calculateStraightLine(assetWithCustomValues, new Date(startDate))
        break
      case "declining-balance":
        schedule = depreciationService.calculateDecliningBalance(assetWithCustomValues, 2, new Date(startDate))
        break
      case "sum-of-years":
        schedule = depreciationService.calculateSumOfYearsDigits(assetWithCustomValues, new Date(startDate))
        break
      case "units-of-production":
        schedule = depreciationService.calculateUnitsOfProduction(
          assetWithCustomValues,
          usefulLife * 2000, // Example: 2000 units per year
          Array(usefulLife).fill(2000), // Assuming constant production
          new Date(startDate)
        )
        break
      default:
        schedule = depreciationService.calculateStraightLine(assetWithCustomValues, new Date(startDate))
    }

    setDepreciationSchedule(schedule)
  }

  const compareDepreciationMethods = () => {
    if (!selectedAsset) return

    const assetWithCustomValues = {
      ...selectedAsset,
      salvageValue,
      usefulLife,
    }

    const methods = depreciationService.compareDepreciationMethods(assetWithCustomValues, new Date(startDate))
    
    // Calculate present value and tax savings for each method
    const comparison: DepreciationComparison = {
      assetId: selectedAsset.id,
      assetName: selectedAsset.name,
      methods: {},
      recommendedMethod: "straight-line" as DepreciationMethod,
      recommendation: "",
    }

    let highestPV = 0

    Object.entries(methods).forEach(([method, schedule]) => {
      const methodKey = method as DepreciationMethod
      const firstYearExpense = schedule.entries[0]?.depreciationAmount || 0
      const totalExpense = schedule.entries.reduce((sum, entry) => sum + entry.depreciationAmount, 0)
      
      // Calculate present value of tax savings
      let presentValueOfExpenses = 0
      let taxSavings = 0
      
      schedule.entries.forEach((entry, index) => {
        const yearTaxSaving = entry.depreciationAmount * taxRate
        const presentValue = yearTaxSaving / Math.pow(1 + discountRate, index + 1)
        
        presentValueOfExpenses += presentValue
        taxSavings += yearTaxSaving
      })
      
      comparison.methods[methodKey] = {
        firstYearExpense,
        totalExpense,
        presentValueOfExpenses,
        taxSavings,
      }
      
      if (presentValueOfExpenses > highestPV) {
        highestPV = presentValueOfExpenses
        comparison.recommendedMethod = methodKey
      }
    })
    
    // Generate recommendation text
    switch (comparison.recommendedMethod) {
      case "straight-line":
        comparison.recommendation = "Straight-line depreciation provides consistent expenses over the asset's life, which is ideal for financial reporting and predictable budgeting."
        break
      case "declining-balance":
        comparison.recommendation = "Declining balance depreciation provides higher deductions in early years, which can maximize tax benefits when the asset is most valuable."
        break
      case "sum-of-years":
        comparison.recommendation = "Sum-of-years-digits provides accelerated depreciation that's less aggressive than double-declining, balancing tax benefits with financial reporting needs."
        break
      case "units-of-production":
        comparison.recommendation = "Units of production method ties depreciation to actual usage, which is ideal for assets where wear and tear is directly related to production volume."
        break
    }
    
    setMethodComparison(comparison)
  }

  const generateForecast = () => {
    if (!selectedAsset) return

    const assetWithCustomValues = {
      ...selectedAsset,
      salvageValue,
      usefulLife,
      depreciationMethod,
    }

    const forecast = depreciationService.generateDepreciationForecast([assetWithCustomValues], 10)
    setDepreciationForecast(forecast)
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Assets</CardTitle>
            <Calculator className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{assets.length}</div>
            <p className="text-xs text-muted-foreground">Depreciable assets</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Book Value</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(assets.reduce((sum, asset) => sum + asset.currentValue, 0))}
            </div>
            <p className="text-xs text-muted-foreground">Current net book value</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Annual Depreciation</CardTitle>
            <TrendingDown className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(
                assets.reduce((sum, asset) => {
                  const annualDepreciation = (asset.acquisitionCost - asset.salvageValue) / asset.usefulLife
                  return sum + annualDepreciation
                }, 0)
              )}
            </div>
            <p className="text-xs text-muted-foreground">Current fiscal year</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tax Savings</CardTitle>
            <PieChart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(
                assets.reduce((sum, asset) => {
                  const annualDepreciation = (asset.acquisitionCost - asset.salvageValue) / asset.usefulLife
                  return sum + annualDepreciation * taxRate
                }, 0)
              )}
            </div>
            <p className="text-xs text-muted-foreground">Estimated tax benefit</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-12">
        {/* Asset Selection */}
        <Card className="md:col-span-3">
          <CardHeader>
            <CardTitle>Assets</CardTitle>
            <CardDescription>Select an asset to calculate depreciation</CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            <ScrollArea className="h-[500px]">
              <div className="p-4 space-y-2">
                {assets.map((asset) => (
                  <div
                    key={asset.id}
                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                      selectedAsset?.id === asset.id
                        ? "border-primary bg-primary/10"
                        : "border-border hover:border-border/80"
                    }`}
                    onClick={() => handleAssetSelect(asset)}
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-semibold">{asset.name}</h3>
                        <p className="text-sm text-muted-foreground">{asset.assetType}</p>
                        <p className="text-sm">Cost: {formatCurrency(asset.acquisitionCost)}</p>
                      </div>
                      <Badge variant={asset.status === "active" ? "default" : "secondary"}>{asset.status}</Badge>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>

        {/* Main Content */}
        <div className="md:col-span-9 space-y-6">
          {selectedAsset && (
            <>
              {/* Asset Details & Depreciation Settings */}
              <Card>
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <div>
                      <CardTitle>{selectedAsset.name}</CardTitle>
                      <CardDescription>
                        {selectedAsset.assetType} | Acquired: {new Date(selectedAsset.acquisitionDate).toLocaleDateString()}
                      </CardDescription>
                    </div>
                    <Button variant="outline" onClick={() => setShowAdvancedSettings(!showAdvancedSettings)}>
                      <Sliders className="mr-2 h-4 w-4" />
                      {showAdvancedSettings ? "Hide" : "Show"} Advanced Settings
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                    <div className="space-y-2">
                      <Label htmlFor="acquisition-cost">Acquisition Cost</Label>
                      <Input
                        id="acquisition-cost"
                        value={selectedAsset.acquisitionCost}
                        readOnly
                        className="bg-muted"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="salvage-value">Salvage Value</Label>
                      <Input
                        id="salvage-value"
                        type="number"
                        value={salvageValue}
                        onChange={(e) => setSalvageValue(Number(e.target.value))}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="useful-life">Useful Life (Years)</Label>
                      <Input
                        id="useful-life"
                        type="number"
                        value={usefulLife}
                        onChange={(e) => setUsefulLife(Number(e.target.value))}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="depreciation-method">Depreciation Method</Label>
                      <Select
                        value={depreciationMethod}
                        onValueChange={(value) => setDepreciationMethod(value as DepreciationMethod)}
                      >
                        <SelectTrigger id="depreciation-method">
                          <SelectValue placeholder="Select method" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="straight-line">Straight Line</SelectItem>
                          <SelectItem value="declining-balance">Declining Balance</SelectItem>
                          <SelectItem value="sum-of-years">Sum of Years Digits</SelectItem>
                          <SelectItem value="units-of-production">Units of Production</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {showAdvancedSettings && (
                    <div className="mt-6 pt-6 border-t">
                      <h3 className="text-lg font-medium mb-4">Advanced Settings</h3>
                      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                        <div className="space-y-2">
                          <Label htmlFor="start-date">Start Date</Label>
                          <Input
                            id="start-date"
                            type="date"
                            value={startDate}
                            onChange={(e) => setStartDate(e.target.value)}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="tax-rate">Tax Rate (%)</Label>
                          <Input
                            id="tax-rate"
                            type="number"
                            value={taxRate * 100}
                            onChange={(e) => setTaxRate(Number(e.target.value) / 100)}
                            step="0.1"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="discount-rate">Discount Rate (%)</Label>
                          <Input
                            id="discount-rate"
                            type="number"
                            value={discountRate * 100}
                            onChange={(e) => setDiscountRate(Number(e.target.value) / 100)}
                            step="0.1"
                          />
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Depreciation Tabs */}
              <Tabs defaultValue="schedule" className="space-y-4">
                <TabsList className="grid grid-cols-4">
                  <TabsTrigger value="schedule">Depreciation Schedule</TabsTrigger>
                  <TabsTrigger value="comparison">Method Comparison</TabsTrigger>
                  <TabsTrigger value="forecast">Forecast</TabsTrigger>
                  <TabsTrigger value="reports">Reports</TabsTrigger>
                </TabsList>

                {/* Depreciation Schedule Tab */}
                <TabsContent value="schedule" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle>Depreciation Schedule</CardTitle>
                      <CardDescription>
                        {depreciationMethod === "straight-line"
                          ? "Equal depreciation expense each year"
                          : depreciationMethod === "declining-balance"
                          ? "Accelerated depreciation with higher expenses in early years"
                          : depreciationMethod === "sum-of-years"
                          ? "Accelerated depreciation based on sum of years' digits"
                          : "Depreciation based on units of production"}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      {depreciationSchedule && (
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Year</TableHead>
                              <TableHead>Period End</TableHead>
                              <TableHead>Depreciation</TableHead>
                              <TableHead>Accumulated</TableHead>
                              <TableHead>Book Value</TableHead>
                              {depreciationMethod === "units-of-production" && <TableHead>Units</TableHead>}
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {depreciationSchedule.entries.map((entry) => (
                              <TableRow key={entry.period}>
                                <TableCell>{entry.period}</TableCell>
                                <TableCell>{entry.periodEndDate}</TableCell>
                                <TableCell>{formatCurrency(entry.depreciationAmount)}</TableCell>
                                <TableCell>{formatCurrency(entry.accumulatedDepreciation)}</TableCell>
                                <TableCell>{formatCurrency(entry.bookValue)}</TableCell>
                                {depreciationMethod === "units-of-production" && (
                                  <TableCell>{entry.units?.toLocaleString()}</TableCell>
                                )}
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      )}
                    </CardContent>
                  </Card>

                  <div className="grid gap-4 md:grid-cols-2">
                    <Card>
                      <CardHeader>
                        <CardTitle>Depreciation Summary</CardTitle>
                      </CardHeader>
                      <CardContent>
                        {depreciationSchedule && (
                          <div className="space-y-4">
                            <div className="flex justify-between">
                              <span>Total Depreciable Amount:</span>
                              <span className="font-semibold">
                                {formatCurrency(depreciationSchedule.acquisitionCost - depreciationSchedule.salvageValue)}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span>Annual Depreciation (Year 1):</span>
                              <span className="font-semibold">
                                {formatCurrency(depreciationSchedule.entries[0]?.depreciationAmount || 0)}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span>Salvage Value:</span>
                              <span>{formatCurrency(depreciationSchedule.salvageValue)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Useful Life:</span>
                              <span>{depreciationSchedule.usefulLife} years</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Depreciation Method:</span>
                              <span className="capitalize">{depreciationSchedule.method.replace(/-/g, " ")}</span>
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle>Tax Impact</CardTitle>
                      </CardHeader>
                      <CardContent>
                        {depreciationSchedule && (
                          <div className="space-y-4">
                            <div className="flex justify-between">
                              <span>First Year Tax Savings:</span>
                              <span className="font-semibold">
                                {formatCurrency((depreciationSchedule.entries[0]?.depreciationAmount || 0) * taxRate)}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span>Total Tax Savings:</span>
                              <span>
                                {formatCurrency(
                                  depreciationSchedule.entries.reduce(
                                    (sum, entry) => sum + entry.depreciationAmount * taxRate,
                                    0
                                  )
                                )}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span>Present Value of Tax Savings:</span>
                              <span>
                                {formatCurrency(
                                  depreciationSchedule.entries.reduce((sum, entry, index) => {
                                    const yearTaxSaving = entry.depreciationAmount * taxRate
                                    return sum + yearTaxSaving / Math.pow(1 + discountRate, index + 1)
                                  }, 0)
                                )}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span>Tax Rate:</span>
                              <span>{formatPercentage(taxRate * 100)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Discount Rate:</span>
                              <span>{formatPercentage(discountRate * 100)}</span>
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>

                {/* Method Comparison Tab */}
                <TabsContent value="comparison" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle>Depreciation Method Comparison</CardTitle>
                      <CardDescription>Compare different depreciation methods to find the optimal strategy</CardDescription>
                    </CardHeader>
                    <CardContent>
                      {methodComparison && (
                        <>
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>Method</TableHead>
                                <TableHead>First Year Expense</TableHead>
                                <TableHead>Total Expense</TableHead>
                                <TableHead>Present Value of Tax Savings</TableHead>
                                <TableHead>Total Tax Savings</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {Object.entries(methodComparison.methods).map(([method, data]) => (
                                <TableRow key={method} className={method === methodComparison.recommendedMethod ? "bg-primary/10" : ""}>
                                  <TableCell className="font-medium capitalize">
                                    {method.replace(/-/g, " ")}
                                    {method === methodComparison.recommendedMethod && (
                                      <Badge className="ml-2 bg-green-100 text-green-800">Recommended</Badge>
                                    )}
                                  </TableCell>
                                  <TableCell>{formatCurrency(data.firstYearExpense)}</TableCell>
                                  <TableCell>{formatCurrency(data.totalExpense)}</TableCell>
                                  <TableCell>{formatCurrency(data.presentValueOfExpenses)}</TableCell>
                                  <TableCell>{formatCurrency(data.taxSavings)}</TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>

                          <div className="mt-6 p-4 border rounded-lg bg-muted/50">
                            <div className="flex items-start gap-3">
                              <Info className="h-5 w-5 text-blue-500 mt-0.5" />
                              <div>
                                <h4 className="font-semibold mb-1">Recommendation</h4>
                                <p>{methodComparison.recommendation}</p>
                              </div>
                            </div>
                          </div>
                        </>
                      )}
                    </CardContent>
                  </Card>

                  <div className="grid gap-4 md:grid-cols-2">
                    <Card>
                      <CardHeader>
                        <CardTitle>First Year Depreciation Comparison</CardTitle>
                      </CardHeader>
                      <CardContent>
                        {methodComparison && (
                          <div className="space-y-4">
                            {Object.entries(methodComparison.methods).map(([method, data]) => {
                              const maxFirstYear = Math.max(
                                ...Object.values(methodComparison.methods).map((d) => d.firstYearExpense)
                              )
                              const percentage = (data.firstYearExpense / maxFirstYear) * 100
                              
                              return (
                                <div key={method} className="space-y-2">
                                  <div className="flex justify-between text-sm">
                                    <span className="capitalize">{method.replace(/-/g, " ")}</span>
                                    <span>{formatCurrency(data.firstYearExpense)}</span>
                                  </div>
                                  <Progress value={percentage} className="h-2" />
                                </div>
                              )
                            })}
                          </div>
                        )}
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle>Tax Benefit Comparison</CardTitle>
                      </CardHeader>
                      <CardContent>
                        {methodComparison && (
                          <div className="space-y-4">
                            {Object.entries(methodComparison.methods).map(([method, data]) => {
                              const maxPV = Math.max(
                                ...Object.values(methodComparison.methods).map((d) => d.presentValueOfExpenses)
                              )
                              const percentage = (data.presentValueOfExpenses / maxPV) * 100
                              
                              return (
                                <div key={method} className="space-y-2">
                                  <div className="flex justify-between text-sm">
                                    <span className="capitalize">{method.replace(/-/g, " ")}</span>
                                    <span>{formatCurrency(data.presentValueOfExpenses)}</span>
                                  </div>
                                  <Progress value={percentage} className="h-2" />
                                </div>
                              )
                            })}
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>

                {/* Forecast Tab */}
                <TabsContent value="forecast" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle>Depreciation Forecast</CardTitle>
                      <CardDescription>10-year depreciation projection</CardDescription>
                    </CardHeader>
                    <CardContent>
                      {depreciationForecast.length > 0 && (
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Year</TableHead>
                              <TableHead>Fiscal Year</TableHead>
                              <TableHead>Depreciation Expense</TableHead>
                              <TableHead>Tax Savings</TableHead>
                              <TableHead>Book Value (End of Year)</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {depreciationForecast.map((forecast) => {
                              const currentYear = new Date().getFullYear()
                              const fiscalYear = currentYear + forecast.year - 1
                              const bookValue = selectedAsset
                                ? Math.max(
                                    selectedAsset.acquisitionCost -
                                      depreciationForecast
                                        .slice(0, forecast.year)
                                        .reduce((sum, f) => sum + f.totalDepreciation, 0),
                                    salvageValue
                                  )
                                : 0

                              return (
                                <TableRow key={forecast.year}>
                                  <TableCell>{forecast.year}</TableCell>
                                  <TableCell>{fiscalYear}</TableCell>
                                  <TableCell>{formatCurrency(forecast.totalDepreciation)}</TableCell>
                                  <TableCell>{formatCurrency(forecast.totalDepreciation * taxRate)}</TableCell>
                                  <TableCell>{formatCurrency(bookValue)}</TableCell>
                                </TableRow>
                              )
                            })}
                          </TableBody>
                        </Table>
                      )}
                    </CardContent>
                  </Card>

                  <div className="grid gap-4 md:grid-cols-2">
                    <Card>
                      <CardHeader>
                        <CardTitle>Cumulative Depreciation</CardTitle>
                      </CardHeader>
                      <CardContent className="h-[300px] flex items-center justify-center">
                        <div className="text-center text-muted-foreground">
                          <BarChart className="h-16 w-16 mx-auto mb-4" />
                          <p>Chart visualization would appear here</p>
                          <p className="text-sm">Showing cumulative depreciation over time</p>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle>Book Value Projection</CardTitle>
                      </CardHeader>
                      <CardContent className="h-[300px] flex items-center justify-center">
                        <div className="text-center text-muted-foreground">
                          <TrendingDown className="h-16 w-16 mx-auto mb-4" />
                          <p>Chart visualization would appear here</p>
                          <p className="text-sm">Showing book value decline over time</p>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>

                {/* Reports Tab */}
                <TabsContent value="reports" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle>Depreciation Reports</CardTitle>
                      <CardDescription>Generate and download depreciation reports</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                        <Button variant="outline" className="h-auto py-4 px-4 justify-start items-start text-left">
                          <div className="flex flex-col items-start gap-1">
                            <div className="flex items-center">
                              <FileText className="h-5 w-5 mr-2" />
                              <span className="font-medium">Depreciation Schedule</span>
                            </div>
                            <p className="text-sm text-muted-foreground">
                              Detailed depreciation schedule for the selected asset
                            </p>
                          </div>
                        </Button>

                        <Button variant="outline" className="h-auto py-4 px-4 justify-start items-start text-left">
                          <div className="flex flex-col items-start gap-1">
                            <div className="flex items-center">
                              <FileText className="h-5 w-5 mr-2" />
                              <span className="font-medium">Tax Depreciation Report</span>
                            </div>
                            <p className="text-sm text-muted-foreground">
                              Report for tax filing purposes with IRS Form 4562 data
                            </p>
                          </div>
                        </Button>

                        <Button variant="outline" className="h-auto py-4 px-4 justify-start items-start text-left">
                          <div className="flex flex-col items-start gap-1">
                            <div className="flex items-center">
                              <FileText className="h-5 w-5 mr-2" />
                              <span className="font-medium">Financial Statement Report</span>
                            </div>
                            <p className="text-sm text-muted-foreground">
                              Report for financial statement disclosure requirements
                            </p>
                          </div>
                        </Button>

                        <Button variant="outline" className="h-auto py-4 px-4 justify-start items-start text-left">
                          <div className="flex flex-col items-start gap-1">
                            <div className="flex items-center">
                              <FileText className="h-5 w-5 mr-2" />
                              <span className="font-medium">Depreciation Journal Entries</span>
                            </div>
                            <p className="text-sm text-muted-foreground">
                              Journal entries for recording depreciation in accounting system
                            </p>
                          </div>
                        </Button>

                        <Button variant="outline" className="h-auto py-4 px-4 justify-start items-start text-left">
                          <div className="flex flex-col items-start gap-1">
                            <div className="flex items-center">
                              <FileText className="h-5 w-5 mr-2" />
                              <span className="font-medium">Depreciation Forecast</span>
                            </div>
                            <p className="text-sm text-muted-foreground">
                              Multi-year forecast of depreciation expenses
                            </p>
                          </div>
                        </Button>

                        <Button variant="outline" className="h-auto py-4 px-4 justify-start items-start text-left">
                          <div className="flex flex-col items-start gap-1">
                            <div className="flex items-center">
                              <FileText className="h-5 w-5 mr-2" />
                              <span className="font-medium">Method Comparison Report</span>
                            </div>
                            <p className="text-sm text-muted-foreground">
                              Detailed comparison of different depreciation methods
                            </p>
                          </div>
                        </Button>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Compliance & Audit</CardTitle>
                      <CardDescription>Ensure compliance with accounting standards and prepare for audits</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex items-start gap-3 p-4 border rounded-lg">
                          <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                          <div>
                            <h4 className="font-semibold">GAAP Compliance</h4>
                            <p className="text-sm text-muted-foreground">
                              Current depreciation method complies with Generally Accepted Accounting Principles
                            </p>
                          </div>
                        </div>

                        <div className="flex items-start gap-3 p-4 border rounded-lg">
                          <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                          <div>
                            <h4 className="font-semibold">IRS Compliance</h4>
                            <p className="text-sm text-muted-foreground">
                              Depreciation method is acceptable for tax reporting purposes
                            </p>
                          </div>
                        </div>

                        <div className="flex items-start gap-3 p-4 border rounded-lg">
                          <AlertTriangle className="h-5 w-5 text-yellow-500 mt-0.5" />
                          <div>
                            <h4 className="font-semibold">Audit Readiness</h4>
                            <p className="text-sm text-muted-foreground">
                              Some documentation may need updating before next audit
                            </p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </>
          )}
        </div>
      </div>
    </div>
  )
}