"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  DollarSign,
  TrendingUp,
  Calculator,
  PieChart,
  BarChart3,
  Target,
  AlertTriangle,
  CheckCircle,
} from "lucide-react"
import { financialService } from "@/lib/modules/financial/services"
import type {
  FinancialAsset,
  FinancialMetrics,
  BudgetItem,
  TCOAnalysis,
  ROIAnalysis,
  LeaseVsBuyAnalysis,
  TaxOptimization,
} from "@/lib/modules/financial/types"
import { useAdminHeader } from "@/hooks/use-admin-header"
import { getFinancialHeaderConfig } from "@/lib/utils/admin-header-configs"

export default function FinancialManagementPage() {
  const [assets, setAssets] = useState<FinancialAsset[]>([])
  const [metrics, setMetrics] = useState<FinancialMetrics | null>(null)
  const [budgetItems, setBudgetItems] = useState<BudgetItem[]>([])
  const [selectedAsset, setSelectedAsset] = useState<FinancialAsset | null>(null)
  const [tcoAnalysis, setTcoAnalysis] = useState<TCOAnalysis | null>(null)
  const [roiAnalysis, setRoiAnalysis] = useState<ROIAnalysis | null>(null)
  const [leaseVsBuyAnalysis, setLeaseVsBuyAnalysis] = useState<LeaseVsBuyAnalysis | null>(null)
  const [taxOptimization, setTaxOptimization] = useState<TaxOptimization | null>(null)
  const [loading, setLoading] = useState(true)

  // Set up the header for this page
  useAdminHeader(getFinancialHeaderConfig)

  useEffect(() => {
    const loadData = async () => {
      try {
        const assetsData = financialService.getAssets()
        const metricsData = financialService.getFinancialMetrics()
        const budgetData = financialService.getBudgetItems()

        setAssets(assetsData)
        setMetrics(metricsData)
        setBudgetItems(budgetData)

        if (assetsData.length > 0) {
          setSelectedAsset(assetsData[0])
        }
      } catch (error) {
        console.error("Error loading financial data:", error)
      } finally {
        setLoading(false)
      }
    }

    loadData()
  }, [])

  const handleAssetSelect = (asset: FinancialAsset) => {
    setSelectedAsset(asset)
    setTcoAnalysis(null)
    setRoiAnalysis(null)
    setLeaseVsBuyAnalysis(null)
    setTaxOptimization(null)
  }

  const runTCOAnalysis = () => {
    if (selectedAsset) {
      const analysis = financialService.calculateTCO(selectedAsset.id)
      setTcoAnalysis(analysis)
    }
  }

  const runROIAnalysis = () => {
    if (selectedAsset) {
      // Sample cash flows for demonstration
      const cashFlows = Array(selectedAsset.usefulLife).fill(selectedAsset.acquisitionCost * 0.2)
      const analysis = financialService.calculateROI(selectedAsset.id, cashFlows)
      setRoiAnalysis(analysis)
    }
  }

  const runLeaseVsBuyAnalysis = () => {
    if (selectedAsset) {
      const analysis = financialService.analyzeLeaseVsBuy(selectedAsset.id)
      setLeaseVsBuyAnalysis(analysis)
    }
  }

  const runTaxOptimization = () => {
    if (selectedAsset) {
      const optimization = financialService.optimizeTaxStrategy(selectedAsset.id)
      setTaxOptimization(optimization)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">

      {/* Key Metrics Cards */}
      {metrics && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card className="border-l-4 border-l-blue-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Asset Value</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(metrics.totalAssetValue)}</div>
              <p className="text-xs text-muted-foreground">
                Portfolio health: {formatPercentage(metrics.portfolioHealth)}
              </p>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-green-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average ROI</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatPercentage(metrics.averageROI)}</div>
              <p className="text-xs text-muted-foreground">Asset turnover: {metrics.assetTurnover.toFixed(2)}x</p>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-orange-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Budget Utilization</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatPercentage(metrics.budgetUtilization)}</div>
              <Progress value={metrics.budgetUtilization} className="mt-2" />
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-purple-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Tax Savings</CardTitle>
              <PieChart className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(metrics.taxSavings)}</div>
              <p className="text-xs text-muted-foreground">
                Maintenance ratio: {formatPercentage(metrics.maintenanceCostRatio)}
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="tco">TCO Analysis</TabsTrigger>
          <TabsTrigger value="roi">ROI Analysis</TabsTrigger>
          <TabsTrigger value="lease-buy">Lease vs Buy</TabsTrigger>
          <TabsTrigger value="tax">Tax Optimization</TabsTrigger>
          <TabsTrigger value="budget">Budget Management</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* Asset Selection */}
            <Card>
              <CardHeader>
                <CardTitle>Asset Portfolio</CardTitle>
                <CardDescription>Select an asset for detailed analysis</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {assets.map((asset) => (
                  <div
                    key={asset.id}
                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                      selectedAsset?.id === asset.id
                        ? "border-primary bg-primary/10"
                        : "border-border hover:border-border/80"
                    }`}
                    onClick={() => handleAssetSelect(asset)}
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-semibold">{asset.name}</h3>
                        <p className="text-sm text-muted-foreground">{asset.assetType}</p>
                        <p className="text-sm">Current Value: {formatCurrency(asset.currentValue)}</p>
                      </div>
                      <Badge variant={asset.status === "active" ? "default" : "secondary"}>{asset.status}</Badge>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Financial Analysis Tools</CardTitle>
                <CardDescription>
                  {selectedAsset ? `Analyze: ${selectedAsset.name}` : "Select an asset to begin analysis"}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button onClick={runTCOAnalysis} disabled={!selectedAsset} className="w-full justify-start">
                  <Calculator className="mr-2 h-4 w-4" />
                  Calculate Total Cost of Ownership
                </Button>
                <Button
                  onClick={runROIAnalysis}
                  disabled={!selectedAsset}
                  variant="outline"
                  className="w-full justify-start"
                >
                  <TrendingUp className="mr-2 h-4 w-4" />
                  Analyze Return on Investment
                </Button>
                <Button
                  onClick={runLeaseVsBuyAnalysis}
                  disabled={!selectedAsset}
                  variant="outline"
                  className="w-full justify-start"
                >
                  <BarChart3 className="mr-2 h-4 w-4" />
                  Compare Lease vs Buy Options
                </Button>
                <Button
                  onClick={runTaxOptimization}
                  disabled={!selectedAsset}
                  variant="outline"
                  className="w-full justify-start"
                >
                  <PieChart className="mr-2 h-4 w-4" />
                  Optimize Tax Strategy
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* TCO Analysis Tab */}
        <TabsContent value="tco" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Total Cost of Ownership Analysis</CardTitle>
              <CardDescription>Comprehensive cost breakdown over asset lifecycle</CardDescription>
            </CardHeader>
            <CardContent>
              {tcoAnalysis ? (
                <div className="space-y-6">
                  <div className="grid gap-4 md:grid-cols-3">
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-lg">Acquisition Costs</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        <div className="flex justify-between">
                          <span>Purchase Price:</span>
                          <span className="font-semibold">
                            {formatCurrency(tcoAnalysis.acquisitionCosts.purchasePrice)}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>Installation:</span>
                          <span>{formatCurrency(tcoAnalysis.acquisitionCosts.installation)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Training:</span>
                          <span>{formatCurrency(tcoAnalysis.acquisitionCosts.training)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Other:</span>
                          <span>{formatCurrency(tcoAnalysis.acquisitionCosts.other)}</span>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-lg">Annual Operational Costs</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        <div className="flex justify-between">
                          <span>Maintenance:</span>
                          <span className="font-semibold">
                            {formatCurrency(tcoAnalysis.operationalCosts.maintenance)}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>Energy:</span>
                          <span>{formatCurrency(tcoAnalysis.operationalCosts.energy)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Insurance:</span>
                          <span>{formatCurrency(tcoAnalysis.operationalCosts.insurance)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Labor:</span>
                          <span>{formatCurrency(tcoAnalysis.operationalCosts.labor)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Supplies:</span>
                          <span>{formatCurrency(tcoAnalysis.operationalCosts.supplies)}</span>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-lg">Disposal Costs</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        <div className="flex justify-between">
                          <span>Removal:</span>
                          <span>{formatCurrency(tcoAnalysis.disposalCosts.removal)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Environmental:</span>
                          <span>{formatCurrency(tcoAnalysis.disposalCosts.environmental)}</span>
                        </div>
                        <div className="flex justify-between text-green-600">
                          <span>Salvage Value:</span>
                          <span className="font-semibold">
                            {formatCurrency(Math.abs(tcoAnalysis.disposalCosts.salvageValue))}
                          </span>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  <Card className="border-2 border-blue-200">
                    <CardContent className="pt-6">
                      <div className="text-center space-y-2">
                        <h3 className="text-2xl font-bold">Total Cost of Ownership</h3>
                        <p className="text-4xl font-bold text-blue-600">
                          {formatCurrency(tcoAnalysis.totalCostOfOwnership)}
                        </p>
                        <p className="text-muted-foreground">
                          Cost per year: {formatCurrency(tcoAnalysis.costPerYear)}
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <div className="text-center py-8">
                  <Calculator className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">
                    Select an asset and click "Calculate Total Cost of Ownership" to view analysis
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* ROI Analysis Tab */}
        <TabsContent value="roi" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Return on Investment Analysis</CardTitle>
              <CardDescription>Investment performance and profitability metrics</CardDescription>
            </CardHeader>
            <CardContent>
              {roiAnalysis ? (
                <div className="space-y-6">
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm">Net Present Value</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p
                          className={`text-2xl font-bold ${roiAnalysis.netPresentValue >= 0 ? "text-green-600" : "text-red-600"}`}
                        >
                          {formatCurrency(roiAnalysis.netPresentValue)}
                        </p>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm">Internal Rate of Return</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-2xl font-bold">{formatPercentage(roiAnalysis.internalRateOfReturn * 100)}</p>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm">Payback Period</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-2xl font-bold">{roiAnalysis.paybackPeriod.toFixed(1)} years</p>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm">Profitability Index</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p
                          className={`text-2xl font-bold ${roiAnalysis.profitabilityIndex >= 1 ? "text-green-600" : "text-red-600"}`}
                        >
                          {roiAnalysis.profitabilityIndex.toFixed(2)}
                        </p>
                      </CardContent>
                    </Card>
                  </div>

                  <Card>
                    <CardHeader>
                      <CardTitle>Investment Summary</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex justify-between items-center">
                        <span>Initial Investment:</span>
                        <span className="font-semibold">{formatCurrency(roiAnalysis.initialInvestment)}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span>Economic Value Added:</span>
                        <span
                          className={`font-semibold ${roiAnalysis.economicValueAdded >= 0 ? "text-green-600" : "text-red-600"}`}
                        >
                          {formatCurrency(roiAnalysis.economicValueAdded)}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span>Discount Rate:</span>
                        <span>{formatPercentage(roiAnalysis.discountRate * 100)}</span>
                      </div>
                      <div className="pt-4 border-t">
                        <div className="flex items-center space-x-2">
                          {roiAnalysis.netPresentValue >= 0 ? (
                            <CheckCircle className="h-5 w-5 text-green-600" />
                          ) : (
                            <AlertTriangle className="h-5 w-5 text-red-600" />
                          )}
                          <span className="font-semibold">
                            Investment Recommendation: {roiAnalysis.netPresentValue >= 0 ? "ACCEPT" : "REJECT"}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <div className="text-center py-8">
                  <TrendingUp className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">
                    Select an asset and click "Analyze Return on Investment" to view analysis
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Lease vs Buy Tab */}
        <TabsContent value="lease-buy" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Lease vs Buy Analysis</CardTitle>
              <CardDescription>Compare financing options to optimize your investment</CardDescription>
            </CardHeader>
            <CardContent>
              {leaseVsBuyAnalysis ? (
                <div className="space-y-6">
                  <div className="grid gap-4 md:grid-cols-3">
                    <Card
                      className={leaseVsBuyAnalysis.recommendation === "purchase" ? "border-green-500 border-2" : ""}
                    >
                      <CardHeader className="pb-2">
                        <CardTitle className="text-lg flex items-center">
                          Purchase
                          {leaseVsBuyAnalysis.recommendation === "purchase" && (
                            <Badge className="ml-2" variant="default">
                              Recommended
                            </Badge>
                          )}
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        <div className="flex justify-between">
                          <span>Initial Cost:</span>
                          <span className="font-semibold">
                            {formatCurrency(leaseVsBuyAnalysis.scenarios.purchase.initialCost)}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>Residual Value:</span>
                          <span className="text-green-600">
                            {formatCurrency(leaseVsBuyAnalysis.scenarios.purchase.residualValue)}
                          </span>
                        </div>
                        <div className="flex justify-between font-semibold">
                          <span>Net Present Value:</span>
                          <span
                            className={
                              leaseVsBuyAnalysis.scenarios.purchase.netPresentValue >= 0
                                ? "text-green-600"
                                : "text-red-600"
                            }
                          >
                            {formatCurrency(leaseVsBuyAnalysis.scenarios.purchase.netPresentValue)}
                          </span>
                        </div>
                      </CardContent>
                    </Card>

                    <Card
                      className={
                        leaseVsBuyAnalysis.recommendation === "operating-lease" ? "border-green-500 border-2" : ""
                      }
                    >
                      <CardHeader className="pb-2">
                        <CardTitle className="text-lg flex items-center">
                          Operating Lease
                          {leaseVsBuyAnalysis.recommendation === "operating-lease" && (
                            <Badge className="ml-2" variant="default">
                              Recommended
                            </Badge>
                          )}
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        <div className="flex justify-between">
                          <span>Monthly Payment:</span>
                          <span className="font-semibold">
                            {formatCurrency(leaseVsBuyAnalysis.scenarios.operatingLease.monthlyPayment)}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>Flexibility Score:</span>
                          <span className="text-blue-600">{leaseVsBuyAnalysis.flexibilityScore}/10</span>
                        </div>
                        <div className="flex justify-between font-semibold">
                          <span>Net Present Value:</span>
                          <span
                            className={
                              leaseVsBuyAnalysis.scenarios.operatingLease.netPresentValue >= 0
                                ? "text-green-600"
                                : "text-red-600"
                            }
                          >
                            {formatCurrency(leaseVsBuyAnalysis.scenarios.operatingLease.netPresentValue)}
                          </span>
                        </div>
                      </CardContent>
                    </Card>

                    <Card
                      className={
                        leaseVsBuyAnalysis.recommendation === "finance-lease" ? "border-green-500 border-2" : ""
                      }
                    >
                      <CardHeader className="pb-2">
                        <CardTitle className="text-lg flex items-center">
                          Finance Lease
                          {leaseVsBuyAnalysis.recommendation === "finance-lease" && (
                            <Badge className="ml-2" variant="default">
                              Recommended
                            </Badge>
                          )}
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        <div className="flex justify-between">
                          <span>Down Payment:</span>
                          <span className="font-semibold">
                            {formatCurrency(leaseVsBuyAnalysis.scenarios.financeLease.downPayment)}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>Monthly Payment:</span>
                          <span>{formatCurrency(leaseVsBuyAnalysis.scenarios.financeLease.monthlyPayment)}</span>
                        </div>
                        <div className="flex justify-between font-semibold">
                          <span>Net Present Value:</span>
                          <span
                            className={
                              leaseVsBuyAnalysis.scenarios.financeLease.netPresentValue >= 0
                                ? "text-green-600"
                                : "text-red-600"
                            }
                          >
                            {formatCurrency(leaseVsBuyAnalysis.scenarios.financeLease.netPresentValue)}
                          </span>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  <Card className="border-2 border-green-200">
                    <CardContent className="pt-6">
                      <div className="text-center space-y-2">
                        <h3 className="text-xl font-bold">
                          Recommendation: {leaseVsBuyAnalysis.recommendation.replace("-", " ").toUpperCase()}
                        </h3>
                        <p className="text-2xl font-bold text-green-600">
                          Potential Savings: {formatCurrency(leaseVsBuyAnalysis.savings)}
                        </p>
                        <p className="text-muted-foreground">
                          Flexibility Score: {leaseVsBuyAnalysis.flexibilityScore}/10
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <div className="text-center py-8">
                  <BarChart3 className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">
                    Select an asset and click "Compare Lease vs Buy Options" to view analysis
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Tax Optimization Tab */}
        <TabsContent value="tax" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Tax Optimization Strategy</CardTitle>
              <CardDescription>Maximize tax benefits through strategic depreciation</CardDescription>
            </CardHeader>
            <CardContent>
              {taxOptimization ? (
                <div className="space-y-6">
                  <div className="grid gap-4 md:grid-cols-2">
                    <Card
                      className={
                        taxOptimization.recommendedStrategy === "straightLine" ? "border-green-500 border-2" : ""
                      }
                    >
                      <CardHeader className="pb-2">
                        <CardTitle className="text-lg flex items-center">
                          Straight Line Depreciation
                          {taxOptimization.recommendedStrategy === "straightLine" && (
                            <Badge className="ml-2" variant="default">
                              Recommended
                            </Badge>
                          )}
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        <div className="flex justify-between">
                          <span>Annual Deduction:</span>
                          <span className="font-semibold">
                            {formatCurrency(taxOptimization.depreciationStrategies.straightLine.annualDeduction)}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>Total Deduction:</span>
                          <span>
                            {formatCurrency(taxOptimization.depreciationStrategies.straightLine.totalDeduction)}
                          </span>
                        </div>
                        <div className="flex justify-between text-green-600 font-semibold">
                          <span>Tax Savings:</span>
                          <span>{formatCurrency(taxOptimization.depreciationStrategies.straightLine.taxSavings)}</span>
                        </div>
                      </CardContent>
                    </Card>

                    <Card
                      className={
                        taxOptimization.recommendedStrategy === "accelerated" ? "border-green-500 border-2" : ""
                      }
                    >
                      <CardHeader className="pb-2">
                        <CardTitle className="text-lg flex items-center">
                          Accelerated Depreciation
                          {taxOptimization.recommendedStrategy === "accelerated" && (
                            <Badge className="ml-2" variant="default">
                              Recommended
                            </Badge>
                          )}
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        <div className="flex justify-between">
                          <span>Annual Deduction:</span>
                          <span className="font-semibold">
                            {formatCurrency(taxOptimization.depreciationStrategies.accelerated.annualDeduction)}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>Total Deduction:</span>
                          <span>
                            {formatCurrency(taxOptimization.depreciationStrategies.accelerated.totalDeduction)}
                          </span>
                        </div>
                        <div className="flex justify-between text-green-600 font-semibold">
                          <span>Tax Savings:</span>
                          <span>{formatCurrency(taxOptimization.depreciationStrategies.accelerated.taxSavings)}</span>
                        </div>
                      </CardContent>
                    </Card>

                    <Card
                      className={
                        taxOptimization.recommendedStrategy === "section179" ? "border-green-500 border-2" : ""
                      }
                    >
                      <CardHeader className="pb-2">
                        <CardTitle className="text-lg flex items-center">
                          Section 179 Deduction
                          {taxOptimization.recommendedStrategy === "section179" && (
                            <Badge className="ml-2" variant="default">
                              Recommended
                            </Badge>
                          )}
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        <div className="flex justify-between">
                          <span>Eligible Amount:</span>
                          <span className="font-semibold">
                            {formatCurrency(taxOptimization.depreciationStrategies.section179.eligibleAmount)}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>Immediate Deduction:</span>
                          <span>{formatCurrency(taxOptimization.depreciationStrategies.section179.deduction)}</span>
                        </div>
                        <div className="flex justify-between text-green-600 font-semibold">
                          <span>Tax Savings:</span>
                          <span>{formatCurrency(taxOptimization.depreciationStrategies.section179.taxSavings)}</span>
                        </div>
                      </CardContent>
                    </Card>

                    <Card
                      className={
                        taxOptimization.recommendedStrategy === "bonusDepreciation" ? "border-green-500 border-2" : ""
                      }
                    >
                      <CardHeader className="pb-2">
                        <CardTitle className="text-lg flex items-center">
                          Bonus Depreciation
                          {taxOptimization.recommendedStrategy === "bonusDepreciation" && (
                            <Badge className="ml-2" variant="default">
                              Recommended
                            </Badge>
                          )}
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        <div className="flex justify-between">
                          <span>Eligible Amount:</span>
                          <span className="font-semibold">
                            {formatCurrency(taxOptimization.depreciationStrategies.bonusDepreciation.eligibleAmount)}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>80% Deduction:</span>
                          <span>
                            {formatCurrency(taxOptimization.depreciationStrategies.bonusDepreciation.deduction)}
                          </span>
                        </div>
                        <div className="flex justify-between text-green-600 font-semibold">
                          <span>Tax Savings:</span>
                          <span>
                            {formatCurrency(taxOptimization.depreciationStrategies.bonusDepreciation.taxSavings)}
                          </span>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  <Card className="border-2 border-green-200">
                    <CardContent className="pt-6">
                      <div className="text-center space-y-2">
                        <h3 className="text-xl font-bold">
                          Recommended Strategy:{" "}
                          {taxOptimization.recommendedStrategy
                            .replace(/([A-Z])/g, " $1")
                            .replace(/^./, (str) => str.toUpperCase())}
                        </h3>
                        <p className="text-3xl font-bold text-green-600">
                          Total Tax Savings: {formatCurrency(taxOptimization.totalTaxSavings)}
                        </p>
                        <p className="text-muted-foreground">
                          Optimized depreciation strategy for maximum tax benefits
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <div className="text-center py-8">
                  <PieChart className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">
                    Select an asset and click "Optimize Tax Strategy" to view analysis
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Budget Management Tab */}
        <TabsContent value="budget" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Budget Management</CardTitle>
              <CardDescription>Track budget performance and variance analysis</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {budgetItems.map((item) => (
                  <Card key={item.id}>
                    <CardContent className="pt-6">
                      <div className="flex justify-between items-start mb-4">
                        <div>
                          <h3 className="font-semibold">
                            {item.category} - {item.subcategory}
                          </h3>
                          <p className="text-sm text-muted-foreground">
                            {item.period} • {item.costCenter}
                          </p>
                        </div>
                        <Badge
                          variant={
                            item.status === "on-track"
                              ? "default"
                              : item.status === "under-budget"
                                ? "secondary"
                                : "destructive"
                          }
                        >
                          {item.status.replace("-", " ")}
                        </Badge>
                      </div>

                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Budgeted:</span>
                          <span className="font-semibold">{formatCurrency(item.budgetedAmount)}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Actual:</span>
                          <span className="font-semibold">{formatCurrency(item.actualAmount)}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Variance:</span>
                          <span className={`font-semibold ${item.variance < 0 ? "text-green-600" : "text-red-600"}`}>
                            {formatCurrency(Math.abs(item.variance))} (
                            {formatPercentage(Math.abs(item.variancePercentage))})
                            {item.variance < 0 ? " under" : " over"}
                          </span>
                        </div>

                        <Progress value={(item.actualAmount / item.budgetedAmount) * 100} className="mt-2" />
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
