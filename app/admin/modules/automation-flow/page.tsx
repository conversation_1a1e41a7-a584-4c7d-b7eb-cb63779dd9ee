"use client"

import React, { useState, useEffect, Suspense } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useAdminHeader } from "@/hooks/use-admin-header"
import { getAutomationFlowHeaderConfig } from "@/lib/utils/admin-header-configs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { 
  Plus, 
  Z<PERSON>, 
  <PERSON><PERSON><PERSON>, 
  Play, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Edit, 
  Trash2, 
  Copy,
  Download,
  Upload,
  BarChart3
} from 'lucide-react'

import { FlowEditor } from '@/components/automation/flow-editor'
import { useFlowEditorStore } from '@/lib/advanced-features/automation/flow-store'
import { FlowWorkflowDefinition } from '@/lib/advanced-features/automation/types'

function AutomationFlowContent() {
  // Set up the header for this page
  useAdminHeader(getAutomationFlowHeaderConfig)

  const router = useRouter()
  const searchParams = useSearchParams()
  const workflowId = searchParams.get('id')
  
  const [workflows, setWorkflows] = useState<FlowWorkflowDefinition[]>([])
  const [showFlowEditor, setShowFlowEditor] = useState(false)
  const [selectedWorkflowId, setSelectedWorkflowId] = useState<string | null>(null)
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [newWorkflowData, setNewWorkflowData] = useState({
    name: '',
    description: '',
    isActive: true
  })
  
  const { setWorkflow } = useFlowEditorStore()

  // Load workflows
  useEffect(() => {
    // In a real app, this would fetch from the API
    const mockWorkflows: FlowWorkflowDefinition[] = [
      {
        id: 'workflow-1',
        name: 'Customer Onboarding',
        description: 'Automate the customer onboarding process',
        isActive: true,
        createdBy: 'admin',
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-02T00:00:00Z',
        executionCount: 42,
        nodes: [],
        edges: [],
        viewport: { x: 0, y: 0, zoom: 1 },
        variables: [],
        webhooks: [],
        triggers: []
      },
      {
        id: 'workflow-2',
        name: 'Invoice Processing',
        description: 'Process invoices and send notifications',
        isActive: false,
        createdBy: 'admin',
        createdAt: '2023-02-01T00:00:00Z',
        updatedAt: '2023-02-02T00:00:00Z',
        executionCount: 18,
        nodes: [],
        edges: [],
        viewport: { x: 0, y: 0, zoom: 1 },
        variables: [],
        webhooks: [],
        triggers: []
      }
    ]
    
    setWorkflows(mockWorkflows)
    
    // If workflowId is provided in URL, open that workflow
    if (workflowId) {
      const workflow = mockWorkflows.find(w => w.id === workflowId)
      if (workflow) {
        handleEditWorkflow(workflow.id)
      }
    }
  }, [workflowId])

  const handleCreateWorkflow = () => {
    // Create a new empty workflow
    const newWorkflow: FlowWorkflowDefinition = {
      id: `workflow-${Date.now()}`,
      name: newWorkflowData.name,
      description: newWorkflowData.description,
      isActive: newWorkflowData.isActive,
      createdBy: 'admin',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      executionCount: 0,
      nodes: [],
      edges: [],
      viewport: { x: 0, y: 0, zoom: 1 },
      variables: [],
      webhooks: [],
      triggers: []
    }
    
    setWorkflows(prev => [...prev, newWorkflow])
    setWorkflow(newWorkflow)
    setSelectedWorkflowId(newWorkflow.id)
    setShowFlowEditor(true)
    setShowCreateDialog(false)
    
    // Reset form
    setNewWorkflowData({
      name: '',
      description: '',
      isActive: true
    })
  }

  const handleEditWorkflow = (workflowId: string) => {
    const workflow = workflows.find(w => w.id === workflowId)
    if (workflow) {
      setWorkflow(workflow)
      setSelectedWorkflowId(workflowId)
      setShowFlowEditor(true)
    }
  }

  const handleDeleteWorkflow = (workflowId: string) => {
    if (confirm('Are you sure you want to delete this workflow?')) {
      setWorkflows(prev => prev.filter(w => w.id !== workflowId))
    }
  }

  const handleDuplicateWorkflow = (workflowId: string) => {
    const workflow = workflows.find(w => w.id === workflowId)
    if (workflow) {
      const duplicate: FlowWorkflowDefinition = {
        ...workflow,
        id: `workflow-${Date.now()}`,
        name: `${workflow.name} (Copy)`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        executionCount: 0
      }
      
      setWorkflows(prev => [...prev, duplicate])
    }
  }

  const handleToggleActive = (workflowId: string) => {
    setWorkflows(prev => 
      prev.map(w => 
        w.id === workflowId 
          ? { ...w, isActive: !w.isActive } 
          : w
      )
    )
  }

  const handleSaveWorkflow = async (workflow: FlowWorkflowDefinition) => {
    // In a real app, this would save to the API
    setWorkflows(prev => 
      prev.map(w => 
        w.id === workflow.id 
          ? { ...workflow, updatedAt: new Date().toISOString() } 
          : w
      )
    )
    
    alert('Workflow saved successfully')
  }

  const handleExecuteWorkflow = async (workflow: FlowWorkflowDefinition) => {
    // In a real app, this would execute the workflow via the API
    alert(`Executing workflow: ${workflow.name}`)
  }

  const handleExportWorkflow = (workflowId: string) => {
    const workflow = workflows.find(w => w.id === workflowId)
    if (workflow) {
      const dataStr = JSON.stringify(workflow, null, 2)
      const dataUri = `data:application/json;charset=utf-8,${encodeURIComponent(dataStr)}`
      
      const exportFileDefaultName = `workflow-${workflow.id}.json`
      
      const linkElement = document.createElement('a')
      linkElement.setAttribute('href', dataUri)
      linkElement.setAttribute('download', exportFileDefaultName)
      linkElement.click()
    }
  }

  return (
    <div className="container py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Automation Flow Editor</h1>
          <p className="text-muted-foreground">
            Create, edit and manage automated workflows
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => router.push('/admin/modules/automation-flow/settings')}>
            <Settings className="mr-2 h-4 w-4" />
            Settings
          </Button>
          <Button onClick={() => setShowCreateDialog(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Create Workflow
          </Button>
        </div>
      </div>

      {/* Workflow Editor */}
      {showFlowEditor && selectedWorkflowId && (
        <div className="h-[calc(100vh-200px)] border rounded-lg overflow-hidden">
          <FlowEditor
            workflowId={selectedWorkflowId}
            onSave={handleSaveWorkflow}
            onExecute={handleExecuteWorkflow}
            className="h-full"
          />
        </div>
      )}

      {/* Workflow List */}
      {!showFlowEditor && (
        <Tabs defaultValue="all">
          <div className="flex justify-between items-center mb-4">
            <TabsList>
              <TabsTrigger value="all">All Workflows</TabsTrigger>
              <TabsTrigger value="active">Active</TabsTrigger>
              <TabsTrigger value="inactive">Inactive</TabsTrigger>
            </TabsList>
            <Input 
              placeholder="Search workflows..." 
              className="max-w-xs"
            />
          </div>

          <TabsContent value="all" className="mt-0">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {workflows.map((workflow) => (
                <WorkflowCard
                  key={workflow.id}
                  workflow={workflow}
                  onEdit={() => handleEditWorkflow(workflow.id)}
                  onDelete={() => handleDeleteWorkflow(workflow.id)}
                  onDuplicate={() => handleDuplicateWorkflow(workflow.id)}
                  onToggleActive={() => handleToggleActive(workflow.id)}
                  onExport={() => handleExportWorkflow(workflow.id)}
                  onExecute={() => handleExecuteWorkflow(workflow)}
                />
              ))}
            </div>
          </TabsContent>

          <TabsContent value="active" className="mt-0">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {workflows
                .filter(workflow => workflow.isActive)
                .map((workflow) => (
                  <WorkflowCard
                    key={workflow.id}
                    workflow={workflow}
                    onEdit={() => handleEditWorkflow(workflow.id)}
                    onDelete={() => handleDeleteWorkflow(workflow.id)}
                    onDuplicate={() => handleDuplicateWorkflow(workflow.id)}
                    onToggleActive={() => handleToggleActive(workflow.id)}
                    onExport={() => handleExportWorkflow(workflow.id)}
                    onExecute={() => handleExecuteWorkflow(workflow)}
                  />
                ))
              }
            </div>
          </TabsContent>

          <TabsContent value="inactive" className="mt-0">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {workflows
                .filter(workflow => !workflow.isActive)
                .map((workflow) => (
                  <WorkflowCard
                    key={workflow.id}
                    workflow={workflow}
                    onEdit={() => handleEditWorkflow(workflow.id)}
                    onDelete={() => handleDeleteWorkflow(workflow.id)}
                    onDuplicate={() => handleDuplicateWorkflow(workflow.id)}
                    onToggleActive={() => handleToggleActive(workflow.id)}
                    onExport={() => handleExportWorkflow(workflow.id)}
                    onExecute={() => handleExecuteWorkflow(workflow)}
                  />
                ))
              }
            </div>
          </TabsContent>
        </Tabs>
      )}

      {/* Create Workflow Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Workflow</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="name">Workflow Name</Label>
              <Input
                id="name"
                value={newWorkflowData.name}
                onChange={(e) => setNewWorkflowData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter workflow name"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={newWorkflowData.description}
                onChange={(e) => setNewWorkflowData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Enter workflow description"
                rows={3}
              />
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="active"
                checked={newWorkflowData.isActive}
                onCheckedChange={(checked) => setNewWorkflowData(prev => ({ ...prev, isActive: checked }))}
              />
              <Label htmlFor="active">Active</Label>
            </div>
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleCreateWorkflow}
              disabled={!newWorkflowData.name}
            >
              Create Workflow
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default function AutomationFlowPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <AutomationFlowContent />
    </Suspense>
  )
}

interface WorkflowCardProps {
  workflow: FlowWorkflowDefinition
  onEdit: () => void
  onDelete: () => void
  onDuplicate: () => void
  onToggleActive: () => void
  onExport: () => void
  onExecute: () => void
}

const WorkflowCard: React.FC<WorkflowCardProps> = ({
  workflow,
  onEdit,
  onDelete,
  onDuplicate,
  onToggleActive,
  onExport,
  onExecute
}) => {
  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="flex items-center gap-2">
              {workflow.name}
              <Badge variant={workflow.isActive ? "default" : "secondary"}>
                {workflow.isActive ? 'Active' : 'Inactive'}
              </Badge>
            </CardTitle>
            <CardDescription>{workflow.description}</CardDescription>
          </div>
          <div className="flex items-center">
            <Switch
              checked={workflow.isActive}
              onCheckedChange={onToggleActive}
            />
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div>
              <span className="text-muted-foreground">Created:</span>{' '}
              {new Date(workflow.createdAt).toLocaleDateString()}
            </div>
            <div>
              <span className="text-muted-foreground">Updated:</span>{' '}
              {new Date(workflow.updatedAt).toLocaleDateString()}
            </div>
            <div>
              <span className="text-muted-foreground">Executions:</span>{' '}
              {workflow.executionCount}
            </div>
            <div>
              <span className="text-muted-foreground">Nodes:</span>{' '}
              {workflow.nodes.length}
            </div>
          </div>

          <div className="flex flex-wrap gap-2">
            <Button size="sm" variant="default" onClick={onEdit}>
              <Edit className="mr-1 h-4 w-4" />
              Edit
            </Button>
            <Button size="sm" variant="outline" onClick={onExecute}>
              <Play className="mr-1 h-4 w-4" />
              Run
            </Button>
            <Button size="sm" variant="outline" onClick={onDuplicate}>
              <Copy className="mr-1 h-4 w-4" />
              Duplicate
            </Button>
            <Button size="sm" variant="outline" onClick={onExport}>
              <Download className="mr-1 h-4 w-4" />
              Export
            </Button>
            <Button size="sm" variant="destructive" onClick={onDelete}>
              <Trash2 className="mr-1 h-4 w-4" />
              Delete
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}