"use client"

import React, { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { 
  ArrowLeft, 
  Save, 
  RefreshCw, 
  Key, 
  Webhook, 
  Database, 
  Server, 
  Shield, 
  Clock, 
  AlertTriangle,
  CheckCircle2
} from 'lucide-react'

export default function AutomationFlowSettingsPage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('general')
  const [isSaving, setIsSaving] = useState(false)
  const [saveSuccess, setSaveSuccess] = useState(false)

  const [settings, setSettings] = useState({
    general: {
      maxWorkflows: 100,
      maxNodesPerWorkflow: 50,
      maxExecutionsPerDay: 1000,
      enableLogging: true,
      logRetentionDays: 30,
      defaultTimeout: 300
    },
    triggerDev: {
      apiKey: 'your-trigger-dev-api-key',
      projectId: 'your-trigger-dev-project-id',
      apiUrl: 'https://api.trigger.dev',
      enabled: true
    },
    webhooks: {
      baseUrl: 'https://your-domain.com/api/webhooks',
      requireAuthentication: true,
      rateLimit: 100,
      timeoutSeconds: 30
    },
    security: {
      requireApproval: false,
      restrictedActions: ['delete_data', 'modify_users', 'send_external_emails'],
      ipWhitelist: [],
      enableAudit: true
    }
  })

  const handleSave = async () => {
    setIsSaving(true)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    setIsSaving(false)
    setSaveSuccess(true)
    
    // Reset success message after 3 seconds
    setTimeout(() => setSaveSuccess(false), 3000)
  }

  const handleChange = (section: string, field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section as keyof typeof prev],
        [field]: value
      }
    }))
  }

  return (
    <div className="container py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" onClick={() => router.push('/admin/modules/automation-flow')}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Automation Flow Settings</h1>
            <p className="text-muted-foreground">
              Configure the automation flow module settings
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {saveSuccess && (
            <Badge variant="outline" className="bg-green-50 text-green-700 flex items-center gap-1">
              <CheckCircle2 className="h-3 w-3" />
              Saved successfully
            </Badge>
          )}
          <Button onClick={handleSave} disabled={isSaving}>
            {isSaving ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Settings
              </>
            )}
          </Button>
        </div>
      </div>

      <Tabs
        orientation="vertical"
        value={activeTab}
        onValueChange={setActiveTab}
        className="flex gap-6"
      >
        <div className="w-64">
          <TabsList className="flex flex-col items-start h-auto bg-transparent p-0 w-full">
            <TabsTrigger
              value="general"
              className="w-full justify-start px-4 py-2 data-[state=active]:bg-muted"
            >
              General
            </TabsTrigger>
            <TabsTrigger
              value="triggerDev"
              className="w-full justify-start px-4 py-2 data-[state=active]:bg-muted"
            >
              Trigger.dev Integration
            </TabsTrigger>
            <TabsTrigger
              value="webhooks"
              className="w-full justify-start px-4 py-2 data-[state=active]:bg-muted"
            >
              Webhooks
            </TabsTrigger>
            <TabsTrigger
              value="security"
              className="w-full justify-start px-4 py-2 data-[state=active]:bg-muted"
            >
              Security
            </TabsTrigger>
            <TabsTrigger
              value="advanced"
              className="w-full justify-start px-4 py-2 data-[state=active]:bg-muted"
            >
              Advanced
            </TabsTrigger>
          </TabsList>
        </div>

        <div className="flex-1">
          <TabsContent value="general" className="mt-0">
            <Card>
              <CardHeader>
                <CardTitle>General Settings</CardTitle>
                <CardDescription>
                  Configure general settings for the automation flow module
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="maxWorkflows">Maximum Workflows</Label>
                    <Input
                      id="maxWorkflows"
                      type="number"
                      value={settings.general.maxWorkflows}
                      onChange={(e) => handleChange('general', 'maxWorkflows', parseInt(e.target.value))}
                    />
                    <p className="text-xs text-muted-foreground">
                      Maximum number of workflows allowed in the system
                    </p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="maxNodesPerWorkflow">Maximum Nodes Per Workflow</Label>
                    <Input
                      id="maxNodesPerWorkflow"
                      type="number"
                      value={settings.general.maxNodesPerWorkflow}
                      onChange={(e) => handleChange('general', 'maxNodesPerWorkflow', parseInt(e.target.value))}
                    />
                    <p className="text-xs text-muted-foreground">
                      Maximum number of nodes allowed in a single workflow
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="maxExecutionsPerDay">Maximum Executions Per Day</Label>
                    <Input
                      id="maxExecutionsPerDay"
                      type="number"
                      value={settings.general.maxExecutionsPerDay}
                      onChange={(e) => handleChange('general', 'maxExecutionsPerDay', parseInt(e.target.value))}
                    />
                    <p className="text-xs text-muted-foreground">
                      Maximum number of workflow executions allowed per day
                    </p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="defaultTimeout">Default Timeout (seconds)</Label>
                    <Input
                      id="defaultTimeout"
                      type="number"
                      value={settings.general.defaultTimeout}
                      onChange={(e) => handleChange('general', 'defaultTimeout', parseInt(e.target.value))}
                    />
                    <p className="text-xs text-muted-foreground">
                      Default timeout for workflow executions in seconds
                    </p>
                  </div>
                </div>

                <Separator />

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="enableLogging">Enable Logging</Label>
                      <p className="text-xs text-muted-foreground">
                        Enable detailed logging for workflow executions
                      </p>
                    </div>
                    <Switch
                      id="enableLogging"
                      checked={settings.general.enableLogging}
                      onCheckedChange={(checked) => handleChange('general', 'enableLogging', checked)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="logRetentionDays">Log Retention (days)</Label>
                    <Input
                      id="logRetentionDays"
                      type="number"
                      value={settings.general.logRetentionDays}
                      onChange={(e) => handleChange('general', 'logRetentionDays', parseInt(e.target.value))}
                      disabled={!settings.general.enableLogging}
                    />
                    <p className="text-xs text-muted-foreground">
                      Number of days to retain execution logs
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="triggerDev" className="mt-0">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Server className="h-5 w-5 text-blue-500" />
                  Trigger.dev Integration
                </CardTitle>
                <CardDescription>
                  Configure the Trigger.dev integration for workflow execution
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="triggerDevEnabled">Enable Trigger.dev Integration</Label>
                    <p className="text-xs text-muted-foreground">
                      Enable integration with Trigger.dev for workflow execution
                    </p>
                  </div>
                  <Switch
                    id="triggerDevEnabled"
                    checked={settings.triggerDev.enabled}
                    onCheckedChange={(checked) => handleChange('triggerDev', 'enabled', checked)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="triggerDevApiKey">API Key</Label>
                  <div className="flex gap-2">
                    <Input
                      id="triggerDevApiKey"
                      type="password"
                      value={settings.triggerDev.apiKey}
                      onChange={(e) => handleChange('triggerDev', 'apiKey', e.target.value)}
                      disabled={!settings.triggerDev.enabled}
                      className="flex-1"
                    />
                    <Button variant="outline" size="icon">
                      <Key className="h-4 w-4" />
                    </Button>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Your Trigger.dev API key
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="triggerDevProjectId">Project ID</Label>
                  <Input
                    id="triggerDevProjectId"
                    value={settings.triggerDev.projectId}
                    onChange={(e) => handleChange('triggerDev', 'projectId', e.target.value)}
                    disabled={!settings.triggerDev.enabled}
                  />
                  <p className="text-xs text-muted-foreground">
                    Your Trigger.dev project ID
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="triggerDevApiUrl">API URL</Label>
                  <Input
                    id="triggerDevApiUrl"
                    value={settings.triggerDev.apiUrl}
                    onChange={(e) => handleChange('triggerDev', 'apiUrl', e.target.value)}
                    disabled={!settings.triggerDev.enabled}
                  />
                  <p className="text-xs text-muted-foreground">
                    The Trigger.dev API URL (defaults to https://api.trigger.dev)
                  </p>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                  <h3 className="text-sm font-medium text-blue-800 flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4" />
                    Connection Status
                  </h3>
                  <p className="text-sm text-blue-700 mt-1">
                    {settings.triggerDev.enabled 
                      ? 'Trigger.dev integration is enabled. Click "Test Connection" to verify your credentials.'
                      : 'Trigger.dev integration is disabled. Enable it to use advanced workflow features.'
                    }
                  </p>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="mt-2"
                    disabled={!settings.triggerDev.enabled}
                  >
                    Test Connection
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="webhooks" className="mt-0">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Webhook className="h-5 w-5 text-blue-500" />
                  Webhook Settings
                </CardTitle>
                <CardDescription>
                  Configure webhook settings for workflow triggers
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="webhookBaseUrl">Webhook Base URL</Label>
                  <Input
                    id="webhookBaseUrl"
                    value={settings.webhooks.baseUrl}
                    onChange={(e) => handleChange('webhooks', 'baseUrl', e.target.value)}
                  />
                  <p className="text-xs text-muted-foreground">
                    The base URL for webhook endpoints
                  </p>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="webhookRequireAuth">Require Authentication</Label>
                    <p className="text-xs text-muted-foreground">
                      Require authentication for webhook endpoints
                    </p>
                  </div>
                  <Switch
                    id="webhookRequireAuth"
                    checked={settings.webhooks.requireAuthentication}
                    onCheckedChange={(checked) => handleChange('webhooks', 'requireAuthentication', checked)}
                  />
                </div>

                <div className="grid grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="webhookRateLimit">Rate Limit (per minute)</Label>
                    <Input
                      id="webhookRateLimit"
                      type="number"
                      value={settings.webhooks.rateLimit}
                      onChange={(e) => handleChange('webhooks', 'rateLimit', parseInt(e.target.value))}
                    />
                    <p className="text-xs text-muted-foreground">
                      Maximum number of webhook requests allowed per minute
                    </p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="webhookTimeout">Timeout (seconds)</Label>
                    <Input
                      id="webhookTimeout"
                      type="number"
                      value={settings.webhooks.timeoutSeconds}
                      onChange={(e) => handleChange('webhooks', 'timeoutSeconds', parseInt(e.target.value))}
                    />
                    <p className="text-xs text-muted-foreground">
                      Webhook request timeout in seconds
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="security" className="mt-0">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5 text-blue-500" />
                  Security Settings
                </CardTitle>
                <CardDescription>
                  Configure security settings for the automation flow module
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="securityRequireApproval">Require Approval</Label>
                    <p className="text-xs text-muted-foreground">
                      Require approval for workflow changes before deployment
                    </p>
                  </div>
                  <Switch
                    id="securityRequireApproval"
                    checked={settings.security.requireApproval}
                    onCheckedChange={(checked) => handleChange('security', 'requireApproval', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="securityEnableAudit">Enable Audit Logging</Label>
                    <p className="text-xs text-muted-foreground">
                      Enable detailed audit logging for all workflow actions
                    </p>
                  </div>
                  <Switch
                    id="securityEnableAudit"
                    checked={settings.security.enableAudit}
                    onCheckedChange={(checked) => handleChange('security', 'enableAudit', checked)}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Restricted Actions</Label>
                  <div className="grid grid-cols-2 gap-2">
                    {['delete_data', 'modify_users', 'send_external_emails', 'execute_scripts', 'modify_system_settings'].map((action) => (
                      <div key={action} className="flex items-center space-x-2">
                        <Switch
                          id={`action-${action}`}
                          checked={settings.security.restrictedActions.includes(action)}
                          onCheckedChange={(checked) => {
                            const newActions = checked
                              ? [...settings.security.restrictedActions, action]
                              : settings.security.restrictedActions.filter(a => a !== action)
                            handleChange('security', 'restrictedActions', newActions)
                          }}
                        />
                        <Label htmlFor={`action-${action}`} className="capitalize">
                          {action.replace(/_/g, ' ')}
                        </Label>
                      </div>
                    ))}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Actions that require special permissions or approval
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="advanced" className="mt-0">
            <Card>
              <CardHeader>
                <CardTitle>Advanced Settings</CardTitle>
                <CardDescription>
                  Configure advanced settings for the automation flow module
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4">
                  <h3 className="text-sm font-medium text-yellow-800 flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4" />
                    Warning
                  </h3>
                  <p className="text-sm text-yellow-700 mt-1">
                    These settings are for advanced users only. Incorrect configuration may cause system instability.
                  </p>
                </div>

                <div className="space-y-4">
                  <Button variant="destructive">
                    Reset All Workflows
                  </Button>
                  
                  <Button variant="destructive">
                    Purge Execution History
                  </Button>
                  
                  <Button variant="outline">
                    Export All Workflows
                  </Button>
                  
                  <Button variant="outline">
                    Import Workflows
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </div>
      </Tabs>
    </div>
  )
}