"use client"

import { useState } from "react"
import { useAdminHeader } from "@/hooks/use-admin-header"
import { getAssetLeasingHeaderConfig } from "@/lib/utils/admin-header-configs"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { PlusCircle } from "lucide-react"
import LeaseList from "@/components/asset-leasing/lease-list"
import LeaseCalendar from "@/components/asset-leasing/lease-calendar"
import PaymentsList from "@/components/asset-leasing/payments-list"
import LeaseMetricsDisplay from "@/components/asset-leasing/lease-metrics"
import LeaseDialog from "@/components/asset-leasing/lease-dialog"

export default function AssetLeasingPage() {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)

  // Set up the header for this page
  useAdminHeader(getAssetLeasingHeaderConfig)

  return (
    <div className="container mx-auto py-6">

      <LeaseMetricsDisplay />

      <Tabs defaultValue="active" className="mt-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="active">Active Leases</TabsTrigger>
          <TabsTrigger value="calendar">Calendar View</TabsTrigger>
          <TabsTrigger value="payments">Payments</TabsTrigger>
          <TabsTrigger value="all">All Leases</TabsTrigger>
        </TabsList>
        <TabsContent value="active">
          <Card>
            <CardHeader>
              <CardTitle>Active Lease Agreements</CardTitle>
              <CardDescription>
                View and manage all currently active lease agreements
              </CardDescription>
            </CardHeader>
            <CardContent>
              <LeaseList status="Active" />
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="calendar">
          <Card>
            <CardHeader>
              <CardTitle>Lease Calendar</CardTitle>
              <CardDescription>
                View lease start dates, end dates, and payment schedules
              </CardDescription>
            </CardHeader>
            <CardContent>
              <LeaseCalendar />
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="payments">
          <Card>
            <CardHeader>
              <CardTitle>Payment Schedule</CardTitle>
              <CardDescription>
                Track upcoming and past payments for all lease agreements
              </CardDescription>
            </CardHeader>
            <CardContent>
              <PaymentsList />
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="all">
          <Card>
            <CardHeader>
              <CardTitle>All Lease Agreements</CardTitle>
              <CardDescription>
                View all lease agreements including draft, active, expired, and terminated
              </CardDescription>
            </CardHeader>
            <CardContent>
              <LeaseList />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <LeaseDialog 
        open={isCreateDialogOpen} 
        onOpenChange={setIsCreateDialogOpen} 
      />
    </div>
  )
}