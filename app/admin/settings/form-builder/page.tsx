"use client";

import React, { useState } from "react";
import { useAdminHeader } from "@/hooks/use-admin-header";
import { getFormBuilderHeaderConfig } from "@/lib/utils/admin-header-configs";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { FormBuilder, FormDefinition, FormRenderer } from "@/components/form-builder";
import { CustomFieldEditor } from "@/components/custom-fields/custom-field-editor";
import { CustomField } from "@/lib/modules/asset-types/types";
import { CustomFieldsService } from "@/lib/modules/custom-fields/service";
import { Plus, Download, Upload, Save, Eye, ArrowLeft } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

// Sample custom fields for demonstration
const sampleCustomFields: CustomField[] = [
  {
    id: "field-1",
    name: "firstName",
    label: "First Name",
    type: "text",
    description: "Enter your first name",
    isRequired: true,
    isUnique: false,
    validation: {
      minLength: 2,
      maxLength: 50,
    },
    displayOrder: 0,
    isActive: true,
  },
  {
    id: "field-2",
    name: "lastName",
    label: "Last Name",
    type: "text",
    description: "Enter your last name",
    isRequired: true,
    isUnique: false,
    validation: {
      minLength: 2,
      maxLength: 50,
    },
    displayOrder: 1,
    isActive: true,
  },
  {
    id: "field-3",
    name: "email",
    label: "Email Address",
    type: "email",
    description: "Enter your email address",
    isRequired: true,
    isUnique: true,
    validation: {
      pattern: "^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$",
      errorMessage: "Please enter a valid email address",
    },
    displayOrder: 2,
    isActive: true,
  },
  {
    id: "field-4",
    name: "phoneNumber",
    label: "Phone Number",
    type: "phone",
    description: "Enter your phone number",
    isRequired: false,
    isUnique: false,
    validation: {},
    displayOrder: 3,
    isActive: true,
  },
  {
    id: "field-5",
    name: "dateOfBirth",
    label: "Date of Birth",
    type: "date",
    description: "Enter your date of birth",
    isRequired: false,
    isUnique: false,
    validation: {},
    displayOrder: 4,
    isActive: true,
  },
  {
    id: "field-6",
    name: "department",
    label: "Department",
    type: "select",
    description: "Select your department",
    isRequired: true,
    isUnique: false,
    options: [
      { value: "hr", label: "Human Resources", isActive: true },
      { value: "it", label: "Information Technology", isActive: true },
      { value: "finance", label: "Finance", isActive: true },
      { value: "marketing", label: "Marketing", isActive: true },
      { value: "operations", label: "Operations", isActive: true },
    ],
    validation: {},
    displayOrder: 5,
    isActive: true,
  },
  {
    id: "field-7",
    name: "skills",
    label: "Skills",
    type: "multiselect",
    description: "Select your skills",
    isRequired: false,
    isUnique: false,
    options: [
      { value: "javascript", label: "JavaScript", isActive: true },
      { value: "react", label: "React", isActive: true },
      { value: "node", label: "Node.js", isActive: true },
      { value: "python", label: "Python", isActive: true },
      { value: "java", label: "Java", isActive: true },
    ],
    validation: {},
    displayOrder: 6,
    isActive: true,
  },
  {
    id: "field-8",
    name: "bio",
    label: "Biography",
    type: "textarea",
    description: "Tell us about yourself",
    isRequired: false,
    isUnique: false,
    validation: {
      maxLength: 500,
    },
    displayOrder: 7,
    isActive: true,
  },
  {
    id: "field-9",
    name: "salary",
    label: "Expected Salary",
    type: "currency",
    description: "Enter your expected salary",
    isRequired: false,
    isUnique: false,
    validation: {
      minValue: 0,
    },
    displayOrder: 8,
    isActive: true,
  },
  {
    id: "field-10",
    name: "agreeToTerms",
    label: "I agree to the terms and conditions",
    type: "boolean",
    isRequired: true,
    isUnique: false,
    validation: {},
    displayOrder: 9,
    isActive: true,
  },
];

// Sample form for demonstration
const sampleForm: FormDefinition = {
  id: "form-1",
  name: "Employee Registration Form",
  description: "Complete this form to register as a new employee",
  sections: [
    {
      id: "section-1",
      title: "Personal Information",
      description: "Enter your personal details",
      columns: 2,
      fields: ["field-1", "field-2", "field-3", "field-4", "field-5"],
    },
    {
      id: "section-2",
      title: "Professional Information",
      description: "Enter your professional details",
      columns: 1,
      fields: ["field-6", "field-7", "field-8", "field-9"],
    },
    {
      id: "section-3",
      title: "Terms and Conditions",
      description: "Please review and accept our terms and conditions",
      columns: 1,
      fields: ["field-10"],
    },
  ],
  settings: {
    submitButtonText: "Submit Registration",
    cancelButtonText: "Cancel",
    showProgressBar: true,
    allowSaveAsDraft: true,
    confirmOnCancel: true,
    layout: "standard",
    labelPosition: "top",
  },
};

export default function FormBuilderPage() {
  // Set up the header for this page
  useAdminHeader(getFormBuilderHeaderConfig);

  const [customFields, setCustomFields] = useState<CustomField[]>(sampleCustomFields);
  const [forms, setForms] = useState<FormDefinition[]>([sampleForm]);
  const [activeForm, setActiveForm] = useState<FormDefinition | null>(sampleForm);
  const [isCreatingField, setIsCreatingField] = useState(false);
  const [editingField, setEditingField] = useState<CustomField | null>(null);
  const [activeTab, setActiveTab] = useState("forms");
  const [showPreview, setShowPreview] = useState(false);
  const [previewData, setPreviewData] = useState<Record<string, any>>({});
  
  const { toast } = useToast();
  const service = CustomFieldsService.getInstance();

  // Handle creating a new form
  const handleCreateForm = () => {
    const newForm: FormDefinition = {
      id: `form-${Date.now()}`,
      name: "New Form",
      description: "Form description",
      sections: [
        {
          id: `section-${Date.now()}`,
          title: "Section 1",
          description: "First section of the form",
          columns: 1,
          fields: [],
        },
      ],
      settings: {
        submitButtonText: "Submit",
        cancelButtonText: "Cancel",
        showProgressBar: true,
        allowSaveAsDraft: true,
        confirmOnCancel: true,
        layout: "standard",
        labelPosition: "top",
      },
    };
    
    setForms([...forms, newForm]);
    setActiveForm(newForm);
    setActiveTab("builder");
  };

  // Handle saving a form
  const handleSaveForm = (form: FormDefinition) => {
    setForms(forms.map((f) => (f.id === form.id ? form : f)));
    setActiveForm(form);
    
    toast({
      title: "Form Saved",
      description: `${form.name} has been saved successfully.`,
    });
  };

  // Handle deleting a form
  const handleDeleteForm = (formId: string) => {
    setForms(forms.filter((f) => f.id !== formId));
    
    if (activeForm?.id === formId) {
      setActiveForm(forms.length > 1 ? forms.find((f) => f.id !== formId) || null : null);
    }
    
    toast({
      title: "Form Deleted",
      description: "The form has been deleted successfully.",
    });
  };

  // Handle form preview
  const handlePreviewForm = (form: FormDefinition) => {
    setActiveForm(form);
    setShowPreview(true);
  };

  // Handle form export
  const handleExportForm = (form: FormDefinition) => {
    const formJson = JSON.stringify(form, null, 2);
    const blob = new Blob([formJson], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${form.name.replace(/\s+/g, "_").toLowerCase()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast({
      title: "Form Exported",
      description: "The form has been exported as JSON.",
    });
  };

  // Handle adding a new custom field
  const handleAddField = () => {
    setIsCreatingField(true);
    setEditingField(null);
  };

  // Handle saving a custom field
  const handleSaveField = (field: CustomField) => {
    if (isCreatingField) {
      setCustomFields([...customFields, field]);
    } else if (editingField) {
      setCustomFields(customFields.map((f) => (f.id === field.id ? field : f)));
    }
    
    setIsCreatingField(false);
    setEditingField(null);
    
    toast({
      title: "Field Saved",
      description: `${field.label} has been saved successfully.`,
    });
  };

  // Handle canceling field creation/editing
  const handleCancelField = () => {
    setIsCreatingField(false);
    setEditingField(null);
  };

  // Handle form submission in preview mode
  const handleSubmitPreview = (values: Record<string, any>) => {
    console.log("Form submitted:", values);
    
    toast({
      title: "Form Submitted",
      description: "Form data has been submitted successfully.",
    });
    
    setShowPreview(false);
  };

  // Handle canceling preview
  const handleCancelPreview = () => {
    setShowPreview(false);
  };

  return (
    <div className="container-fluid p-0 h-[calc(100vh-4rem)] flex flex-col">
      <div className="flex justify-between items-center p-4 border-b bg-background sticky top-0 z-10">
        <div className="flex items-center">
          {activeTab === "builder" && (
            <Button variant="ghost" size="sm" className="mr-2" onClick={() => setActiveTab("forms")}>
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back
            </Button>
          )}
          <div>
            <h1 className="text-xl font-bold tracking-tight">
              {activeTab === "forms" ? "Form Builder" : activeForm?.name || "Form Builder"}
            </h1>
            {activeTab === "forms" && (
              <p className="text-sm text-muted-foreground">
                Create and manage custom forms for your application
              </p>
            )}
          </div>
        </div>
        <div className="flex space-x-2">
          {activeTab === "forms" ? (
            <>
              <Button variant="outline" size="sm" onClick={handleAddField}>
                <Plus className="mr-1 h-4 w-4" />
                Add Field
              </Button>
              <Button size="sm" onClick={handleCreateForm}>
                <Plus className="mr-1 h-4 w-4" />
                New Form
              </Button>
            </>
          ) : (
            <Button variant="outline" size="sm" onClick={handleAddField}>
              <Plus className="mr-1 h-4 w-4" />
              Add Field
            </Button>
          )}
        </div>
      </div>

      <div className="flex-1 overflow-auto">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full">
          <TabsList className="hidden">
            <TabsTrigger value="forms">My Forms</TabsTrigger>
            <TabsTrigger value="builder" disabled={!activeForm}>
              Form Builder
            </TabsTrigger>
          </TabsList>

        <TabsContent value="forms" className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {forms.map((form) => (
              <div
                key={form.id}
                className="border rounded-lg overflow-hidden hover:shadow-md transition-shadow"
              >
                <div className="p-3 bg-muted">
                  <h3 className="font-semibold">{form.name}</h3>
                  <p className="text-xs text-muted-foreground truncate">{form.description}</p>
                </div>
                <div className="p-3">
                  <div className="text-xs text-muted-foreground mb-3 flex justify-between">
                    <span>{form.sections.length} sections</span>
                    <span>
                      {form.sections.reduce(
                        (total, section) => total + section.fields.length,
                        0
                      )}{" "}
                      fields
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePreviewForm(form)}
                      className="h-8 px-2 text-xs"
                    >
                      <Eye className="mr-1 h-3 w-3" />
                      Preview
                    </Button>
                    <div className="space-x-1">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setActiveForm(form);
                          setActiveTab("builder");
                        }}
                        className="h-8 px-2 text-xs"
                      >
                        Edit
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteForm(form.id)}
                        className="h-8 px-2 text-xs text-destructive hover:text-destructive hover:bg-destructive/10"
                      >
                        Delete
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="builder" className="p-0 h-full">
          {activeForm && (
            <div className="h-full">
              <FormBuilder
                initialForm={activeForm}
                availableFields={customFields}
                onSave={handleSaveForm}
                onPreview={handlePreviewForm}
                onExport={handleExportForm}
                onAddField={handleAddField}
              />
            </div>
          )}
        </TabsContent>
      </Tabs>
      </div>

      {/* Field Editor Dialog */}
      <Dialog open={isCreatingField || !!editingField} onOpenChange={(open) => !open && handleCancelField()}>
        <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{isCreatingField ? "Create New Field" : "Edit Field"}</DialogTitle>
          </DialogHeader>
          <div className="py-2">
            <CustomFieldEditor
              field={editingField}
              onSave={handleSaveField}
              onCancel={handleCancelField}
            />
          </div>
        </DialogContent>
      </Dialog>

      {/* Form Preview Dialog */}
      <Dialog open={showPreview} onOpenChange={setShowPreview}>
        <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Form Preview: {activeForm?.name}</DialogTitle>
          </DialogHeader>
          {activeForm && (
            <div className="py-2">
              <FormRenderer
                form={activeForm}
                fields={customFields}
                values={previewData}
                onChange={setPreviewData}
                onSubmit={handleSubmitPreview}
                onCancel={handleCancelPreview}
                onSaveDraft={() => {
                  toast({
                    title: "Draft Saved",
                    description: "Your form draft has been saved.",
                  });
                }}
              />
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}