"use client";

import React, { useState } from "react";
import { useAdminHeader } from "@/hooks/use-admin-header";
import { getCustomFieldsHeaderConfig } from "@/lib/utils/admin-header-configs";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { CustomFieldsProvider } from "@/lib/modules/custom-fields/context";
import { CustomFieldsList } from "@/components/custom-fields/custom-fields-list";
import { CustomFieldEditor } from "@/components/custom-fields/custom-field-editor";
import { FormGenerator } from "@/components/custom-fields/form-generator";
import { CustomField } from "@/lib/modules/asset-types/types";
import { CustomFieldsService } from "@/lib/modules/custom-fields/service";

export default function CustomFieldsPage() {
  // Set up the header for this page
  useAdminHeader(getCustomFieldsHeaderConfig);

  const [fields, setFields] = useState<CustomField[]>([]);
  const [editingField, setEditingField] = useState<CustomField | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [activeTab, setActiveTab] = useState("fields");
  const [formValues, setFormValues] = useState<Record<string, any>>({});

  const service = CustomFieldsService.getInstance();

  const handleAddField = () => {
    setIsCreating(true);
    setEditingField(null);
    setActiveTab("editor");
  };

  const handleEditField = (field: CustomField) => {
    setEditingField(field);
    setIsCreating(false);
    setActiveTab("editor");
  };

  const handleDeleteField = (fieldId: string) => {
    setFields(fields.filter((field) => field.id !== fieldId));
  };

  const handleSaveField = (field: CustomField) => {
    if (isCreating) {
      setFields([...fields, field]);
    } else {
      setFields(fields.map((f) => (f.id === field.id ? field : f)));
    }
    setEditingField(null);
    setIsCreating(false);
    setActiveTab("fields");
  };

  const handleCancelEdit = () => {
    setEditingField(null);
    setIsCreating(false);
    setActiveTab("fields");
  };

  const handleReorderFields = (reorderedFields: CustomField[]) => {
    setFields(reorderedFields);
  };

  return (
    <CustomFieldsProvider initialFields={fields}>
      <div className="container py-6 space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Custom Fields Editor</h1>
            <p className="text-muted-foreground">
              Create and manage custom fields for assets, asset types, and workflow forms
            </p>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="fields">Fields</TabsTrigger>
            <TabsTrigger value="editor" disabled={!isCreating && !editingField}>
              {isCreating ? "Create Field" : editingField ? "Edit Field" : "Field Editor"}
            </TabsTrigger>
            <TabsTrigger value="preview">Form Preview</TabsTrigger>
          </TabsList>

          <TabsContent value="fields" className="pt-4">
            <CustomFieldsList
              fields={fields}
              onEdit={handleEditField}
              onDelete={handleDeleteField}
              onAdd={handleAddField}
              onReorder={handleReorderFields}
            />
          </TabsContent>

          <TabsContent value="editor" className="pt-4">
            {(isCreating || editingField) && (
              <CustomFieldEditor
                field={editingField}
                onSave={handleSaveField}
                onCancel={handleCancelEdit}
              />
            )}
          </TabsContent>

          <TabsContent value="preview" className="pt-4">
            <div className="bg-card border rounded-lg p-6 shadow-sm">
              <h2 className="text-2xl font-bold mb-6">Form Preview</h2>
              {fields.length === 0 ? (
                <div className="text-center py-12">
                  <p className="text-muted-foreground mb-4">No fields defined yet.</p>
                  <Button onClick={handleAddField}>Add Your First Field</Button>
                </div>
              ) : (
                <div className="space-y-6">
                  <FormGenerator
                    fields={fields}
                    values={formValues}
                    onChange={setFormValues}
                    showGroups={true}
                  />
                  <div className="flex justify-end">
                    <Button>Submit Form</Button>
                  </div>
                  <div className="mt-8 p-4 bg-muted rounded-md">
                    <h3 className="text-lg font-medium mb-2">Form Values (Debug)</h3>
                    <pre className="text-xs overflow-auto p-2 bg-background rounded border">
                      {JSON.stringify(formValues, null, 2)}
                    </pre>
                  </div>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </CustomFieldsProvider>
  );
}