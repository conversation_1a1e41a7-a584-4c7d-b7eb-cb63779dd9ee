"use client";

import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { AssetOperationFormBuilder } from "@/components/form-builder/asset-operation-form-builder";
import { FormDefinition } from "@/components/form-builder";
import { AssetOperationType } from "@/lib/types/asset-type-forms";
import { CustomField, DepreciationSettings, LifecycleStage, MaintenanceSchedule } from "@/lib/modules/asset-types/types";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { 
  ArrowL<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  FileText,
  TrendingDown,
  Git<PERSON>ranch,
  Wrench,
  Plus,
  Edit,
  Trash2,
  Save,
  AlertCircle,
  CheckCircle,
  Info
} from "lucide-react";

interface AssetTypeData {
  id: string;
  name: string;
  code: string;
  description: string;
  categoryId: string;
  category: {
    id: string;
    name: string;
  };
  customFields: CustomField[];
  depreciationSettings?: DepreciationSettings;
  lifecycleStages: LifecycleStage[];
  maintenanceSchedules: MaintenanceSchedule[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function AssetTypeDetailPage() {
  const params = useParams();
  const router = useRouter();
  const assetTypeId = params.id as string;

  const [assetType, setAssetType] = useState<AssetTypeData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("forms");
  const [isSaving, setIsSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState<"idle" | "saving" | "saved" | "error">("idle");

  useEffect(() => {
    loadAssetType();
  }, [assetTypeId]);

  const loadAssetType = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/asset-types/${assetTypeId}`);
      
      if (!response.ok) {
        throw new Error("Failed to load asset type");
      }

      const data = await response.json();
      setAssetType(data);

    } catch (error) {
      console.error("Error loading asset type:", error);
      setError("Failed to load asset type. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleFormSave = async (form: FormDefinition, operationType: AssetOperationType) => {
    try {
      setIsSaving(true);
      setSaveStatus("saving");

      // First save the form definition
      const formResponse = await fetch("/api/form-definitions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: form.name,
          description: form.description,
          sections: form.sections,
          settings: form.settings,
          createdBy: "current-user", // TODO: Get from auth context
        }),
      });

      if (!formResponse.ok) {
        throw new Error("Failed to save form definition");
      }

      const savedForm = await formResponse.json();

      // Then associate it with the asset type
      const associationResponse = await fetch("/api/asset-type-forms", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          assetTypeId,
          formId: savedForm.id,
          operationType,
          isDefault: true,
          createdBy: "current-user", // TODO: Get from auth context
        }),
      });

      if (!associationResponse.ok) {
        throw new Error("Failed to associate form with asset type");
      }

      setSaveStatus("saved");
      setTimeout(() => setSaveStatus("idle"), 2000);

    } catch (error) {
      console.error("Error saving form:", error);
      setSaveStatus("error");
      setTimeout(() => setSaveStatus("idle"), 3000);
    } finally {
      setIsSaving(false);
    }
  };

  const handleFormPreview = (form: FormDefinition, operationType: AssetOperationType) => {
    // Open preview modal or navigate to preview page
    console.log("Preview form:", form, operationType);
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-2">Loading asset type...</span>
        </div>
      </div>
    );
  }

  if (error || !assetType) {
    return (
      <div className="container mx-auto py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error || "Asset type not found"}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
          
          <div>
            <h1 className="text-3xl font-bold">{assetType.name}</h1>
            <div className="flex items-center gap-2 mt-1">
              <Badge variant="outline">{assetType.code}</Badge>
              <Badge variant={assetType.isActive ? "default" : "secondary"}>
                {assetType.isActive ? "Active" : "Inactive"}
              </Badge>
              <span className="text-sm text-muted-foreground">
                {assetType.category.name}
              </span>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Edit className="h-4 w-4 mr-2" />
            Edit Details
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
        </div>
      </div>

      {/* Description */}
      {assetType.description && (
        <Card>
          <CardContent className="p-4">
            <p className="text-muted-foreground">{assetType.description}</p>
          </CardContent>
        </Card>
      )}

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="forms" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Forms
          </TabsTrigger>
          <TabsTrigger value="depreciation" className="flex items-center gap-2">
            <TrendingDown className="h-4 w-4" />
            Depreciation
          </TabsTrigger>
          <TabsTrigger value="lifecycle" className="flex items-center gap-2">
            <GitBranch className="h-4 w-4" />
            Lifecycle
          </TabsTrigger>
          <TabsTrigger value="maintenance" className="flex items-center gap-2">
            <Wrench className="h-4 w-4" />
            Maintenance
          </TabsTrigger>
          <TabsTrigger value="fields" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Fields
          </TabsTrigger>
        </TabsList>

        {/* Forms Tab */}
        <TabsContent value="forms" className="space-y-6">
          <AssetOperationFormBuilder
            assetTypeId={assetType.id}
            assetTypeName={assetType.name}
            availableFields={assetType.customFields}
            onSave={handleFormSave}
            onPreview={handleFormPreview}
          />
        </TabsContent>

        {/* Depreciation Tab */}
        <TabsContent value="depreciation" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingDown className="h-5 w-5" />
                Depreciation Settings
              </CardTitle>
            </CardHeader>
            <CardContent>
              {assetType.depreciationSettings ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                      <label className="text-sm font-medium">Method</label>
                      <p className="text-sm text-muted-foreground capitalize">
                        {assetType.depreciationSettings.method.replace('_', ' ')}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Useful Life</label>
                      <p className="text-sm text-muted-foreground">
                        {assetType.depreciationSettings.usefulLife} years
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Salvage Value</label>
                      <p className="text-sm text-muted-foreground">
                        {assetType.depreciationSettings.salvageValueType === 'percentage' ? 
                          `${assetType.depreciationSettings.salvageValue}%` :
                          `$${assetType.depreciationSettings.salvageValue}`
                        }
                      </p>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Depreciation Configuration</p>
                      <p className="text-sm text-muted-foreground">
                        Assets of this type will use these depreciation settings
                      </p>
                    </div>
                    <Button variant="outline" size="sm">
                      <Edit className="h-4 w-4 mr-2" />
                      Edit Settings
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <TrendingDown className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="font-medium mb-2">No Depreciation Settings</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Configure depreciation settings for assets of this type
                  </p>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Depreciation Settings
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Lifecycle Tab */}
        <TabsContent value="lifecycle" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <GitBranch className="h-5 w-5" />
                Lifecycle Stages
              </CardTitle>
            </CardHeader>
            <CardContent>
              {assetType.lifecycleStages.length > 0 ? (
                <div className="space-y-4">
                  <div className="grid gap-4">
                    {assetType.lifecycleStages.map((stage, index) => (
                      <div key={stage.id} className="flex items-center gap-4 p-4 border rounded-lg">
                        <div className="flex-shrink-0">
                          <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                            <span className="text-sm font-medium">{index + 1}</span>
                          </div>
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <h4 className="font-medium">{stage.name}</h4>
                            <Badge variant={stage.isActive ? "default" : "secondary"}>
                              {stage.isActive ? "Active" : "Inactive"}
                            </Badge>
                            {stage.isInitial && (
                              <Badge variant="outline">Initial</Badge>
                            )}
                            {stage.isFinal && (
                              <Badge variant="outline">Final</Badge>
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground">{stage.description}</p>
                          {stage.requiredFields && stage.requiredFields.length > 0 && (
                            <div className="flex items-center gap-1 mt-2">
                              <span className="text-xs text-muted-foreground">Required fields:</span>
                              {stage.requiredFields.map(field => (
                                <Badge key={field} variant="outline" className="text-xs">
                                  {field}
                                </Badge>
                              ))}
                            </div>
                          )}
                        </div>
                        <div className="flex items-center gap-2">
                          <Button variant="outline" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  <Separator />
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Lifecycle Management</p>
                      <p className="text-sm text-muted-foreground">
                        Define stages and transitions for asset lifecycle
                      </p>
                    </div>
                    <Button variant="outline" size="sm">
                      <Plus className="h-4 w-4 mr-2" />
                      Add Stage
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <GitBranch className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="font-medium mb-2">No Lifecycle Stages</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Define lifecycle stages for assets of this type
                  </p>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Lifecycle Stages
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Maintenance Tab */}
        <TabsContent value="maintenance" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Wrench className="h-5 w-5" />
                Maintenance Schedules
              </CardTitle>
            </CardHeader>
            <CardContent>
              {assetType.maintenanceSchedules.length > 0 ? (
                <div className="space-y-4">
                  <div className="grid gap-4">
                    {assetType.maintenanceSchedules.map((schedule) => (
                      <div key={schedule.id} className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <h4 className="font-medium">{schedule.name}</h4>
                            <Badge variant={schedule.isActive ? "default" : "secondary"}>
                              {schedule.isActive ? "Active" : "Inactive"}
                            </Badge>
                            <Badge variant="outline" className="capitalize">
                              {schedule.type}
                            </Badge>
                          </div>
                          <div className="flex items-center gap-2">
                            <Button variant="outline" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button variant="outline" size="sm">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                        
                        <p className="text-sm text-muted-foreground mb-3">{schedule.description}</p>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                          <div>
                            <span className="font-medium">Frequency:</span>
                            <p className="text-muted-foreground">
                              Every {schedule.frequency.interval} {schedule.frequency.type}
                            </p>
                          </div>
                          <div>
                            <span className="font-medium">Priority:</span>
                            <p className="text-muted-foreground capitalize">{schedule.priority}</p>
                          </div>
                          <div>
                            <span className="font-medium">Duration:</span>
                            <p className="text-muted-foreground">{schedule.estimatedDuration} minutes</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  <Separator />
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Maintenance Scheduling</p>
                      <p className="text-sm text-muted-foreground">
                        Automated maintenance schedules for assets of this type
                      </p>
                    </div>
                    <Button variant="outline" size="sm">
                      <Plus className="h-4 w-4 mr-2" />
                      Add Schedule
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <Wrench className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="font-medium mb-2">No Maintenance Schedules</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Configure maintenance schedules for assets of this type
                  </p>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Maintenance Schedule
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Fields Tab */}
        <TabsContent value="fields" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Custom Fields
              </CardTitle>
            </CardHeader>
            <CardContent>
              {assetType.customFields.length > 0 ? (
                <div className="space-y-4">
                  <div className="grid gap-4">
                    {assetType.customFields.map((field) => (
                      <div key={field.id} className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <h4 className="font-medium">{field.label}</h4>
                            <Badge variant="outline" className="capitalize">
                              {field.type}
                            </Badge>
                            {field.isRequired && (
                              <Badge variant="destructive">Required</Badge>
                            )}
                            {field.isUnique && (
                              <Badge variant="secondary">Unique</Badge>
                            )}
                          </div>
                          <div className="flex items-center gap-2">
                            <Button variant="outline" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button variant="outline" size="sm">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                        
                        {field.description && (
                          <p className="text-sm text-muted-foreground mb-2">{field.description}</p>
                        )}
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                          <div>
                            <span className="font-medium">Name:</span>
                            <p className="text-muted-foreground">{field.name}</p>
                          </div>
                          <div>
                            <span className="font-medium">Group:</span>
                            <p className="text-muted-foreground">{field.groupName || "Default"}</p>
                          </div>
                          <div>
                            <span className="font-medium">Order:</span>
                            <p className="text-muted-foreground">{field.displayOrder}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  <Separator />
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Field Configuration</p>
                      <p className="text-sm text-muted-foreground">
                        Custom fields specific to this asset type
                      </p>
                    </div>
                    <Button variant="outline" size="sm">
                      <Plus className="h-4 w-4 mr-2" />
                      Add Field
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <Settings className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="font-medium mb-2">No Custom Fields</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Add custom fields specific to this asset type
                  </p>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Custom Field
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}