'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useWorkflows } from '@/lib/hooks/use-asset-automation'
import { FlowWorkflowDefinition } from '@/lib/advanced-features/automation/types'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from '@/components/ui/breadcrumb'
import { Input } from '@/components/ui/input'
import { Skeleton } from '@/components/ui/skeleton'
import { AlertTriangle, CheckCircle, Edit, Home, Play, Plus, RefreshCw, Search, Trash2, Workflow } from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'

export default function WorkflowsPage() {
  const router = useRouter()
  const { workflows, loading, error, fetchWorkflows, deleteWorkflow } = useWorkflows()
  const [searchQuery, setSearchQuery] = useState('')
  
  // Filter workflows based on search query
  const filteredWorkflows = workflows.filter(workflow => 
    workflow.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    workflow.description?.toLowerCase().includes(searchQuery.toLowerCase())
  )
  
  // Handle refresh
  const handleRefresh = () => {
    fetchWorkflows()
  }
  
  // Handle create new workflow
  const handleCreateWorkflow = () => {
    router.push('/admin/asset-automation/workflows/new')
  }
  
  // Handle edit workflow
  const handleEditWorkflow = (id: string) => {
    router.push(`/admin/asset-automation/workflows/${id}/edit`)
  }
  
  // Handle view workflow
  const handleViewWorkflow = (id: string) => {
    router.push(`/admin/asset-automation/workflows/${id}`)
  }
  
  // Handle execute workflow
  const handleExecuteWorkflow = async (id: string) => {
    try {
      // Call the API to execute the workflow
      const response = await fetch(`/api/admin/asset-automation/workflows/${id}/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ input: {} })
      })
      
      if (!response.ok) {
        throw new Error('Failed to execute workflow')
      }
      
      const data = await response.json()
      
      // Navigate to the execution details page
      router.push(`/admin/asset-automation/executions/${data.executionId}`)
    } catch (error) {
      console.error('Error executing workflow:', error)
      // Show error notification
    }
  }
  
  // Handle delete workflow
  const handleDeleteWorkflow = async (id: string) => {
    if (confirm('Are you sure you want to delete this workflow? This action cannot be undone.')) {
      try {
        await deleteWorkflow(id)
      } catch (error) {
        console.error('Error deleting workflow:', error)
        // Show error notification
      }
    }
  }
  
  // Format time ago
  const formatTimeAgo = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true })
    } catch (error) {
      return dateString
    }
  }
  
  if (loading) {
    return (
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex items-center justify-between">
          <Skeleton className="h-10 w-1/3" />
          <Skeleton className="h-10 w-32" />
        </div>
        <Card>
          <CardHeader>
            <CardTitle>
              <Skeleton className="h-8 w-3/4" />
            </CardTitle>
            <CardDescription>
              <Skeleton className="h-4 w-1/2" />
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Skeleton className="h-[400px] w-full" />
          </CardContent>
        </Card>
      </div>
    )
  }
  
  if (error) {
    return (
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex items-center justify-between">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/admin">
                  <Home className="h-4 w-4 mr-1" />
                  Home
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink href="/admin/asset-automation">
                  Asset Automation
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink>
                  Workflows
                </BreadcrumbLink>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
          
          <Button onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4 mr-1" />
            Retry
          </Button>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle className="text-red-500">Error Loading Workflows</CardTitle>
            <CardDescription>
              Failed to load workflows
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2 text-red-500">
              <AlertTriangle className="h-5 w-5" />
              <p>{error.message || 'An error occurred while loading workflows'}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }
  
  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/admin">
                <Home className="h-4 w-4 mr-1" />
                Home
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/admin/asset-automation">
                Asset Automation
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink>
                Workflows
              </BreadcrumbLink>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4 mr-1" />
            Refresh
          </Button>
          <Button onClick={handleCreateWorkflow}>
            <Plus className="h-4 w-4 mr-1" />
            New Workflow
          </Button>
        </div>
      </div>
      
      <Card className="w-full">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Asset Automation Workflows</CardTitle>
              <CardDescription>
                Manage and execute your asset automation workflows
              </CardDescription>
            </div>
            <div className="relative w-64">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search workflows..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {filteredWorkflows.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              {searchQuery ? (
                <p>No workflows found matching your search</p>
              ) : (
                <div className="space-y-2">
                  <Workflow className="h-12 w-12 mx-auto text-muted-foreground/50" />
                  <p>No workflows found</p>
                  <p className="text-sm">Create your first workflow to get started</p>
                  <Button onClick={handleCreateWorkflow} className="mt-2">
                    <Plus className="h-4 w-4 mr-1" />
                    Create Workflow
                  </Button>
                </div>
              )}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[100px]">Status</TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Last Updated</TableHead>
                  <TableHead>Executions</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredWorkflows.map((workflow) => (
                  <TableRow 
                    key={workflow.id}
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleViewWorkflow(workflow.id)}
                  >
                    <TableCell>
                      {workflow.isActive ? (
                        <Badge className="bg-green-500">Active</Badge>
                      ) : (
                        <Badge variant="outline">Inactive</Badge>
                      )}
                    </TableCell>
                    <TableCell className="font-medium">
                      {workflow.name}
                    </TableCell>
                    <TableCell>
                      {workflow.description || 'No description'}
                    </TableCell>
                    <TableCell>
                      {formatTimeAgo(workflow.updatedAt)}
                    </TableCell>
                    <TableCell>
                      {workflow.executionCount || 0}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-1">
                        <Button 
                          size="icon" 
                          variant="ghost"
                          onClick={(e) => {
                            e.stopPropagation()
                            handleExecuteWorkflow(workflow.id)
                          }}
                          title="Execute Workflow"
                        >
                          <Play className="h-4 w-4" />
                        </Button>
                        <Button 
                          size="icon" 
                          variant="ghost"
                          onClick={(e) => {
                            e.stopPropagation()
                            handleEditWorkflow(workflow.id)
                          }}
                          title="Edit Workflow"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button 
                          size="icon" 
                          variant="ghost"
                          className="text-red-500 hover:text-red-700 hover:bg-red-50"
                          onClick={(e) => {
                            e.stopPropagation()
                            handleDeleteWorkflow(workflow.id)
                          }}
                          title="Delete Workflow"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
        <CardFooter className="border-t bg-muted/50">
          <div className="text-xs text-muted-foreground">
            Total workflows: {filteredWorkflows.length}
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}