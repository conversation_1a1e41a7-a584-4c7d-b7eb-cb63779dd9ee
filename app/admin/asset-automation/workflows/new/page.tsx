'use client'

import { useState, useEffect, Suspense } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from '@/components/ui/breadcrumb'
import { ArrowLeft, Home, Workflow } from 'lucide-react'
import { toast } from '@/components/ui/use-toast'
import { useCreateWorkflow } from '@/lib/hooks/use-asset-automation'
import { assetWorkflowTemplates } from '@/lib/advanced-features/automation/asset-node-types'

function NewWorkflowContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const templateId = searchParams.get('template')
  
  const [name, setName] = useState('')
  const [description, setDescription] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  const { createWorkflow, loading, error } = useCreateWorkflow()
  
  // Load template if specified
  useEffect(() => {
    if (templateId) {
      const template = assetWorkflowTemplates.find(t => t.id === templateId)
      if (template) {
        setName(template.name)
        setDescription(template.description || '')
      }
    }
  }, [templateId])
  
  // Navigate back to workflows
  const handleBack = () => {
    router.push('/admin/asset-automation/workflows')
  }
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!name) {
      toast({
        title: "Validation Error",
        description: "Workflow name is required",
        variant: "destructive"
      })
      return
    }
    
    setIsSubmitting(true)
    
    try {
      // Create workflow
      const newWorkflow = await createWorkflow({
        name,
        description,
        templateId
      })
      
      // Show success toast
      toast({
        title: "Workflow Created",
        description: "Your workflow has been created successfully.",
        variant: "success"
      })
      
      // Navigate to edit page
      router.push(`/admin/asset-automation/workflows/${newWorkflow.id}/edit`)
    } catch (error: any) {
      console.error('Error creating workflow:', error)
      
      // Show error toast
      toast({
        title: "Error Creating Workflow",
        description: error.message || "An error occurred while creating the workflow.",
        variant: "destructive"
      })
      
      setIsSubmitting(false)
    }
  }
  
  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/admin">
                <Home className="h-4 w-4 mr-1" />
                Home
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/admin/asset-automation">
                Asset Automation
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/admin/asset-automation/workflows">
                Workflows
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink>
                New Workflow
              </BreadcrumbLink>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        
        <Button variant="outline" onClick={handleBack}>
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back
        </Button>
      </div>
      
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Workflow className="h-5 w-5" />
            Create New Workflow
          </CardTitle>
          <CardDescription>
            {templateId 
              ? 'Create a new workflow based on a template' 
              : 'Create a new workflow from scratch'}
          </CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Workflow Name</Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="Enter workflow name"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Enter workflow description"
                rows={3}
              />
            </div>
            
            {templateId && (
              <div className="bg-muted p-4 rounded-md">
                <p className="text-sm font-medium">Using Template</p>
                <p className="text-sm text-muted-foreground">
                  {assetWorkflowTemplates.find(t => t.id === templateId)?.name || templateId}
                </p>
              </div>
            )}
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button 
              type="button" 
              variant="outline" 
              onClick={handleBack}
            >
              Cancel
            </Button>
            <Button 
              type="submit"
              disabled={isSubmitting || !name}
            >
              {isSubmitting ? 'Creating...' : 'Create Workflow'}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  )
}

export default function NewWorkflowPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <NewWorkflowContent />
    </Suspense>
  )
}