'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { useWorkflow, useWorkflowExecutions } from '@/lib/hooks/use-asset-automation'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from '@/components/ui/breadcrumb'
import { Skeleton } from '@/components/ui/skeleton'
import { AlertTriangle, ArrowLeft, BarChart3, Edit, Home, Play, RefreshCw, Webhook, Workflow } from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import WorkflowExecutionList from '@/components/automation/workflow-execution-list'

export default function WorkflowDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const workflowId = params.id as string
  const { workflow, loading, error, fetchWorkflow } = useWorkflow(workflowId)
  const [activeTab, setActiveTab] = useState('overview')
  
  // Handle refresh
  const handleRefresh = () => {
    fetchWorkflow()
  }
  
  // Handle edit workflow
  const handleEditWorkflow = () => {
    router.push(`/admin/asset-automation/workflows/${workflowId}/edit`)
  }
  
  // Handle execute workflow
  const handleExecuteWorkflow = async () => {
    try {
      // Call the API to execute the workflow
      const response = await fetch(`/api/admin/asset-automation/workflows/${workflowId}/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ input: {} })
      })
      
      if (!response.ok) {
        throw new Error('Failed to execute workflow')
      }
      
      const data = await response.json()
      
      // Navigate to the execution details page
      router.push(`/admin/asset-automation/executions/${data.executionId}`)
    } catch (error) {
      console.error('Error executing workflow:', error)
      // Show error notification
    }
  }
  
  // Handle back
  const handleBack = () => {
    router.push('/admin/asset-automation/workflows')
  }
  
  // Handle select execution
  const handleSelectExecution = (executionId: string) => {
    router.push(`/admin/asset-automation/executions/${executionId}`)
  }
  
  // Format time ago
  const formatTimeAgo = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true })
    } catch (error) {
      return dateString
    }
  }
  
  if (loading) {
    return (
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex items-center justify-between">
          <Skeleton className="h-10 w-1/3" />
          <Skeleton className="h-10 w-32" />
        </div>
        <Card>
          <CardHeader>
            <CardTitle>
              <Skeleton className="h-8 w-3/4" />
            </CardTitle>
            <CardDescription>
              <Skeleton className="h-4 w-1/2" />
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Skeleton className="h-[400px] w-full" />
          </CardContent>
        </Card>
      </div>
    )
  }
  
  if (error) {
    return (
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex items-center justify-between">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/admin">
                  <Home className="h-4 w-4 mr-1" />
                  Home
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink href="/admin/asset-automation">
                  Asset Automation
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink href="/admin/asset-automation/workflows">
                  Workflows
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink>
                  Workflow Details
                </BreadcrumbLink>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
          
          <Button variant="outline" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back
          </Button>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle className="text-red-500">Error Loading Workflow</CardTitle>
            <CardDescription>
              Failed to load workflow details
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2 text-red-500">
              <AlertTriangle className="h-5 w-5" />
              <p>{error.message || 'An error occurred while loading workflow details'}</p>
            </div>
            <Button onClick={handleRefresh} className="mt-4">
              Retry
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }
  
  if (!workflow) {
    return (
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex items-center justify-between">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/admin">
                  <Home className="h-4 w-4 mr-1" />
                  Home
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink href="/admin/asset-automation">
                  Asset Automation
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink href="/admin/asset-automation/workflows">
                  Workflows
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink>
                  Workflow Details
                </BreadcrumbLink>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
          
          <Button variant="outline" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back
          </Button>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle>Workflow Not Found</CardTitle>
            <CardDescription>
              The requested workflow could not be found
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p>The workflow with ID {workflowId} does not exist or has been deleted.</p>
            <Button onClick={handleBack} className="mt-4">
              Back to Workflows
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }
  
  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/admin">
                <Home className="h-4 w-4 mr-1" />
                Home
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/admin/asset-automation">
                Asset Automation
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/admin/asset-automation/workflows">
                Workflows
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink>
                {workflow.name}
              </BreadcrumbLink>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back
          </Button>
          <Button variant="outline" onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4 mr-1" />
            Refresh
          </Button>
          <Button variant="outline" onClick={handleEditWorkflow}>
            <Edit className="h-4 w-4 mr-1" />
            Edit
          </Button>
          <Button onClick={handleExecuteWorkflow}>
            <Play className="h-4 w-4 mr-1" />
            Execute
          </Button>
        </div>
      </div>
      
      <Card className="w-full">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center space-x-2">
                <CardTitle>{workflow.name}</CardTitle>
                {workflow.isActive ? (
                  <Badge className="bg-green-500">Active</Badge>
                ) : (
                  <Badge variant="outline">Inactive</Badge>
                )}
              </div>
              <CardDescription>
                {workflow.description || 'No description'}
              </CardDescription>
            </div>
            <div className="flex flex-col items-end text-sm text-muted-foreground">
              <div>Created: {formatTimeAgo(workflow.createdAt)}</div>
              <div>Last updated: {formatTimeAgo(workflow.updatedAt)}</div>
              {workflow.lastExecuted && (
                <div>Last executed: {formatTimeAgo(workflow.lastExecuted)}</div>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="executions">Executions</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
              <TabsTrigger value="webhooks">Webhooks</TabsTrigger>
            </TabsList>
            
            <TabsContent value="overview" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Nodes</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{workflow.nodes.length}</div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Edges</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{workflow.edges.length}</div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Executions</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{workflow.executionCount || 0}</div>
                  </CardContent>
                </Card>
              </div>
              
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm font-medium">Workflow Structure</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="bg-muted rounded-md p-4 h-[400px] flex items-center justify-center">
                    <div className="text-center">
                      <Workflow className="h-12 w-12 mx-auto text-muted-foreground/50 mb-2" />
                      <p className="text-muted-foreground">
                        Workflow visualization will be displayed here
                      </p>
                      <Button variant="outline" className="mt-2" onClick={handleEditWorkflow}>
                        <Edit className="h-4 w-4 mr-1" />
                        Open in Editor
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="executions">
              <WorkflowExecutionList 
                workflowId={workflowId} 
                onSelectExecution={handleSelectExecution} 
              />
            </TabsContent>
            
            <TabsContent value="analytics">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm font-medium">Workflow Analytics</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="bg-muted rounded-md p-4 h-[400px] flex items-center justify-center">
                    <div className="text-center">
                      <BarChart3 className="h-12 w-12 mx-auto text-muted-foreground/50 mb-2" />
                      <p className="text-muted-foreground">
                        Workflow analytics will be displayed here
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="webhooks">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm font-medium">Workflow Webhooks</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="bg-muted rounded-md p-4 h-[400px] flex items-center justify-center">
                    <div className="text-center">
                      <Webhook className="h-12 w-12 mx-auto text-muted-foreground/50 mb-2" />
                      <p className="text-muted-foreground">
                        {workflow.webhooks && workflow.webhooks.length > 0 
                          ? 'Webhook configuration will be displayed here'
                          : 'No webhooks configured for this workflow'}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
        <CardFooter className="border-t bg-muted/50 flex justify-between">
          <div className="text-xs text-muted-foreground">
            Workflow ID: {workflow.id}
          </div>
          <div className="text-xs text-muted-foreground">
            Created by: {workflow.createdBy || 'Unknown'}
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}