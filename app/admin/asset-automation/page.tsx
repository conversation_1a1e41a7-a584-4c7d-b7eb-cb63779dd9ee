'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAdminHeader } from "@/hooks/use-admin-header"
import { getAssetAutomationHeaderConfig } from "@/lib/utils/admin-header-configs"
import { useWorkflows } from '@/lib/hooks/use-asset-automation'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from '@/components/ui/breadcrumb'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Skeleton } from '@/components/ui/skeleton'
import { 
  AlertTriangle, 
  BarChart3, 
  Clock, 
  Home, 
  Play, 
  Plus, 
  RefreshCw, 
  Settings, 
  Workflow 
} from 'lucide-react'
import { assetWorkflowTemplates } from '@/lib/advanced-features/automation/asset-node-types'

export default function AssetAutomationPage() {
  const router = useRouter()
  const { workflows, loading, error, fetchWorkflows } = useWorkflows()
  const [activeTab, setActiveTab] = useState('overview')

  // Set up the header for this page
  useAdminHeader(getAssetAutomationHeaderConfig)
  
  // Handle refresh
  const handleRefresh = () => {
    fetchWorkflows()
  }
  
  // Handle create new workflow
  const handleCreateWorkflow = () => {
    router.push('/admin/asset-automation/workflows/new')
  }
  
  // Handle view all workflows
  const handleViewAllWorkflows = () => {
    router.push('/admin/asset-automation/workflows')
  }
  
  // Handle view workflow
  const handleViewWorkflow = (id: string) => {
    router.push(`/admin/asset-automation/workflows/${id}`)
  }
  
  // Handle use template
  const handleUseTemplate = (templateId: string) => {
    router.push(`/admin/asset-automation/workflows/new?template=${templateId}`)
  }
  
  // Get recent workflows (up to 5)
  const recentWorkflows = workflows.slice(0, 5)
  
  if (loading) {
    return (
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex items-center justify-between">
          <Skeleton className="h-10 w-1/3" />
          <Skeleton className="h-10 w-32" />
        </div>
        <Card>
          <CardHeader>
            <CardTitle>
              <Skeleton className="h-8 w-3/4" />
            </CardTitle>
            <CardDescription>
              <Skeleton className="h-4 w-1/2" />
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Skeleton className="h-[400px] w-full" />
          </CardContent>
        </Card>
      </div>
    )
  }
  
  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/admin">
                <Home className="h-4 w-4 mr-1" />
                Home
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink>
                Asset Automation
              </BreadcrumbLink>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4 mr-1" />
            Refresh
          </Button>
          <Button onClick={handleCreateWorkflow}>
            <Plus className="h-4 w-4 mr-1" />
            New Workflow
          </Button>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Workflows</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{workflows.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Active Workflows</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">
              {workflows.filter(w => w.isActive).length}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Executions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">
              {workflows.reduce((sum, w) => sum + (w.executionCount || 0), 0)}
            </div>
          </CardContent>
        </Card>
      </div>
      
      <Card className="w-full">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Asset Automation</CardTitle>
              <CardDescription>
                Automate your asset management processes
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="templates">Templates</TabsTrigger>
              <TabsTrigger value="recent">Recent Workflows</TabsTrigger>
            </TabsList>
            
            <TabsContent value="overview" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Getting Started</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p>
                      Asset Automation allows you to create workflows to automate your asset management processes.
                      Get started by creating a new workflow or using one of our templates.
                    </p>
                    <div className="flex space-x-2">
                      <Button onClick={handleCreateWorkflow}>
                        <Plus className="h-4 w-4 mr-1" />
                        Create Workflow
                      </Button>
                      <Button variant="outline" onClick={() => setActiveTab('templates')}>
                        View Templates
                      </Button>
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Quick Actions</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-2">
                      <Button variant="outline" className="justify-start" onClick={handleViewAllWorkflows}>
                        <Workflow className="h-4 w-4 mr-2" />
                        View All Workflows
                      </Button>
                      <Button variant="outline" className="justify-start" onClick={() => router.push('/admin/asset-automation/schedules')}>
                        <Clock className="h-4 w-4 mr-2" />
                        Scheduled Tasks
                      </Button>
                      <Button variant="outline" className="justify-start" onClick={() => router.push('/admin/asset-automation/analytics')}>
                        <BarChart3 className="h-4 w-4 mr-2" />
                        Analytics
                      </Button>
                      <Button variant="outline" className="justify-start" onClick={() => router.push('/admin/asset-automation/settings')}>
                        <Settings className="h-4 w-4 mr-2" />
                        Settings
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
              
              {error && (
                <Card className="border-red-200 bg-red-50">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-red-700">Error</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center space-x-2 text-red-700">
                      <AlertTriangle className="h-5 w-5" />
                      <p>{error.message || 'An error occurred while loading workflows'}</p>
                    </div>
                    <Button variant="outline" onClick={handleRefresh} className="mt-2">
                      <RefreshCw className="h-4 w-4 mr-1" />
                      Retry
                    </Button>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
            
            <TabsContent value="templates">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {assetWorkflowTemplates.map((template) => (
                  <Card key={template.id} className="overflow-hidden">
                    <CardHeader className="pb-2">
                      <CardTitle>{template.name}</CardTitle>
                      <CardDescription>
                        {template.description}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="bg-muted rounded-md p-4 h-32 flex items-center justify-center">
                        <div className="text-center">
                          <Workflow className="h-8 w-8 mx-auto text-muted-foreground/50 mb-2" />
                          <p className="text-xs text-muted-foreground">
                            {template.nodes.length} nodes, {template.edges.length} connections
                          </p>
                        </div>
                      </div>
                    </CardContent>
                    <CardFooter className="bg-muted/50">
                      <Button 
                        variant="outline" 
                        className="w-full"
                        onClick={() => handleUseTemplate(template.id)}
                      >
                        Use Template
                      </Button>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            </TabsContent>
            
            <TabsContent value="recent">
              {recentWorkflows.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Workflow className="h-12 w-12 mx-auto text-muted-foreground/50 mb-2" />
                  <p>No workflows found</p>
                  <p className="text-sm">Create your first workflow to get started</p>
                  <Button onClick={handleCreateWorkflow} className="mt-2">
                    <Plus className="h-4 w-4 mr-1" />
                    Create Workflow
                  </Button>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {recentWorkflows.map((workflow) => (
                    <Card key={workflow.id} className="overflow-hidden">
                      <CardHeader className="pb-2">
                        <CardTitle>{workflow.name}</CardTitle>
                        <CardDescription>
                          {workflow.description || 'No description'}
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="bg-muted rounded-md p-4 h-32 flex items-center justify-center">
                          <div className="text-center">
                            <Workflow className="h-8 w-8 mx-auto text-muted-foreground/50 mb-2" />
                            <p className="text-xs text-muted-foreground">
                              {workflow.nodes.length} nodes, {workflow.edges.length} connections
                            </p>
                          </div>
                        </div>
                      </CardContent>
                      <CardFooter className="bg-muted/50 flex justify-between">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleViewWorkflow(workflow.id)}
                        >
                          View
                        </Button>
                        <Button 
                          size="sm"
                          onClick={() => {
                            // Execute workflow
                            router.push(`/api/admin/asset-automation/workflows/${workflow.id}/execute`)
                          }}
                        >
                          <Play className="h-4 w-4 mr-1" />
                          Execute
                        </Button>
                      </CardFooter>
                    </Card>
                  ))}
                  
                  {workflows.length > 5 && (
                    <Card className="flex flex-col justify-center items-center p-6">
                      <Workflow className="h-12 w-12 text-muted-foreground/50 mb-4" />
                      <p className="text-muted-foreground mb-4">
                        View all {workflows.length} workflows
                      </p>
                      <Button onClick={handleViewAllWorkflows}>
                        View All
                      </Button>
                    </Card>
                  )}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}