"use client"

import { useState, useEffect } from "react"
import { useAdminHeader } from "@/hooks/use-admin-header"
import { getAdvancedAIHeaderConfig } from "@/lib/utils/admin-header-configs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Input } from "@/components/ui/input"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import {
  Brain,
  Lightbulb,
  TrendingUp,
  AlertTriangle,
  Target,
  Zap,
  Search,
  Sparkles,
  BotIcon as Robot,
  Activity,
  Eye,
  ThumbsUp,
} from "lucide-react"
import { AIEngineService } from "@/lib/advanced-features/ai-engine/services"
import type { AIInsight } from "@/lib/advanced-features/ai-engine/types"

export default function AIInsightsPage() {
  // Set up the header for this page
  useAdminHeader(getAdvancedAIHeaderConfig)

  const [insights, setInsights] = useState<AIInsight[]>([])
  const [nlpQuery, setNlpQuery] = useState("")
  const [nlpResponse, setNlpResponse] = useState<any>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState("all")

  useEffect(() => {
    loadInsights()
  }, [])

  const loadInsights = async () => {
    const aiService = AIEngineService.getInstance()

    // Generate sample insights
    await aiService.generateMaintenancePredictions("AST-001")
    await aiService.generateInventoryOptimization()
    await aiService.detectAnomalies("maintenance", [])
    await aiService.generateSmartRecommendations("general")

    const allInsights = aiService.getInsights()
    setInsights(allInsights)
  }

  const handleNLPQuery = async () => {
    if (!nlpQuery.trim()) return

    setIsProcessing(true)
    try {
      const aiService = AIEngineService.getInstance()
      const response = await aiService.processNaturalLanguageQuery(nlpQuery)
      setNlpResponse(response)
    } catch (error) {
      console.error("NLP Query failed:", error)
    } finally {
      setIsProcessing(false)
    }
  }

  const getInsightIcon = (type: string) => {
    switch (type) {
      case "prediction":
        return <Brain className="h-5 w-5" />
      case "recommendation":
        return <Lightbulb className="h-5 w-5" />
      case "anomaly":
        return <AlertTriangle className="h-5 w-5" />
      case "optimization":
        return <Target className="h-5 w-5" />
      default:
        return <Sparkles className="h-5 w-5" />
    }
  }

  const getImpactBadge = (impact: string) => {
    switch (impact) {
      case "critical":
        return <Badge variant="destructive">Critical</Badge>
      case "high":
        return <Badge className="bg-orange-100 text-orange-800 hover:bg-orange-100">High</Badge>
      case "medium":
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Medium</Badge>
      case "low":
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">Low</Badge>
      default:
        return <Badge variant="secondary">{impact}</Badge>
    }
  }

  const filteredInsights =
    selectedCategory === "all" ? insights : insights.filter((insight) => insight.category === selectedCategory)

  return (
    <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight flex items-center">
            <Brain className="mr-3 h-8 w-8 text-primary" />
            AI-Powered Insights
          </h2>
          <p className="text-muted-foreground">Intelligent analytics and predictive recommendations</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">
            <Activity className="mr-2 h-4 w-4" />
            Train Models
          </Button>
          <Button onClick={loadInsights}>
            <Zap className="mr-2 h-4 w-4" />
            Generate Insights
          </Button>
        </div>
      </div>

      <Tabs defaultValue="insights" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="insights">Insights</TabsTrigger>
          <TabsTrigger value="nlp">AI Assistant</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="insights" className="space-y-6">
          {/* AI Insights Overview */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card className="relative overflow-hidden">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Insights</CardTitle>
                <Brain className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{insights.length}</div>
                <p className="text-xs text-muted-foreground">Generated this week</p>
                <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-purple-500 to-pink-500" />
              </CardContent>
            </Card>

            <Card className="relative overflow-hidden">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">High Impact</CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-600">
                  {insights.filter((i) => i.impact === "high" || i.impact === "critical").length}
                </div>
                <p className="text-xs text-muted-foreground">Require attention</p>
                <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-orange-500 to-red-500" />
              </CardContent>
            </Card>

            <Card className="relative overflow-hidden">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Actionable</CardTitle>
                <Target className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{insights.filter((i) => i.actionable).length}</div>
                <p className="text-xs text-muted-foreground">Ready to implement</p>
                <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-green-500 to-emerald-500" />
              </CardContent>
            </Card>

            <Card className="relative overflow-hidden">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Avg Confidence</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {insights.length > 0
                    ? Math.round((insights.reduce((acc, i) => acc + i.confidence, 0) / insights.length) * 100)
                    : 0}
                  %
                </div>
                <Progress
                  value={
                    insights.length > 0
                      ? (insights.reduce((acc, i) => acc + i.confidence, 0) / insights.length) * 100
                      : 0
                  }
                  className="mt-2"
                />
                <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-cyan-500" />
              </CardContent>
            </Card>
          </div>

          {/* Insights List */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>AI Insights Dashboard</CardTitle>
                  <CardDescription>Intelligent recommendations and predictions</CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="px-3 py-2 border rounded-md"
                  >
                    <option value="all">All Categories</option>
                    <option value="maintenance">Maintenance</option>
                    <option value="inventory">Inventory</option>
                    <option value="financial">Financial</option>
                  </select>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[600px]">
                <div className="space-y-4">
                  {filteredInsights.map((insight) => (
                    <div key={insight.id} className="p-6 border rounded-lg hover:bg-muted/50 transition-colors">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center space-x-3">
                          <div className="p-2 bg-primary/10 rounded-lg">{getInsightIcon(insight.type)}</div>
                          <div>
                            <h3 className="font-semibold text-lg">{insight.title}</h3>
                            <p className="text-sm text-muted-foreground capitalize">
                              {insight.type} • {insight.category}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {getImpactBadge(insight.impact)}
                          <Badge variant="outline">{Math.round(insight.confidence * 100)}% confidence</Badge>
                        </div>
                      </div>

                      <p className="text-muted-foreground mb-4">{insight.description}</p>

                      {insight.actionable && insight.suggestedActions.length > 0 && (
                        <div className="mb-4">
                          <h4 className="font-medium mb-2">Suggested Actions:</h4>
                          <ul className="space-y-1">
                            {insight.suggestedActions.map((action, index) => (
                              <li key={index} className="flex items-center text-sm">
                                <div className="w-2 h-2 bg-primary rounded-full mr-2" />
                                {action}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                          <span>Generated: {new Date(insight.createdAt).toLocaleDateString()}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button size="sm" variant="outline">
                            <Eye className="mr-2 h-4 w-4" />
                            Details
                          </Button>
                          {insight.actionable && (
                            <Button size="sm">
                              <Zap className="mr-2 h-4 w-4" />
                              Take Action
                            </Button>
                          )}
                          <Button size="sm" variant="ghost">
                            <ThumbsUp className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="nlp" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Robot className="mr-2 h-5 w-5" />
                AI Assistant
              </CardTitle>
              <CardDescription>Natural language interface for data queries and insights</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex space-x-2">
                <Input
                  placeholder="Ask me anything about your assets, maintenance, or inventory..."
                  value={nlpQuery}
                  onChange={(e) => setNlpQuery(e.target.value)}
                  onKeyPress={(e) => e.key === "Enter" && handleNLPQuery()}
                  className="flex-1"
                />
                <Button onClick={handleNLPQuery} disabled={isProcessing}>
                  {isProcessing ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary" />
                  ) : (
                    <Search className="h-4 w-4" />
                  )}
                </Button>
              </div>

              {nlpResponse && (
                <div className="space-y-4">
                  <div className="p-4 bg-muted rounded-lg">
                    <div className="flex items-start space-x-3">
                      <Avatar className="h-8 w-8">
                        <AvatarFallback>AI</AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <p className="font-medium">AI Assistant</p>
                        <p className="text-sm text-muted-foreground mt-1">{nlpResponse.response}</p>
                        <div className="flex items-center space-x-2 mt-2">
                          <Badge variant="outline">Intent: {nlpResponse.intent}</Badge>
                          <Badge variant="outline">Confidence: {Math.round(nlpResponse.confidence * 100)}%</Badge>
                        </div>
                      </div>
                    </div>
                  </div>

                  {nlpResponse.results && nlpResponse.results.length > 0 && (
                    <div className="space-y-2">
                      <h4 className="font-medium">Results:</h4>
                      {nlpResponse.results.map((result: any, index: number) => (
                        <div key={index} className="p-3 border rounded-lg">
                          <div className="flex items-center justify-between">
                            <span className="font-medium">{result.name || result.task}</span>
                            <Badge>{result.status}</Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              <div className="grid grid-cols-2 gap-2">
                {[
                  "Show me all critical assets",
                  "How many maintenance tasks are overdue?",
                  "What's our inventory turnover rate?",
                  "Find assets with high failure risk",
                ].map((suggestion, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    onClick={() => setNlpQuery(suggestion)}
                    className="text-left justify-start h-auto p-2"
                  >
                    <span className="text-xs">{suggestion}</span>
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>AI Analytics</CardTitle>
              <CardDescription>Performance metrics and usage statistics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[500px] flex items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg">
                <div className="text-center">
                  <Activity className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                  <p className="text-lg text-muted-foreground mb-2">AI Performance Analytics</p>
                  <p className="text-sm text-muted-foreground">Track AI system performance and insights</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
