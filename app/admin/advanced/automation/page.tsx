"use client"

import { useState } from "react"
import { useAdminHeader } from "@/hooks/use-admin-header"
import { getAdvancedAutomationHeaderConfig } from "@/lib/utils/admin-header-configs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Progress } from "@/components/ui/progress"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Settings, Plus, Bot, Workflow, Play, CheckCircle, Calendar, BarChart3, Cog, Zap, Edit, Trash2 } from "lucide-react"
import { FlowEditor } from "@/components/automation/flow-editor"
import { useFlowEditorStore } from "@/lib/advanced-features/automation/flow-store"
import { FlowWorkflowDefinition } from "@/lib/advanced-features/automation/types"

export default function AutomationPage() {
  // Set up the header for this page
  useAdminHeader(getAdvancedAutomationHeaderConfig)

  const [workflows] = useState([
    {
      id: "1",
      name: "Asset Maintenance Alert",
      description: "Automatically create maintenance tasks when asset utilization exceeds threshold",
      isActive: true,
      executionCount: 15,
      lastExecuted: new Date().toISOString(),
      createdAt: new Date().toISOString(),
      createdBy: "admin",
    },
    {
      id: "2",
      name: "Inventory Reorder Automation",
      description: "Automatically reorder inventory when stock falls below minimum threshold",
      isActive: true,
      executionCount: 8,
      lastExecuted: new Date().toISOString(),
      createdAt: new Date().toISOString(),
      createdBy: "admin",
    },
  ])

  const [showFlowEditor, setShowFlowEditor] = useState(false)
  const [selectedWorkflowId, setSelectedWorkflowId] = useState<string | null>(null)
  const { setWorkflow } = useFlowEditorStore()

  const handleCreateWorkflow = () => {
    // Create a new empty workflow
    const newWorkflow: FlowWorkflowDefinition = {
      id: `workflow-${Date.now()}`,
      name: "New Workflow",
      description: "Drag and drop nodes to build your automation",
      isActive: false,
      createdBy: "admin",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      executionCount: 0,
      nodes: [],
      edges: [],
      viewport: { x: 0, y: 0, zoom: 1 },
      variables: [],
      webhooks: [],
      triggers: []
    }
    
    setWorkflow(newWorkflow)
    setSelectedWorkflowId(newWorkflow.id)
    setShowFlowEditor(true)
  }

  const handleEditWorkflow = (workflowId: string) => {
    // In a real app, this would load the workflow from the API
    setSelectedWorkflowId(workflowId)
    setShowFlowEditor(true)
  }

  const handleSaveWorkflow = async (workflow: FlowWorkflowDefinition) => {
    try {
      const response = await fetch('/api/workflows', {
        method: workflow.createdAt === workflow.updatedAt ? 'POST' : 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(workflow)
      })
      
      if (response.ok) {
        console.log('Workflow saved successfully')
        // Refresh workflows list in real app
      }
    } catch (error) {
      console.error('Failed to save workflow:', error)
    }
  }

  const handleExecuteWorkflow = async (workflow: FlowWorkflowDefinition) => {
    try {
      const response = await fetch('/api/workflows/execute', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ workflowId: workflow.id })
      })
      
      if (response.ok) {
        const result = await response.json()
        console.log('Workflow executed:', result)
      }
    } catch (error) {
      console.error('Failed to execute workflow:', error)
    }
  }

  const metrics = {
    totalWorkflows: workflows.length,
    activeWorkflows: workflows.filter((w) => w.isActive).length,
    successfulExecutions: 20,
    recentExecutions: 23,
  }

  return (
    <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">

      <Tabs defaultValue="workflows" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="workflows">Workflows</TabsTrigger>
          <TabsTrigger value="rules">Rules</TabsTrigger>
          <TabsTrigger value="schedules">Schedules</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="workflows" className="space-y-6">
          {/* Workflow Stats */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card className="relative overflow-hidden">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Workflows</CardTitle>
                <Workflow className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.totalWorkflows}</div>
                <p className="text-xs text-muted-foreground">Automation workflows</p>
                <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-purple-500" />
              </CardContent>
            </Card>

            <Card className="relative overflow-hidden">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Workflows</CardTitle>
                <Play className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{metrics.activeWorkflows}</div>
                <p className="text-xs text-muted-foreground">Currently running</p>
                <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-green-500 to-emerald-500" />
              </CardContent>
            </Card>

            <Card className="relative overflow-hidden">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
                <CheckCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {Math.round((metrics.successfulExecutions / metrics.recentExecutions) * 100)}%
                </div>
                <Progress value={(metrics.successfulExecutions / metrics.recentExecutions) * 100} className="mt-2" />
                <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-green-500 to-blue-500" />
              </CardContent>
            </Card>

            <Card className="relative overflow-hidden">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Executions</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.recentExecutions}</div>
                <p className="text-xs text-muted-foreground">This week</p>
                <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-orange-500 to-red-500" />
              </CardContent>
            </Card>
          </div>

          {/* Workflows List */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Automation Workflows</CardTitle>
                  <CardDescription>Manage your automated processes and workflows</CardDescription>
                </div>
                <Button onClick={handleCreateWorkflow}>
                  <Plus className="mr-2 h-4 w-4" />
                  New Workflow
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {workflows.map((workflow) => (
                  <div key={workflow.id} className="p-6 border rounded-lg hover:bg-muted/50 transition-colors">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-primary/10 rounded-lg">
                          <Workflow className="h-5 w-5" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-lg">{workflow.name}</h3>
                          <p className="text-sm text-muted-foreground">{workflow.description}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant={workflow.isActive ? "default" : "secondary"}>
                          {workflow.isActive ? "Active" : "Inactive"}
                        </Badge>
                        <Switch checked={workflow.isActive} />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-4">
                      <div>
                        <p className="text-sm font-medium">Executions</p>
                        <p className="text-sm text-muted-foreground">{workflow.executionCount} total</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Last Run</p>
                        <p className="text-sm text-muted-foreground">
                          {new Date(workflow.lastExecuted).toLocaleDateString()}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Created By</p>
                        <p className="text-sm text-muted-foreground">{workflow.createdBy}</p>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                        <span>Created: {new Date(workflow.createdAt).toLocaleDateString()}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => handleEditWorkflow(workflow.id)}
                        >
                          <Edit className="mr-2 h-4 w-4" />
                          Edit Flow
                        </Button>
                        <Button size="sm" variant="outline">
                          <Play className="mr-2 h-4 w-4" />
                          Test Run
                        </Button>
                        <Button size="sm">
                          <BarChart3 className="mr-2 h-4 w-4" />
                          Analytics
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="rules" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Automation Rules</CardTitle>
              <CardDescription>Simple event-based automation rules</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px] flex items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg">
                <div className="text-center">
                  <Cog className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                  <p className="text-lg text-muted-foreground mb-2">Automation Rules</p>
                  <p className="text-sm text-muted-foreground">Configure simple if-then automation rules</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="schedules" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Smart Schedules</CardTitle>
              <CardDescription>Automated scheduling and recurring tasks</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px] flex items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg">
                <div className="text-center">
                  <Calendar className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                  <p className="text-lg text-muted-foreground mb-2">Smart Scheduling</p>
                  <p className="text-sm text-muted-foreground">Intelligent scheduling and recurring automation</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Automation Analytics</CardTitle>
              <CardDescription>Performance metrics and insights</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px] flex items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg">
                <div className="text-center">
                  <BarChart3 className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                  <p className="text-lg text-muted-foreground mb-2">Automation Analytics</p>
                  <p className="text-sm text-muted-foreground">Track automation performance and ROI</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Flow Editor Dialog */}
      <Dialog open={showFlowEditor} onOpenChange={setShowFlowEditor}>
        <DialogContent className="max-w-7xl h-[90vh] p-0">
          <DialogHeader className="p-6 pb-2">
            <DialogTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-blue-500" />
              Flow Editor
              {selectedWorkflowId && (
                <Badge variant="outline" className="ml-2">
                  {selectedWorkflowId}
                </Badge>
              )}
            </DialogTitle>
          </DialogHeader>
          
          <div className="flex-1 h-full">
            <FlowEditor
              workflowId={selectedWorkflowId || undefined}
              onSave={handleSaveWorkflow}
              onExecute={handleExecuteWorkflow}
              className="h-full"
            />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
