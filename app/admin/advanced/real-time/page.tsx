"use client"

import { useState, useEffect } from "react"
import { useAdminHeader } from "@/hooks/use-admin-header"
import { getAdvancedRealTimeHeaderConfig } from "@/lib/utils/admin-header-configs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Textarea } from "@/components/ui/textarea"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Switch } from "@/components/ui/switch"
import {
  Bell,
  Users,
  MessageCircle,
  Zap,
  Send,
  Settings,
  Eye,
  Clock,
  CheckCircle,
  AlertCircle,
  Globe,
  Wifi,
} from "lucide-react"
import { RealtimeService } from "@/lib/advanced-features/real-time/services"
import type { Notification, CollaborationSession, Comment } from "@/lib/advanced-features/real-time/types"

export default function RealtimePage() {
  // Set up the header for this page
  useAdminHeader(getAdvancedRealTimeHeaderConfig)

  const [notifications, setNotifications] = useState<Notification[]>([])
  const [sessions, setSessions] = useState<CollaborationSession[]>([])
  const [comments, setComments] = useState<Comment[]>([])
  const [newComment, setNewComment] = useState("")
  const [isConnected, setIsConnected] = useState(true)
  const [activeUsers, setActiveUsers] = useState(12)

  useEffect(() => {
    loadRealtimeData()

    const interval = setInterval(() => {
      setIsConnected(Math.random() > 0.1)
      setActiveUsers((prev) => Math.max(1, prev + Math.floor(Math.random() * 6 - 3)))
    }, 5000)

    return () => clearInterval(interval)
  }, [])

  const loadRealtimeData = async () => {
    const realtimeService = RealtimeService.getInstance()

    // Generate sample notifications
    await realtimeService.createNotification({
      userId: "user-1",
      title: "Maintenance Alert",
      message: "Asset AST-001 requires immediate attention",
      type: "warning",
      category: "maintenance",
      actionUrl: "/assets/AST-001",
      actionText: "View Asset",
    })

    await realtimeService.createNotification({
      userId: "user-1",
      title: "Inventory Low Stock",
      message: "Item ITM-002 is below minimum threshold",
      type: "error",
      category: "inventory",
    })

    // Start collaboration session
    await realtimeService.startCollaborationSession("AST-001", "asset", "user-1", "John Doe")

    // Add sample comment
    await realtimeService.addComment({
      entityId: "AST-001",
      entityType: "asset",
      userId: "user-1",
      userName: "John Doe",
      content: "This asset needs immediate inspection based on the latest sensor readings.",
      mentions: [],
      attachments: [],
    })

    // Load data
    setNotifications(realtimeService.getNotifications("user-1"))
    const session = realtimeService.getCollaborationSession("AST-001", "asset")
    if (session) setSessions([session])
    setComments(realtimeService.getComments("AST-001", "asset"))
  }

  const handleSendComment = async () => {
    if (!newComment.trim()) return

    const realtimeService = RealtimeService.getInstance()
    await realtimeService.addComment({
      entityId: "AST-001",
      entityType: "asset",
      userId: "user-1",
      userName: "Current User",
      content: newComment,
      mentions: [],
      attachments: [],
    })

    setComments(realtimeService.getComments("AST-001", "asset"))
    setNewComment("")
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "error":
        return <AlertCircle className="h-4 w-4 text-red-500" />
      case "warning":
        return <AlertCircle className="h-4 w-4 text-yellow-500" />
      case "success":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      default:
        return <Bell className="h-4 w-4 text-blue-500" />
    }
  }

  return (
    <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight flex items-center">
            <Zap className="mr-3 h-8 w-8 text-primary" />
            Real-time Collaboration
          </h2>
          <p className="text-muted-foreground">Live updates, notifications, and team collaboration</p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Wifi className="h-4 w-4 text-green-500" />
            <span className="text-sm text-muted-foreground">{isConnected ? "Connected" : "Disconnected"}</span>
          </div>
          <div className="flex items-center space-x-2">
            <Globe className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">{activeUsers} active users</span>
          </div>
        </div>
      </div>

      <Tabs defaultValue="notifications" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="collaboration">Collaboration</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="notifications" className="space-y-6">
          {/* Notification Stats */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card className="relative overflow-hidden">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Notifications</CardTitle>
                <Bell className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{notifications.length}</div>
                <p className="text-xs text-muted-foreground">Last 24 hours</p>
                <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-purple-500" />
              </CardContent>
            </Card>

            <Card className="relative overflow-hidden">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Unread</CardTitle>
                <AlertCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">{notifications.filter((n) => !n.read).length}</div>
                <p className="text-xs text-muted-foreground">Require attention</p>
                <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-red-500 to-orange-500" />
              </CardContent>
            </Card>

            <Card className="relative overflow-hidden">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Critical Alerts</CardTitle>
                <AlertCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-600">
                  {notifications.filter((n) => n.type === "error").length}
                </div>
                <p className="text-xs text-muted-foreground">High priority</p>
                <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-orange-500 to-red-500" />
              </CardContent>
            </Card>

            <Card className="relative overflow-hidden">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Response Time</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">2.3m</div>
                <p className="text-xs text-muted-foreground">Average response</p>
                <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-green-500 to-emerald-500" />
              </CardContent>
            </Card>
          </div>

          {/* Notifications List */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Live Notifications</CardTitle>
                  <CardDescription>Real-time alerts and updates</CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm">
                    Mark All Read
                  </Button>
                  <Button size="sm">
                    <Settings className="mr-2 h-4 w-4" />
                    Configure
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[500px]">
                <div className="space-y-4">
                  {notifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={`p-4 border rounded-lg transition-colors ${
                        !notification.read ? "bg-blue-50 border-blue-200" : "hover:bg-muted/50"
                      }`}
                    >
                      <div className="flex items-start space-x-3">
                        <div className="mt-1">{getNotificationIcon(notification.type)}</div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between mb-1">
                            <h4 className="font-medium text-sm">{notification.title}</h4>
                            <div className="flex items-center space-x-2">
                              {!notification.read && <div className="w-2 h-2 bg-blue-500 rounded-full" />}
                              <span className="text-xs text-muted-foreground">
                                {new Date(notification.createdAt).toLocaleTimeString()}
                              </span>
                            </div>
                          </div>
                          <p className="text-sm text-muted-foreground mb-2">{notification.message}</p>
                          <div className="flex items-center justify-between">
                            <Badge variant="outline" className="text-xs">
                              {notification.category}
                            </Badge>
                            {notification.actionUrl && (
                              <Button size="sm" variant="outline">
                                <Eye className="mr-2 h-3 w-3" />
                                {notification.actionText || "View"}
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="collaboration" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Active Sessions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="mr-2 h-5 w-5" />
                  Active Sessions
                </CardTitle>
                <CardDescription>Live collaboration sessions</CardDescription>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[300px]">
                  <div className="space-y-4">
                    {sessions.map((session) => (
                      <div key={session.id} className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between mb-3">
                          <h4 className="font-medium">Asset {session.entityId}</h4>
                          <Badge className="bg-green-100 text-green-800">
                            {session.participants.filter((p) => p.isActive).length} active
                          </Badge>
                        </div>
                        <div className="flex -space-x-2 mb-3">
                          {session.participants.slice(0, 4).map((participant) => (
                            <Avatar key={participant.userId} className="h-8 w-8 border-2 border-background">
                              <AvatarFallback className="text-xs">
                                {participant.userName
                                  .split(" ")
                                  .map((n) => n[0])
                                  .join("")}
                              </AvatarFallback>
                            </Avatar>
                          ))}
                        </div>
                        <div className="flex items-center justify-between text-sm text-muted-foreground">
                          <span>Started: {new Date(session.startedAt).toLocaleTimeString()}</span>
                          <Button size="sm" variant="outline">
                            <Eye className="mr-2 h-3 w-3" />
                            Join
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>

            {/* Comments */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MessageCircle className="mr-2 h-5 w-5" />
                  Live Comments
                </CardTitle>
                <CardDescription>Real-time discussions</CardDescription>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[200px] mb-4">
                  <div className="space-y-3">
                    {comments.map((comment) => (
                      <div key={comment.id} className="flex space-x-3">
                        <Avatar className="h-8 w-8">
                          <AvatarFallback className="text-xs">
                            {comment.userName
                              .split(" ")
                              .map((n) => n[0])
                              .join("")}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <span className="font-medium text-sm">{comment.userName}</span>
                            <span className="text-xs text-muted-foreground">
                              {new Date(comment.createdAt).toLocaleTimeString()}
                            </span>
                          </div>
                          <p className="text-sm">{comment.content}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
                <div className="flex space-x-2">
                  <Textarea
                    placeholder="Add a comment..."
                    value={newComment}
                    onChange={(e) => setNewComment(e.target.value)}
                    className="min-h-[60px]"
                  />
                  <Button onClick={handleSendComment} size="sm">
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Notification Preferences</CardTitle>
              <CardDescription>Configure your real-time notification settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Email Notifications</h4>
                    <p className="text-sm text-muted-foreground">Receive notifications via email</p>
                  </div>
                  <Switch defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Push Notifications</h4>
                    <p className="text-sm text-muted-foreground">Browser push notifications</p>
                  </div>
                  <Switch defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">In-App Notifications</h4>
                    <p className="text-sm text-muted-foreground">Show notifications within the app</p>
                  </div>
                  <Switch defaultChecked />
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-medium">Notification Categories</h4>
                <div className="grid grid-cols-2 gap-4">
                  {["Maintenance", "Inventory", "Leasing", "System"].map((category) => (
                    <div key={category} className="flex items-center justify-between">
                      <span className="text-sm">{category}</span>
                      <Switch defaultChecked />
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
