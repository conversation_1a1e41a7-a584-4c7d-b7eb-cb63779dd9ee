"use client"

import { useState, useEffect } from "react"
import { useAdminHeader } from "@/hooks/use-admin-header"
import { getStorefrontHeaderConfig } from "@/lib/utils/admin-header-configs"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Separator } from "@/components/ui/separator"
import { ShoppingCart, Search, Star, CreditCard, Plus, Minus, Heart, Share2, Truck, Shield, Award } from "lucide-react"
import { ecommerceService } from "@/lib/modules/ecommerce/services"
import type { EcommerceProduct, ShoppingCart as Cart } from "@/lib/modules/ecommerce/types"

export default function StorefrontPage() {
  const [products, setProducts] = useState<EcommerceProduct[]>([])
  const [cart, setCart] = useState<Cart | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState<string>("all")
  const [priceRange, setPriceRange] = useState<string>("all")
  const [selectedProduct, setSelectedProduct] = useState<EcommerceProduct | null>(null)
  const [loading, setLoading] = useState(true)

  // Set up the header for this page
  useAdminHeader(getStorefrontHeaderConfig)

  // Mock customer ID - in real app this would come from auth
  const customerId = "cust-001"

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)
      const [productsData, cartData] = await Promise.all([
        Promise.resolve(ecommerceService.getProducts({ status: "active" })),
        ecommerceService.getCart(customerId),
      ])

      setProducts(productsData)
      setCart(cartData)
    } catch (error) {
      console.error("Failed to load storefront data:", error)
    } finally {
      setLoading(false)
    }
  }

  const addToCart = async (productId: string, quantity = 1) => {
    try {
      const updatedCart = await ecommerceService.addToCart(customerId, productId, quantity)
      setCart(updatedCart)
    } catch (error) {
      console.error("Failed to add to cart:", error)
    }
  }

  const updateCartQuantity = async (itemId: string, quantity: number) => {
    try {
      if (quantity <= 0) {
        const updatedCart = await ecommerceService.removeFromCart(customerId, itemId)
        setCart(updatedCart)
      } else {
        const updatedCart = await ecommerceService.updateCartItem(customerId, itemId, quantity)
        setCart(updatedCart)
      }
    } catch (error) {
      console.error("Failed to update cart:", error)
    }
  }

  const filteredProducts = products.filter((product) => {
    const matchesSearch =
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === "all" || product.category === selectedCategory

    let matchesPrice = true
    if (priceRange !== "all") {
      const [min, max] = priceRange.split("-").map(Number)
      matchesPrice = product.pricing.basePrice >= min && (!max || product.pricing.basePrice <= max)
    }

    return matchesSearch && matchesCategory && matchesPrice
  })

  const categories = [...new Set(products.map((p) => p.category))]
  const cartItemCount = cart?.items.reduce((sum, item) => sum + item.quantity, 0) || 0

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {[...Array(8)].map((_, i) => (
              <Card key={i} className="overflow-hidden">
                <div className="h-48 bg-muted animate-pulse" />
                <CardContent className="p-4">
                  <div className="h-4 bg-muted animate-pulse rounded mb-2" />
                  <div className="h-3 bg-muted animate-pulse rounded mb-4" />
                  <div className="h-6 bg-muted animate-pulse rounded" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-white border-b sticky top-0 z-40">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-blue-600">WizeAssets Store</h1>
              <Badge variant="secondary" className="hidden sm:inline-flex">
                Marketplace
              </Badge>
            </div>

            <div className="flex items-center space-x-4">
              <div className="hidden md:flex items-center space-x-2 text-sm text-muted-foreground">
                <CreditCard className="h-4 w-4 text-blue-500" />
                <span>Pay with Credits</span>
              </div>

              <Dialog>
                <DialogTrigger asChild>
                  <Button variant="outline" size="sm" className="relative">
                    <ShoppingCart className="h-4 w-4 mr-2" />
                    Cart
                    {cartItemCount > 0 && (
                      <Badge className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs">
                        {cartItemCount}
                      </Badge>
                    )}
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-md">
                  <DialogHeader>
                    <DialogTitle>Shopping Cart</DialogTitle>
                    <DialogDescription>{cartItemCount} items in your cart</DialogDescription>
                  </DialogHeader>

                  {cart && cart.items.length > 0 ? (
                    <div className="space-y-4">
                      {cart.items.map((item) => {
                        const product = products.find((p) => p.id === item.productId)
                        return (
                          <div key={item.id} className="flex items-center space-x-3 py-2">
                            <div className="h-12 w-12 bg-muted rounded-md flex items-center justify-center">
                              <ShoppingCart className="h-6 w-6 text-muted-foreground" />
                            </div>
                            <div className="flex-1">
                              <div className="font-medium text-sm">{product?.name}</div>
                              <div className="text-xs text-muted-foreground">
                                ${item.unitPrice} • {item.unitCredits} credits
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => updateCartQuantity(item.id, item.quantity - 1)}
                              >
                                <Minus className="h-3 w-3" />
                              </Button>
                              <span className="text-sm font-medium w-8 text-center">{item.quantity}</span>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => updateCartQuantity(item.id, item.quantity + 1)}
                              >
                                <Plus className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                        )
                      })}

                      <Separator />

                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Subtotal:</span>
                          <span>${cart.subtotal.toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between text-sm text-blue-600">
                          <span>Credit Discount:</span>
                          <span>-${cart.creditDiscount.toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Shipping:</span>
                          <span>{cart.shippingCost === 0 ? "FREE" : `$${cart.shippingCost.toFixed(2)}`}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Tax:</span>
                          <span>${cart.taxAmount.toFixed(2)}</span>
                        </div>
                        <Separator />
                        <div className="flex justify-between font-medium">
                          <span>Total:</span>
                          <span>${cart.total.toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between text-sm text-blue-600">
                          <span>Credits Required:</span>
                          <span>{cart.totalCredits.toLocaleString()}</span>
                        </div>
                      </div>

                      <Button className="w-full">Proceed to Checkout</Button>
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <ShoppingCart className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <p className="text-muted-foreground">Your cart is empty</p>
                    </div>
                  )}
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4 mb-8">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search products..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {categories.map((category) => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={priceRange} onValueChange={setPriceRange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Price Range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Prices</SelectItem>
              <SelectItem value="0-500">$0 - $500</SelectItem>
              <SelectItem value="500-1000">$500 - $1,000</SelectItem>
              <SelectItem value="1000-2000">$1,000 - $2,000</SelectItem>
              <SelectItem value="2000-">$2,000+</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Products Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {filteredProducts.map((product) => (
            <Card key={product.id} className="overflow-hidden hover:shadow-lg transition-shadow">
              <div className="relative">
                <div className="h-48 bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
                  <ShoppingCart className="h-16 w-16 text-blue-300" />
                </div>

                <div className="absolute top-2 right-2 flex space-x-1">
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0 bg-white/80 hover:bg-white">
                    <Heart className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0 bg-white/80 hover:bg-white">
                    <Share2 className="h-4 w-4" />
                  </Button>
                </div>

                {product.pricing.salePrice && <Badge className="absolute top-2 left-2 bg-destructive text-destructive-foreground">Sale</Badge>}

                <Badge variant="secondary" className="absolute bottom-2 left-2 capitalize">
                  {product.condition}
                </Badge>
              </div>

              <CardContent className="p-4">
                <div className="space-y-2">
                  <div className="flex items-start justify-between">
                    <h3 className="font-semibold text-sm leading-tight line-clamp-2">{product.name}</h3>
                    <div className="flex items-center space-x-1 ml-2">
                      <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                      <span className="text-xs text-muted-foreground">4.5</span>
                    </div>
                  </div>

                  <p className="text-xs text-muted-foreground line-clamp-2">{product.shortDescription}</p>

                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className="text-xs">
                      {product.brand}
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      {product.category}
                    </Badge>
                  </div>

                  <div className="space-y-1">
                    <div className="flex items-center justify-between">
                      <span className="text-lg font-bold">${product.pricing.basePrice.toLocaleString()}</span>
                      {product.pricing.salePrice && (
                        <span className="text-sm text-muted-foreground line-through">
                          ${product.pricing.salePrice.toLocaleString()}
                        </span>
                      )}
                    </div>

                    <div className="flex items-center space-x-1 text-blue-600">
                      <CreditCard className="h-3 w-3" />
                      <span className="text-sm font-medium">
                        {product.pricing.creditPrice.toLocaleString()} credits
                      </span>
                      <Badge variant="secondary" className="text-xs">
                        {product.pricing.creditDiscount}% off
                      </Badge>
                    </div>
                  </div>

                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <div className="flex items-center space-x-1">
                      <Truck className="h-3 w-3" />
                      <span>Free shipping</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Shield className="h-3 w-3" />
                      <span>
                        {product.warranty.duration}
                        {product.warranty.durationType.charAt(0)} warranty
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between pt-2">
                    <div className="text-xs">
                      <span className="text-muted-foreground">Stock: </span>
                      <span
                        className={
                          product.inventory.availableStock <= product.inventory.lowStockThreshold
                            ? "text-red-600 font-medium"
                            : "text-green-600"
                        }
                      >
                        {product.inventory.availableStock}
                      </span>
                    </div>

                    <div className="flex space-x-2">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button variant="outline" size="sm" onClick={() => setSelectedProduct(product)}>
                            View
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl">
                          {selectedProduct && (
                            <>
                              <DialogHeader>
                                <DialogTitle>{selectedProduct.name}</DialogTitle>
                                <DialogDescription>{selectedProduct.description}</DialogDescription>
                              </DialogHeader>

                              <div className="grid gap-6 md:grid-cols-2">
                                <div className="space-y-4">
                                  <div className="h-64 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg flex items-center justify-center">
                                    <ShoppingCart className="h-24 w-24 text-blue-300" />
                                  </div>

                                  <div className="grid grid-cols-3 gap-2">
                                    {[...Array(3)].map((_, i) => (
                                      <div
                                        key={i}
                                        className="h-16 bg-muted rounded-md flex items-center justify-center"
                                      >
                                        <ShoppingCart className="h-6 w-6 text-muted-foreground" />
                                      </div>
                                    ))}
                                  </div>
                                </div>

                                <div className="space-y-4">
                                  <div>
                                    <div className="flex items-center space-x-2 mb-2">
                                      <Badge variant="outline">{selectedProduct.brand}</Badge>
                                      <Badge variant="outline">{selectedProduct.condition}</Badge>
                                    </div>

                                    <div className="space-y-2">
                                      <div className="flex items-center justify-between">
                                        <span className="text-2xl font-bold">
                                          ${selectedProduct.pricing.basePrice.toLocaleString()}
                                        </span>
                                        <div className="flex items-center space-x-1">
                                          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                                          <span className="text-sm">4.5 (24 reviews)</span>
                                        </div>
                                      </div>

                                      <div className="flex items-center space-x-2 text-blue-600">
                                        <CreditCard className="h-4 w-4" />
                                        <span className="font-medium">
                                          {selectedProduct.pricing.creditPrice.toLocaleString()} credits
                                        </span>
                                        <Badge variant="secondary">{selectedProduct.pricing.creditDiscount}% off</Badge>
                                      </div>
                                    </div>
                                  </div>

                                  <Separator />

                                  <div className="space-y-3">
                                    <h4 className="font-medium">Specifications</h4>
                                    {selectedProduct.specifications.map((spec, index) => (
                                      <div key={index} className="flex justify-between text-sm">
                                        <span className="text-muted-foreground">{spec.name}:</span>
                                        <span className="font-medium">{spec.value}</span>
                                      </div>
                                    ))}
                                  </div>

                                  <Separator />

                                  <div className="space-y-3">
                                    <div className="flex items-center space-x-4 text-sm">
                                      <div className="flex items-center space-x-1">
                                        <Truck className="h-4 w-4 text-green-600" />
                                        <span>Free shipping</span>
                                      </div>
                                      <div className="flex items-center space-x-1">
                                        <Shield className="h-4 w-4 text-blue-600" />
                                        <span>
                                          {selectedProduct.warranty.duration} {selectedProduct.warranty.durationType}{" "}
                                          warranty
                                        </span>
                                      </div>
                                      <div className="flex items-center space-x-1">
                                        <Award className="h-4 w-4 text-purple-600" />
                                        <span>Certified</span>
                                      </div>
                                    </div>

                                    <div className="text-sm">
                                      <span className="text-muted-foreground">Stock: </span>
                                      <span
                                        className={
                                          selectedProduct.inventory.availableStock <=
                                          selectedProduct.inventory.lowStockThreshold
                                            ? "text-red-600 font-medium"
                                            : "text-green-600 font-medium"
                                        }
                                      >
                                        {selectedProduct.inventory.availableStock} available
                                      </span>
                                    </div>
                                  </div>

                                  <Button
                                    className="w-full"
                                    onClick={() => addToCart(selectedProduct.id)}
                                    disabled={selectedProduct.inventory.availableStock === 0}
                                  >
                                    <ShoppingCart className="h-4 w-4 mr-2" />
                                    {selectedProduct.inventory.availableStock === 0 ? "Out of Stock" : "Add to Cart"}
                                  </Button>
                                </div>
                              </div>
                            </>
                          )}
                        </DialogContent>
                      </Dialog>

                      <Button
                        size="sm"
                        onClick={() => addToCart(product.id)}
                        disabled={product.inventory.availableStock === 0}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredProducts.length === 0 && (
          <div className="text-center py-12">
            <ShoppingCart className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No products found</h3>
            <p className="text-muted-foreground">Try adjusting your search or filters</p>
          </div>
        )}
      </div>
    </div>
  )
}
