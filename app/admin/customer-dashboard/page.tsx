"use client"

import { useState, useEffect } from "react"
import { useAdminHeader } from "@/hooks/use-admin-header"
import { getCustomerDashboardHeaderConfig } from "@/lib/utils/admin-header-configs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarInitials } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  CreditCard,
  Package,
  Truck,
  MessageCircle,
  User,
  Settings,
  HelpCircle,
  FileText,
  Download,
  Eye,
  Star,
  Gift,
  TrendingUp,
  Calendar,
  Phone,
  Mail,
  Plus,
} from "lucide-react"
import { ecommerceService } from "@/lib/modules/ecommerce/services"
import type { Customer, Order, CreditTransaction } from "@/lib/modules/ecommerce/types"

export default function CustomerDashboardPage() {
  const [customer, setCustomer] = useState<Customer | null>(null)
  const [orders, setOrders] = useState<Order[]>([])
  const [creditHistory, setCreditHistory] = useState<CreditTransaction[]>([])
  const [loading, setLoading] = useState(true)
  const [supportTicketOpen, setSupportTicketOpen] = useState(false)

  // Set up the header for this page
  useAdminHeader(getCustomerDashboardHeaderConfig)

  // Mock customer ID - in real app this would come from auth
  const customerId = "cust-001"

  useEffect(() => {
    loadCustomerData()
  }, [])

  const loadCustomerData = async () => {
    try {
      setLoading(true)
      const customerData = ecommerceService.getCustomerById(customerId)
      const ordersData = ecommerceService.getOrders(customerId)

      if (customerData) {
        setCustomer(customerData)
        setCreditHistory(customerData.creditHistory)
      }
      setOrders(ordersData)
    } catch (error) {
      console.error("Failed to load customer data:", error)
    } finally {
      setLoading(false)
    }
  }

  if (loading || !customer) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="grid gap-6 md:grid-cols-3">
            <div className="md:col-span-1">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-4">
                    <div className="h-16 w-16 bg-muted animate-pulse rounded-full" />
                    <div className="space-y-2">
                      <div className="h-4 w-24 bg-muted animate-pulse rounded" />
                      <div className="h-3 w-32 bg-muted animate-pulse rounded" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
            <div className="md:col-span-2">
              <div className="grid gap-4 md:grid-cols-2">
                {[...Array(4)].map((_, i) => (
                  <Card key={i}>
                    <CardContent className="p-6">
                      <div className="h-4 w-20 bg-muted animate-pulse rounded mb-2" />
                      <div className="h-8 w-16 bg-muted animate-pulse rounded" />
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  const creditUtilization = (customer.creditBalance / customer.creditLimit) * 100
  const recentOrders = orders.slice(0, 5)
  const recentCredits = creditHistory.slice(0, 5)

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-blue-600">Customer Portal</h1>
              <Badge variant="outline" className="capitalize">
                {customer.tier} Member
              </Badge>
            </div>

            <div className="flex items-center space-x-4">
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>

              <Dialog open={supportTicketOpen} onOpenChange={setSupportTicketOpen}>
                <DialogTrigger asChild>
                  <Button size="sm">
                    <MessageCircle className="h-4 w-4 mr-2" />
                    Support
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Contact Support</DialogTitle>
                    <DialogDescription>How can we help you today?</DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="subject">Subject</Label>
                      <Input id="subject" placeholder="Brief description of your issue" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="category">Category</Label>
                      <select className="w-full p-2 border rounded-md">
                        <option value="order">Order Issue</option>
                        <option value="product">Product Question</option>
                        <option value="account">Account Issue</option>
                        <option value="technical">Technical Support</option>
                        <option value="billing">Billing Question</option>
                      </select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="message">Message</Label>
                      <Textarea id="message" placeholder="Please describe your issue in detail" rows={4} />
                    </div>
                    <div className="flex justify-end space-x-2">
                      <Button variant="outline" onClick={() => setSupportTicketOpen(false)}>
                        Cancel
                      </Button>
                      <Button onClick={() => setSupportTicketOpen(false)}>Submit Ticket</Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="grid gap-6 md:grid-cols-3">
          {/* Profile Sidebar */}
          <div className="md:col-span-1 space-y-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4 mb-6">
                  <Avatar className="h-16 w-16">
                    <AvatarFallback>
                      <AvatarInitials name={`${customer.firstName} ${customer.lastName}`} />
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="text-lg font-semibold">
                      {customer.firstName} {customer.lastName}
                    </h3>
                    <p className="text-sm text-muted-foreground">{customer.email}</p>
                    <Badge variant="outline" className="mt-1 capitalize">
                      {customer.tier} Member
                    </Badge>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center space-x-2 text-sm">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span>{customer.email}</span>
                  </div>
                  {customer.phone && (
                    <div className="flex items-center space-x-2 text-sm">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <span>{customer.phone}</span>
                    </div>
                  )}
                  {customer.company && (
                    <div className="flex items-center space-x-2 text-sm">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <span>{customer.company}</span>
                    </div>
                  )}
                  <div className="flex items-center space-x-2 text-sm">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span>Member since {new Date(customer.registrationDate).toLocaleDateString()}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Credit Balance */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <CreditCard className="h-5 w-5 text-blue-500" />
                  <span>Credit Balance</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600">{customer.creditBalance.toLocaleString()}</div>
                    <div className="text-sm text-muted-foreground">Available Credits</div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Credit Limit:</span>
                      <span className="font-medium">{customer.creditLimit.toLocaleString()}</span>
                    </div>
                    <Progress value={creditUtilization} className="h-2" />
                    <div className="text-xs text-muted-foreground text-center">
                      {creditUtilization.toFixed(1)}% utilized
                    </div>
                  </div>

                  <Button variant="outline" className="w-full">
                    <Plus className="h-4 w-4 mr-2" />
                    Request Credit Increase
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button variant="outline" className="w-full justify-start">
                  <Package className="h-4 w-4 mr-2" />
                  Browse Products
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <FileText className="h-4 w-4 mr-2" />
                  Download Invoices
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <HelpCircle className="h-4 w-4 mr-2" />
                  Help Center
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <MessageCircle className="h-4 w-4 mr-2" />
                  Contact Support
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="md:col-span-2">
            <Tabs defaultValue="overview" className="space-y-6">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="orders">Orders</TabsTrigger>
                <TabsTrigger value="credits">Credits</TabsTrigger>
                <TabsTrigger value="support">Support</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-6">
                {/* Stats Cards */}
                <div className="grid gap-4 md:grid-cols-2">
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
                      <Package className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{customer.orderHistory.length}</div>
                      <p className="text-xs text-muted-foreground">{recentOrders.length} in last 30 days</p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">Total Spent</CardTitle>
                      <TrendingUp className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">
                        ${orders.reduce((sum, order) => sum + order.pricing.total, 0).toLocaleString()}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {orders.reduce((sum, order) => sum + order.pricing.creditsUsed, 0).toLocaleString()} credits
                        used
                      </p>
                    </CardContent>
                  </Card>
                </div>

                {/* Recent Orders */}
                <Card>
                  <CardHeader>
                    <CardTitle>Recent Orders</CardTitle>
                    <CardDescription>Your latest purchases and their status</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {recentOrders.map((order) => (
                        <div key={order.id} className="flex items-center justify-between p-4 border rounded-lg">
                          <div className="flex items-center space-x-4">
                            <div className="h-12 w-12 bg-muted rounded-lg flex items-center justify-center">
                              <Package className="h-6 w-6 text-muted-foreground" />
                            </div>
                            <div>
                              <div className="font-medium">{order.orderNumber}</div>
                              <div className="text-sm text-muted-foreground">
                                {order.items.length} items • ${order.pricing.total.toFixed(2)}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {new Date(order.createdAt).toLocaleDateString()}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-3">
                            <Badge
                              variant={
                                order.status === "delivered"
                                  ? "default"
                                  : order.status === "cancelled"
                                    ? "destructive"
                                    : "secondary"
                              }
                            >
                              {order.status}
                            </Badge>
                            <Button variant="ghost" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Credit Activity */}
                <Card>
                  <CardHeader>
                    <CardTitle>Recent Credit Activity</CardTitle>
                    <CardDescription>Your latest credit transactions</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {recentCredits.map((transaction) => (
                        <div key={transaction.id} className="flex items-center justify-between py-2">
                          <div className="flex items-center space-x-3">
                            <div
                              className={`h-8 w-8 rounded-full flex items-center justify-center ${
                                transaction.type === "purchase"
                                  ? "bg-red-100 text-red-600"
                                  : transaction.type === "refund"
                                    ? "bg-green-100 text-green-600"
                                    : "bg-blue-100 text-blue-600"
                              }`}
                            >
                              {transaction.type === "purchase" ? "-" : "+"}
                            </div>
                            <div>
                              <div className="text-sm font-medium">{transaction.description}</div>
                              <div className="text-xs text-muted-foreground">
                                {new Date(transaction.date).toLocaleDateString()}
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div
                              className={`font-medium ${
                                transaction.type === "purchase" ? "text-red-600" : "text-green-600"
                              }`}
                            >
                              {transaction.type === "purchase" ? "-" : "+"}
                              {transaction.amount.toLocaleString()}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              Balance: {transaction.balance.toLocaleString()}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="orders" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Order History</CardTitle>
                    <CardDescription>Complete history of your purchases</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Order #</TableHead>
                          <TableHead>Date</TableHead>
                          <TableHead>Items</TableHead>
                          <TableHead>Total</TableHead>
                          <TableHead>Credits</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {orders.map((order) => (
                          <TableRow key={order.id}>
                            <TableCell className="font-medium">{order.orderNumber}</TableCell>
                            <TableCell>{new Date(order.createdAt).toLocaleDateString()}</TableCell>
                            <TableCell>{order.items.length}</TableCell>
                            <TableCell>${order.pricing.total.toFixed(2)}</TableCell>
                            <TableCell>{order.pricing.creditsUsed.toLocaleString()}</TableCell>
                            <TableCell>
                              <Badge
                                variant={
                                  order.status === "delivered"
                                    ? "default"
                                    : order.status === "cancelled"
                                      ? "destructive"
                                      : "secondary"
                                }
                              >
                                {order.status}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <div className="flex space-x-2">
                                <Button variant="ghost" size="sm">
                                  <Eye className="h-4 w-4" />
                                </Button>
                                <Button variant="ghost" size="sm">
                                  <Download className="h-4 w-4" />
                                </Button>
                                {order.status === "delivered" && (
                                  <Button variant="ghost" size="sm">
                                    <Star className="h-4 w-4" />
                                  </Button>
                                )}
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="credits" className="space-y-6">
                <div className="grid gap-4 md:grid-cols-2">
                  <Card>
                    <CardHeader>
                      <CardTitle>Credit Summary</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex justify-between">
                        <span>Available Balance:</span>
                        <span className="font-bold text-blue-600">{customer.creditBalance.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Credit Limit:</span>
                        <span className="font-medium">{customer.creditLimit.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Total Earned:</span>
                        <span className="font-medium">
                          {creditHistory
                            .filter((t) => t.type !== "purchase")
                            .reduce((sum, t) => sum + t.amount, 0)
                            .toLocaleString()}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>Total Spent:</span>
                        <span className="font-medium">
                          {creditHistory
                            .filter((t) => t.type === "purchase")
                            .reduce((sum, t) => sum + t.amount, 0)
                            .toLocaleString()}
                        </span>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Earn More Credits</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-2">
                          <Gift className="h-5 w-5 text-green-500" />
                          <span className="text-sm">Refer a Friend</span>
                        </div>
                        <span className="text-sm font-medium text-green-600">+500 credits</span>
                      </div>
                      <div className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-2">
                          <Star className="h-5 w-5 text-yellow-500" />
                          <span className="text-sm">Write a Review</span>
                        </div>
                        <span className="text-sm font-medium text-green-600">+50 credits</span>
                      </div>
                      <div className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-2">
                          <Package className="h-5 w-5 text-blue-500" />
                          <span className="text-sm">Large Order Bonus</span>
                        </div>
                        <span className="text-sm font-medium text-green-600">+2% back</span>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <Card>
                  <CardHeader>
                    <CardTitle>Credit Transaction History</CardTitle>
                    <CardDescription>Detailed history of all credit transactions</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Date</TableHead>
                          <TableHead>Type</TableHead>
                          <TableHead>Description</TableHead>
                          <TableHead>Amount</TableHead>
                          <TableHead>Balance</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {creditHistory.map((transaction) => (
                          <TableRow key={transaction.id}>
                            <TableCell>{new Date(transaction.date).toLocaleDateString()}</TableCell>
                            <TableCell>
                              <Badge variant="outline" className="capitalize">
                                {transaction.type}
                              </Badge>
                            </TableCell>
                            <TableCell>{transaction.description}</TableCell>
                            <TableCell className={transaction.type === "purchase" ? "text-red-600" : "text-green-600"}>
                              {transaction.type === "purchase" ? "-" : "+"}
                              {transaction.amount.toLocaleString()}
                            </TableCell>
                            <TableCell>{transaction.balance.toLocaleString()}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="support" className="space-y-6">
                <div className="grid gap-6 md:grid-cols-2">
                  <Card>
                    <CardHeader>
                      <CardTitle>Contact Support</CardTitle>
                      <CardDescription>Get help with your orders and account</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <Button className="w-full justify-start">
                        <MessageCircle className="h-4 w-4 mr-2" />
                        Start Live Chat
                      </Button>
                      <Button variant="outline" className="w-full justify-start">
                        <Mail className="h-4 w-4 mr-2" />
                        Send Email
                      </Button>
                      <Button variant="outline" className="w-full justify-start">
                        <Phone className="h-4 w-4 mr-2" />
                        Request Callback
                      </Button>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Help Resources</CardTitle>
                      <CardDescription>Find answers to common questions</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <Button variant="ghost" className="w-full justify-start">
                        <HelpCircle className="h-4 w-4 mr-2" />
                        Frequently Asked Questions
                      </Button>
                      <Button variant="ghost" className="w-full justify-start">
                        <FileText className="h-4 w-4 mr-2" />
                        User Guide
                      </Button>
                      <Button variant="ghost" className="w-full justify-start">
                        <Truck className="h-4 w-4 mr-2" />
                        Shipping Information
                      </Button>
                      <Button variant="ghost" className="w-full justify-start">
                        <CreditCard className="h-4 w-4 mr-2" />
                        Credit System Guide
                      </Button>
                    </CardContent>
                  </Card>
                </div>

                <Card>
                  <CardHeader>
                    <CardTitle>Support Tickets</CardTitle>
                    <CardDescription>Track your support requests</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-8">
                      <MessageCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-medium mb-2">No support tickets</h3>
                      <p className="text-muted-foreground mb-4">You haven't submitted any support requests yet.</p>
                      <Button onClick={() => setSupportTicketOpen(true)}>
                        <Plus className="h-4 w-4 mr-2" />
                        Create Support Ticket
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  )
}
