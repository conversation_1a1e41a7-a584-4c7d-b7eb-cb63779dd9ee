import type { Metada<PERSON> } from "next"
import Link from "next/link"
import { Suspense } from "react"

import { ResetPasswordForm } from "@/components/auth/reset-password-form"
import { AnimatedSection } from "@/components/auth/animated-section"

export const metadata: Metadata = {
  title: "Reset Password | WizeAssets",
  description: "Set a new password for your WizeAssets account",
}

export default function ResetPasswordPage() {
  return (
    <div className="container relative min-h-screen flex-col items-center justify-center grid lg:max-w-none lg:grid-cols-2 lg:px-0">
      <AnimatedSection 
        testimonial={{
          quote: "Our compliance with South African data protection regulations is crucial. WizeAssets ensures our data is protected with industry-standard encryption.",
          author: "<PERSON><PERSON>, Compliance Officer at Durban Data Solutions"
        }}
        gradientColors={{
          top: "bg-violet-500/20",
          bottom: "bg-fuchsia-500/20",
          center: "bg-pink-500/20"
        }}
      />
      <div className="lg:p-8">
        <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
          <div className="flex flex-col space-y-2 text-center">
            <h1 className="text-2xl font-semibold tracking-tight">
              Reset your password
            </h1>
            <p className="text-sm text-muted-foreground">
              Enter your new password below
            </p>
          </div>
          <Suspense fallback={<div>Loading...</div>}>
            <ResetPasswordForm />
          </Suspense>
          <p className="px-8 text-center text-sm text-muted-foreground">
            Remember your password?{" "}
            <Link
              href="/login"
              className="underline underline-offset-4 hover:text-primary"
            >
              Sign in
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}

