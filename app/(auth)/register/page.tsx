import type { Metada<PERSON> } from "next"
import Link from "next/link"

import { RegisterForm } from "@/components/auth/register-form"
import { AnimatedSection } from "@/components/auth/animated-section"

export const metadata: Metadata = {
  title: "Register | WizeAssets",
  description: "Create your WizeAssets account",
}

export default function RegisterPage() {
  return (
    <div className="container relative min-h-screen flex-col items-center justify-center grid lg:max-w-none lg:grid-cols-2 lg:px-0">
      <AnimatedSection 
        testimonial={{
          quote: "As a growing South African business, WizeAssets has helped us scale our operations efficiently. We've reduced asset downtime by 40%.",
          author: "<PERSON><PERSON><PERSON>, CEO at Johannesburg Logistics"
        }}
        gradientColors={{
          top: "bg-emerald-500/20",
          bottom: "bg-teal-500/20",
          center: "bg-cyan-500/20"
        }}
      />
      <div className="lg:p-8">
        <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
          <div className="flex flex-col space-y-2 text-center">
            <h1 className="text-2xl font-semibold tracking-tight">
              Create an account
            </h1>
            <p className="text-sm text-muted-foreground">
              Enter your details below to create your account
            </p>
          </div>
          <RegisterForm />
          <p className="px-8 text-center text-sm text-muted-foreground">
            Already have an account?{" "}
            <Link
              href="/login"
              className="underline underline-offset-4 hover:text-primary"
            >
              Sign in
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}

