import { <PERSON>ada<PERSON> } from "next";
import { ClientLoginForm } from "@/components/auth/client-login-form";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";

export const metadata: Metadata = {
  title: "Client Login | WizeAssets",
  description: "Sign in to your WizeAssets client account.",
};

export default function ClientLoginPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="w-full max-w-md">
        {/* Back to home link */}
        <div className="mb-6">
          <Link
            href="/"
            className="inline-flex items-center text-sm text-muted-foreground hover:text-primary"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to home
          </Link>
        </div>

        <Card className="shadow-lg">
          <CardHeader className="text-center space-y-2">
            <div className="mx-auto w-12 h-12 bg-primary rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-xl">W</span>
            </div>
          </CardHeader>
          <CardContent>
            <ClientLoginForm />
          </CardContent>
        </Card>

        {/* Additional links */}
        <div className="mt-6 text-center space-y-2">
          <div className="text-sm text-muted-foreground">
            <Link href="/auth/admin-login" className="text-primary hover:underline">
              Admin Login
            </Link>
            {" • "}
            <Link href="/support" className="text-primary hover:underline">
              Need Help?
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}