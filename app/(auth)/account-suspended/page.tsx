import { <PERSON>ada<PERSON> } from "next";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertTriangle, Mail, Phone } from "lucide-react";
import Link from "next/link";

export const metadata: Metadata = {
  title: "Account Suspended | WizeAssets",
  description: "Your account has been suspended.",
};

export default function AccountSuspendedPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 to-orange-100 p-4">
      <div className="w-full max-w-md">
        <Card className="shadow-lg">
          <CardHeader className="text-center space-y-4">
            <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
            <CardTitle className="text-xl text-red-800">Account Suspended</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Your account has been suspended. Please contact our support team for assistance.
              </AlertDescription>
            </Alert>

            <div className="text-center space-y-4">
              <p className="text-sm text-muted-foreground">
                Your account access has been temporarily restricted. This may be due to:
              </p>

              <ul className="text-sm text-muted-foreground text-left space-y-1">
                <li>• Violation of terms of service</li>
                <li>• Suspicious account activity</li>
                <li>• Payment issues</li>
                <li>• Administrative review</li>
              </ul>

              <div className="space-y-3 pt-4">
                <h4 className="font-medium">Contact Support</h4>
                
                <div className="space-y-2">
                  <Button className="w-full" asChild>
                    <Link href="mailto:<EMAIL>">
                      <Mail className="h-4 w-4 mr-2" />
                      Email Support
                    </Link>
                  </Button>

                  <Button variant="outline" className="w-full" asChild>
                    <Link href="tel:******-123-4567">
                      <Phone className="h-4 w-4 mr-2" />
                      Call Support
                    </Link>
                  </Button>
                </div>
              </div>
            </div>

            <div className="text-center text-sm text-muted-foreground border-t pt-4">
              <p>
                Support Hours: Monday - Friday, 9 AM - 6 PM EST
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}