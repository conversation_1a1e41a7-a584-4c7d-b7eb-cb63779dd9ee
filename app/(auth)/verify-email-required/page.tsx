import { <PERSON>ada<PERSON> } from "next";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Mail, RefreshCw } from "lucide-react";
import Link from "next/link";

export const metadata: Metadata = {
  title: "Email Verification Required | WizeAssets",
  description: "Please verify your email address to continue.",
};

export default function VerifyEmailRequiredPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="w-full max-w-md">
        <Card className="shadow-lg">
          <CardHeader className="text-center space-y-4">
            <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
              <Mail className="h-8 w-8 text-blue-600" />
            </div>
            <CardTitle className="text-xl">Email Verification Required</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <Alert>
              <Mail className="h-4 w-4" />
              <AlertDescription>
                Please check your email and click the verification link to activate your account.
              </AlertDescription>
            </Alert>

            <div className="text-center space-y-4">
              <p className="text-sm text-muted-foreground">
                We've sent a verification email to your registered email address. 
                Click the link in the email to verify your account and start using WizeAssets.
              </p>

              <div className="space-y-2">
                <Button className="w-full" asChild>
                  <Link href="/auth/client-login">
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Try Signing In Again
                  </Link>
                </Button>

                <Button variant="outline" className="w-full" asChild>
                  <Link href="/auth/resend-verification">
                    Resend Verification Email
                  </Link>
                </Button>
              </div>
            </div>

            <div className="text-center text-sm text-muted-foreground">
              <p>
                Need help?{" "}
                <Link href="/support" className="text-primary hover:underline">
                  Contact Support
                </Link>
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}