import type { Metada<PERSON> } from "next"
import Link from "next/link"

import { ForgotPasswordForm } from "@/components/auth/forgot-password-form"
import { AnimatedSection } from "@/components/auth/animated-section"

export const metadata: Metadata = {
  title: "Forgot Password | WizeAssets",
  description: "Reset your WizeAssets password",
}

export default function ForgotPasswordPage() {
  return (
    <div className="container relative min-h-screen flex-col items-center justify-center grid lg:max-w-none lg:grid-cols-2 lg:px-0">
      <AnimatedSection 
        testimonial={{
          quote: "WizeAssets' security measures meet South African POPIA standards. Their password recovery system is both secure and user-friendly.",
          author: "<PERSON><PERSON><PERSON>, IT Security Manager at Cape Town Financial Services"
        }}
        gradientColors={{
          top: "bg-amber-500/20",
          bottom: "bg-orange-500/20",
          center: "bg-red-500/20"
        }}
      />
      <div className="lg:p-8">
        <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
          <div className="flex flex-col space-y-2 text-center">
            <h1 className="text-2xl font-semibold tracking-tight">
              Forgot your password?
            </h1>
            <p className="text-sm text-muted-foreground">
              Enter your email address and we'll send you a link to reset your password
            </p>
          </div>
          <ForgotPasswordForm />
          <p className="px-8 text-center text-sm text-muted-foreground">
            Remember your password?{" "}
            <Link
              href="/login"
              className="underline underline-offset-4 hover:text-primary"
            >
              Sign in
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}

