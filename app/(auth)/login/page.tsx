import type { Metada<PERSON> } from "next"
import Link from "next/link"
import { Suspense } from "react"

import { LoginForm } from "@/components/auth/login-form"
import { AnimatedSection } from "@/components/auth/animated-section"

export const metadata: Metadata = {
  title: "Login | WizeAssets",
  description: "Login to your WizeAssets account",
}

export default function LoginPage() {
  return (
    <div className="container relative min-h-screen flex-col items-center justify-center grid lg:max-w-none lg:grid-cols-2 lg:px-0">
      <AnimatedSection 
        testimonial={{
          quote: "WizeAssets has transformed how we manage our company's assets. The platform is intuitive and powerful.",
          author: "<PERSON>, CFO at TechCorp"
        }}
        gradientColors={{
          top: "bg-blue-500/20",
          bottom: "bg-indigo-500/20",
          center: "bg-purple-500/20"
        }}
      />
      <div className="lg:p-8">
        <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
          <div className="flex flex-col space-y-2 text-center">
            <h1 className="text-2xl font-semibold tracking-tight">
              Welcome back
            </h1>
            <p className="text-sm text-muted-foreground">
              Enter your credentials to sign in to your account
            </p>
          </div>
          <Suspense fallback={<div>Loading...</div>}>
            <LoginForm />
          </Suspense>
          <p className="px-8 text-center text-sm text-muted-foreground">
            Don&apos;t have an account?{" "}
            <Link
              href="/register"
              className="underline underline-offset-4 hover:text-primary"
            >
              Sign up
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}

