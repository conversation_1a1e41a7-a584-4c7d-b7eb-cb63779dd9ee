"use client"
import { motion } from "framer-motion"
import { LaptopDashboard } from "./laptop-dashboard"
import { AnimatedErpShowcase } from "./animated-erp-showcase"

export function HeroAnimation() {
  return (
    <div className="relative">
      {/* Background Elements */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1.5, delay: 0.5 }}
        className="absolute inset-0 bg-gradient-to-br from-blue-50/50 via-purple-50/30 to-pink-50/50 rounded-3xl"
      />
      
      {/* Floating Orbs */}
      <motion.div
        initial={{ opacity: 0, scale: 0 }}
        animate={{ opacity: 0.6, scale: 1 }}
        transition={{ duration: 2, delay: 1 }}
        className="absolute top-10 right-20 w-32 h-32 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-2xl"
      />
      
      <motion.div
        initial={{ opacity: 0, scale: 0 }}
        animate={{ opacity: 0.4, scale: 1 }}
        transition={{ duration: 2, delay: 1.5 }}
        className="absolute bottom-20 left-20 w-24 h-24 bg-gradient-to-r from-pink-400/20 to-orange-400/20 rounded-full blur-xl"
      />

      {/* Main Laptop Dashboard */}
      <div className="relative z-10 py-12">
        <AnimatedErpShowcase />
      </div>

      {/* Bottom Stats */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 2.5 }}
        className="relative z-10 flex justify-center items-center space-x-8 mt-8"
      >
        <div className="text-center">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5, delay: 2.7 }}
            className="text-3xl font-bold text-gray-800"
          >
            500+
          </motion.div>
          <div className="text-sm text-gray-600">Companies Trust Us</div>
        </div>
        
        <div className="w-px h-12 bg-gray-300"></div>
        
        <div className="text-center">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5, delay: 2.9 }}
            className="text-3xl font-bold text-gray-800"
          >
            1M+
          </motion.div>
          <div className="text-sm text-gray-600">Assets Managed</div>
        </div>
        
        <div className="w-px h-12 bg-gray-300"></div>
        
        <div className="text-center">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5, delay: 3.1 }}
            className="text-3xl font-bold text-gray-800"
          >
            99.9%
          </motion.div>
          <div className="text-sm text-gray-600">Uptime Guarantee</div>
        </div>
      </motion.div>
    </div>
  )
}

