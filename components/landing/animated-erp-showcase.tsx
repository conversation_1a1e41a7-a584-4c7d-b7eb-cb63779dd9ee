"use client"
import { motion, AnimatePresence } from "framer-motion"
import { useState, useEffect } from "react"
import { 
  Package, 
  TrendingUp, 
  Shield, 
  Clock, 
  MapPin, 
  DollarSign,
  Activity,
  Users,
  BarChart3,
  Zap,
  Database,
  Settings
} from "lucide-react"

interface AssetData {
  id: string
  name: string
  category: string
  value: number
  status: 'active' | 'maintenance' | 'retired'
  location: string
}

const mockAssets: AssetData[] = [
  { id: '1', name: 'Server Rack A1', category: 'IT Equipment', value: 25000, status: 'active', location: 'Data Center' },
  { id: '2', name: 'Manufacturing Line B', category: 'Machinery', value: 150000, status: 'active', location: 'Factory Floor' },
  { id: '3', name: 'Company Vehicle Fleet', category: 'Vehicles', value: 80000, status: 'maintenance', location: 'Parking Lot' },
  { id: '4', name: 'Office Furniture Set', category: 'Furniture', value: 12000, status: 'active', location: 'Office Building' },
]

const views = ['dashboard', 'assets', 'analytics', 'reports']

// Generate particles data once to avoid re-renders
const particles = Array.from({ length: 20 }, (_, i) => ({
  id: i,
  left: Math.random() * 100,
  top: Math.random() * 100,
  duration: Math.random() * 3 + 2,
  delay: Math.random() * 2,
}))

export function AnimatedErpShowcase() {
  const [currentView, setCurrentView] = useState(0)
  const [animatedMetrics, setAnimatedMetrics] = useState({
    totalAssets: 0,
    totalValue: 0,
    activeAssets: 0,
    locations: 0
  })
  
  // Simulate real-time data updates
  useEffect(() => {
    const interval = setInterval(() => {
      setAnimatedMetrics({
        totalAssets: Math.floor(Math.random() * 50) + 1200,
        totalValue: Math.floor(Math.random() * 500000) + 2400000,
        activeAssets: Math.floor(Math.random() * 40) + 1150,
        locations: Math.floor(Math.random() * 5) + 15
      })
    }, 3000)
    
    return () => clearInterval(interval)
  }, [])
  
  // Auto-cycle through views
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentView((prev) => (prev + 1) % views.length)
    }, 4000)
    
    return () => clearInterval(interval)
  }, [])

  const containerVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  }

  const floatingVariants = {
    initial: { y: 0 },
    animate: {
      y: [-10, 10, -10],
      transition: {
        duration: 4,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="relative w-full max-w-6xl mx-auto rounded-2xl overflow-hidden bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 shadow-2xl"
    >
      {/* Animated background gradient */}
      <div className="absolute inset-0 bg-gradient-to-tr from-blue-500/10 via-purple-500/10 to-emerald-500/10 animate-pulse" />
      
      {/* Floating particles */}
      <div className="absolute inset-0 overflow-hidden">
        {particles.map((particle) => (
          <motion.div
            key={particle.id}
            className="absolute w-2 h-2 bg-blue-400/30 rounded-full"
            style={{
              left: `${particle.left}%`,
              top: `${particle.top}%`,
            }}
            animate={{
              y: [0, -100, 0],
              opacity: [0, 1, 0],
            }}
            transition={{
              duration: particle.duration,
              repeat: Infinity,
              delay: particle.delay,
            }}
          />
        ))}
      </div>

      {/* Header */}
      <motion.div
        variants={itemVariants}
        className="relative z-10 p-6 border-b border-white/10 backdrop-blur-sm"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <motion.div
              variants={floatingVariants}
              initial="initial"
              animate="animate"
              className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg"
            >
              <Database className="w-6 h-6 text-white" />
            </motion.div>
            <div>
              <h2 className="text-2xl font-bold text-white">WizeAssets ERP</h2>
              <p className="text-blue-200">Intelligent Asset Management</p>
            </div>
          </div>
          
          <div className="flex space-x-2">
            {views.map((view, index) => (
              <motion.button
                key={view}
                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                  currentView === index
                    ? 'bg-blue-500 text-white'
                    : 'bg-white/10 text-blue-200 hover:bg-white/20'
                }`}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setCurrentView(index)}
              >
                {view.charAt(0).toUpperCase() + view.slice(1)}
              </motion.button>
            ))}
          </div>
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="relative z-10 p-6">
        <AnimatePresence mode="wait">
          {currentView === 0 && (
            <motion.div
              key="dashboard"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.5 }}
              className="space-y-6"
            >
              {/* Metrics Cards */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {[
                  { icon: Package, label: 'Total Assets', value: animatedMetrics.totalAssets, color: 'from-blue-500 to-blue-600' },
                  { icon: DollarSign, label: 'Total Value', value: `$${(animatedMetrics.totalValue / 1000000).toFixed(1)}M`, color: 'from-emerald-500 to-emerald-600' },
                  { icon: Activity, label: 'Active Assets', value: animatedMetrics.activeAssets, color: 'from-orange-500 to-orange-600' },
                  { icon: MapPin, label: 'Locations', value: animatedMetrics.locations, color: 'from-purple-500 to-purple-600' }
                ].map((metric, index) => (
                  <motion.div
                    key={metric.label}
                    variants={itemVariants}
                    className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20"
                    whileHover={{ scale: 1.02, y: -2 }}
                  >
                    <div className={`w-10 h-10 bg-gradient-to-br ${metric.color} rounded-lg flex items-center justify-center mb-3`}>
                      <metric.icon className="w-5 h-5 text-white" />
                    </div>
                    <p className="text-blue-200 text-sm">{metric.label}</p>
                    <motion.p
                      className="text-2xl font-bold text-white"
                      key={metric.value}
                      initial={{ scale: 1.2, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      transition={{ duration: 0.3 }}
                    >
                      {metric.value}
                    </motion.p>
                  </motion.div>
                ))}
              </div>

              {/* Chart Placeholder */}
              <motion.div
                variants={itemVariants}
                className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20"
              >
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">Asset Performance</h3>
                  <BarChart3 className="w-5 h-5 text-blue-400" />
                </div>
                <div className="flex items-end space-x-2 h-32">
                  {[40, 65, 55, 80, 45, 90, 75].map((height, index) => (
                    <motion.div
                      key={index}
                      className="bg-gradient-to-t from-blue-500 to-blue-400 rounded-t flex-1"
                      initial={{ height: 0 }}
                      animate={{ height: `${height}%` }}
                      transition={{ duration: 0.8, delay: index * 0.1 }}
                    />
                  ))}
                </div>
              </motion.div>
            </motion.div>
          )}

          {currentView === 1 && (
            <motion.div
              key="assets"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.5 }}
              className="space-y-4"
            >
              <h3 className="text-xl font-semibold text-white mb-4">Asset Inventory</h3>
              {mockAssets.map((asset, index) => (
                <motion.div
                  key={asset.id}
                  variants={itemVariants}
                  className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20"
                  whileHover={{ scale: 1.01, x: 5 }}
                  custom={index}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className={`w-3 h-3 rounded-full ${
                        asset.status === 'active' ? 'bg-green-400' :
                        asset.status === 'maintenance' ? 'bg-yellow-400' : 'bg-red-400'
                      }`} />
                      <div>
                        <p className="font-medium text-white">{asset.name}</p>
                        <p className="text-sm text-blue-200">{asset.category}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-white">${asset.value.toLocaleString()}</p>
                      <p className="text-sm text-blue-200">{asset.location}</p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          )}

          {currentView === 2 && (
            <motion.div
              key="analytics"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.5 }}
              className="space-y-6"
            >
              <h3 className="text-xl font-semibold text-white mb-4">Analytics Dashboard</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <motion.div
                  variants={itemVariants}
                  className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20"
                >
                  <h4 className="text-lg font-medium text-white mb-4">Asset Utilization</h4>
                  <div className="space-y-3">
                    {['IT Equipment', 'Machinery', 'Vehicles', 'Furniture'].map((category, index) => (
                      <div key={category} className="space-y-1">
                        <div className="flex justify-between text-sm">
                          <span className="text-blue-200">{category}</span>
                          <span className="text-white">{85 - index * 5}%</span>
                        </div>
                        <div className="w-full bg-white/20 rounded-full h-2">
                          <motion.div
                            className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full"
                            initial={{ width: 0 }}
                            animate={{ width: `${85 - index * 5}%` }}
                            transition={{ duration: 1, delay: index * 0.2 }}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </motion.div>

                <motion.div
                  variants={itemVariants}
                  className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20"
                >
                  <h4 className="text-lg font-medium text-white mb-4">Maintenance Schedule</h4>
                  <div className="space-y-3">
                    {[
                      { task: 'Server Maintenance', date: 'Tomorrow', priority: 'high' },
                      { task: 'Vehicle Inspection', date: 'Next Week', priority: 'medium' },
                      { task: 'Equipment Calibration', date: '2 Weeks', priority: 'low' }
                    ].map((task, index) => (
                      <motion.div
                        key={task.task}
                        className="flex items-center justify-between p-3 bg-white/5 rounded-lg"
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                      >
                        <div>
                          <p className="text-white font-medium">{task.task}</p>
                          <p className="text-blue-200 text-sm">{task.date}</p>
                        </div>
                        <div className={`w-3 h-3 rounded-full ${
                          task.priority === 'high' ? 'bg-red-400' :
                          task.priority === 'medium' ? 'bg-yellow-400' : 'bg-green-400'
                        }`} />
                      </motion.div>
                    ))}
                  </div>
                </motion.div>
              </div>
            </motion.div>
          )}

          {currentView === 3 && (
            <motion.div
              key="reports"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.5 }}
              className="space-y-6"
            >
              <h3 className="text-xl font-semibold text-white mb-4">Reports & Insights</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {[
                  { icon: TrendingUp, title: 'ROI Analysis', value: '+15.2%', description: 'Year over year growth' },
                  { icon: Shield, title: 'Compliance Score', value: '98.5%', description: 'Regulatory compliance' },
                  { icon: Clock, title: 'Avg. Downtime', value: '2.3hrs', description: 'Per month reduction' }
                ].map((report, index) => (
                  <motion.div
                    key={report.title}
                    variants={itemVariants}
                    className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 text-center"
                    whileHover={{ scale: 1.05 }}
                    custom={index}
                  >
                    <motion.div
                      className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4"
                      whileHover={{ rotate: 360 }}
                      transition={{ duration: 0.6 }}
                    >
                      <report.icon className="w-6 h-6 text-white" />
                    </motion.div>
                    <h4 className="text-lg font-semibold text-white mb-2">{report.title}</h4>
                    <p className="text-2xl font-bold text-emerald-400 mb-1">{report.value}</p>
                    <p className="text-sm text-blue-200">{report.description}</p>
                  </motion.div>
                ))}
              </div>

              <motion.div
                variants={itemVariants}
                className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20"
              >
                <h4 className="text-lg font-medium text-white mb-4">Quick Actions</h4>
                <div className="flex flex-wrap gap-3">
                  {[
                    { icon: Zap, label: 'Generate Report', color: 'from-yellow-500 to-orange-500' },
                    { icon: Users, label: 'Assign Assets', color: 'from-blue-500 to-purple-500' },
                    { icon: Settings, label: 'Configure Alerts', color: 'from-emerald-500 to-teal-500' }
                  ].map((action, index) => (
                    <motion.button
                      key={action.label}
                      className={`flex items-center space-x-2 px-4 py-2 bg-gradient-to-r ${action.color} rounded-lg text-white font-medium`}
                      whileHover={{ scale: 1.05, y: -2 }}
                      whileTap={{ scale: 0.95 }}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <action.icon className="w-4 h-4" />
                      <span>{action.label}</span>
                    </motion.button>
                  ))}
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Footer Status Bar */}
      <motion.div
        variants={itemVariants}
        className="relative z-10 px-6 py-3 border-t border-white/10 backdrop-blur-sm bg-white/5"
      >
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
              <span className="text-blue-200">System Online</span>
            </div>
            <div className="text-blue-200">Last Updated: {new Date().toLocaleTimeString()}</div>
          </div>
          <div className="text-blue-200">WizeAssets ERP v2.0</div>
        </div>
      </motion.div>
    </motion.div>
  )
}