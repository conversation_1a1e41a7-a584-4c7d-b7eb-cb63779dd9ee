"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Check } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"

export function PricingSection() {
  const [annual, setAnnual] = useState(true)

  const plans = [
    {
      name: "Starter",
      description: "Perfect for small businesses in South Africa just getting started with asset management.",
      price: annual ? 499 : 599,
      features: [
        "Up to 100 assets",
        "Basic reporting",
        "Email support",
        "1 user",
        "Asset tracking",
        "Maintenance scheduling",
      ],
      cta: "Start Free Trial",
      popular: false,
    },
    {
      name: "Professional",
      description: "Ideal for growing South African businesses with more complex asset management needs.",
      price: annual ? 1499 : 1799,
      features: [
        "Up to 500 assets",
        "Advanced reporting",
        "Priority email support",
        "5 users",
        "Asset tracking",
        "Maintenance scheduling",
        "Document management",
        "Custom fields",
        "API access",
      ],
      cta: "Start Free Trial",
      popular: true,
    },
    {
      name: "Enterprise",
      description: "For large South African organizations with advanced asset management requirements.",
      price: annual ? 3999 : 4999,
      features: [
        "Unlimited assets",
        "Custom reporting",
        "24/7 phone support",
        "Unlimited users",
        "Asset tracking",
        "Maintenance scheduling",
        "Document management",
        "Custom fields",
        "API access",
        "Single sign-on",
        "Custom integrations",
        "Dedicated account manager",
      ],
      cta: "Contact Sales",
      popular: false,
    },
  ]

  return (
    <div>
      <div className="mb-8 flex items-center justify-center gap-4">
        <Label htmlFor="pricing-toggle" className={annual ? "text-muted-foreground" : ""}>
          Monthly
        </Label>
        <Switch id="pricing-toggle" checked={annual} onCheckedChange={setAnnual} />
        <Label htmlFor="pricing-toggle" className={!annual ? "text-muted-foreground" : ""}>
          Annual
          <span className="ml-2 rounded-full bg-primary/10 px-2 py-0.5 text-xs text-primary">Save 20%</span>
        </Label>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        {plans.map((plan, index) => (
          <motion.div
            key={plan.name}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
            className={`relative rounded-xl border ${
              plan.popular ? "border-primary shadow-lg" : "border-border"
            } bg-background p-6`}
          >
            {plan.popular && (
              <div className="absolute -top-3 left-1/2 -translate-x-1/2 rounded-full bg-primary px-3 py-1 text-xs font-medium text-primary-foreground">
                Most Popular
              </div>
            )}

            <div className="mb-6">
              <h3 className="text-xl font-bold">{plan.name}</h3>
              <p className="mt-2 text-sm text-muted-foreground">{plan.description}</p>
            </div>

            <div className="mb-6">
              <div className="flex items-baseline">
                <span className="text-3xl font-bold">R{plan.price}</span>
                <span className="ml-1 text-muted-foreground">/month</span>
              </div>
              {annual && (
                <p className="mt-1 text-xs text-muted-foreground">Billed annually (R{plan.price * 12}/year)</p>
              )}
            </div>

            <Button className="w-full" variant={plan.popular ? "default" : "outline"}>
              {plan.cta}
            </Button>

            <ul className="mt-6 space-y-4">
              {plan.features.map((feature) => (
                <li key={feature} className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-primary" />
                  <span className="text-sm">{feature}</span>
                </li>
              ))}
            </ul>
          </motion.div>
        ))}
      </div>
    </div>
  )
}

