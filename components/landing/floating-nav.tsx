"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { motion } from "framer-motion"
import { useAuth } from "@/contexts/auth-context"

const publicNavItems = [
  {
    name: "Features",
    href: "/features",
  },
  {
    name: "Pricing",
    href: "/pricing",
  },
  {
    name: "About",
    href: "/about",
  },
  {
    name: "Contact",
    href: "/contact",
  },
]

const privateNavItems = [
  {
    name: "Dashboard",
    href: "/admin",
  },
  {
    name: "Assets",
    href: "/admin/assets",
  },
  {
    name: "Reports",
    href: "/admin/reports",
  },
  {
    name: "Settings",
    href: "/settings",
  },
]

export function FloatingNav() {
  const pathname = usePathname()
  const { user, logout } = useAuth()

  const navItems = user ? privateNavItems : publicNavItems

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="fixed inset-x-4 top-4 z-50 mx-auto max-w-2xl"
    >
      <div className="relative">
        {/* Glass effect background */}
        <div className="absolute inset-0 rounded-full bg-white/10 backdrop-blur-md" />
        
        {/* Navigation content */}
        <nav className="relative flex items-center justify-between rounded-full border border-white/10 bg-white/5 px-4 py-2">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <span className="text-xl font-bold text-white">WizeAssets</span>
          </Link>

          {/* Navigation Links */}
          <div className="hidden md:flex items-center space-x-1">
            {navItems.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className={cn(
                  "relative rounded-full px-4 py-2 text-sm font-medium transition-colors",
                  pathname === item.href
                    ? "text-white"
                    : "text-gray-400 hover:text-white"
                )}
              >
                {pathname === item.href && (
                  <motion.div
                    layoutId="activeNav"
                    className="absolute inset-0 rounded-full bg-white/10"
                    transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                  />
                )}
                <span className="relative z-10">{item.name}</span>
              </Link>
            ))}
          </div>

          {/* Auth Buttons */}
          <div className="flex items-center space-x-2">
            {user ? (
              <>
                <Button asChild variant="ghost" size="sm" className="rounded-full">
                  <Link href="/profile">
                    {user.name}
                  </Link>
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="rounded-full"
                  onClick={() => logout()}
                >
                  Sign Out
                </Button>
              </>
            ) : (
              <>
                <Button asChild variant="ghost" size="sm" className="rounded-full">
                  <Link href="/login">Sign In</Link>
                </Button>
                <Button asChild size="sm" className="rounded-full">
                  <Link href="/register">Get Started</Link>
                </Button>
              </>
            )}
          </div>
        </nav>
      </div>
    </motion.div>
  )
} 