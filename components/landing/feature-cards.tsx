"use client"

import { motion } from "framer-motion"
import { BarChart3, Calendar, ClipboardList, Database, FileText, Settings, Shield, Truck } from "lucide-react"

export function FeatureCards() {
  const features = [
    {
      icon: <ClipboardList className="h-10 w-10" />,
      title: "Asset Tracking",
      description:
        "Track all your assets with detailed information including purchase date, warranty, location, and assigned user.",
    },
    {
      icon: <Calendar className="h-10 w-10" />,
      title: "Maintenance Management",
      description: "Schedule and track maintenance activities, set up automated alerts for upcoming maintenance.",
    },
    {
      icon: <BarChart3 className="h-10 w-10" />,
      title: "Reporting & Analytics",
      description: "Generate comprehensive reports and gain insights with powerful analytics dashboards.",
    },
    {
      icon: <Shield className="h-10 w-10" />,
      title: "Role-Based Access",
      description: "Control who can view and edit asset information with granular permission settings.",
    },
    {
      icon: <FileText className="h-10 w-10" />,
      title: "Document Management",
      description: "Attach and manage documents related to assets, such as invoices, warranties, and manuals.",
    },
    {
      icon: <Truck className="h-10 w-10" />,
      title: "Lifecycle Management",
      description: "Manage the complete lifecycle of assets from acquisition to disposal.",
    },
    {
      icon: <Database className="h-10 w-10" />,
      title: "Integration Capabilities",
      description: "Seamlessly integrate with other business systems like accounting software and CRM.",
    },
    {
      icon: <Settings className="h-10 w-10" />,
      title: "Customizable Workflows",
      description: "Create custom workflows for asset-related processes tailored to your organization's needs.",
    },
  ]

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  }

  return (
    <motion.div
      className="grid gap-6 sm:grid-cols-2 lg:grid-cols-4"
      variants={container}
      initial="hidden"
      whileInView="show"
      viewport={{ once: true, margin: "-100px" }}
    >
      {features.map((feature, index) => (
        <motion.div
          key={index}
          variants={item}
          className="group relative overflow-hidden rounded-lg border bg-background p-6 shadow-md transition-all hover:shadow-lg hover:-translate-y-1"
        >
          <div className="absolute -right-20 -top-20 h-40 w-40 rounded-full bg-primary/10 transition-all group-hover:scale-150" />

          <div className="relative mb-4 text-primary">{feature.icon}</div>

          <div className="relative space-y-2">
            <h3 className="font-bold">{feature.title}</h3>
            <p className="text-sm text-muted-foreground">{feature.description}</p>
          </div>
        </motion.div>
      ))}
    </motion.div>
  )
}

