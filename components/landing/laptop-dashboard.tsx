"use client"
import { motion } from "framer-motion"
import { Bar<PERSON>hart<PERSON>, <PERSON>, DollarSign, TrendingUp, Bell, Search, Menu, Setting<PERSON> } from "lucide-react"

export function LaptopDashboard() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, delay: 0.2 }}
      className="relative mx-auto max-w-6xl"
    >
      {/* Laptop Base */}
      <div className="relative">
        {/* Laptop Screen Frame */}
        <div className="relative bg-gray-800 rounded-t-3xl p-4 shadow-2xl">
          {/* Screen Bezel */}
          <div className="bg-black rounded-t-2xl p-6 relative overflow-hidden">
            {/* Webcam */}
            <div className="absolute top-2 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-gray-600 rounded-full"></div>
            
            {/* Dashboard Screen */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 1, delay: 0.5 }}
              className="bg-gray-50 rounded-lg h-[500px] relative overflow-hidden"
            >
              {/* Top Navigation Bar */}
              <motion.div
                initial={{ y: -20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.8 }}
                className="bg-white border-b px-6 py-4 flex items-center justify-between"
              >
                <div className="flex items-center space-x-4">
                  <Menu className="w-6 h-6 text-gray-600" />
                  <h1 className="text-xl font-bold text-gray-800">WizeAssets Dashboard</h1>
                </div>
                <div className="flex items-center space-x-4">
                  <Search className="w-5 h-5 text-gray-500" />
                  <Bell className="w-5 h-5 text-gray-500" />
                  <Settings className="w-5 h-5 text-gray-500" />
                  <div className="w-8 h-8 bg-blue-500 rounded-full"></div>
                </div>
              </motion.div>

              {/* Dashboard Content */}
              <div className="p-6 grid grid-cols-12 gap-6 h-full">
                {/* Stats Cards */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 1.0 }}
                  className="col-span-3"
                >
                  <div className="bg-white rounded-lg p-4 shadow-sm border">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-600">Total Assets</p>
                        <p className="text-2xl font-bold text-gray-900">2,847</p>
                      </div>
                      <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <BarChart3 className="w-6 h-6 text-blue-600" />
                      </div>
                    </div>
                    <div className="mt-2 flex items-center">
                      <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                      <span className="text-sm text-green-600">+12.5%</span>
                    </div>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 1.1 }}
                  className="col-span-3"
                >
                  <div className="bg-white rounded-lg p-4 shadow-sm border">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-600">Asset Value</p>
                        <p className="text-2xl font-bold text-gray-900">$4.2M</p>
                      </div>
                      <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <DollarSign className="w-6 h-6 text-green-600" />
                      </div>
                    </div>
                    <div className="mt-2 flex items-center">
                      <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                      <span className="text-sm text-green-600">+8.2%</span>
                    </div>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 1.2 }}
                  className="col-span-3"
                >
                  <div className="bg-white rounded-lg p-4 shadow-sm border">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-600">Active Users</p>
                        <p className="text-2xl font-bold text-gray-900">186</p>
                      </div>
                      <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <Users className="w-6 h-6 text-purple-600" />
                      </div>
                    </div>
                    <div className="mt-2 flex items-center">
                      <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                      <span className="text-sm text-green-600">+5.1%</span>
                    </div>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 1.3 }}
                  className="col-span-3"
                >
                  <div className="bg-white rounded-lg p-4 shadow-sm border">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-600">Efficiency</p>
                        <p className="text-2xl font-bold text-gray-900">94.2%</p>
                      </div>
                      <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                        <TrendingUp className="w-6 h-6 text-orange-600" />
                      </div>
                    </div>
                    <div className="mt-2 flex items-center">
                      <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                      <span className="text-sm text-green-600">+2.3%</span>
                    </div>
                  </div>
                </motion.div>

                {/* Chart Area */}
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.8, delay: 1.4 }}
                  className="col-span-8 bg-white rounded-lg shadow-sm border p-6"
                >
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Asset Performance</h3>
                  <div className="h-48 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg flex items-end justify-center space-x-2 p-4">
                    {/* Animated Chart Bars */}
                    {[65, 80, 45, 90, 75, 85, 60, 95, 70, 85, 55, 75].map((height, index) => (
                      <motion.div
                        key={index}
                        initial={{ height: 0 }}
                        animate={{ height: `${height}%` }}
                        transition={{ duration: 0.8, delay: 1.6 + index * 0.1 }}
                        className="bg-gradient-to-t from-blue-500 to-purple-500 rounded-t-sm"
                        style={{ width: '20px' }}
                      />
                    ))}
                  </div>
                </motion.div>

                {/* Recent Activity */}
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 1.5 }}
                  className="col-span-4 bg-white rounded-lg shadow-sm border p-6"
                >
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
                  <div className="space-y-4">
                    {[
                      { action: "Asset Added", item: "Laptop Dell XPS", time: "2m ago" },
                      { action: "Maintenance", item: "Printer HP LaserJet", time: "1h ago" },
                      { action: "Asset Transfer", item: "Monitor Samsung", time: "3h ago" },
                      { action: "New User", item: "John Smith", time: "5h ago" }
                    ].map((activity, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, x: 10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.4, delay: 1.8 + index * 0.1 }}
                        className="flex items-center justify-between p-2 hover:bg-gray-50 rounded"
                      >
                        <div>
                          <p className="text-sm font-medium text-gray-900">{activity.action}</p>
                          <p className="text-xs text-gray-500">{activity.item}</p>
                        </div>
                        <span className="text-xs text-gray-400">{activity.time}</span>
                      </motion.div>
                    ))}
                  </div>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Laptop Base/Keyboard */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="relative bg-gray-800 rounded-b-3xl h-8 shadow-2xl"
        >
          {/* Trackpad */}
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-16 h-4 bg-gray-700 rounded border border-gray-600"></div>
        </motion.div>

        {/* Laptop Shadow */}
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="absolute inset-0 bg-gradient-radial from-black/20 via-transparent to-transparent blur-3xl transform translate-y-8 scale-110 -z-10"
        />
      </div>

      {/* Floating Elements */}
      <motion.div
        initial={{ opacity: 0, x: -50 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.8, delay: 2.0 }}
        className="absolute -left-16 top-1/2 transform -translate-y-1/2 bg-white rounded-lg shadow-xl p-4 border"
      >
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600">99.9%</div>
          <div className="text-sm text-gray-600">Uptime</div>
        </div>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, x: 50 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.8, delay: 2.2 }}
        className="absolute -right-16 top-1/3 transform -translate-y-1/2 bg-white rounded-lg shadow-xl p-4 border"
      >
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">24/7</div>
          <div className="text-sm text-gray-600">Support</div>
        </div>
      </motion.div>
    </motion.div>
  )
}