"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { ChevronLeft, ChevronRight, Quote } from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { But<PERSON> } from "@/components/ui/button"

export function TestimonialSlider() {
  const testimonials = [
    {
      id: 1,
      content:
        "WizeAssets has transformed how we manage our company assets. The intuitive interface and powerful features have saved us countless hours and improved our asset utilization.",
      author: {
        name: "<PERSON>",
        title: "CTO at TechInnovate",
        avatar: "/placeholder.svg?height=40&width=40",
        initials: "SC",
      },
    },
    {
      id: 2,
      content:
        "Since implementing WizeAssets, we've reduced asset downtime by 35% and improved our maintenance scheduling. The reporting features provide invaluable insights for our management team.",
      author: {
        name: "<PERSON>",
        title: "Operations Director at GlobalManufacturing",
        avatar: "/placeholder.svg?height=40&width=40",
        initials: "<PERSON><PERSON>",
      },
    },
    {
      id: 3,
      content:
        "The customizable workflows in WizeAssets have allowed us to tailor the system to our specific needs. The support team has been exceptional in helping us get the most out of the platform.",
      author: {
        name: "<PERSON>sha <PERSON>",
        title: "IT Manager at HealthPlus",
        avatar: "/placeholder.svg?height=40&width=40",
        initials: "AP",
      },
    },
  ]

  const [current, setCurrent] = useState(0)
  const [direction, setDirection] = useState(0)

  const nextTestimonial = () => {
    setDirection(1)
    setCurrent((prev) => (prev + 1) % testimonials.length)
  }

  const prevTestimonial = () => {
    setDirection(-1)
    setCurrent((prev) => (prev - 1 + testimonials.length) % testimonials.length)
  }

  useEffect(() => {
    const timer = setTimeout(() => {
      nextTestimonial()
    }, 8000)

    return () => clearTimeout(timer)
  }, [current])

  const variants = {
    enter: (direction: number) => ({
      x: direction > 0 ? 1000 : -1000,
      opacity: 0,
    }),
    center: {
      x: 0,
      opacity: 1,
    },
    exit: (direction: number) => ({
      x: direction < 0 ? 1000 : -1000,
      opacity: 0,
    }),
  }

  return (
    <div className="relative">
      <div className="absolute -top-16 left-0 text-9xl text-primary/10">
        <Quote />
      </div>

      <div className="relative overflow-hidden rounded-xl border bg-background/80 backdrop-blur-sm p-8 md:p-12">
        <div className="mx-auto max-w-4xl">
          <AnimatePresence custom={direction} mode="wait">
            <motion.div
              key={current}
              custom={direction}
              variants={variants}
              initial="enter"
              animate="center"
              exit="exit"
              transition={{ duration: 0.5, ease: "easeInOut" }}
              className="flex flex-col items-center text-center"
            >
              <p className="mb-8 text-xl md:text-2xl leading-relaxed">{testimonials[current].content}</p>
              <div className="flex flex-col items-center">
                <Avatar className="h-16 w-16 mb-4">
                  <AvatarImage src={testimonials[current].author.avatar} alt={testimonials[current].author.name} />
                  <AvatarFallback>{testimonials[current].author.initials}</AvatarFallback>
                </Avatar>
                <div className="text-lg font-semibold">{testimonials[current].author.name}</div>
                <div className="text-sm text-muted-foreground">{testimonials[current].author.title}</div>
              </div>
            </motion.div>
          </AnimatePresence>
        </div>

        <div className="absolute bottom-4 left-0 right-0 flex justify-center gap-2">
          {testimonials.map((_, index) => (
            <button
              key={index}
              onClick={() => {
                setDirection(index > current ? 1 : -1)
                setCurrent(index)
              }}
              className={`h-2 w-2 rounded-full ${index === current ? "bg-primary" : "bg-primary/30"}`}
              aria-label={`Go to testimonial ${index + 1}`}
            />
          ))}
        </div>

        <Button
          variant="ghost"
          size="icon"
          className="absolute left-4 top-1/2 -translate-y-1/2 rounded-full"
          onClick={prevTestimonial}
        >
          <ChevronLeft className="h-6 w-6" />
          <span className="sr-only">Previous testimonial</span>
        </Button>

        <Button
          variant="ghost"
          size="icon"
          className="absolute right-4 top-1/2 -translate-y-1/2 rounded-full"
          onClick={nextTestimonial}
        >
          <ChevronRight className="h-6 w-6" />
          <span className="sr-only">Next testimonial</span>
        </Button>
      </div>
    </div>
  )
}

