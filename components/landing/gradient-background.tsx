"use client"

import { useEffect, useRef } from "react"

export function GradientBackground() {
  const canvasRef = useRef<HTMLCanvasElement>(null)

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    let width = window.innerWidth
    let height = window.innerHeight

    const resizeCanvas = () => {
      width = window.innerWidth
      height = window.innerHeight
      canvas.width = width
      canvas.height = height * 3 // Make canvas taller to cover the entire page
      drawGradient()
    }

    const drawGradient = () => {
      const gradient = ctx.createLinearGradient(0, 0, width, height)
      gradient.addColorStop(0, "rgba(59, 130, 246, 0.05)") // blue-500 with low opacity
      gradient.addColorStop(0.3, "rgba(99, 102, 241, 0.08)") // indigo-500 with low opacity
      gradient.addColorStop(0.6, "rgba(139, 92, 246, 0.05)") // purple-500 with low opacity
      gradient.addColorStop(1, "rgba(236, 72, 153, 0.03)") // pink-500 with very low opacity

      ctx.fillStyle = gradient
      ctx.fillRect(0, 0, width, height * 3)

      // Add some subtle circular gradients
      for (let i = 0; i < 5; i++) {
        const x = Math.random() * width
        const y = Math.random() * height * 3
        const radius = Math.random() * 300 + 100

        const circleGradient = ctx.createRadialGradient(x, y, 0, x, y, radius)
        circleGradient.addColorStop(0, "rgba(79, 70, 229, 0.1)") // indigo-600 with low opacity
        circleGradient.addColorStop(1, "rgba(79, 70, 229, 0)")

        ctx.fillStyle = circleGradient
        ctx.beginPath()
        ctx.arc(x, y, radius, 0, Math.PI * 2)
        ctx.fill()
      }
    }

    window.addEventListener("resize", resizeCanvas)
    resizeCanvas()

    return () => {
      window.removeEventListener("resize", resizeCanvas)
    }
  }, [])

  return (
    <canvas
      ref={canvasRef}
      className="fixed inset-0 -z-10 h-full w-full object-cover"
      style={{ pointerEvents: "none" }}
    />
  )
}

