"use client"

import React, { useState, useEffect } from 'react'
import { useExecution } from '@/lib/hooks/use-asset-automation'
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertTriangle,
  ArrowLeft,
  Download,
  RefreshCw,
  Code,
  Activity,
  Database,
  Layers
} from 'lucide-react'
import { format } from 'date-fns'
import { TriggerJobExecution } from '@/lib/advanced-features/automation/types'

interface ExecutionDetailsProps {
  execution: TriggerJobExecution
  onBack: () => void
  onRefresh: () => void
}

export const ExecutionDetails: React.FC<ExecutionDetailsProps> = ({
  execution: propExecution,
  onBack,
  onRefresh: propOnRefresh
}) => {
  const [activeTab, setActiveTab] = useState('overview')
  
  // Use real API data via hook
  const {
    execution: apiExecution,
    loading,
    error,
    fetchExecution
  } = useExecution(propExecution?.id)
  
  // Use API execution if available, otherwise use prop execution
  const execution = apiExecution || propExecution
  
  // Refresh execution
  const onRefresh = () => {
    fetchExecution()
    if (propOnRefresh) {
      propOnRefresh()
    }
  }
  
  // Auto-refresh execution every 5 seconds if it's running
  useEffect(() => {
    if (execution?.status === 'running' || execution?.status === 'queued') {
      const interval = setInterval(onRefresh, 5000)
      return () => clearInterval(interval)
    }
  }, [execution?.status])

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return (
          <Badge className="bg-green-100 text-green-800 hover:bg-green-200">
            <CheckCircle className="h-3 w-3 mr-1" />
            Completed
          </Badge>
        )
      case 'failed':
        return (
          <Badge variant="destructive">
            <XCircle className="h-3 w-3 mr-1" />
            Failed
          </Badge>
        )
      case 'running':
        return (
          <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200">
            <Clock className="h-3 w-3 mr-1 animate-spin" />
            Running
          </Badge>
        )
      case 'queued':
        return (
          <Badge variant="outline" className="text-muted-foreground">
            <Clock className="h-3 w-3 mr-1" />
            Queued
          </Badge>
        )
      case 'cancelled':
        return (
          <Badge variant="secondary">
            <AlertTriangle className="h-3 w-3 mr-1" />
            Cancelled
          </Badge>
        )
      default:
        return (
          <Badge variant="outline">{status}</Badge>
        )
    }
  }

  const downloadExecutionData = () => {
    const dataStr = JSON.stringify(execution, null, 2)
    const dataUri = `data:application/json;charset=utf-8,${encodeURIComponent(dataStr)}`
    
    const exportFileDefaultName = `execution-${execution.id}.json`
    
    const linkElement = document.createElement('a')
    linkElement.setAttribute('href', dataUri)
    linkElement.setAttribute('download', exportFileDefaultName)
    linkElement.click()
  }

  return (
    <Card className="h-full flex flex-col">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" onClick={onBack}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <CardTitle className="flex items-center gap-2">
                Execution Details
                {getStatusBadge(execution.status)}
              </CardTitle>
              <CardDescription>
                {execution.id} • {format(new Date(execution.startedAt), 'PPpp')}
              </CardDescription>
            </div>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={onRefresh}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button variant="outline" size="sm" onClick={downloadExecutionData}>
              <Download className="h-4 w-4 mr-2" />
              Download
            </Button>
          </div>
        </div>
      </CardHeader>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <TabsList className="mx-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="nodes">Node Results</TabsTrigger>
          <TabsTrigger value="variables">Variables</TabsTrigger>
          <TabsTrigger value="errors">Errors ({execution.context.errors.length})</TabsTrigger>
          <TabsTrigger value="raw">Raw Data</TabsTrigger>
        </TabsList>

        <CardContent className="p-0 flex-1">
          <TabsContent value="overview" className="m-0 h-full">
            <ScrollArea className="h-full">
              <div className="p-6">
                <div className="grid grid-cols-2 gap-6">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">Execution Info</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <dl className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <dt className="text-muted-foreground">ID:</dt>
                          <dd className="font-medium">{execution.id}</dd>
                        </div>
                        <div className="flex justify-between">
                          <dt className="text-muted-foreground">Job ID:</dt>
                          <dd className="font-medium">{execution.jobId}</dd>
                        </div>
                        <div className="flex justify-between">
                          <dt className="text-muted-foreground">Status:</dt>
                          <dd>{getStatusBadge(execution.status)}</dd>
                        </div>
                        <div className="flex justify-between">
                          <dt className="text-muted-foreground">Started:</dt>
                          <dd>{format(new Date(execution.startedAt), 'PPpp')}</dd>
                        </div>
                        {execution.completedAt && (
                          <div className="flex justify-between">
                            <dt className="text-muted-foreground">Completed:</dt>
                            <dd>{format(new Date(execution.completedAt), 'PPpp')}</dd>
                          </div>
                        )}
                        <div className="flex justify-between">
                          <dt className="text-muted-foreground">Duration:</dt>
                          <dd>
                            {execution.completedAt 
                              ? `${Math.round((new Date(execution.completedAt).getTime() - new Date(execution.startedAt).getTime()) / 1000)}s`
                              : 'In progress'
                            }
                          </dd>
                        </div>
                      </dl>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">Execution Stats</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <dl className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <dt className="text-muted-foreground">Nodes Executed:</dt>
                          <dd className="font-medium">
                            {Object.keys(execution.context.nodeResults).length}
                          </dd>
                        </div>
                        <div className="flex justify-between">
                          <dt className="text-muted-foreground">Errors:</dt>
                          <dd className="font-medium">
                            <Badge variant={execution.context.errors.length > 0 ? "destructive" : "outline"}>
                              {execution.context.errors.length}
                            </Badge>
                          </dd>
                        </div>
                        <div className="flex justify-between">
                          <dt className="text-muted-foreground">Webhook Responses:</dt>
                          <dd className="font-medium">
                            {Object.keys(execution.context.webhookResponses).length}
                          </dd>
                        </div>
                        <div className="flex justify-between">
                          <dt className="text-muted-foreground">Variables:</dt>
                          <dd className="font-medium">
                            {Object.keys(execution.context.variables).length}
                          </dd>
                        </div>
                      </dl>
                    </CardContent>
                  </Card>
                </div>

                {execution.context.errors.length > 0 && (
                  <Card className="mt-6 border-red-200">
                    <CardHeader className="pb-2 text-red-700">
                      <CardTitle className="text-base flex items-center gap-2">
                        <AlertTriangle className="h-4 w-4" />
                        Execution Errors
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        {execution.context.errors.map((error, index) => (
                          <div key={index} className="bg-red-50 p-3 rounded-md">
                            <div className="flex justify-between">
                              <span className="font-medium text-red-800">
                                {error.nodeId || 'Unknown Node'}
                              </span>
                              <Badge variant="outline" className="text-red-700 bg-red-100">
                                {error.severity}
                              </Badge>
                            </div>
                            <p className="mt-1 text-red-700">{error.error}</p>
                            <div className="text-xs text-red-600 mt-1">
                              {format(new Date(error.timestamp), 'PPpp')}
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}

                {execution.output && (
                  <Card className="mt-6">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">Execution Output</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <pre className="bg-muted p-4 rounded-md overflow-auto text-sm font-mono max-h-60">
                        {JSON.stringify(execution.output, null, 2)}
                      </pre>
                    </CardContent>
                  </Card>
                )}
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent value="nodes" className="m-0 h-full">
            <ScrollArea className="h-full">
              <div className="p-6">
                <h3 className="text-lg font-medium mb-4">Node Execution Results</h3>
                
                {Object.keys(execution.context.nodeResults).length > 0 ? (
                  <div className="space-y-4">
                    {Object.entries(execution.context.nodeResults).map(([nodeId, result]) => (
                      <Card key={nodeId}>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-base flex items-center gap-2">
                            <Activity className="h-4 w-4 text-blue-500" />
                            Node: {nodeId}
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <pre className="bg-muted p-3 rounded-md overflow-auto text-sm font-mono max-h-40">
                            {JSON.stringify(result, null, 2)}
                          </pre>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <Layers className="h-12 w-12 text-muted-foreground mx-auto mb-4 opacity-50" />
                    <h3 className="text-lg font-medium">No node results available</h3>
                    <p className="text-muted-foreground mt-1">
                      This execution has no recorded node results
                    </p>
                  </div>
                )}
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent value="variables" className="m-0 h-full">
            <ScrollArea className="h-full">
              <div className="p-6">
                <h3 className="text-lg font-medium mb-4">Execution Variables</h3>
                
                {Object.keys(execution.context.variables).length > 0 ? (
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base flex items-center gap-2">
                        <Database className="h-4 w-4 text-blue-500" />
                        Variables
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <pre className="bg-muted p-4 rounded-md overflow-auto text-sm font-mono max-h-96">
                        {JSON.stringify(execution.context.variables, null, 2)}
                      </pre>
                    </CardContent>
                  </Card>
                ) : (
                  <div className="text-center py-12">
                    <Database className="h-12 w-12 text-muted-foreground mx-auto mb-4 opacity-50" />
                    <h3 className="text-lg font-medium">No variables available</h3>
                    <p className="text-muted-foreground mt-1">
                      This execution has no recorded variables
                    </p>
                  </div>
                )}
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent value="errors" className="m-0 h-full">
            <ScrollArea className="h-full">
              <div className="p-6">
                <h3 className="text-lg font-medium mb-4">Execution Errors</h3>
                
                {execution.context.errors.length > 0 ? (
                  <div className="space-y-4">
                    {execution.context.errors.map((error, index) => (
                      <Card key={index} className="border-red-200">
                        <CardHeader className="pb-2 text-red-700">
                          <CardTitle className="text-base flex items-center gap-2">
                            <AlertTriangle className="h-4 w-4" />
                            Error in {error.nodeId || 'Unknown Node'}
                          </CardTitle>
                          <CardDescription className="text-red-600">
                            {format(new Date(error.timestamp), 'PPpp')}
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="bg-red-50 p-3 rounded-md">
                            <div className="flex justify-between mb-2">
                              <span className="font-medium text-red-800">
                                Error Message
                              </span>
                              <Badge variant="outline" className="text-red-700 bg-red-100">
                                {error.severity}
                              </Badge>
                            </div>
                            <p className="text-red-700 whitespace-pre-wrap font-mono text-sm">
                              {error.error}
                            </p>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                    <h3 className="text-lg font-medium">No errors found</h3>
                    <p className="text-muted-foreground mt-1">
                      This execution completed without any errors
                    </p>
                  </div>
                )}
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent value="raw" className="m-0 h-full">
            <ScrollArea className="h-full">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium">Raw Execution Data</h3>
                  <Button variant="outline" size="sm" onClick={downloadExecutionData}>
                    <Download className="h-4 w-4 mr-2" />
                    Download JSON
                  </Button>
                </div>
                
                <Card>
                  <CardContent className="p-4">
                    <pre className="bg-muted p-4 rounded-md overflow-auto text-sm font-mono max-h-[calc(100vh-300px)]">
                      {JSON.stringify(execution, null, 2)}
                    </pre>
                  </CardContent>
                </Card>
              </div>
            </ScrollArea>
          </TabsContent>
        </CardContent>
      </Tabs>

      <CardFooter className="border-t p-4">
        <div className="flex justify-between w-full">
          <Button variant="outline" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to History
          </Button>
          
          {execution.status === 'running' && (
            <Button variant="outline" className="text-red-600 hover:text-red-700 hover:bg-red-50">
              Cancel Execution
            </Button>
          )}
        </div>
      </CardFooter>
    </Card>
  )
}

export default ExecutionDetails