"use client"

import React, { useState, useEffect } from 'react'
import { useWebhooks } from '@/lib/hooks/use-asset-automation'
import { toast } from '@/components/ui/use-toast'
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Copy, Globe, Webhook, Code, Play, CheckCircle2, AlertCircle } from 'lucide-react'
import { WebhookConfig } from '@/lib/advanced-features/automation/types'

interface WebhookConfigProps {
  webhook: WebhookConfig
  workflowId: string
  onUpdate: (webhook: WebhookConfig) => void
  onTest: (webhook: WebhookConfig) => Promise<any>
}

export const WebhookConfiguration: React.FC<WebhookConfigProps> = ({
  webhook: propWebhook,
  workflowId,
  onUpdate: propOnUpdate,
  onTest: propOnTest
}) => {
  const [config, setConfig] = useState<WebhookConfig>(propWebhook)
  const [activeTab, setActiveTab] = useState('settings')
  const [testResult, setTestResult] = useState<any>(null)
  const [isTesting, setIsTesting] = useState(false)
  
  // Use real API data via hook
  const {
    webhooks,
    loading,
    error,
    updateWebhook,
    testWebhook
  } = useWebhooks(workflowId)
  
  // Update config when webhook prop changes
  useEffect(() => {
    setConfig(propWebhook)
  }, [propWebhook])
  
  // Handle update
  const handleUpdate = async () => {
    try {
      // Update webhook via API
      const updatedWebhook = await updateWebhook(config.id, config)
      
      // Call onUpdate callback if provided
      if (propOnUpdate) {
        propOnUpdate(updatedWebhook)
      }
      
      // Show success toast
      toast({
        title: "Webhook updated",
        description: "Your webhook has been updated successfully.",
        variant: "success"
      })
    } catch (error: any) {
      console.error('Error updating webhook:', error)
      
      // Show error toast
      toast({
        title: "Error updating webhook",
        description: error.message || "An error occurred while updating the webhook.",
        variant: "destructive"
      })
    }
  }
  
  // Handle test
  const handleTest = async () => {
    setIsTesting(true)
    setIsLoading(true)
    setTestResult(null)
    setTestStatus('idle')
    
    try {
      // Test webhook via API
      const result = await testWebhook(config.id, {
        test: true,
        timestamp: new Date().toISOString()
      })
      
      setTestResult({
        success: true,
        data: result
      })
      
      setTestStatus('success')
      
      // Call onTest callback if provided
      if (propOnTest) {
        propOnTest(config)
      }
      
      // Show success toast
      toast({
        title: "Webhook tested",
        description: "Your webhook has been tested successfully.",
        variant: "success"
      })
    } catch (error: any) {
      console.error('Error testing webhook:', error)
      
      setTestResult({
        success: false,
        error: error.message || "An error occurred while testing the webhook."
      })
      
      setTestStatus('error')
      
      // Show error toast
      toast({
        title: "Error testing webhook",
        description: error.message || "An error occurred while testing the webhook.",
        variant: "destructive"
      })
    } finally {
      setIsTesting(false)
      setIsLoading(false)
    }
  }
  const [testStatus, setTestStatus] = useState<'idle' | 'success' | 'error'>('idle')
  const [isLoading, setIsLoading] = useState(false)

  const baseUrl = typeof window !== 'undefined' 
    ? `${window.location.protocol}//${window.location.host}`
    : ''
  
  const webhookUrl = `${baseUrl}/api/webhooks/${workflowId}/${config.id}`

  const handleChange = (key: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const handleHeaderChange = (key: string, value: string) => {
    setConfig(prev => ({
      ...prev,
      headers: {
        ...prev.headers,
        [key]: value
      }
    }))
  }

  const handleAddHeader = () => {
    setConfig(prev => ({
      ...prev,
      headers: {
        ...prev.headers,
        '': ''
      }
    }))
  }

  const handleRemoveHeader = (key: string) => {
    const newHeaders = { ...config.headers }
    delete newHeaders[key]
    
    setConfig(prev => ({
      ...prev,
      headers: newHeaders
    }))
  }

  const handleAuthChange = (type: string) => {
    setConfig(prev => ({
      ...prev,
      authentication: {
        type: type as any,
        config: {}
      }
    }))
  }

  const handleAuthConfigChange = (key: string, value: string) => {
    setConfig(prev => ({
      ...prev,
      authentication: {
        ...prev.authentication!,
        config: {
          ...prev.authentication!.config,
          [key]: value
        }
      }
    }))
  }

  const handleSave = () => {
    handleUpdate()
  }

  // This function is redundant and can be removed as we already have a handleTest function above

  const handleCopyUrl = () => {
    navigator.clipboard.writeText(webhookUrl)
  }

  const renderAuthFields = () => {
    const authType = config.authentication?.type || 'none'
    
    switch (authType) {
      case 'basic':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Username</Label>
              <Input
                value={config.authentication?.config.username || ''}
                onChange={(e) => handleAuthConfigChange('username', e.target.value)}
                placeholder="Username"
              />
            </div>
            <div className="space-y-2">
              <Label>Password</Label>
              <Input
                type="password"
                value={config.authentication?.config.password || ''}
                onChange={(e) => handleAuthConfigChange('password', e.target.value)}
                placeholder="Password"
              />
            </div>
          </div>
        )
      
      case 'bearer':
        return (
          <div className="space-y-2">
            <Label>Token</Label>
            <Input
              value={config.authentication?.config.token || ''}
              onChange={(e) => handleAuthConfigChange('token', e.target.value)}
              placeholder="Bearer token"
            />
          </div>
        )
      
      case 'api_key':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Key Name</Label>
              <Input
                value={config.authentication?.config.name || ''}
                onChange={(e) => handleAuthConfigChange('name', e.target.value)}
                placeholder="API key name (e.g. X-API-Key)"
              />
            </div>
            <div className="space-y-2">
              <Label>Key Value</Label>
              <Input
                value={config.authentication?.config.value || ''}
                onChange={(e) => handleAuthConfigChange('value', e.target.value)}
                placeholder="API key value"
              />
            </div>
            <div className="space-y-2">
              <Label>Location</Label>
              <Select
                value={config.authentication?.config.in || 'header'}
                onValueChange={(value) => handleAuthConfigChange('in', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select location" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="header">Header</SelectItem>
                  <SelectItem value="query">Query Parameter</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )
      
      default:
        return (
          <div className="text-center py-4 text-muted-foreground">
            <p>No authentication configured</p>
          </div>
        )
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Webhook className="h-5 w-5 text-blue-500" />
            <div>
              <CardTitle>{config.name}</CardTitle>
              <CardDescription>Webhook Configuration</CardDescription>
            </div>
          </div>
          <Badge variant="outline">{config.method}</Badge>
        </div>
      </CardHeader>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mx-6">
          <TabsTrigger value="settings">Settings</TabsTrigger>
          <TabsTrigger value="headers">Headers</TabsTrigger>
          <TabsTrigger value="auth">Authentication</TabsTrigger>
          <TabsTrigger value="test">Test</TabsTrigger>
        </TabsList>

        <CardContent className="p-6 pt-2">
          <TabsContent value="settings" className="mt-0 space-y-4">
            <div className="space-y-2">
              <Label>Webhook Name</Label>
              <Input
                value={config.name}
                onChange={(e) => handleChange('name', e.target.value)}
                placeholder="Webhook name"
              />
            </div>

            <div className="space-y-2">
              <Label>HTTP Method</Label>
              <Select
                value={config.method}
                onValueChange={(value) => handleChange('method', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select method" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="GET">GET</SelectItem>
                  <SelectItem value="POST">POST</SelectItem>
                  <SelectItem value="PUT">PUT</SelectItem>
                  <SelectItem value="DELETE">DELETE</SelectItem>
                  <SelectItem value="PATCH">PATCH</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Webhook URL</Label>
              <div className="flex gap-2">
                <Input
                  value={webhookUrl}
                  readOnly
                  className="font-mono text-sm bg-muted flex-1"
                />
                <Button variant="outline" size="icon" onClick={handleCopyUrl}>
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
              <p className="text-xs text-muted-foreground">
                This is the URL that will receive webhook requests
              </p>
            </div>

            <div className="flex items-center justify-between space-y-0 py-2">
              <Label>Active</Label>
              <Switch
                checked={config.authentication?.type !== 'none'}
                onCheckedChange={(checked) => 
                  handleChange('authentication', checked 
                    ? { type: 'api_key', config: {} } 
                    : { type: 'none', config: {} }
                  )
                }
              />
            </div>
          </TabsContent>

          <TabsContent value="headers" className="mt-0 space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="font-medium">Custom Headers</h3>
              <Button variant="outline" size="sm" onClick={handleAddHeader}>
                Add Header
              </Button>
            </div>

            {Object.keys(config.headers).length > 0 ? (
              <div className="space-y-4">
                {Object.entries(config.headers).map(([key, value], index) => (
                  <div key={index} className="flex gap-2 items-start">
                    <Input
                      value={key}
                      onChange={(e) => {
                        const newHeaders = { ...config.headers }
                        delete newHeaders[key]
                        handleHeaderChange(e.target.value, value)
                      }}
                      placeholder="Header name"
                      className="flex-1"
                    />
                    <Input
                      value={value}
                      onChange={(e) => handleHeaderChange(key, e.target.value)}
                      placeholder="Header value"
                      className="flex-1"
                    />
                    <Button 
                      variant="ghost" 
                      size="icon"
                      onClick={() => handleRemoveHeader(key)}
                    >
                      <AlertCircle className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4 text-muted-foreground">
                <p>No custom headers configured</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="auth" className="mt-0 space-y-4">
            <div className="space-y-2">
              <Label>Authentication Type</Label>
              <Select
                value={config.authentication?.type || 'none'}
                onValueChange={handleAuthChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select authentication type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">None</SelectItem>
                  <SelectItem value="basic">Basic Auth</SelectItem>
                  <SelectItem value="bearer">Bearer Token</SelectItem>
                  <SelectItem value="api_key">API Key</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {renderAuthFields()}
          </TabsContent>

          <TabsContent value="test" className="mt-0 space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="font-medium">Test Webhook</h3>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleTest}
                disabled={isLoading}
              >
                {isLoading ? 'Testing...' : 'Send Test Request'}
              </Button>
            </div>

            <div className="space-y-2">
              <Label>Webhook URL</Label>
              <div className="flex gap-2">
                <Input
                  value={webhookUrl}
                  readOnly
                  className="font-mono text-sm bg-muted flex-1"
                />
                <Button variant="outline" size="icon" onClick={handleCopyUrl}>
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Method</Label>
              <Badge variant="outline" className="text-sm">
                {config.method}
              </Badge>
            </div>

            {testResult && (
              <div className={`
                mt-4 p-4 rounded-md border
                ${testStatus === 'success' ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}
              `}>
                <div className="flex items-center gap-2 mb-2">
                  {testStatus === 'success' ? (
                    <>
                      <CheckCircle2 className="h-5 w-5 text-green-600" />
                      <h3 className="font-medium text-green-800">Test Successful</h3>
                    </>
                  ) : (
                    <>
                      <AlertCircle className="h-5 w-5 text-red-600" />
                      <h3 className="font-medium text-red-800">Test Failed</h3>
                    </>
                  )}
                </div>
                
                <pre className={`
                  text-xs font-mono p-3 rounded overflow-auto max-h-40
                  ${testStatus === 'success' ? 'bg-green-100' : 'bg-red-100'}
                `}>
                  {JSON.stringify(testResult, null, 2)}
                </pre>
              </div>
            )}
          </TabsContent>
        </CardContent>
      </Tabs>

      <CardFooter className="flex justify-end gap-2 p-6 pt-2 border-t">
        <Button variant="outline">Cancel</Button>
        <Button onClick={handleSave}>Save Webhook</Button>
      </CardFooter>
    </Card>
  )
}

export default WebhookConfiguration