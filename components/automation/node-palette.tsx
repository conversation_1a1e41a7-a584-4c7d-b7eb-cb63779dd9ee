"use client"

import React, { useState, use<PERSON>em<PERSON> } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Button } from '@/components/ui/button'
import { 
  Search,
  Play, 
  Mail, 
  Globe, 
  Database, 
  Bell, 
  GitBranch, 
  Split, 
  RotateCcw, 
  Shuffle, 
  Filter, 
  Merge, 
  Clock, 
  FileText, 
  Workflow,
  Webhook,
  Zap,
  Plus
} from 'lucide-react'
import { defaultNodeTypes, createNodeInstance } from '@/lib/advanced-features/automation/node-types'
import { assetNodeCategories, createAssetNode } from '@/lib/advanced-features/automation/asset-node-types'
import { useFlowEditorStore } from '@/lib/advanced-features/automation/flow-store'
import { NodeType, NodeTypeDefinition } from '@/lib/advanced-features/automation/types'

// Icon mapping
const iconMap = {
  Play: Play,
  Mail: Mail,
  Globe: Globe,
  Database: Database,
  Bell: Bell,
  GitBranch: GitBranch,
  Split: Split,
  RotateCcw: RotateCcw,
  Shuffle: Shuffle,
  Filter: Filter,
  Merge: Merge,
  Clock: Clock,
  FileText: FileText,
  Workflow: Workflow,
  Webhook: Webhook,
  Zap: Zap
}

interface NodePaletteProps {
  className?: string
  onNodeDragStart?: (nodeType: NodeType) => void
  onNodeAdd?: (nodeType: NodeType, position?: { x: number; y: number }) => void
}

export const NodePalette: React.FC<NodePaletteProps> = ({
  className,
  onNodeDragStart,
  onNodeAdd
}) => {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const { setDraggedNodeType } = useFlowEditorStore()

  // Group node types by category
  const categorizedNodes = useMemo(() => {
    const categories = {
      trigger: [] as NodeTypeDefinition[],
      action: [] as NodeTypeDefinition[],
      logic: [] as NodeTypeDefinition[],
      data: [] as NodeTypeDefinition[],
      integration: [] as NodeTypeDefinition[],
      'asset-management': [] as NodeTypeDefinition[],
      financial: [] as NodeTypeDefinition[],
      operations: [] as NodeTypeDefinition[]
    }

    // Add standard node types
    defaultNodeTypes.forEach(nodeType => {
      categories[nodeType.category].push(nodeType)
    })

    // Add asset-specific node types
    assetNodeCategories.forEach(category => {
      category.nodes.forEach(node => {
        categories[category.id].push({
          type: node.type as NodeType,
          label: node.name,
          description: node.description,
          category: category.id,
          icon: 'Zap'
        })
      })
    })

    return categories
  }, [])

  // Filter nodes based on search query
  const filteredNodes = useMemo(() => {
    const query = searchQuery.toLowerCase()
    const allNodes = selectedCategory === 'all' 
      ? defaultNodeTypes 
      : categorizedNodes[selectedCategory as keyof typeof categorizedNodes] || []

    if (!query) return allNodes

    return allNodes.filter(node =>
      node.label.toLowerCase().includes(query) ||
      node.description.toLowerCase().includes(query) ||
      node.type.toLowerCase().includes(query)
    )
  }, [searchQuery, selectedCategory, categorizedNodes])

  const handleDragStart = (event: React.DragEvent, nodeType: NodeType) => {
    event.dataTransfer.setData('application/reactflow', nodeType)
    event.dataTransfer.effectAllowed = 'move'
    setDraggedNodeType(nodeType)
    onNodeDragStart?.(nodeType)
  }

  const handleDragEnd = () => {
    setDraggedNodeType(null)
  }

  const handleNodeClick = (nodeType: NodeType) => {
    // Add node to center of viewport
    onNodeAdd?.(nodeType, { x: 250, y: 250 })
  }

  const NodeCard: React.FC<{ nodeType: NodeTypeDefinition }> = ({ nodeType }) => {
    const IconComponent = iconMap[nodeType.icon as keyof typeof iconMap] || Zap

    const categoryColors = {
      trigger: 'from-green-500 to-emerald-600',
      action: 'from-blue-500 to-indigo-600',
      logic: 'from-amber-500 to-orange-600',
      data: 'from-purple-500 to-violet-600',
      integration: 'from-red-500 to-rose-600',
      'asset-management': 'from-cyan-500 to-teal-600',
      financial: 'from-emerald-500 to-green-600',
      operations: 'from-indigo-500 to-blue-600'
    }

    const gradientClass = categoryColors[nodeType.category]

    return (
      <Card 
        className="cursor-grab hover:shadow-md transition-shadow border-2 hover:border-blue-300 group"
        draggable
        onDragStart={(e) => handleDragStart(e, nodeType.type)}
        onDragEnd={handleDragEnd}
        onClick={() => handleNodeClick(nodeType.type)}
      >
        <CardContent className="p-3">
          <div className="flex items-start gap-3">
            <div className={`p-2 rounded-lg bg-gradient-to-r ${gradientClass} text-white flex-shrink-0`}>
              <IconComponent className="h-4 w-4" />
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between mb-1">
                <h3 className="font-semibold text-sm truncate">{nodeType.label}</h3>
                <Badge variant="outline" className="text-xs ml-2">
                  {nodeType.category}
                </Badge>
              </div>
              
              <p className="text-xs text-muted-foreground line-clamp-2 mb-2">
                {nodeType.description}
              </p>
              
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <div className="flex gap-2">
                  {nodeType.inputs.length > 0 && (
                    <span>{nodeType.inputs.length} inputs</span>
                  )}
                  {nodeType.outputs.length > 0 && (
                    <span>{nodeType.outputs.length} outputs</span>
                  )}
                </div>
                
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={(e) => {
                    e.stopPropagation()
                    handleNodeClick(nodeType.type)
                  }}
                >
                  <Plus className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  const categoryStats = useMemo(() => {
    return Object.entries(categorizedNodes).map(([category, nodes]) => ({
      category,
      count: nodes.length
    }))
  }, [categorizedNodes])

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center gap-2">
          <Zap className="h-5 w-5 text-blue-500" />
          Node Palette
        </CardTitle>
        
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search nodes..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 h-9"
          />
        </div>
      </CardHeader>

      <CardContent className="p-0">
        <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
          <TabsList className="grid grid-cols-3 mx-4 mb-4">
            <TabsTrigger value="all" className="text-xs">
              All
            </TabsTrigger>
            <TabsTrigger value="standard" className="text-xs">
              Standard
            </TabsTrigger>
            <TabsTrigger value="asset-erp" className="text-xs">
              Asset ERP
            </TabsTrigger>
          </TabsList>
          
          {selectedCategory === 'standard' && (
            <TabsList className="grid grid-cols-5 mx-4 mb-4">
              {categoryStats
                .filter(({ category }) => ['trigger', 'action', 'logic', 'data', 'integration'].includes(category))
                .map(({ category, count }) => (
                  <TabsTrigger key={category} value={category} className="text-xs capitalize">
                    {category} ({count})
                  </TabsTrigger>
                ))}
            </TabsList>
          )}
          
          {selectedCategory === 'asset-erp' && (
            <TabsList className="grid grid-cols-3 mx-4 mb-4">
              {categoryStats
                .filter(({ category }) => ['asset-management', 'financial', 'operations'].includes(category))
                .map(({ category, count }) => (
                  <TabsTrigger key={category} value={category} className="text-xs capitalize">
                    {category.replace('-', ' ')} ({count})
                  </TabsTrigger>
                ))}
            </TabsList>
          )}

          <ScrollArea className="h-[600px] px-4">
            <TabsContent value="all" className="mt-0">
              <div className="space-y-2">
                {filteredNodes.map(nodeType => (
                  <NodeCard key={nodeType.type} nodeType={nodeType} />
                ))}
              </div>
            </TabsContent>

            {Object.entries(categorizedNodes).map(([category, nodes]) => (
              <TabsContent key={category} value={category} className="mt-0">
                <div className="space-y-2">
                  {nodes
                    .filter(node => {
                      const query = searchQuery.toLowerCase()
                      return !query ||
                        node.label.toLowerCase().includes(query) ||
                        node.description.toLowerCase().includes(query) ||
                        node.type.toLowerCase().includes(query)
                    })
                    .map(nodeType => (
                      <NodeCard key={nodeType.type} nodeType={nodeType} />
                    ))
                  }
                </div>
              </TabsContent>
            ))}
          </ScrollArea>
        </Tabs>

        {filteredNodes.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No nodes found matching "{searchQuery}"</p>
            <p className="text-sm">Try adjusting your search terms</p>
          </div>
        )}

        {/* Drag Instructions */}
        <div className="p-4 bg-muted/50 border-t">
          <p className="text-xs text-muted-foreground text-center">
            <strong>Tip:</strong> Drag nodes to the canvas or click to add them
          </p>
        </div>
      </CardContent>
    </Card>
  )
}

export default NodePalette