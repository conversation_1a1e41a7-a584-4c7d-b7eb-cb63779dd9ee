"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { AlertCircle, Save, X, Trash2, Code, Settings, Info, CheckCircle2 } from 'lucide-react'

import { useFlowEditorStore } from '@/lib/advanced-features/automation/flow-store'
import { getNodeTypeDefinition } from '@/lib/advanced-features/automation/node-types'
import { FlowNode, NodeTypeDefinition } from '@/lib/advanced-features/automation/types'

interface NodeConfigPanelProps {
  nodeId: string
  onClose: () => void
}

export const NodeConfigPanel: React.FC<NodeConfigPanelProps> = ({ nodeId, onClose }) => {
  const { getNodeById, updateNode, deleteNode } = useFlowEditorStore()
  const node = getNodeById(nodeId)
  
  const [config, setConfig] = useState<Record<string, any>>({})
  const [activeTab, setActiveTab] = useState('config')
  const [errors, setErrors] = useState<string[]>([])
  const [isConfigured, setIsConfigured] = useState(false)

  // Get node type definition
  const nodeTypeDef = node ? getNodeTypeDefinition(node.type) : null

  useEffect(() => {
    if (node) {
      setConfig(node.data.config || {})
      setErrors(node.data.errors || [])
      setIsConfigured(node.data.isConfigured || false)
    }
  }, [node])

  if (!node || !nodeTypeDef) {
    return (
      <Card className="w-full h-full">
        <CardContent className="p-6 flex items-center justify-center">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium">Node not found</h3>
            <p className="text-muted-foreground">The selected node could not be found.</p>
            <Button variant="outline" className="mt-4" onClick={onClose}>
              Close
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  const handleConfigChange = (key: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const validateConfig = (): string[] => {
    const newErrors: string[] = []
    
    if (!nodeTypeDef) return ['Node type definition not found']

    // Check required fields
    Object.entries(nodeTypeDef.configSchema).forEach(([key, schema]) => {
      if (schema.required && (!config[key] || config[key] === '')) {
        newErrors.push(`${schema.label || key} is required`)
      }
    })

    return newErrors
  }

  const handleSave = () => {
    const validationErrors = validateConfig()
    setErrors(validationErrors)

    if (validationErrors.length === 0) {
      updateNode(nodeId, {
        data: {
          ...node.data,
          config,
          isConfigured: true,
          errors: []
        }
      })
      setIsConfigured(true)
    } else {
      updateNode(nodeId, {
        data: {
          ...node.data,
          config,
          isConfigured: false,
          errors: validationErrors
        }
      })
      setIsConfigured(false)
    }
  }

  const handleDelete = () => {
    if (confirm('Are you sure you want to delete this node?')) {
      deleteNode(nodeId)
      onClose()
    }
  }

  const renderConfigField = (key: string, schema: any) => {
    const value = config[key]
    const label = schema.label || key.charAt(0).toUpperCase() + key.slice(1)
    
    switch (schema.type) {
      case 'string':
        return (
          <div className="space-y-2" key={key}>
            <Label htmlFor={key}>{label}</Label>
            <Input
              id={key}
              value={value || ''}
              onChange={(e) => handleConfigChange(key, e.target.value)}
              placeholder={schema.placeholder || ''}
              required={schema.required}
            />
            {schema.description && (
              <p className="text-xs text-muted-foreground">{schema.description}</p>
            )}
          </div>
        )
      
      case 'textarea':
        return (
          <div className="space-y-2" key={key}>
            <Label htmlFor={key}>{label}</Label>
            <Textarea
              id={key}
              value={value || ''}
              onChange={(e) => handleConfigChange(key, e.target.value)}
              placeholder={schema.placeholder || ''}
              required={schema.required}
              rows={5}
            />
            {schema.description && (
              <p className="text-xs text-muted-foreground">{schema.description}</p>
            )}
          </div>
        )
      
      case 'number':
        return (
          <div className="space-y-2" key={key}>
            <Label htmlFor={key}>{label}</Label>
            <Input
              id={key}
              type="number"
              value={value || 0}
              onChange={(e) => handleConfigChange(key, parseFloat(e.target.value))}
              placeholder={schema.placeholder || ''}
              required={schema.required}
            />
            {schema.description && (
              <p className="text-xs text-muted-foreground">{schema.description}</p>
            )}
          </div>
        )
      
      case 'boolean':
        return (
          <div className="flex items-center justify-between space-y-0 py-2" key={key}>
            <Label htmlFor={key}>{label}</Label>
            <Switch
              id={key}
              checked={value || false}
              onCheckedChange={(checked) => handleConfigChange(key, checked)}
            />
            {schema.description && (
              <p className="text-xs text-muted-foreground">{schema.description}</p>
            )}
          </div>
        )
      
      case 'select':
        return (
          <div className="space-y-2" key={key}>
            <Label htmlFor={key}>{label}</Label>
            <Select
              value={value || ''}
              onValueChange={(value) => handleConfigChange(key, value)}
            >
              <SelectTrigger>
                <SelectValue placeholder={schema.placeholder || `Select ${label}`} />
              </SelectTrigger>
              <SelectContent>
                {schema.options?.map((option: string) => (
                  <SelectItem key={option} value={option}>
                    {option}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {schema.description && (
              <p className="text-xs text-muted-foreground">{schema.description}</p>
            )}
          </div>
        )
      
      case 'object':
        return (
          <div className="space-y-2" key={key}>
            <Label htmlFor={key}>{label}</Label>
            <Textarea
              id={key}
              value={value ? JSON.stringify(value, null, 2) : '{}'}
              onChange={(e) => {
                try {
                  const parsed = JSON.parse(e.target.value)
                  handleConfigChange(key, parsed)
                } catch (error) {
                  // Don't update if invalid JSON
                }
              }}
              placeholder={schema.placeholder || '{}'}
              required={schema.required}
              rows={5}
              className="font-mono text-sm"
            />
            {schema.description && (
              <p className="text-xs text-muted-foreground">{schema.description}</p>
            )}
          </div>
        )
      
      case 'array':
        return (
          <div className="space-y-2" key={key}>
            <Label htmlFor={key}>{label}</Label>
            <Textarea
              id={key}
              value={value ? JSON.stringify(value, null, 2) : '[]'}
              onChange={(e) => {
                try {
                  const parsed = JSON.parse(e.target.value)
                  handleConfigChange(key, parsed)
                } catch (error) {
                  // Don't update if invalid JSON
                }
              }}
              placeholder={schema.placeholder || '[]'}
              required={schema.required}
              rows={5}
              className="font-mono text-sm"
            />
            {schema.description && (
              <p className="text-xs text-muted-foreground">{schema.description}</p>
            )}
          </div>
        )
      
      default:
        return (
          <div className="space-y-2" key={key}>
            <Label htmlFor={key}>{label}</Label>
            <Input
              id={key}
              value={value || ''}
              onChange={(e) => handleConfigChange(key, e.target.value)}
              placeholder={schema.placeholder || ''}
              required={schema.required}
            />
            {schema.description && (
              <p className="text-xs text-muted-foreground">{schema.description}</p>
            )}
          </div>
        )
    }
  }

  return (
    <Card className="w-full h-full border-0 rounded-none">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className={`p-2 rounded-lg bg-${nodeTypeDef.color} text-white`}>
              <Settings className="h-4 w-4" />
            </div>
            <div>
              <CardTitle className="text-lg">{node.data.label}</CardTitle>
              <CardDescription>{node.data.description || nodeTypeDef.description}</CardDescription>
            </div>
          </div>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
        
        <div className="flex items-center gap-2 mt-2">
          <Badge variant="outline">{node.type}</Badge>
          {isConfigured ? (
            <Badge variant="success" className="bg-green-100 text-green-800">
              <CheckCircle2 className="h-3 w-3 mr-1" />
              Configured
            </Badge>
          ) : (
            <Badge variant="destructive">Not Configured</Badge>
          )}
        </div>
      </CardHeader>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mx-6">
          <TabsTrigger value="config">Configuration</TabsTrigger>
          <TabsTrigger value="inputs">Inputs</TabsTrigger>
          <TabsTrigger value="outputs">Outputs</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
        </TabsList>

        <CardContent className="p-0">
          <ScrollArea className="h-[calc(100vh-300px)]">
            <div className="p-6 pt-2">
              <TabsContent value="config" className="mt-0 space-y-4">
                {nodeTypeDef.configSchema && Object.entries(nodeTypeDef.configSchema).map(
                  ([key, schema]) => renderConfigField(key, schema)
                )}

                {errors.length > 0 && (
                  <div className="bg-red-50 border border-red-200 rounded-md p-3 mt-4">
                    <div className="flex items-center gap-2 text-red-700 font-medium">
                      <AlertCircle className="h-4 w-4" />
                      Configuration Errors
                    </div>
                    <ul className="mt-2 space-y-1 text-sm text-red-600 list-disc list-inside">
                      {errors.map((error, index) => (
                        <li key={index}>{error}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="inputs" className="mt-0">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium">Input Ports</h3>
                    <Badge variant="outline">{node.data.inputs?.length || 0} inputs</Badge>
                  </div>
                  
                  {node.data.inputs && node.data.inputs.length > 0 ? (
                    <div className="space-y-3">
                      {node.data.inputs.map((input, index) => (
                        <Card key={index}>
                          <CardContent className="p-3">
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="font-medium">{input.label || input.id}</p>
                                <p className="text-xs text-muted-foreground">
                                  Type: {input.dataType}
                                  {input.required && ' (Required)'}
                                </p>
                              </div>
                              <Badge variant={input.required ? "destructive" : "outline"}>
                                {input.required ? 'Required' : 'Optional'}
                              </Badge>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-6 text-muted-foreground">
                      <Info className="h-12 w-12 mx-auto mb-2 opacity-50" />
                      <p>This node has no input ports</p>
                    </div>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="outputs" className="mt-0">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium">Output Ports</h3>
                    <Badge variant="outline">{node.data.outputs?.length || 0} outputs</Badge>
                  </div>
                  
                  {node.data.outputs && node.data.outputs.length > 0 ? (
                    <div className="space-y-3">
                      {node.data.outputs.map((output, index) => (
                        <Card key={index}>
                          <CardContent className="p-3">
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="font-medium">{output.label || output.id}</p>
                                <p className="text-xs text-muted-foreground">
                                  Type: {output.dataType}
                                </p>
                              </div>
                              <Badge variant="secondary">
                                {output.id}
                              </Badge>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-6 text-muted-foreground">
                      <Info className="h-12 w-12 mx-auto mb-2 opacity-50" />
                      <p>This node has no output ports</p>
                    </div>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="advanced" className="mt-0">
                <div className="space-y-4">
                  <div>
                    <h3 className="font-medium mb-2">Node ID</h3>
                    <Input value={node.id} readOnly className="bg-muted" />
                  </div>
                  
                  <div>
                    <h3 className="font-medium mb-2">Node Type</h3>
                    <Input value={node.type} readOnly className="bg-muted" />
                  </div>
                  
                  <div>
                    <h3 className="font-medium mb-2">Position</h3>
                    <div className="flex gap-2">
                      <Input 
                        value={node.position.x} 
                        readOnly 
                        className="bg-muted"
                        prefix="X:"
                      />
                      <Input 
                        value={node.position.y} 
                        readOnly 
                        className="bg-muted"
                        prefix="Y:"
                      />
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div>
                    <h3 className="font-medium mb-2">Raw Configuration</h3>
                    <Textarea
                      value={JSON.stringify(config, null, 2)}
                      readOnly
                      className="font-mono text-sm bg-muted h-40"
                    />
                  </div>
                </div>
              </TabsContent>
            </div>
          </ScrollArea>
        </CardContent>
      </Tabs>

      <CardFooter className="flex justify-between p-6 pt-2 border-t">
        <Button variant="destructive" onClick={handleDelete}>
          <Trash2 className="h-4 w-4 mr-2" />
          Delete Node
        </Button>
        <div className="flex gap-2">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            <Save className="h-4 w-4 mr-2" />
            Save Configuration
          </Button>
        </div>
      </CardFooter>
    </Card>
  )
}

export default NodeConfigPanel