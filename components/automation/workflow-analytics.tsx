"use client"

import React, { useEffect } from 'react'
import { useWorkflowAnalytics } from '@/lib/hooks/use-asset-automation'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  BarChart, 
  BarChart3, 
  Clock, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  Activity,
  TrendingUp,
  TrendingDown
} from 'lucide-react'
import { format } from 'date-fns'
import { WorkflowAnalytics, NodeAnalytics } from '@/lib/advanced-features/automation/types'
import { getChartColorArray } from '@/lib/utils/colors'
import {
  BarChart as RechartsBarChart,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ianGrid,
  <PERSON><PERSON><PERSON>,
  <PERSON>spons<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  Legend
} from 'recharts'

interface WorkflowAnalyticsProps {
  workflowId: string
  analytics?: WorkflowAnalytics
}

export const WorkflowAnalyticsComponent: React.FC<WorkflowAnalyticsProps> = ({
  workflowId,
  analytics: propAnalytics
}) => {
  const [activeTab, setActiveTab] = React.useState('overview')
  
  // Use real API data via hook
  const {
    analytics: apiAnalytics,
    loading,
    error,
    fetchAnalytics
  } = useWorkflowAnalytics(workflowId)
  
  // Use API analytics if available, otherwise use prop analytics
  const analytics = apiAnalytics || propAnalytics
  
  // Fetch analytics on mount and when tab changes
  useEffect(() => {
    fetchAnalytics()
    
    // Refresh analytics every 30 seconds
    const interval = setInterval(fetchAnalytics, 30000)
    return () => clearInterval(interval)
  }, [])

  // Show loading state
  if (loading || !analytics) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Workflow Analytics</CardTitle>
          <CardDescription>Loading analytics data...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    )
  }
  
  // Show error state
  if (error) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Workflow Analytics</CardTitle>
          <CardDescription className="text-red-500">Error loading analytics data</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64 text-red-500">
            <AlertTriangle className="h-6 w-6 mr-2" />
            <span>{error.message || 'An error occurred while loading analytics data'}</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Prepare data for charts
  const executionData = [
    { name: 'Successful', value: analytics.successfulExecutions, color: 'hsl(var(--chart-2))' },
    { name: 'Failed', value: analytics.failedExecutions, color: 'hsl(var(--destructive))' }
  ]

  const nodeExecutionData = Object.entries(analytics.nodePerformance || {}).map(([nodeId, data]) => ({
    name: nodeId,
    executions: data.totalExecutions,
    failures: data.failedExecutions,
    avgTime: data.averageExecutionTime
  }))

  const COLORS = getChartColorArray()

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle>Workflow Analytics</CardTitle>
        <CardDescription>
          Performance metrics and execution statistics
        </CardDescription>
      </CardHeader>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mx-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="nodes">Node Performance</TabsTrigger>
          <TabsTrigger value="charts">Charts</TabsTrigger>
        </TabsList>

        <CardContent className="p-0">
          <TabsContent value="overview" className="m-0">
            <div className="p-6">
              <div className="grid grid-cols-2 gap-4 mb-6">
                <Card>
                  <CardContent className="p-6">
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="text-sm text-muted-foreground">Total Executions</p>
                        <h3 className="text-2xl font-bold">{analytics.totalExecutions}</h3>
                      </div>
                      <BarChart3 className="h-8 w-8 text-primary opacity-80" />
                    </div>
                    {analytics.lastExecution && (
                      <p className="text-xs text-muted-foreground mt-2">
                        Last execution: {format(new Date(analytics.lastExecution), 'PPp')}
                      </p>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="text-sm text-muted-foreground">Success Rate</p>
                        <h3 className="text-2xl font-bold">
                          {analytics.totalExecutions > 0
                            ? `${Math.round((analytics.successfulExecutions / analytics.totalExecutions) * 100)}%`
                            : 'N/A'
                          }
                        </h3>
                      </div>
                      <div className="flex items-center gap-1">
                        {analytics.errorRate < 0.1 ? (
                          <TrendingUp className="h-8 w-8 text-chart-2" />
                        ) : (
                          <TrendingDown className="h-8 w-8 text-destructive" />
                        )}
                      </div>
                    </div>
                    <Progress 
                      value={analytics.totalExecutions > 0
                        ? (analytics.successfulExecutions / analytics.totalExecutions) * 100
                        : 0
                      } 
                      className="mt-2"
                    />
                  </CardContent>
                </Card>
              </div>

              <div className="grid grid-cols-3 gap-4 mb-6">
                <Card>
                  <CardContent className="p-6">
                    <div className="flex flex-col items-center">
                      <CheckCircle className="h-8 w-8 text-chart-2 mb-2" />
                      <p className="text-sm text-muted-foreground">Successful</p>
                      <h3 className="text-xl font-bold">{analytics.successfulExecutions}</h3>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex flex-col items-center">
                      <XCircle className="h-8 w-8 text-destructive mb-2" />
                      <p className="text-sm text-muted-foreground">Failed</p>
                      <h3 className="text-xl font-bold">{analytics.failedExecutions}</h3>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex flex-col items-center">
                      <Clock className="h-8 w-8 text-primary mb-2" />
                      <p className="text-sm text-muted-foreground">Avg. Duration</p>
                      <h3 className="text-xl font-bold">
                        {analytics.averageExecutionTime > 0
                          ? `${(analytics.averageExecutionTime / 1000).toFixed(2)}s`
                          : 'N/A'
                        }
                      </h3>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base">Execution Distribution</CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="h-60">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={executionData}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                          label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        >
                          {executionData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip />
                        <Legend />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="nodes" className="m-0">
            <ScrollArea className="h-[500px]">
              <div className="p-6">
                <h3 className="text-lg font-medium mb-4">Node Performance</h3>
                
                {Object.entries(analytics.nodeAnalytics).length > 0 ? (
                  <div className="space-y-4">
                    {Object.entries(analytics.nodeAnalytics).map(([nodeId, data]) => (
                      <Card key={nodeId}>
                        <CardContent className="p-4">
                          <div className="flex justify-between items-center mb-2">
                            <div>
                              <h4 className="font-medium">{nodeId}</h4>
                              <p className="text-xs text-muted-foreground">
                                {data.lastExecuted 
                                  ? `Last executed: ${format(new Date(data.lastExecuted), 'PPp')}`
                                  : 'Never executed'
                                }
                              </p>
                            </div>
                            <Badge 
                              variant={data.failures > 0 ? "destructive" : "outline"}
                              className="ml-2"
                            >
                              {data.failures > 0 
                                ? `${data.failures} failures`
                                : 'No failures'
                              }
                            </Badge>
                          </div>
                          
                          <div className="grid grid-cols-3 gap-4 mt-4">
                            <div>
                              <p className="text-xs text-muted-foreground">Executions</p>
                              <p className="font-medium">{data.executions}</p>
                            </div>
                            <div>
                              <p className="text-xs text-muted-foreground">Success Rate</p>
                              <p className="font-medium">
                                {data.executions > 0
                                  ? `${Math.round(((data.executions - data.failures) / data.executions) * 100)}%`
                                  : 'N/A'
                                }
                              </p>
                            </div>
                            <div>
                              <p className="text-xs text-muted-foreground">Avg. Duration</p>
                              <p className="font-medium">
                                {data.averageExecutionTime > 0
                                  ? `${(data.averageExecutionTime / 1000).toFixed(2)}s`
                                  : 'N/A'
                                }
                              </p>
                            </div>
                          </div>
                          
                          <Progress 
                            value={data.executions > 0
                              ? ((data.executions - data.failures) / data.executions) * 100
                              : 0
                            } 
                            className="mt-2"
                          />
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-4 opacity-50" />
                    <h3 className="text-lg font-medium">No node analytics available</h3>
                    <p className="text-muted-foreground mt-1">
                      Run the workflow to collect node performance data
                    </p>
                  </div>
                )}
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent value="charts" className="m-0">
            <div className="p-6">
              <div className="space-y-6">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">Node Execution Count</CardTitle>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="h-60">
                      <ResponsiveContainer width="100%" height="100%">
                        <RechartsBarChart data={nodeExecutionData}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="name" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Bar dataKey="executions" fill="hsl(var(--primary))" name="Executions" />
                          <Bar dataKey="failures" fill="hsl(var(--destructive))" name="Failures" />
                        </RechartsBarChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">Average Execution Time (ms)</CardTitle>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="h-60">
                      <ResponsiveContainer width="100%" height="100%">
                        <LineChart data={nodeExecutionData}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="name" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Line 
                            type="monotone" 
                            dataKey="avgTime" 
                            stroke="hsl(var(--chart-5))" 
                            name="Avg. Time (ms)"
                            activeDot={{ r: 8 }} 
                          />
                        </LineChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>
        </CardContent>
      </Tabs>
    </Card>
  )
}

export default WorkflowAnalyticsComponent