"use client"

import React, { memo, useMemo } from 'react'
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Settings, 
  AlertCircle, 
  CheckCircle2, 
  Play, 
  Mail, 
  Globe, 
  Database, 
  Bell, 
  GitBranch, 
  Split, 
  RotateCcw, 
  Shuffle, 
  Filter, 
  Merge, 
  Clock, 
  FileText, 
  Workflow,
  Webhook,
  Zap
} from 'lucide-react'
import { FlowNode } from '@/lib/advanced-features/automation/types'
import { useFlowEditorStore } from '@/lib/advanced-features/automation/flow-store'

// Icon mapping
const iconMap = {
  Play: Play,
  Mail: Mail,
  Globe: Globe,
  Database: Database,
  Bell: Bell,
  GitBranch: GitBranch,
  Split: Split,
  RotateCcw: RotateCcw,
  Shuffle: Shuffle,
  Filter: Filter,
  Merge: Merge,
  Clock: Clock,
  FileText: FileText,
  Workflow: Workflow,
  Webhook: Webhook,
  Zap: Zap
}

interface FlowNodeProps extends NodeProps {
  data: FlowNode['data']
  selected: boolean
}

// Base Node Component
export const BaseFlowNode = memo<FlowNodeProps>(({ data, selected, id }) => {
  const { selectNode, updateNode } = useFlowEditorStore()
  const IconComponent = iconMap[data.icon as keyof typeof iconMap] || Zap

  const categoryColors = {
    trigger: 'from-green-500 to-emerald-600',
    action: 'from-blue-500 to-indigo-600',
    logic: 'from-amber-500 to-orange-600',
    data: 'from-purple-500 to-violet-600',
    integration: 'from-red-500 to-rose-600'
  }

  const nodeType = data.config?.nodeType || 'action'
  const gradientClass = categoryColors[nodeType as keyof typeof categoryColors] || categoryColors.action

  const handleNodeClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    selectNode(id, e.metaKey || e.ctrlKey)
  }

  const handleConfigClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    // This would open the node configuration panel
    console.log('Configure node:', id)
  }

  const hasErrors = data.errors && data.errors.length > 0
  const isConfigured = data.isConfigured

  return (
    <div
      className={`
        relative min-w-[200px] max-w-[300px] cursor-pointer transition-all duration-200
        ${selected ? 'ring-2 ring-blue-500 ring-offset-2' : ''}
        hover:scale-105 hover:shadow-lg
      `}
      onClick={handleNodeClick}
    >
      {/* Input Handles */}
      {data.inputs?.map((input, index) => (
        <Handle
          key={input.id}
          type="target"
          position={Position.Left}
          id={input.id}
          style={{
            top: `${((index + 1) / (data.inputs!.length + 1)) * 100}%`,
            background: input.required ? 'hsl(var(--destructive))' : 'hsl(var(--muted-foreground))',
            width: '12px',
            height: '12px',
            border: '2px solid white'
          }}
          className="transition-colors hover:bg-primary"
        />
      ))}

      {/* Output Handles */}
      {data.outputs?.map((output, index) => (
        <Handle
          key={output.id}
          type="source"
          position={Position.Right}
          id={output.id}
          style={{
            top: `${((index + 1) / (data.outputs!.length + 1)) * 100}%`,
            background: 'hsl(var(--chart-2))',
            width: '12px',
            height: '12px',
            border: '2px solid white'
          }}
          className="transition-colors hover:bg-chart-2"
        />
      ))}

      <Card className="border-2 shadow-lg hover:shadow-xl transition-shadow">
        {/* Header with gradient */}
        <div className={`h-2 bg-gradient-to-r ${gradientClass} rounded-t-lg`} />
        
        <CardContent className="p-4">
          {/* Node Header */}
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <div className={`p-2 rounded-lg bg-gradient-to-r ${gradientClass} text-white`}>
                <IconComponent className="h-4 w-4" />
              </div>
              <div>
                <h3 className="font-semibold text-sm">{data.label}</h3>
                {data.description && (
                  <p className="text-xs text-muted-foreground line-clamp-1">
                    {data.description}
                  </p>
                )}
              </div>
            </div>
            
            {/* Status Indicators */}
            <div className="flex items-center gap-1">
              {hasErrors && (
                <AlertCircle className="h-4 w-4 text-red-500"/>
              )}
              {isConfigured && (
                <CheckCircle2 className="h-4 w-4 text-green-500" />
              )}
              <Button
                size="sm"
                variant="ghost"
                className="h-6 w-6 p-0"
                onClick={handleConfigClick}
              >
                <Settings className="h-3 w-3" />
              </Button>
            </div>
          </div>

          {/* Configuration Preview */}
          {data.config && Object.keys(data.config).length > 0 && (
            <div className="space-y-1">
              {Object.entries(data.config)
                .slice(0, 2)
                .map(([key, value]) => (
                  <div key={key} className="flex justify-between text-xs">
                    <span className="text-muted-foreground capitalize">
                      {key.replace(/([A-Z])/g, ' $1').trim()}:
                    </span>
                    <span className="font-mono truncate max-w-[100px]">
                      {typeof value === 'string' ? value : JSON.stringify(value)}
                    </span>
                  </div>
                ))}
            </div>
          )}

          {/* Connection Ports Info */}
          <div className="flex justify-between items-center mt-3 pt-2 border-t border-gray-100">
            <div className="flex gap-2">
              {data.inputs && data.inputs.length > 0 && (
                <Badge variant="outline" className="text-xs px-1">
                  {data.inputs.length} in
                </Badge>
              )}
              {data.outputs && data.outputs.length > 0 && (
                <Badge variant="outline" className="text-xs px-1">
                  {data.outputs.length} out
                </Badge>
              )}
            </div>
            
            {/* Node Type Badge */}
            <Badge 
              variant={isConfigured ? "default" : "secondary"} 
              className="text-xs"
            >
              {nodeType}
            </Badge>
          </div>

          {/* Error Messages */}
          {hasErrors && (
            <div className="mt-2 p-2 bg-destructive/10 border border-destructive/20 rounded text-xs">
              <p className="text-destructive font-medium">Errors:</p>
              <ul className="text-red-600 list-disc list-inside">
                {data.errors!.slice(0, 2).map((error, index) => (
                  <li key={index} className="truncate" title={error}>
                    {error}
                  </li>
                ))}
                {data.errors!.length > 2 && (
                  <li className="text-red-500">
                    +{data.errors!.length - 2} more errors
                  </li>
                )}
              </ul>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
})

BaseFlowNode.displayName = 'BaseFlowNode'

// Specialized Node Components
export const TriggerNode = memo<FlowNodeProps>((props) => {
  return <BaseFlowNode {...props} />
})

export const ActionNode = memo<FlowNodeProps>((props) => {
  return <BaseFlowNode {...props} />
})

export const ConditionNode = memo<FlowNodeProps>((props) => {
  const { data } = props
  
  return (
    <BaseFlowNode
      {...props}
      data={{
        ...data,
        outputs: [
          { id: 'true', type: 'output', dataType: 'any', label: 'True' },
          { id: 'false', type: 'output', dataType: 'any', label: 'False' }
        ]
      }}
    />
  )
})

export const DelayNode = memo<FlowNodeProps>((props) => {
  const { data } = props
  const duration = data.config?.duration || 0
  const unit = data.config?.unit || 'seconds'
  
  return (
    <BaseFlowNode
      {...props}
      data={{
        ...data,
        description: `Wait ${duration} ${unit}`
      }}
    />
  )
})

export const WebhookNode = memo<FlowNodeProps>((props) => {
  const { data } = props
  const method = data.config?.method || 'POST'
  const path = data.config?.path || '/webhook'
  
  return (
    <BaseFlowNode
      {...props}
      data={{
        ...data,
        description: `${method} ${path}`
      }}
    />
  )
})

export const DecisionNode = memo<FlowNodeProps>((props) => {
  const { data } = props
  const options = data.config?.options || []
  
  return (
    <BaseFlowNode
      {...props}
      data={{
        ...data,
        outputs: [
          ...options.map((option: any, index: number) => ({
            id: `option-${index}`,
            type: 'output' as const,
            dataType: 'any' as const,
            label: option.label || `Option ${index + 1}`
          })),
          { id: 'default', type: 'output' as const, dataType: 'any' as const, label: 'Default' }
        ]
      }}
    />
  )
})

export const LoopNode = memo<FlowNodeProps>((props) => {
  const { data } = props
  const arrayField = data.config?.array || 'data.items'
  
  return (
    <BaseFlowNode
      {...props}
      data={{
        ...data,
        description: `Loop over ${arrayField}`,
        outputs: [
          { id: 'item', type: 'output', dataType: 'any', label: 'Item' },
          { id: 'complete', type: 'output', dataType: 'any', label: 'Complete' }
        ]
      }}
    />
  )
})

export const SubflowNode = memo<FlowNodeProps>((props) => {
  const { data } = props
  const workflowId = data.config?.workflowId || 'Unknown'
  
  return (
    <BaseFlowNode
      {...props}
      data={{
        ...data,
        description: `Execute: ${workflowId}`
      }}
    />
  )
})

// Node type mapping for ReactFlow
export const nodeTypes = {
  trigger: TriggerNode,
  action: ActionNode,
  condition: ConditionNode,
  delay: DelayNode,
  webhook: WebhookNode,
  decision: DecisionNode,
  loop: LoopNode,
  subflow: SubflowNode,
  email: ActionNode,
  database: ActionNode,
  api: ActionNode,
  notification: ActionNode,
  file: ActionNode,
  transform: ActionNode,
  filter: ActionNode,
  merge: ActionNode
}

TriggerNode.displayName = 'TriggerNode'
ActionNode.displayName = 'ActionNode'
ConditionNode.displayName = 'ConditionNode'
DelayNode.displayName = 'DelayNode'
WebhookNode.displayName = 'WebhookNode'
DecisionNode.displayName = 'DecisionNode'
LoopNode.displayName = 'LoopNode'
SubflowNode.displayName = 'SubflowNode'