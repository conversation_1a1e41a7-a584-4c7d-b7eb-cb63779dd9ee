'use client'

import { useState, useCallback, useEffect } from 'react'
import { ReactFlowProvider } from 'reactflow'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { toast } from '@/components/ui/use-toast'
import { Save, Play, History, BarChart3, Webhook } from 'lucide-react'

import FlowEditor from './flow-editor/index'
import WorkflowExecutionList from './workflow-execution-list'
import WorkflowExecutionDetails from './workflow-execution-details'
import { 
  useWorkflow, 
  useWorkflowExecutions, 
  useExecution 
} from '@/lib/hooks/use-asset-automation'
import { FlowWorkflowDefinition } from '@/lib/advanced-features/automation/types'

interface FlowEditorModularProps {
  workflowId?: string
  className?: string
  readOnly?: boolean
}

export default function FlowEditorModular({
  workflowId,
  className,
  readOnly = false
}: FlowEditorModularProps) {
  const [activeTab, setActiveTab] = useState('editor')
  const [selectedExecutionId, setSelectedExecutionId] = useState<string | null>(null)
  
  // Use real API services via hooks
  const { 
    workflow, 
    loading: workflowLoading, 
    error: workflowError,
    updateWorkflow: saveWorkflowToApi,
    executeWorkflow: executeWorkflowApi
  } = useWorkflow(workflowId)
  
  const {
    executions,
    loading: executionsLoading,
    error: executionsError,
    fetchExecutions
  } = useWorkflowExecutions(workflowId)
  
  const {
    execution: selectedExecution,
    loading: executionLoading,
    error: executionError
  } = useExecution(selectedExecutionId)
  
  // Fetch executions when tab changes
  useEffect(() => {
    if (activeTab === 'executions') {
      fetchExecutions()
    }
  }, [activeTab, fetchExecutions])
  
  // Handle save workflow
  const handleSave = async (updatedWorkflow: FlowWorkflowDefinition) => {
    try {
      // Save to API
      const savedWorkflow = await saveWorkflowToApi(updatedWorkflow)
      
      // Show success toast
      toast({
        title: "Workflow saved",
        description: "Your workflow has been saved successfully.",
        variant: "success"
      })
      
      return savedWorkflow
    } catch (error: any) {
      console.error('Error saving workflow:', error)
      
      // Show error toast
      toast({
        title: "Error saving workflow",
        description: error.message || "An error occurred while saving the workflow.",
        variant: "destructive"
      })
      
      throw error
    }
  }
  
  // Handle execute workflow
  const handleExecute = async (workflowToExecute: FlowWorkflowDefinition) => {
    try {
      // Save workflow first
      await handleSave(workflowToExecute)
      
      // Execute workflow via API
      const executionId = await executeWorkflowApi()
      
      // Refresh executions list
      fetchExecutions()
      
      // Switch to executions tab
      setActiveTab('executions')
      
      // Select the new execution
      setSelectedExecutionId(executionId)
      
      // Show success toast
      toast({
        title: "Workflow executed",
        description: "Your workflow has been executed successfully.",
        variant: "success"
      })
    } catch (error: any) {
      console.error('Error executing workflow:', error)
      
      // Show error toast
      toast({
        title: "Error executing workflow",
        description: error.message || "An error occurred while executing the workflow.",
        variant: "destructive"
      })
    }
  }
  
  // Handle select execution
  const handleSelectExecution = useCallback((executionId: string) => {
    setSelectedExecutionId(executionId)
  }, [])
  
  // Render loading state
  if (workflowLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Loading Workflow</CardTitle>
          <CardDescription>Please wait while the workflow is loading...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[600px] flex items-center justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    )
  }
  
  // Render error state
  if (workflowError) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Error Loading Workflow</CardTitle>
          <CardDescription>An error occurred while loading the workflow.</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[600px] flex items-center justify-center">
            <div className="text-center">
              <p className="text-red-500 mb-4">{workflowError.message}</p>
              <Button onClick={() => window.location.reload()}>Retry</Button>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }
  
  // Render workflow not found
  if (!workflow) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Workflow Not Found</CardTitle>
          <CardDescription>The requested workflow could not be found.</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[600px] flex items-center justify-center">
            <div className="text-center">
              <p className="text-muted-foreground mb-4">
                The workflow with ID {workflowId} does not exist or has been deleted.
              </p>
              <Button onClick={() => window.history.back()}>Go Back</Button>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }
  
  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>{workflow.name}</CardTitle>
            <CardDescription>{workflow.description || 'No description'}</CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            {!readOnly && (
              <Button 
                onClick={() => handleExecute(workflow)}
                disabled={workflowLoading}
              >
                <Play className="h-4 w-4 mr-1" />
                Execute
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mx-4 mb-2">
            <TabsTrigger value="editor">
              Editor
            </TabsTrigger>
            <TabsTrigger value="executions">
              <History className="h-4 w-4 mr-1" />
              Executions
            </TabsTrigger>
            <TabsTrigger value="analytics">
              <BarChart3 className="h-4 w-4 mr-1" />
              Analytics
            </TabsTrigger>
            <TabsTrigger value="webhooks">
              <Webhook className="h-4 w-4 mr-1" />
              Webhooks
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="editor" className="m-0">
            <ReactFlowProvider>
              <FlowEditor 
                workflow={workflow} 
                onSave={handleSave} 
                onExecute={handleExecute}
                readOnly={readOnly}
              />
            </ReactFlowProvider>
          </TabsContent>
          
          <TabsContent value="executions" className="m-0 p-4">
            {selectedExecutionId ? (
              <div className="space-y-4">
                <Button 
                  variant="outline" 
                  onClick={() => setSelectedExecutionId(null)}
                >
                  Back to Executions
                </Button>
                <WorkflowExecutionDetails executionId={selectedExecutionId} />
              </div>
            ) : (
              <WorkflowExecutionList 
                workflowId={workflowId!} 
                onSelectExecution={handleSelectExecution} 
              />
            )}
          </TabsContent>
          
          <TabsContent value="analytics" className="m-0 p-4">
            <div className="h-[600px] flex items-center justify-center">
              <div className="text-center">
                <BarChart3 className="h-12 w-12 mx-auto text-muted-foreground/50 mb-2" />
                <p className="text-muted-foreground">
                  Workflow analytics will be displayed here
                </p>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="webhooks" className="m-0 p-4">
            <div className="h-[600px] flex items-center justify-center">
              <div className="text-center">
                <Webhook className="h-12 w-12 mx-auto text-muted-foreground/50 mb-2" />
                <p className="text-muted-foreground">
                  Webhook configuration will be displayed here
                </p>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}