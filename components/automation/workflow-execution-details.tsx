import { useState, useEffect } from 'react'
import { useExecution } from '@/lib/hooks/use-asset-automation'
import { TriggerJobExecution } from '@/lib/advanced-features/automation/types'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Skeleton } from '@/components/ui/skeleton'
import { Play, AlertTriangle, CheckCircle, Clock, XCircle, RefreshCw } from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'

interface WorkflowExecutionDetailsProps {
  executionId: string
}

export default function WorkflowExecutionDetails({ executionId }: WorkflowExecutionDetailsProps) {
  const { execution, loading, error, fetchExecution } = useExecution(executionId)
  const [activeTab, setActiveTab] = useState('overview')

  // Refresh execution data
  const handleRefresh = () => {
    fetchExecution()
  }

  // Format execution status for display
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-500">Completed</Badge>
      case 'failed':
        return <Badge className="bg-red-500">Failed</Badge>
      case 'running':
        return <Badge className="bg-blue-500">Running</Badge>
      case 'queued':
        return <Badge className="bg-yellow-500">Queued</Badge>
      case 'cancelled':
        return <Badge className="bg-gray-500">Cancelled</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  // Format execution status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'failed':
        return <XCircle className="h-5 w-5 text-red-500" />
      case 'running':
        return <Play className="h-5 w-5 text-blue-500" />
      case 'queued':
        return <Clock className="h-5 w-5 text-yellow-500" />
      case 'cancelled':
        return <AlertTriangle className="h-5 w-5 text-gray-500" />
      default:
        return <AlertTriangle className="h-5 w-5" />
    }
  }

  // Format time ago
  const formatTimeAgo = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true })
    } catch (error) {
      return dateString
    }
  }

  // Format duration
  const formatDuration = (startDate: string, endDate?: string) => {
    if (!endDate) return 'In progress'
    
    try {
      const start = new Date(startDate).getTime()
      const end = new Date(endDate).getTime()
      const durationMs = end - start
      
      if (durationMs < 1000) return `${durationMs}ms`
      if (durationMs < 60000) return `${Math.round(durationMs / 1000)}s`
      if (durationMs < 3600000) return `${Math.round(durationMs / 60000)}m ${Math.round((durationMs % 60000) / 1000)}s`
      
      const hours = Math.floor(durationMs / 3600000)
      const minutes = Math.round((durationMs % 3600000) / 60000)
      return `${hours}h ${minutes}m`
    } catch (error) {
      return 'Unknown'
    }
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>
            <Skeleton className="h-8 w-3/4" />
          </CardTitle>
          <CardDescription>
            <Skeleton className="h-4 w-1/2" />
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[400px] w-full" />
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-red-500">Error Loading Execution</CardTitle>
          <CardDescription>
            Failed to load execution details
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 text-red-500">
            <AlertTriangle className="h-5 w-5" />
            <p>{error.message || 'An error occurred while loading execution details'}</p>
          </div>
          <Button onClick={handleRefresh} className="mt-4">
            Retry
          </Button>
        </CardContent>
      </Card>
    )
  }

  if (!execution) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Execution Not Found</CardTitle>
          <CardDescription>
            The requested execution could not be found
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p>The execution with ID {executionId} does not exist or has been deleted.</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              {getStatusIcon(execution.status)}
              <span>Workflow Execution {execution.id}</span>
            </CardTitle>
            <CardDescription>
              Job ID: {execution.jobId} • Started {formatTimeAgo(execution.startedAt)}
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            {getStatusBadge(execution.status)}
            <Button size="sm" variant="outline" onClick={handleRefresh}>
              <RefreshCw className="h-4 w-4 mr-1" />
              Refresh
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="input">Input</TabsTrigger>
            <TabsTrigger value="output">Output</TabsTrigger>
            <TabsTrigger value="context">Context</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Status</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold flex items-center">
                    {getStatusIcon(execution.status)}
                    <span className="ml-2 capitalize">{execution.status}</span>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Duration</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {formatDuration(execution.startedAt, execution.completedAt)}
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Started</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {formatTimeAgo(execution.startedAt)}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {new Date(execution.startedAt).toLocaleString()}
                  </div>
                </CardContent>
              </Card>
            </div>
            
            {execution.context && execution.context.errors && execution.context.errors.length > 0 && (
              <Card className="border-red-200 bg-red-50">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-red-700">Errors</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {execution.context.errors.map((error: any, index: number) => (
                      <li key={index} className="text-red-700">
                        <div className="font-medium">{error.nodeId || 'Workflow Error'}</div>
                        <div className="text-sm">{error.error}</div>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}
            
            {execution.context && execution.context.nodeResults && (
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Node Execution</CardTitle>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-[300px]">
                    <ul className="space-y-4">
                      {Object.entries(execution.context.nodeResults).map(([nodeId, result]: [string, any]) => (
                        <li key={nodeId} className="border-b pb-3">
                          <div className="flex items-center justify-between">
                            <div className="font-medium">{nodeId}</div>
                            {result.error ? (
                              <Badge variant="destructive">Failed</Badge>
                            ) : (
                              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                                Success
                              </Badge>
                            )}
                          </div>
                          
                          {result.startTime && result.endTime && (
                            <div className="text-xs text-muted-foreground mt-1">
                              Duration: {formatDuration(result.startTime, result.endTime)}
                            </div>
                          )}
                          
                          {result.error ? (
                            <div className="mt-2 text-sm text-red-600 bg-red-50 p-2 rounded">
                              {result.error}
                            </div>
                          ) : (
                            <div className="mt-2 text-xs text-muted-foreground">
                              Executed successfully
                            </div>
                          )}
                        </li>
                      ))}
                    </ul>
                  </ScrollArea>
                </CardContent>
              </Card>
            )}
          </TabsContent>
          
          <TabsContent value="input">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm font-medium">Input Data</CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="bg-muted p-4 rounded-md overflow-auto text-xs">
                  {JSON.stringify(execution.context?.variables?.input || {}, null, 2)}
                </pre>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="output">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm font-medium">Output Data</CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="bg-muted p-4 rounded-md overflow-auto text-xs">
                  {JSON.stringify(execution.output || {}, null, 2)}
                </pre>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="context">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm font-medium">Execution Context</CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[500px]">
                  <pre className="bg-muted p-4 rounded-md overflow-auto text-xs">
                    {JSON.stringify(execution.context || {}, null, 2)}
                  </pre>
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="border-t bg-muted/50 flex justify-between">
        <div className="text-xs text-muted-foreground">
          Execution ID: {execution.id}
        </div>
        <div className="text-xs text-muted-foreground">
          {execution.completedAt ? `Completed ${formatTimeAgo(execution.completedAt)}` : 'In progress'}
        </div>
      </CardFooter>
    </Card>
  )
}