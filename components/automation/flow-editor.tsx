"use client"

import React, { use<PERSON><PERSON>back, useRef, useMemo, useState, useEffect } from 'react'
import {
  ReactFlow,
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  MiniMap,
  Background,
  BackgroundVariant,
  ReactFlowProvider,
  OnConnect,
  OnNodesChange,
  OnEdgesChange,
  ReactFlowInstance,
  Panel,
  NodeMouseHandler
} from 'reactflow'
import 'reactflow/dist/style.css'
import '@xyflow/react/dist/style.css';

import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { toast } from '@/components/ui/use-toast'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Save, 
  Play, 
  Square, 
  RotateCcw, 
  RotateCw, 
  Copy, 
  Scissors, 
  Clipboard,
  ZoomIn,
  ZoomOut,
  Maximize,
  <PERSON>rid,
  Eye,
  EyeOff,
  Settings,
  AlertTriangle,
  Webhook,
  BarChart3,
  History,
  Workflow
} from 'lucide-react'

import { nodeTypes } from './flow-nodes'
import NodePalette from './node-palette'
import NodeConfigPanel from './node-config-panel'
import WebhookConfiguration from './webhook-config'
import ExecutionHistory from './execution-history'
import ExecutionDetails from './execution-details'
import WorkflowAnalyticsComponent from './workflow-analytics'
import { useFlowEditorStore } from '@/lib/advanced-features/automation/flow-store'
import { createNodeInstance } from '@/lib/advanced-features/automation/node-types'
import { 
  useWorkflow, 
  useWorkflowExecutions, 
  useExecution, 
  useWorkflowAnalytics,
  useWebhooks,
  useAssetNodeFunctions
} from '@/lib/hooks/use-asset-automation'
import { 
  FlowNode, 
  FlowEdge, 
  NodeType, 
  WebhookConfig,
  TriggerJobExecution,
  WorkflowAnalytics
} from '@/lib/advanced-features/automation/types'

// Define the missing types
type OnDrop = (event: React.DragEvent<HTMLDivElement>) => void;
type OnDragOver = (event: React.DragEvent<HTMLDivElement>) => void;

interface FlowEditorProps {
  workflowId?: string
  className?: string
  onSave?: (workflow: any) => void
  onExecute?: (workflow: any) => void
  readOnly?: boolean
}

const FlowEditorComponent: React.FC<FlowEditorProps> = ({
  workflowId,
  className,
  onSave,
  onExecute,
  readOnly = false
}) => {
  const reactFlowWrapper = useRef<HTMLDivElement>(null)
  const [reactFlowInstance, setReactFlowInstance] = useState<ReactFlowInstance | null>(null)
  const [showMiniMap, setShowMiniMap] = useState(true)
  const [showGrid, setShowGrid] = useState(true)
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('editor')
  const [selectedExecutionId, setSelectedExecutionId] = useState<string | null>(null)
  
  // Use real API services via hooks
  const { 
    workflow: apiWorkflow, 
    loading: workflowLoading, 
    error: workflowError,
    updateWorkflow: saveWorkflowToApi,
    executeWorkflow: executeWorkflowApi
  } = useWorkflow(workflowId)
  
  const {
    executions,
    loading: executionsLoading,
    error: executionsError,
    fetchExecutions
  } = useWorkflowExecutions(workflowId)
  
  const {
    execution: selectedExecution,
    loading: executionLoading,
    error: executionError
  } = useExecution(selectedExecutionId)
  
  const {
    analytics,
    loading: analyticsLoading,
    error: analyticsError,
    fetchAnalytics
  } = useWorkflowAnalytics(workflowId)
  
  const {
    webhooks,
    loading: webhooksLoading,
    error: webhooksError,
    createWebhook,
    updateWebhook,
    deleteWebhook,
    testWebhook
  } = useWebhooks(workflowId)
  
  const {
    executeFunction: executeAssetFunction,
    loading: assetFunctionLoading
  } = useAssetNodeFunctions()

  // Store state
  const {
    workflow,
    selectedNodes,
    selectedEdges,
    isDirty,
    isEditing,
    setWorkflow,
    addNode,
    addEdge,
    updateNode,
    updateEdge,
    deleteNode,
    deleteEdge,
    selectNode,
    selectEdge,
    clearSelection,
    copySelection,
    pasteFromClipboard,
    undo,
    redo,
    validateWorkflow,
    autoLayout,
    setEditing
  } = useFlowEditorStore()
  
  // Sync API workflow with local state
  useEffect(() => {
    if (apiWorkflow && !workflowLoading) {
      // Update local state with API data
      setWorkflow(apiWorkflow)
    }
  }, [apiWorkflow, workflowLoading, setWorkflow])
  
  // Fetch executions and analytics when tab changes
  useEffect(() => {
    if (activeTab === 'executions') {
      fetchExecutions()
    } else if (activeTab === 'analytics') {
      fetchAnalytics()
    }
  }, [activeTab, fetchExecutions, fetchAnalytics])

  // Convert store data to ReactFlow format
  const nodes = useMemo(() => {
    if (!workflow) return []
    return workflow.nodes.map(node => ({
      ...node,
      selected: selectedNodes.includes(node.id)
    }))
  }, [workflow, selectedNodes])

  const edges = useMemo(() => {
    if (!workflow) return []
    return workflow.edges.map(edge => ({
      ...edge,
      selected: selectedEdges.includes(edge.id),
      animated: edge.animated || false,
      style: { stroke: edge.data?.color || '#6b7280' }
    }))
  }, [workflow, selectedEdges])

  // ReactFlow event handlers
  const onNodesChange: OnNodesChange = useCallback((changes) => {
    changes.forEach(change => {
      if (change.type === 'position' && change.dragging === false) {
        updateNode(change.id, { position: change.position })
      } else if (change.type === 'select') {
        if (change.selected) {
          selectNode(change.id, false)
        }
      } else if (change.type === 'remove') {
        deleteNode(change.id)
      }
    })
  }, [updateNode, selectNode, deleteNode])

  const onEdgesChange: OnEdgesChange = useCallback((changes) => {
    changes.forEach(change => {
      if (change.type === 'select') {
        if (change.selected) {
          selectEdge(change.id, false)
        }
      } else if (change.type === 'remove') {
        deleteEdge(change.id)
      }
    })
  }, [selectEdge, deleteEdge])

  const onConnect: OnConnect = useCallback((connection) => {
    const edge: FlowEdge = {
      id: `edge-${Date.now()}`,
      source: connection.source!,
      target: connection.target!,
      sourceHandle: connection.sourceHandle || undefined,
      targetHandle: connection.targetHandle || undefined,
      type: 'default',
      animated: true
    }
    addEdge(edge)
  }, [addEdge])

  const onDrop: OnDrop = useCallback((event) => {
    event.preventDefault()

    const reactFlowBounds = reactFlowWrapper.current?.getBoundingClientRect()
    const nodeType = event.dataTransfer.getData('application/reactflow') as NodeType

    if (!nodeType || !reactFlowInstance || !reactFlowBounds) return

    const position = reactFlowInstance.project({
      x: event.clientX - reactFlowBounds.left,
      y: event.clientY - reactFlowBounds.top,
    })

    const newNode = createNodeInstance(nodeType, position)
    addNode(newNode as FlowNode)
  }, [reactFlowInstance, addNode])

  const onDragOver: OnDragOver = useCallback((event) => {
    event.preventDefault()
    event.dataTransfer.dropEffect = 'move'
  }, [])

  const onNodeDoubleClick: NodeMouseHandler = useCallback((event, node) => {
    setSelectedNodeId(node.id)
  }, [])

  // Toolbar actions
  const handleSave = async () => {
    if (workflow) {
      try {
        // Save to API
        const updatedWorkflow = await saveWorkflowToApi({
          ...workflow,
          nodes,
          edges
        })
        
        // Update local state
        setWorkflow(updatedWorkflow)
        
        // Call onSave callback if provided
        if (onSave) {
          onSave(updatedWorkflow)
        }
        
        // Show success toast
        toast({
          title: "Workflow saved",
          description: "Your workflow has been saved successfully.",
          variant: "success"
        })
      } catch (error: any) {
        console.error('Error saving workflow:', error)
        
        // Show error toast
        toast({
          title: "Error saving workflow",
          description: error.message || "An error occurred while saving the workflow.",
          variant: "destructive"
        })
      }
    }
  }

  const handleExecute = async () => {
    if (workflow) {
      const errors = validateWorkflow()
      if (errors.length === 0) {
        try {
          // Execute workflow via API
          const executionId = await executeWorkflowApi()
          
          // Refresh executions list
          fetchExecutions()
          
          // Switch to executions tab
          setActiveTab('executions')
          
          // Select the new execution
          setSelectedExecutionId(executionId)
          
          // Show success toast
          toast({
            title: "Workflow executed",
            description: "Your workflow has been executed successfully.",
            variant: "success"
          })
          
          // Call onExecute callback if provided
          if (onExecute) {
            onExecute(workflow)
          }
        } catch (error: any) {
          console.error('Error executing workflow:', error)
          
          // Show error toast
          toast({
            title: "Error executing workflow",
            description: error.message || "An error occurred while executing the workflow.",
            variant: "destructive"
          })
        }
      } else {
        // Show validation errors
        toast({
          title: "Cannot execute workflow",
          description: (
            <ul className="list-disc pl-4">
              {errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          ),
          variant: "destructive"
        })
      }
    }
  }

  const handleFitView = () => {
    reactFlowInstance?.fitView({ padding: 0.2 })
  }

  const handleZoomIn = () => {
    reactFlowInstance?.zoomIn()
  }

  const handleZoomOut = () => {
    reactFlowInstance?.zoomOut()
  }

  const handleCopy = () => {
    copySelection()
  }

  const handlePaste = () => {
    const viewport = reactFlowInstance?.getViewport()
    const center = {
      x: (viewport?.x || 0) + 250,
      y: (viewport?.y || 0) + 250
    }
    pasteFromClipboard(center)
  }

  const handleUpdateWebhook = (webhook: WebhookConfig) => {
    if (!workflow) return

    const webhooks = [...workflow.webhooks]
    const index = webhooks.findIndex(w => w.id === webhook.id)
    
    if (index >= 0) {
      webhooks[index] = webhook
    } else {
      webhooks.push(webhook)
    }

    updateWorkflow({ webhooks })
  }

  const handleTestWebhook = async (webhook: WebhookConfig) => {
    // In a real app, this would make an actual test request
    return {
      success: true,
      message: 'Webhook test successful',
      timestamp: new Date().toISOString()
    }
  }

  const updateWorkflow = (updates: Partial<any>) => {
    if (!workflow) return
    setWorkflow({
      ...workflow,
      ...updates
    })
  }

  const refreshExecutions = () => {
    // In a real app, this would fetch executions from the API
    // For now, we'll use mock data
    setExecutions([
      {
        id: 'exec-1',
        jobId: 'workflow-1',
        status: 'completed',
        startedAt: new Date(Date.now() - 3600000),
        completedAt: new Date(Date.now() - 3590000),
        output: { success: true },
        context: {
          workflowId: 'workflow-1',
          executionId: 'exec-1',
          variables: { input: 'test' },
          nodeResults: {
            'node-1': { result: 'success' },
            'node-2': { result: 'success' }
          },
          startTime: new Date(Date.now() - 3600000),
          webhookResponses: {},
          errors: []
        }
      },
      {
        id: 'exec-2',
        jobId: 'workflow-1',
        status: 'failed',
        startedAt: new Date(Date.now() - 7200000),
        completedAt: new Date(Date.now() - 7190000),
        error: 'Node execution failed',
        context: {
          workflowId: 'workflow-1',
          executionId: 'exec-2',
          variables: { input: 'test' },
          nodeResults: {
            'node-1': { result: 'success' }
          },
          startTime: new Date(Date.now() - 7200000),
          webhookResponses: {},
          errors: [
            {
              nodeId: 'node-2',
              error: 'Execution failed: Invalid input',
              timestamp: new Date(Date.now() - 7190000),
              severity: 'error'
            }
          ]
        }
      }
    ])

    // Mock analytics data
    setAnalytics({
      workflowId: 'workflow-1',
      totalExecutions: 10,
      successfulExecutions: 8,
      failedExecutions: 2,
      averageExecutionTime: 5000,
      lastExecution: new Date(),
      errorRate: 0.2,
      nodeAnalytics: {
        'node-1': {
          nodeId: 'node-1',
          executions: 10,
          failures: 0,
          averageExecutionTime: 2000,
          lastExecuted: new Date()
        },
        'node-2': {
          nodeId: 'node-2',
          executions: 10,
          failures: 2,
          averageExecutionTime: 3000,
          lastExecuted: new Date()
        }
      }
    })
  }

  const workflowErrors = useMemo(() => validateWorkflow(), [validateWorkflow])

  // Initialize executions and analytics
  useEffect(() => {
    if (workflowId) {
      refreshExecutions()
    }
  }, [workflowId])

  return (
    <div className={`flex h-full ${className}`}>
      {/* Left Sidebar */}
      {!readOnly && activeTab === 'editor' && (
        <div className="w-80 border-r bg-background">
          <Tabs defaultValue="nodes">
            <TabsList className="w-full">
              <TabsTrigger value="nodes" className="flex-1">Nodes</TabsTrigger>
              <TabsTrigger value="templates" className="flex-1">Templates</TabsTrigger>
              <TabsTrigger value="webhooks" className="flex-1">Webhooks</TabsTrigger>
            </TabsList>
            
            <TabsContent value="nodes" className="mt-0">
              <NodePalette 
                onNodeAdd={(nodeType, position) => {
                  // Check if it's an asset-specific node type
                  const isAssetNode = [
                    'assetCreate', 'assetUpdate', 'assetQuery', 'assetDepreciation',
                    'assetMaintenance', 'assetTransfer', 'assetDisposal', 'inventoryCheck',
                    'purchaseOrder', 'invoiceProcess', 'approvalRequest', 'notifyStakeholders',
                    'generateReport'
                  ].includes(nodeType);
                  
                  let newNode;
                  if (isAssetNode) {
                    // Import dynamically to avoid circular dependencies
                    import('@/lib/advanced-features/automation/asset-node-types').then(({ createAssetNode }) => {
                      newNode = createAssetNode(nodeType as any, position || { x: 250, y: 250 });
                      addNode(newNode as FlowNode);
                    });
                  } else {
                    newNode = createNodeInstance(nodeType, position || { x: 250, y: 250 });
                    addNode(newNode as FlowNode);
                  }
                }}
              />
            </TabsContent>
            
            <TabsContent value="templates" className="mt-0 p-4">
              <div className="space-y-4">
                <h3 className="text-sm font-medium">Asset Management Templates</h3>
                <div className="space-y-2">
                  {/* Asset Acquisition Template */}
                  <Card className="cursor-pointer hover:bg-muted/50" onClick={() => {
                    // Import asset workflow templates
                    import('@/lib/advanced-features/automation/asset-node-types').then(({ assetWorkflowTemplates, createAssetNode }) => {
                      const template = assetWorkflowTemplates.find(t => t.id === 'asset-acquisition');
                      if (template) {
                        // Clear current workflow
                        setNodes([]);
                        setEdges([]);
                        
                        // Add nodes from template
                        const nodeMap: Record<string, string> = {};
                        template.nodes.forEach((nodeTemplate, index) => {
                          const nodeId = `${nodeTemplate.type}-${Date.now()}-${index}`;
                          nodeMap[nodeTemplate.type] = nodeId;
                          
                          const newNode = createAssetNode(
                            nodeTemplate.type as any, 
                            nodeTemplate.position
                          );
                          newNode.id = nodeId;
                          addNode(newNode as FlowNode);
                        });
                        
                        // Add edges from template
                        template.edges.forEach((edgeTemplate, index) => {
                          const sourceId = nodeMap[edgeTemplate.source];
                          const targetId = nodeMap[edgeTemplate.target];
                          if (sourceId && targetId) {
                            const edge: FlowEdge = {
                              id: `edge-${Date.now()}-${index}`,
                              source: sourceId,
                              target: targetId,
                              type: 'smoothstep'
                            };
                            addEdge(edge);
                          }
                        });
                      }
                    });
                  }}>
                    <CardContent className="p-3">
                      <div className="flex items-center gap-3">
                        <div className="bg-cyan-100 p-2 rounded-md">
                          <Workflow className="h-5 w-5 text-cyan-600" />
                        </div>
                        <div>
                          <h4 className="font-medium">Asset Acquisition Process</h4>
                          <p className="text-xs text-muted-foreground">Complete workflow for acquiring new assets</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  
                  {/* Asset Maintenance Template */}
                  <Card className="cursor-pointer hover:bg-muted/50" onClick={() => {
                    import('@/lib/advanced-features/automation/asset-node-types').then(({ assetWorkflowTemplates, createAssetNode }) => {
                      const template = assetWorkflowTemplates.find(t => t.id === 'asset-maintenance');
                      if (template) {
                        setNodes([]);
                        setEdges([]);
                        
                        const nodeMap: Record<string, string> = {};
                        template.nodes.forEach((nodeTemplate, index) => {
                          const nodeId = `${nodeTemplate.type}-${Date.now()}-${index}`;
                          nodeMap[nodeTemplate.type] = nodeId;
                          
                          const newNode = createAssetNode(
                            nodeTemplate.type as any, 
                            nodeTemplate.position
                          );
                          newNode.id = nodeId;
                          addNode(newNode as FlowNode);
                        });
                        
                        template.edges.forEach((edgeTemplate, index) => {
                          const sourceId = nodeMap[edgeTemplate.source];
                          const targetId = nodeMap[edgeTemplate.target];
                          if (sourceId && targetId) {
                            const edge: FlowEdge = {
                              id: `edge-${Date.now()}-${index}`,
                              source: sourceId,
                              target: targetId,
                              type: 'smoothstep'
                            };
                            addEdge(edge);
                          }
                        });
                      }
                    });
                  }}>
                    <CardContent className="p-3">
                      <div className="flex items-center gap-3">
                        <div className="bg-indigo-100 p-2 rounded-md">
                          <Workflow className="h-5 w-5 text-indigo-600" />
                        </div>
                        <div>
                          <h4 className="font-medium">Asset Maintenance Workflow</h4>
                          <p className="text-xs text-muted-foreground">Workflow for scheduling and tracking asset maintenance</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  
                  {/* Asset Disposal Template */}
                  <Card className="cursor-pointer hover:bg-muted/50" onClick={() => {
                    import('@/lib/advanced-features/automation/asset-node-types').then(({ assetWorkflowTemplates, createAssetNode }) => {
                      const template = assetWorkflowTemplates.find(t => t.id === 'asset-disposal');
                      if (template) {
                        setNodes([]);
                        setEdges([]);
                        
                        const nodeMap: Record<string, string> = {};
                        template.nodes.forEach((nodeTemplate, index) => {
                          const nodeId = `${nodeTemplate.type}-${Date.now()}-${index}`;
                          nodeMap[nodeTemplate.type] = nodeId;
                          
                          const newNode = createAssetNode(
                            nodeTemplate.type as any, 
                            nodeTemplate.position
                          );
                          newNode.id = nodeId;
                          addNode(newNode as FlowNode);
                        });
                        
                        template.edges.forEach((edgeTemplate, index) => {
                          const sourceId = nodeMap[edgeTemplate.source];
                          const targetId = nodeMap[edgeTemplate.target];
                          if (sourceId && targetId) {
                            const edge: FlowEdge = {
                              id: `edge-${Date.now()}-${index}`,
                              source: sourceId,
                              target: targetId,
                              type: 'smoothstep'
                            };
                            addEdge(edge);
                          }
                        });
                      }
                    });
                  }}>
                    <CardContent className="p-3">
                      <div className="flex items-center gap-3">
                        <div className="bg-red-100 p-2 rounded-md">
                          <Workflow className="h-5 w-5 text-red-600" />
                        </div>
                        <div>
                          <h4 className="font-medium">Asset Disposal Process</h4>
                          <p className="text-xs text-muted-foreground">Workflow for properly disposing of assets</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  
                  {/* Inventory Audit Template */}
                  <Card className="cursor-pointer hover:bg-muted/50" onClick={() => {
                    import('@/lib/advanced-features/automation/asset-node-types').then(({ assetWorkflowTemplates, createAssetNode }) => {
                      const template = assetWorkflowTemplates.find(t => t.id === 'inventory-audit');
                      if (template) {
                        setNodes([]);
                        setEdges([]);
                        
                        const nodeMap: Record<string, string> = {};
                        template.nodes.forEach((nodeTemplate, index) => {
                          const nodeId = `${nodeTemplate.type}-${Date.now()}-${index}`;
                          nodeMap[nodeTemplate.type] = nodeId;
                          
                          const newNode = createAssetNode(
                            nodeTemplate.type as any, 
                            nodeTemplate.position
                          );
                          newNode.id = nodeId;
                          addNode(newNode as FlowNode);
                        });
                        
                        template.edges.forEach((edgeTemplate, index) => {
                          const sourceId = nodeMap[edgeTemplate.source];
                          const targetId = nodeMap[edgeTemplate.target];
                          if (sourceId && targetId) {
                            const edge: FlowEdge = {
                              id: `edge-${Date.now()}-${index}`,
                              source: sourceId,
                              target: targetId,
                              type: 'smoothstep'
                            };
                            addEdge(edge);
                          }
                        });
                      }
                    });
                  }}>
                    <CardContent className="p-3">
                      <div className="flex items-center gap-3">
                        <div className="bg-green-100 p-2 rounded-md">
                          <Workflow className="h-5 w-5 text-green-600" />
                        </div>
                        <div>
                          <h4 className="font-medium">Inventory Audit Process</h4>
                          <p className="text-xs text-muted-foreground">Workflow for conducting inventory audits</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="webhooks" className="mt-0 p-4">
              {workflow?.webhooks && workflow.webhooks.length > 0 ? (
                <div className="space-y-4">
                  {workflow.webhooks.map((webhook) => (
                    <WebhookConfiguration
                      key={webhook.id}
                      webhook={webhook}
                      workflowId={workflow.id}
                      onUpdate={handleUpdateWebhook}
                      onTest={handleTestWebhook}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Webhook className="h-12 w-12 text-muted-foreground mx-auto mb-4 opacity-50" />
                  <h3 className="text-lg font-medium">No webhooks configured</h3>
                  <p className="text-muted-foreground mt-1 mb-4">
                    Add a webhook to trigger this workflow
                  </p>
                  <Button
                    onClick={() => {
                      const newWebhook: WebhookConfig = {
                        id: `webhook-${Date.now()}`,
                        name: 'New Webhook',
                        url: '',
                        method: 'POST',
                        headers: {}
                      }
                      handleUpdateWebhook(newWebhook)
                    }}
                  >
                    Add Webhook
                  </Button>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>
      )}

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Toolbar */}
        <Card className="border-b rounded-none">
          <div className="flex items-center justify-between p-3">
            <div className="flex items-center gap-2">
              <Tabs value={activeTab} onValueChange={setActiveTab} className="mr-4">
                <TabsList>
                  <TabsTrigger value="editor" className="flex items-center gap-1">
                    <Settings className="h-4 w-4" />
                    Editor
                  </TabsTrigger>
                  <TabsTrigger value="history" className="flex items-center gap-1">
                    <History className="h-4 w-4" />
                    History
                  </TabsTrigger>
                  <TabsTrigger value="analytics" className="flex items-center gap-1">
                    <BarChart3 className="h-4 w-4" />
                    Analytics
                  </TabsTrigger>
                </TabsList>
              </Tabs>

              {activeTab === 'editor' && (
                <>
                  <Button
                    size="sm"
                    onClick={handleSave}
                    disabled={!isDirty || readOnly}
                    className="flex items-center gap-2"
                  >
                    <Save className="h-4 w-4" />
                    Save
                  </Button>
                  
                  <Button
                    size="sm"
                    onClick={handleExecute}
                    disabled={!workflow || readOnly}
                    className="flex items-center gap-2"
                  >
                    <Play className="h-4 w-4" />
                    Execute
                  </Button>

                  <Separator orientation="vertical" className="h-6" />

                  <Button size="sm" variant="outline" onClick={undo}>
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                  
                  <Button size="sm" variant="outline" onClick={redo}>
                    <RotateCw className="h-4 w-4" />
                  </Button>

                  <Separator orientation="vertical" className="h-6" />

                  <Button size="sm" variant="outline" onClick={handleCopy}>
                    <Copy className="h-4 w-4" />
                  </Button>
                  
                  <Button size="sm" variant="outline" onClick={handlePaste}>
                    <Clipboard className="h-4 w-4" />
                  </Button>

                  <Separator orientation="vertical" className="h-6" />

                  <Button size="sm" variant="outline" onClick={autoLayout}>
                    <Grid className="h-4 w-4" />
                  </Button>
                </>
              )}

              {activeTab === 'history' && (
                <Button
                  size="sm"
                  onClick={refreshExecutions}
                  className="flex items-center gap-2"
                >
                  <RotateCw className="h-4 w-4" />
                  Refresh
                </Button>
              )}
            </div>

            {activeTab === 'editor' && (
              <div className="flex items-center gap-2">
                {/* Workflow Stats */}
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Badge variant="outline">
                    {nodes.length} nodes
                  </Badge>
                  <Badge variant="outline">
                    {edges.length} connections
                  </Badge>
                  {workflowErrors.length > 0 && (
                    <Badge variant="destructive" className="flex items-center gap-1">
                      <AlertTriangle className="h-3 w-3" />
                      {workflowErrors.length} errors
                    </Badge>
                  )}
                </div>

                <Separator orientation="vertical" className="h-6" />

                {/* View Controls */}
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setShowMiniMap(!showMiniMap)}
                >
                  {showMiniMap ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>

                <Button size="sm" variant="outline" onClick={handleZoomOut}>
                  <ZoomOut className="h-4 w-4" />
                </Button>
                
                <Button size="sm" variant="outline" onClick={handleZoomIn}>
                  <ZoomIn className="h-4 w-4" />
                </Button>
                
                <Button size="sm" variant="outline" onClick={handleFitView}>
                  <Maximize className="h-4 w-4" />
                </Button>
              </div>
            )}
          </div>
        </Card>

        {/* Content Area */}
        <div className="flex-1 flex">
          {/* Flow Canvas */}
          {activeTab === 'editor' && (
            <div className="flex-1 relative" ref={reactFlowWrapper}>
              <ReactFlow
                nodes={nodes}
                edges={edges}
                onNodesChange={onNodesChange}
                onEdgesChange={onEdgesChange}
                onConnect={onConnect}
                onInit={setReactFlowInstance}
                onDrop={onDrop}
                onDragOver={onDragOver}
                onNodeDoubleClick={onNodeDoubleClick}
                nodeTypes={nodeTypes}
                fitView
                attributionPosition="bottom-right"
                proOptions={{ hideAttribution: true }}
                deleteKeyCode={readOnly ? null : 'Delete'}
              >
                {/* Controls */}
                <Controls 
                  showInteractive={false}
                  position="bottom-left"
                />
                
                {/* MiniMap */}
                {showMiniMap && (
                  <MiniMap
                    position="top-right"
                    nodeColor="#3b82f6"
                    maskColor="rgba(0, 0, 0, 0.1)"
                    className="border border-gray-300 rounded"
                  />
                )}
                
                {/* Background */}
                <Background
                  variant={showGrid ? BackgroundVariant.Dots : BackgroundVariant.Cross}
                  gap={20}
                  size={1}
                  className="opacity-30"
                />

                {/* Workflow Status Panel */}
                <Panel position="top-left">
                  <Card className="p-3 shadow-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-semibold text-sm">
                        {workflow?.name || 'Untitled Workflow'}
                      </h3>
                      <Badge variant={isEditing ? "default" : "secondary"}>
                        {isEditing ? 'Editing' : 'Viewing'}
                      </Badge>
                    </div>
                    
                    <div className="space-y-1 text-xs text-muted-foreground">
                      <div>Nodes: {nodes.length}</div>
                      <div>Connections: {edges.length}</div>
                      <div>Selected: {selectedNodes.length + selectedEdges.length}</div>
                      {workflowErrors.length > 0 && (
                        <div className="text-red-600 font-medium">
                          Errors: {workflowErrors.length}
                        </div>
                      )}
                    </div>

                    {isDirty && (
                      <Badge variant="outline" className="mt-2 text-xs">
                        Unsaved changes
                      </Badge>
                    )}
                  </Card>
                </Panel>
              </ReactFlow>

              {/* Node Configuration Panel */}
              {selectedNodeId && (
                <div className="absolute top-0 right-0 h-full w-96 bg-background border-l shadow-lg z-10">
                  <NodeConfigPanel 
                    nodeId={selectedNodeId} 
                    onClose={() => setSelectedNodeId(null)} 
                  />
                </div>
              )}
            </div>
          )}

          {/* History Tab */}
          {activeTab === 'history' && (
            <div className="flex-1 p-6">
              {selectedExecutionId ? (
                <ExecutionDetails
                  execution={executions.find(e => e.id === selectedExecutionId)!}
                  onBack={() => setSelectedExecutionId(null)}
                  onRefresh={refreshExecutions}
                />
              ) : (
                <ExecutionHistory
                  workflowId={workflowId || ''}
                  executions={executions}
                  onRefresh={refreshExecutions}
                  onViewDetails={(executionId) => setSelectedExecutionId(executionId)}
                />
              )}
            </div>
          )}

          {/* Analytics Tab */}
          {activeTab === 'analytics' && analytics && (
            <div className="flex-1 p-6">
              <WorkflowAnalyticsComponent analytics={analytics} />
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

// Wrap with ReactFlowProvider
export const FlowEditor: React.FC<FlowEditorProps> = (props) => {
  return (
    <ReactFlowProvider>
      <FlowEditorComponent {...props} />
    </ReactFlowProvider>
  )
}

export default FlowEditor