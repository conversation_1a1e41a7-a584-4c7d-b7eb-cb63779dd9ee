import { useState } from 'react'
import { useWorkflowExecutions } from '@/lib/hooks/use-asset-automation'
import { TriggerJobExecution } from '@/lib/advanced-features/automation/types'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import { AlertTriangle, CheckCircle, Clock, Play, RefreshCw, XCircle } from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { getWorkflowStatusBadge, getWorkflowStatusIcon } from '@/lib/utils/colors'

interface WorkflowExecutionListProps {
  workflowId: string
  onSelectExecution?: (executionId: string) => void
}

export default function WorkflowExecutionList({ 
  workflowId, 
  onSelectExecution 
}: WorkflowExecutionListProps) {
  const { executions, loading, error, fetchExecutions } = useWorkflowExecutions(workflowId)
  
  // Handle refresh
  const handleRefresh = () => {
    fetchExecutions()
  }
  
  // Handle execution selection
  const handleSelectExecution = (executionId: string) => {
    if (onSelectExecution) {
      onSelectExecution(executionId)
    }
  }
  
  // Format execution status for display
  const getStatusBadge = (status: string) => {
    return <Badge className={getWorkflowStatusBadge(status)}>{status}</Badge>
  }
  
  // Format execution status icon
  const getStatusIcon = (status: string) => {
    const iconClass = `h-4 w-4 ${getWorkflowStatusIcon(status)}`
    
    switch (status) {
      case 'completed':
        return <CheckCircle className={iconClass} />
      case 'failed':
        return <XCircle className={iconClass} />
      case 'running':
        return <Play className={iconClass} />
      case 'queued':
        return <Clock className={iconClass} />
      default:
        return <AlertTriangle className="h-4 w-4" />
    }
  }
  
  // Format time ago
  const formatTimeAgo = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true })
    } catch (error) {
      return dateString
    }
  }
  
  // Format duration
  const formatDuration = (startDate: string, endDate?: string) => {
    if (!endDate) return 'In progress'
    
    try {
      const start = new Date(startDate).getTime()
      const end = new Date(endDate).getTime()
      const durationMs = end - start
      
      if (durationMs < 1000) return `${durationMs}ms`
      if (durationMs < 60000) return `${Math.round(durationMs / 1000)}s`
      if (durationMs < 3600000) return `${Math.round(durationMs / 60000)}m ${Math.round((durationMs % 60000) / 1000)}s`
      
      const hours = Math.floor(durationMs / 3600000)
      const minutes = Math.round((durationMs % 3600000) / 60000)
      return `${hours}h ${minutes}m`
    } catch (error) {
      return 'Unknown'
    }
  }
  
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>
            <Skeleton className="h-8 w-3/4" />
          </CardTitle>
          <CardDescription>
            <Skeleton className="h-4 w-1/2" />
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[400px] w-full" />
        </CardContent>
      </Card>
    )
  }
  
  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-red-500">Error Loading Executions</CardTitle>
          <CardDescription>
            Failed to load workflow executions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 text-red-500">
            <AlertTriangle className="h-5 w-5" />
            <p>{error.message || 'An error occurred while loading workflow executions'}</p>
          </div>
          <Button onClick={handleRefresh} className="mt-4">
            Retry
          </Button>
        </CardContent>
      </Card>
    )
  }
  
  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Workflow Executions</CardTitle>
            <CardDescription>
              History of workflow executions
            </CardDescription>
          </div>
          <Button size="sm" variant="outline" onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4 mr-1" />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {executions.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <p>No executions found for this workflow</p>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[100px]">Status</TableHead>
                <TableHead>Execution ID</TableHead>
                <TableHead>Started</TableHead>
                <TableHead>Duration</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {executions.map((execution) => (
                <TableRow 
                  key={execution.id}
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleSelectExecution(execution.id)}
                >
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(execution.status)}
                      <span>{getStatusBadge(execution.status)}</span>
                    </div>
                  </TableCell>
                  <TableCell className="font-medium">
                    {execution.id}
                  </TableCell>
                  <TableCell>
                    {formatTimeAgo(execution.startedAt)}
                  </TableCell>
                  <TableCell>
                    {formatDuration(execution.startedAt, execution.completedAt)}
                  </TableCell>
                  <TableCell className="text-right">
                    <Button 
                      size="sm" 
                      variant="ghost"
                      onClick={(e) => {
                        e.stopPropagation()
                        handleSelectExecution(execution.id)
                      }}
                    >
                      View Details
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
      <CardFooter className="border-t bg-muted/50">
        <div className="text-xs text-muted-foreground">
          Total executions: {executions.length}
        </div>
      </CardFooter>
    </Card>
  )
}