"use client"

import React, { useState, useEffect } from 'react'
import { useWorkflowExecutions } from '@/lib/hooks/use-asset-automation'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  Play, 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertTriangle, 
  RefreshCw,
  ChevronRight,
  ChevronDown,
  Eye
} from 'lucide-react'
import { format, formatDistanceToNow } from 'date-fns'
import { TriggerJobExecution, ExecutionError } from '@/lib/advanced-features/automation/types'

interface ExecutionHistoryProps {
  workflowId: string
  executions: TriggerJobExecution[]
  onRefresh: () => void
  onViewDetails: (executionId: string) => void
}

export const ExecutionHistory: React.FC<ExecutionHistoryProps> = ({
  workflowId,
  executions: propExecutions,
  onRefresh: propOnRefresh,
  onViewDetails
}) => {
  const [expandedExecution, setExpandedExecution] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('all')
  
  // Use real API data via hook
  const {
    executions: apiExecutions,
    loading,
    error,
    fetchExecutions
  } = useWorkflowExecutions(workflowId)
  
  // Use API executions if available, otherwise use prop executions
  const executions = apiExecutions.length > 0 ? apiExecutions : propExecutions
  
  // Refresh executions
  const onRefresh = () => {
    fetchExecutions()
    if (propOnRefresh) {
      propOnRefresh()
    }
  }
  
  // Auto-refresh executions every 10 seconds if there are running executions
  useEffect(() => {
    const hasRunningExecutions = executions.some(exec => 
      exec.status === 'running' || exec.status === 'queued'
    )
    
    if (hasRunningExecutions) {
      const interval = setInterval(onRefresh, 10000)
      return () => clearInterval(interval)
    }
  }, [executions])

  const toggleExpand = (executionId: string) => {
    setExpandedExecution(expandedExecution === executionId ? null : executionId)
  }

  const filteredExecutions = executions.filter(execution => {
    if (activeTab === 'all') return true
    return execution.status === activeTab
  })

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return (
          <Badge className="bg-green-100 text-green-800 hover:bg-green-200">
            <CheckCircle className="h-3 w-3 mr-1" />
            Completed
          </Badge>
        )
      case 'failed':
        return (
          <Badge variant="destructive">
            <XCircle className="h-3 w-3 mr-1" />
            Failed
          </Badge>
        )
      case 'running':
        return (
          <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200">
            <Play className="h-3 w-3 mr-1" />
            Running
          </Badge>
        )
      case 'queued':
        return (
          <Badge variant="outline" className="text-muted-foreground">
            <Clock className="h-3 w-3 mr-1" />
            Queued
          </Badge>
        )
      case 'cancelled':
        return (
          <Badge variant="secondary">
            <AlertTriangle className="h-3 w-3 mr-1" />
            Cancelled
          </Badge>
        )
      default:
        return (
          <Badge variant="outline">{status}</Badge>
        )
    }
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Execution History</CardTitle>
            <CardDescription>Recent workflow executions</CardDescription>
          </div>
          <Button variant="outline" size="sm" onClick={onRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </CardHeader>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mx-6">
          <TabsTrigger value="all">All</TabsTrigger>
          <TabsTrigger value="completed">Completed</TabsTrigger>
          <TabsTrigger value="failed">Failed</TabsTrigger>
          <TabsTrigger value="running">Running</TabsTrigger>
        </TabsList>

        <CardContent className="p-0">
          <ScrollArea className="h-[400px]">
            {filteredExecutions.length > 0 ? (
              <div className="divide-y">
                {filteredExecutions.map((execution) => (
                  <div key={execution.id} className="p-4">
                    <div 
                      className="flex items-center justify-between cursor-pointer"
                      onClick={() => toggleExpand(execution.id)}
                    >
                      <div className="flex items-center gap-3">
                        {expandedExecution === execution.id ? (
                          <ChevronDown className="h-4 w-4 text-muted-foreground" />
                        ) : (
                          <ChevronRight className="h-4 w-4 text-muted-foreground" />
                        )}
                        <div>
                          <div className="font-medium">{execution.id}</div>
                          <div className="text-xs text-muted-foreground">
                            Started: {format(new Date(execution.startedAt), 'MMM d, yyyy HH:mm:ss')}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {getStatusBadge(execution.status)}
                        <Button 
                          variant="ghost" 
                          size="icon"
                          onClick={(e) => {
                            e.stopPropagation()
                            onViewDetails(execution.id)
                          }}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    {expandedExecution === execution.id && (
                      <div className="mt-3 pl-7">
                        <div className="space-y-3">
                          <div className="grid grid-cols-2 gap-2 text-sm">
                            <div>
                              <span className="text-muted-foreground">Status:</span>{' '}
                              {execution.status}
                            </div>
                            <div>
                              <span className="text-muted-foreground">Duration:</span>{' '}
                              {execution.completedAt 
                                ? formatDistanceToNow(new Date(execution.completedAt), { 
                                    addSuffix: false,
                                    includeSeconds: true
                                  })
                                : 'In progress'
                              }
                            </div>
                          </div>

                          {execution.context.errors.length > 0 && (
                            <div className="mt-2">
                              <h4 className="text-sm font-medium text-red-600 mb-1">Errors:</h4>
                              <div className="bg-red-50 border border-red-200 rounded-md p-2 text-sm">
                                <ul className="list-disc list-inside space-y-1">
                                  {execution.context.errors.map((error: ExecutionError, index: number) => (
                                    <li key={index} className="text-red-700">
                                      <span className="font-medium">{error.nodeId}:</span>{' '}
                                      {error.error}
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            </div>
                          )}

                          <div className="flex justify-end">
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation()
                                onViewDetails(execution.id)
                              }}
                            >
                              <Eye className="h-3 w-3 mr-1" />
                              View Details
                            </Button>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center h-full py-12 text-center">
                <Clock className="h-12 w-12 text-muted-foreground mb-4 opacity-50" />
                <h3 className="text-lg font-medium">No executions found</h3>
                <p className="text-muted-foreground mt-1">
                  {activeTab === 'all' 
                    ? 'This workflow has not been executed yet'
                    : `No ${activeTab} executions found`
                  }
                </p>
              </div>
            )}
          </ScrollArea>
        </CardContent>
      </Tabs>
    </Card>
  )
}

export default ExecutionHistory