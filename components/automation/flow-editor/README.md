# Flow Editor Component

This is a modular flow editor component for the WizAssets ERP system's automation feature.

## Component Structure

- `flow-editor/index.tsx` - Main flow editor component
- `flow-editor/components/` - Modular components
  - `node-palette.tsx` - Component for displaying available nodes
  - `workflow-properties.tsx` - Component for editing workflow properties
  - `custom-nodes.tsx` - Custom node components for ReactFlow
  - `custom-edges.tsx` - Custom edge components for ReactFlow
  - `properties-panel.tsx` - Panel for editing node/edge/workflow properties
  - `index.ts` - Exports all components

## Usage

```tsx
import FlowEditor from '@/components/automation/flow-editor'
import { FlowWorkflowDefinition } from '@/lib/advanced-features/automation/types'

// Example workflow
const workflow: FlowWorkflowDefinition = {
  // ...workflow properties
}

// Example usage
export default function WorkflowEditorPage() {
  const handleSave = (updatedWorkflow: FlowWorkflowDefinition) => {
    // Save workflow
  }
  
  const handleExecute = (workflowToExecute: FlowWorkflowDefinition) => {
    // Execute workflow
  }
  
  return (
    <FlowEditor
      workflow={workflow}
      onSave={handleSave}
      onExecute={handleExecute}
      readOnly={false}
    />
  )
}
```

## Features

- Drag and drop node creation
- Visual workflow editing
- Node and edge configuration
- Workflow properties editing
- Resizable panels
- Undo/redo functionality
- Copy/paste nodes
- Zoom and pan controls
- Minimap for navigation
- Read-only mode support

## Dependencies

- ReactFlow
- Lucide React icons
- Shadcn UI components