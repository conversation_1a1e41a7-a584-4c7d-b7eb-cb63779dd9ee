import { useState, useCallback, useRef, useEffect, useMemo } from 'react'
import React<PERSON>low, {
  ReactFlowProvider,
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,
  addEdge,
  Connection,
  Edge,
  Node,
  NodeTypes,
  EdgeTypes,
  OnConnect,
  OnEdgesChange,
  OnNodesChange,
  Panel
} from 'reactflow'
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from '@/components/ui/resizable'
import { Button } from '@/components/ui/button'
import { Save, Play, Undo, Redo, ZoomIn, ZoomOut } from 'lucide-react'
import 'reactflow/dist/style.css'

// Import modular components
import { NodePalette, PropertiesPanel, nodeTypes, edgeTypes } from './components'
import { FlowWorkflowDefinition } from '@/lib/advanced-features/automation/types'

interface FlowEditorProps {
  workflow: FlowWorkflowDefinition
  onSave: (workflow: FlowWorkflowDefinition) => void
  onExecute?: (workflow: FlowWorkflowDefinition) => void
  readOnly?: boolean
}

export default function FlowEditor({ 
  workflow, 
  onSave, 
  onExecute,
  readOnly = false 
}: FlowEditorProps) {
  return (
    <ReactFlowProvider>
      <FlowEditorContent 
        workflow={workflow} 
        onSave={onSave} 
        onExecute={onExecute}
        readOnly={readOnly}
      />
    </ReactFlowProvider>
  )
}

function FlowEditorContent({ 
  workflow, 
  onSave, 
  onExecute,
  readOnly 
}: FlowEditorProps) {
  // Memoize initial nodes to prevent recreation on every render
  const initialNodes = useMemo(() => 
    workflow.nodes.map(node => ({
      ...node,
      type: (node.type || 'action') as 'webhook' | 'trigger' | 'condition' | 'action' | 'delay' | 'decision' | 'loop' | 'subflow'
    })), [workflow.nodes]
  )
  
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes)
  const [edges, setEdges, onEdgesChange] = useEdgesState(workflow.edges)
  
  // State for selected elements
  const [selectedNode, setSelectedNode] = useState<Node | null>(null)
  const [selectedEdge, setSelectedEdge] = useState<Edge | null>(null)
  
  // State for editor settings
  const [isDirty, setIsDirty] = useState(false)
  
  // History for undo/redo
  const [history, setHistory] = useState<{nodes: Node[]; edges: Edge[]}[]>([])
  const [historyIndex, setHistoryIndex] = useState(-1)
  const [isInitialized, setIsInitialized] = useState(false)
  
  // Refs
  const reactFlowWrapper = useRef<HTMLDivElement>(null)
  const reactFlowInstance = useRef<any>(null)
  
  // Initialize history only once
  useEffect(() => {
    if (!isInitialized) {
      setHistory([{ nodes: initialNodes, edges: workflow.edges }])
      setHistoryIndex(0)
      setIsInitialized(true)
    }
  }, [initialNodes, workflow.edges, isInitialized])
  
  // Update history when nodes or edges change (but not during initialization)
  const prevNodesRef = useRef<Node[]>()
  const prevEdgesRef = useRef<Edge[]>()
  
  useEffect(() => {
    if (isInitialized && historyIndex >= 0) {
      // Check if nodes or edges actually changed
      const nodesChanged = JSON.stringify(nodes) !== JSON.stringify(prevNodesRef.current)
      const edgesChanged = JSON.stringify(edges) !== JSON.stringify(prevEdgesRef.current)
      
      if (nodesChanged || edgesChanged) {
        // Only add to history if changes were made
        const currentState = { nodes, edges }
        const previousState = history[historyIndex]
        
        if (JSON.stringify(currentState) !== JSON.stringify(previousState)) {
          // Truncate history if we're not at the end
          const newHistory = history.slice(0, historyIndex + 1)
          setHistory([...newHistory, currentState])
          setHistoryIndex(historyIndex + 1)
          setIsDirty(true)
        }
        
        // Update refs
        prevNodesRef.current = nodes
        prevEdgesRef.current = edges
      }
    }
  }, [nodes, edges, isInitialized, historyIndex, history])
  
  // Handle node selection
  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    setSelectedNode(node)
    setSelectedEdge(null)
  }, [])
  
  // Handle edge selection
  const onEdgeClick = useCallback((event: React.MouseEvent, edge: Edge) => {
    setSelectedEdge(edge)
    setSelectedNode(null)
  }, [])
  
  // Handle pane click (deselect)
  const onPaneClick = useCallback(() => {
    setSelectedNode(null)
    setSelectedEdge(null)
  }, [])
  
  // Handle connection
  const onConnect: OnConnect = useCallback((connection: Connection) => {
    setEdges((eds) => addEdge({
      ...connection,
      type: 'custom',
      animated: true,
      style: { stroke: '#555' }
    }, eds))
  }, [setEdges])
  
  // Handle node changes
  const handleNodesChange: OnNodesChange = useCallback((changes) => {
    onNodesChange(changes)
  }, [onNodesChange])
  
  // Handle edge changes
  const handleEdgesChange: OnEdgesChange = useCallback((changes) => {
    onEdgesChange(changes)
  }, [onEdgesChange])
  
  // Handle node update
  const handleNodeUpdate = useCallback((nodeId: string, data: any) => {
    setNodes((nds) =>
      nds.map((node) => {
        if (node.id === nodeId) {
          return {
            ...node,
            data: {
              ...node.data,
              ...data
            }
          }
        }
        return node
      })
    )
  }, [setNodes])
  
  // Handle workflow update
  const handleWorkflowUpdate = useCallback((data: Partial<FlowWorkflowDefinition>) => {
    // This would be handled by the parent component
    // We just mark as dirty here
    setIsDirty(true)
  }, [])
  
  // Handle node add
  const handleAddNode = useCallback((nodeType: string, nodeData: any) => {
    // Validate node type to ensure it's one of the allowed types
    const validNodeType = ['webhook', 'trigger', 'condition', 'action', 'delay', 'decision', 'loop', 'subflow'].includes(nodeType) 
      ? nodeType 
      : 'action';
      
    const newNode = {
      id: `node_${Date.now()}`,
      type: validNodeType,
      position: {
        x: Math.random() * 300,
        y: Math.random() * 300
      },
      data: {
        label: nodeData.label || `New ${validNodeType} Node`,
        ...nodeData
      }
    }
    
    setNodes((nds) => [...nds, newNode])
    setSelectedNode(newNode)
  }, [setNodes])
  
  // Handle node delete
  const handleDeleteNode = useCallback((nodeId: string) => {
    setNodes((nds) => nds.filter((node) => node.id !== nodeId))
    setEdges((eds) => eds.filter((edge) => edge.source !== nodeId && edge.target !== nodeId))
    setSelectedNode(null)
  }, [setNodes, setEdges])
  
  // Handle edge delete
  const handleDeleteEdge = useCallback((edgeId: string) => {
    setEdges((eds) => eds.filter((edge) => edge.id !== edgeId))
    setSelectedEdge(null)
  }, [setEdges])
  
  // Handle save
  const handleSave = useCallback(() => {
    // Ensure nodes conform to FlowNode type by validating node types
    const validatedNodes = nodes.map(node => ({
      ...node,
      // Ensure node type is one of the allowed types in FlowNode
      type: (node.type || 'action') as 'webhook' | 'trigger' | 'condition' | 'action' | 'delay' | 'decision' | 'loop' | 'subflow'
    }))
    
    // Validate edges to ensure they conform to FlowEdge type
    // Specifically handle sourceHandle and targetHandle which can be null in ReactFlow
    const validatedEdges = edges.map(edge => ({
      ...edge,
      // Convert null to undefined for sourceHandle and targetHandle
      sourceHandle: edge.sourceHandle || undefined,
      targetHandle: edge.targetHandle || undefined
    }))
    
    const updatedWorkflow = {
      ...workflow,
      nodes: validatedNodes,
      edges: validatedEdges
    }
    onSave(updatedWorkflow)
    setIsDirty(false)
  }, [workflow, nodes, edges, onSave])
  
  // Handle execute
  const handleExecute = useCallback(() => {
    if (onExecute) {
      // Use the same validation as in handleSave
      const validatedNodes = nodes.map(node => ({
        ...node,
        type: (node.type || 'action') as 'webhook' | 'trigger' | 'condition' | 'action' | 'delay' | 'decision' | 'loop' | 'subflow'
      }))
      
      // Validate edges to ensure they conform to FlowEdge type
      const validatedEdges = edges.map(edge => ({
        ...edge,
        // Convert null to undefined for sourceHandle and targetHandle
        sourceHandle: edge.sourceHandle || undefined,
        targetHandle: edge.targetHandle || undefined
      }))
      
      const currentWorkflow = {
        ...workflow,
        nodes: validatedNodes,
        edges: validatedEdges
      }
      onExecute(currentWorkflow)
    }
  }, [workflow, nodes, edges, onExecute])
  
  // Handle undo
  const handleUndo = useCallback(() => {
    if (historyIndex > 0) {
      const newIndex = historyIndex - 1
      const { nodes: historyNodes, edges: historyEdges } = history[newIndex]
      setNodes(historyNodes)
      setEdges(historyEdges)
      setHistoryIndex(newIndex)
    }
  }, [history, historyIndex, setNodes, setEdges])
  
  // Handle redo
  const handleRedo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      const newIndex = historyIndex + 1
      const { nodes: historyNodes, edges: historyEdges } = history[newIndex]
      setNodes(historyNodes)
      setEdges(historyEdges)
      setHistoryIndex(newIndex)
    }
  }, [history, historyIndex, setNodes, setEdges])
  
  // Handle zoom in
  const handleZoomIn = useCallback(() => {
    if (reactFlowInstance.current) {
      reactFlowInstance.current.zoomIn()
    }
  }, [])
  
  // Handle zoom out
  const handleZoomOut = useCallback(() => {
    if (reactFlowInstance.current) {
      reactFlowInstance.current.zoomOut()
    }
  }, [])
  
  return (
    <div className="h-[calc(100vh-12rem)] w-full">
      <ResizablePanelGroup direction="horizontal">
        {/* Left Panel - Properties and Node Palette */}
        <ResizablePanel defaultSize={20} minSize={15} maxSize={30}>
          <ResizablePanelGroup direction="vertical">
            {/* Node Palette */}
            <ResizablePanel defaultSize={50}>
              <NodePalette 
                onAddNode={handleAddNode} 
                readOnly={readOnly} 
              />
            </ResizablePanel>
            
            <ResizableHandle />
            
            {/* Properties Panel */}
            <ResizablePanel defaultSize={50}>
              <PropertiesPanel
                selectedNode={selectedNode}
                selectedEdge={selectedEdge}
                workflow={workflow}
                onNodeUpdate={handleNodeUpdate}
                onNodeDelete={handleDeleteNode}
                onEdgeDelete={handleDeleteEdge}
                onWorkflowUpdate={handleWorkflowUpdate}
                readOnly={readOnly}
              />
            </ResizablePanel>
          </ResizablePanelGroup>
        </ResizablePanel>
        
        <ResizableHandle />
        
        {/* Right Panel - Flow Canvas */}
        <ResizablePanel defaultSize={80}>
          <div className="h-full w-full relative" ref={reactFlowWrapper}>
            <ReactFlow
              nodes={nodes}
              edges={edges}
              onNodesChange={readOnly ? undefined : handleNodesChange}
              onEdgesChange={readOnly ? undefined : handleEdgesChange}
              onConnect={readOnly ? undefined : onConnect}
              onNodeClick={onNodeClick}
              onEdgeClick={onEdgeClick}
              onPaneClick={onPaneClick}
              nodeTypes={nodeTypes}
              edgeTypes={edgeTypes}
              fitView
              attributionPosition="bottom-right"
              onInit={(instance) => {
                reactFlowInstance.current = instance
              }}
              proOptions={{ hideAttribution: true }}
              deleteKeyCode={readOnly ? null : 'Delete'}
              selectionKeyCode={readOnly ? null : 'Shift'}
              multiSelectionKeyCode={readOnly ? null : 'Control'}
              snapToGrid={true}
              snapGrid={[15, 15]}
            >
              <Background />
              <Controls showInteractive={false} />
              <MiniMap />
              
              {/* Editor Controls */}
              <Panel position="top-right" className="flex space-x-2">
                <Button 
                  size="icon" 
                  variant="outline"
                  onClick={handleZoomIn}
                  title="Zoom In"
                >
                  <ZoomIn className="h-4 w-4" />
                </Button>
                <Button 
                  size="icon" 
                  variant="outline"
                  onClick={handleZoomOut}
                  title="Zoom Out"
                >
                  <ZoomOut className="h-4 w-4" />
                </Button>
              </Panel>
              
              {/* History Controls */}
              <Panel position="top-left" className="flex space-x-2">
                <Button 
                  size="icon" 
                  variant="outline"
                  onClick={handleUndo}
                  disabled={historyIndex <= 0 || readOnly}
                  title="Undo"
                >
                  <Undo className="h-4 w-4" />
                </Button>
                <Button 
                  size="icon" 
                  variant="outline"
                  onClick={handleRedo}
                  disabled={historyIndex >= history.length - 1 || readOnly}
                  title="Redo"
                >
                  <Redo className="h-4 w-4" />
                </Button>
              </Panel>
              
              {/* Action Buttons */}
              {!readOnly && (
                <Panel position="bottom-center" className="flex space-x-2">
                  <Button 
                    variant="outline"
                    onClick={handleSave}
                    disabled={!isDirty}
                  >
                    <Save className="h-4 w-4 mr-1" />
                    Save
                  </Button>
                  {onExecute && (
                    <Button 
                      onClick={handleExecute}
                    >
                      <Play className="h-4 w-4 mr-1" />
                      Execute
                    </Button>
                  )}
                </Panel>
              )}
            </ReactFlow>
          </div>
        </ResizablePanel>
      </ResizablePanelGroup>
    </div>
  )
}