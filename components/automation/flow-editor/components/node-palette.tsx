import { useState } from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion'
import { Badge } from '@/components/ui/badge'
import { Search, Plus } from 'lucide-react'
import { nodeCategories } from '@/lib/advanced-features/automation/node-categories'

interface NodePaletteProps {
  onAddNode: (type: string, data: any) => void
  readOnly?: boolean
}

export function NodePalette({ onAddNode, readOnly = false }: NodePaletteProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [expandedCategories, setExpandedCategories] = useState<string[]>(['basic'])
  
  // Filter nodes based on search query
  const getFilteredNodes = (nodes: any[]) => {
    if (!searchQuery) return nodes
    
    return nodes.filter(node => 
      node.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
      node.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      node.type.toLowerCase().includes(searchQuery.toLowerCase())
    )
  }
  
  // Handle node add
  const handleAddNode = (nodeType: string, nodeData: any) => {
    if (!readOnly) {
      onAddNode(nodeType, nodeData)
    }
  }
  
  return (
    <Card className="h-full border-0 rounded-none">
      <CardHeader className="px-4 py-3">
        <CardTitle className="text-md">Node Palette</CardTitle>
        <CardDescription>Drag nodes to the canvas</CardDescription>
        
        <div className="relative mt-2">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search nodes..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </CardHeader>
      <CardContent className="px-2 py-0">
        <ScrollArea className="h-[calc(100vh-20rem)]">
          <Accordion
            type="multiple"
            value={expandedCategories}
            onValueChange={setExpandedCategories}
            className="space-y-2"
          >
            {Object.entries(nodeCategories).map(([category, categoryData]) => {
              const filteredNodes = getFilteredNodes(categoryData.nodes)
              
              // Skip empty categories when searching
              if (searchQuery && filteredNodes.length === 0) {
                return null
              }
              
              return (
                <AccordionItem 
                  key={category} 
                  value={category}
                  className="border rounded-md overflow-hidden"
                >
                  <AccordionTrigger className="px-3 py-2 hover:bg-muted/50">
                    <div className="flex items-center">
                      {categoryData.icon && (
                        <categoryData.icon className="h-4 w-4 mr-2 text-muted-foreground" />
                      )}
                      <span>{categoryData.label}</span>
                      <Badge variant="outline" className="ml-2">
                        {filteredNodes.length}
                      </Badge>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="px-2 py-1">
                    <div className="grid grid-cols-1 gap-2">
                      {filteredNodes.map((node) => (
                        <Card 
                          key={node.type} 
                          className={`cursor-pointer hover:bg-muted/50 transition-colors ${readOnly ? 'opacity-50' : ''}`}
                          onClick={() => handleAddNode(node.type, node.defaultData)}
                        >
                          <CardContent className="p-3">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center">
                                {node.icon && (
                                  <node.icon className="h-4 w-4 mr-2 text-muted-foreground" />
                                )}
                                <div>
                                  <h3 className="text-sm font-medium">{node.label}</h3>
                                  {node.description && (
                                    <p className="text-xs text-muted-foreground line-clamp-1">
                                      {node.description}
                                    </p>
                                  )}
                                </div>
                              </div>
                              {!readOnly && (
                                <Button 
                                  variant="ghost" 
                                  size="icon" 
                                  className="h-6 w-6"
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    handleAddNode(node.type, node.defaultData)
                                  }}
                                >
                                  <Plus className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              )
            })}
          </Accordion>
          
          {searchQuery && Object.values(nodeCategories).flatMap(category => 
            getFilteredNodes(category.nodes)
          ).length === 0 && (
            <div className="text-center py-4 text-muted-foreground">
              <p>No nodes found matching "{searchQuery}"</p>
              <p className="text-sm">Try adjusting your search terms</p>
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  )
}