import { memo } from 'react'
import { EdgeProps, getBezierPath, EdgeLabelRenderer } from 'reactflow'

// Custom Edge Component
const CustomEdge = memo<EdgeProps>(({
  id,
  source,
  target,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  data,
  markerEnd,
  selected
}) => {
  const [edgePath, labelX, labelY] = getBezierPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition
  })

  return (
    <>
      <path
        id={id}
        style={{
          ...style,
          strokeWidth: selected ? 2 : 1,
          stroke: selected ? 'hsl(var(--primary))' : 'hsl(var(--muted-foreground))'
        }}
        className="react-flow__edge-path"
        d={edgePath}
        markerEnd={markerEnd}
      />
      {data?.label && (
        <EdgeLabelRenderer>
          <div
            style={{
              position: 'absolute',
              transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,
              pointerEvents: 'all'
            }}
            className="px-1 py-0.5 bg-white border rounded text-xs nodrag nopan"
          >
            {data.label}
          </div>
        </EdgeLabelRenderer>
      )}
    </>
  )
})

CustomEdge.displayName = 'CustomEdge'

// Specialized Edge Components
const SuccessEdge = memo<EdgeProps>((props) => {
  return (
    <CustomEdge
      {...props}
      style={{ stroke: 'hsl(var(--chart-2))', strokeWidth: 2 }}
    />
  )
})

const ErrorEdge = memo<EdgeProps>((props) => {
  return (
    <CustomEdge
      {...props}
      style={{ stroke: 'hsl(var(--destructive))', strokeWidth: 2 }}
    />
  )
})

const ConditionalEdge = memo<EdgeProps>((props) => {
  return (
    <CustomEdge
      {...props}
      style={{ stroke: 'hsl(var(--chart-4))', strokeWidth: 2 }}
    />
  )
})

// Edge type mapping for ReactFlow
export const edgeTypes = {
  custom: CustomEdge,
  success: SuccessEdge,
  error: ErrorEdge,
  conditional: ConditionalEdge
}

SuccessEdge.displayName = 'SuccessEdge'
ErrorEdge.displayName = 'ErrorEdge'
ConditionalEdge.displayName = 'ConditionalEdge'