import { useState, useEffect } from 'react'
import { Node, Edge } from 'reactflow'
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Trash2, Settings, Code, Info } from 'lucide-react'
import { FlowWorkflowDefinition } from '@/lib/advanced-features/automation/types'

interface PropertiesPanelProps {
  selectedNode: Node | null
  selectedEdge: Edge | null
  workflow: FlowWorkflowDefinition
  onNodeUpdate: (nodeId: string, data: any) => void
  onNodeDelete: (nodeId: string) => void
  onEdgeDelete: (edgeId: string) => void
  onWorkflowUpdate: (data: Partial<FlowWorkflowDefinition>) => void
  readOnly?: boolean
}

export function PropertiesPanel({
  selectedNode,
  selectedEdge,
  workflow,
  onNodeUpdate,
  onNodeDelete,
  onEdgeDelete,
  onWorkflowUpdate,
  readOnly = false
}: PropertiesPanelProps) {
  const [activeTab, setActiveTab] = useState('general')
  const [nodeConfig, setNodeConfig] = useState<Record<string, any>>({})
  
  // Update node config when selected node changes
  useEffect(() => {
    if (selectedNode) {
      setNodeConfig(selectedNode.data.config || {})
      setActiveTab('general')
    }
  }, [selectedNode])
  
  // Handle node config change
  const handleNodeConfigChange = (key: string, value: any) => {
    if (readOnly) return
    
    const updatedConfig = {
      ...nodeConfig,
      [key]: value
    }
    
    setNodeConfig(updatedConfig)
    
    if (selectedNode) {
      onNodeUpdate(selectedNode.id, {
        config: updatedConfig
      })
    }
  }
  
  // Handle node label change
  const handleNodeLabelChange = (value: string) => {
    if (readOnly || !selectedNode) return
    
    onNodeUpdate(selectedNode.id, {
      label: value
    })
  }
  
  // Handle node delete
  const handleNodeDelete = () => {
    if (readOnly || !selectedNode) return
    
    if (confirm('Are you sure you want to delete this node?')) {
      onNodeDelete(selectedNode.id)
    }
  }
  
  // Handle edge delete
  const handleEdgeDelete = () => {
    if (readOnly || !selectedEdge) return
    
    if (confirm('Are you sure you want to delete this connection?')) {
      onEdgeDelete(selectedEdge.id)
    }
  }
  
  // Handle workflow name change
  const handleWorkflowNameChange = (value: string) => {
    if (readOnly) return
    
    onWorkflowUpdate({
      name: value
    })
  }
  
  // Handle workflow description change
  const handleWorkflowDescriptionChange = (value: string) => {
    if (readOnly) return
    
    onWorkflowUpdate({
      description: value
    })
  }
  
  // Render node properties
  const renderNodeProperties = () => {
    if (!selectedNode) return null
    
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium">Node Properties</h3>
            <p className="text-sm text-muted-foreground">
              Configure the selected node
            </p>
          </div>
          {!readOnly && (
            <Button 
              variant="destructive" 
              size="sm"
              onClick={handleNodeDelete}
            >
              <Trash2 className="h-4 w-4 mr-1" />
              Delete
            </Button>
          )}
        </div>
        
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="config">Configuration</TabsTrigger>
            <TabsTrigger value="advanced">Advanced</TabsTrigger>
          </TabsList>
          
          <TabsContent value="general" className="space-y-4 pt-4">
            <div className="space-y-2">
              <Label htmlFor="node-label">Label</Label>
              <Input
                id="node-label"
                value={selectedNode.data.label || ''}
                onChange={(e) => handleNodeLabelChange(e.target.value)}
                disabled={readOnly}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="node-type">Type</Label>
              <Input
                id="node-type"
                value={selectedNode.type || ''}
                disabled
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="node-id">ID</Label>
              <Input
                id="node-id"
                value={selectedNode.id || ''}
                disabled
              />
            </div>
          </TabsContent>
          
          <TabsContent value="config" className="space-y-4 pt-4">
            {Object.entries(nodeConfig).map(([key, value]) => {
              // Skip internal properties
              if (key.startsWith('_')) return null
              
              const label = key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1')
              
              if (typeof value === 'boolean') {
                return (
                  <div key={key} className="flex items-center justify-between">
                    <Label htmlFor={`node-config-${key}`}>{label}</Label>
                    <Switch
                      id={`node-config-${key}`}
                      checked={value}
                      onCheckedChange={(checked) => handleNodeConfigChange(key, checked)}
                      disabled={readOnly}
                    />
                  </div>
                )
              }
              
              if (typeof value === 'number') {
                return (
                  <div key={key} className="space-y-2">
                    <Label htmlFor={`node-config-${key}`}>{label}</Label>
                    <Input
                      id={`node-config-${key}`}
                      type="number"
                      value={value}
                      onChange={(e) => handleNodeConfigChange(key, parseFloat(e.target.value))}
                      disabled={readOnly}
                    />
                  </div>
                )
              }
              
              if (typeof value === 'object') {
                return (
                  <div key={key} className="space-y-2">
                    <Label htmlFor={`node-config-${key}`}>{label}</Label>
                    <Textarea
                      id={`node-config-${key}`}
                      value={JSON.stringify(value, null, 2)}
                      onChange={(e) => {
                        try {
                          const parsed = JSON.parse(e.target.value)
                          handleNodeConfigChange(key, parsed)
                        } catch (error) {
                          // Don't update if invalid JSON
                        }
                      }}
                      className="font-mono text-sm"
                      rows={5}
                      disabled={readOnly}
                    />
                  </div>
                )
              }
              
              return (
                <div key={key} className="space-y-2">
                  <Label htmlFor={`node-config-${key}`}>{label}</Label>
                  <Input
                    id={`node-config-${key}`}
                    value={value}
                    onChange={(e) => handleNodeConfigChange(key, e.target.value)}
                    disabled={readOnly}
                  />
                </div>
              )
            })}
            
            {Object.keys(nodeConfig).length === 0 && (
              <div className="text-center py-4 text-muted-foreground">
                <p>No configuration options available for this node</p>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="advanced" className="space-y-4 pt-4">
            <div className="space-y-2">
              <Label htmlFor="node-position">Position</Label>
              <div className="grid grid-cols-2 gap-2">
                <div className="space-y-2">
                  <Label htmlFor="node-position-x">X</Label>
                  <Input
                    id="node-position-x"
                    value={selectedNode.position.x}
                    disabled
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="node-position-y">Y</Label>
                  <Input
                    id="node-position-y"
                    value={selectedNode.position.y}
                    disabled
                  />
                </div>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="node-data">Node Data</Label>
              <Textarea
                id="node-data"
                value={JSON.stringify(selectedNode.data, null, 2)}
                className="font-mono text-sm"
                rows={10}
                disabled
              />
            </div>
          </TabsContent>
        </Tabs>
      </div>
    )
  }
  
  // Render edge properties
  const renderEdgeProperties = () => {
    if (!selectedEdge) return null
    
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium">Connection Properties</h3>
            <p className="text-sm text-muted-foreground">
              Configure the selected connection
            </p>
          </div>
          {!readOnly && (
            <Button 
              variant="destructive" 
              size="sm"
              onClick={handleEdgeDelete}
            >
              <Trash2 className="h-4 w-4 mr-1" />
              Delete
            </Button>
          )}
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="edge-id">ID</Label>
          <Input
            id="edge-id"
            value={selectedEdge.id || ''}
            disabled
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="edge-source">Source</Label>
          <Input
            id="edge-source"
            value={selectedEdge.source || ''}
            disabled
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="edge-target">Target</Label>
          <Input
            id="edge-target"
            value={selectedEdge.target || ''}
            disabled
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="edge-source-handle">Source Handle</Label>
          <Input
            id="edge-source-handle"
            value={selectedEdge.sourceHandle || 'default'}
            disabled
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="edge-target-handle">Target Handle</Label>
          <Input
            id="edge-target-handle"
            value={selectedEdge.targetHandle || 'default'}
            disabled
          />
        </div>
      </div>
    )
  }
  
  // Render workflow properties
  const renderWorkflowProperties = () => {
    if (selectedNode || selectedEdge) return null
    
    return (
      <div className="space-y-4">
        <div>
          <h3 className="text-lg font-medium">Workflow Properties</h3>
          <p className="text-sm text-muted-foreground">
            Configure the workflow
          </p>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="workflow-name">Name</Label>
          <Input
            id="workflow-name"
            value={workflow.name || ''}
            onChange={(e) => handleWorkflowNameChange(e.target.value)}
            disabled={readOnly}
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="workflow-description">Description</Label>
          <Textarea
            id="workflow-description"
            value={workflow.description || ''}
            onChange={(e) => handleWorkflowDescriptionChange(e.target.value)}
            disabled={readOnly}
            rows={3}
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="workflow-id">ID</Label>
          <Input
            id="workflow-id"
            value={workflow.id || ''}
            disabled
          />
        </div>
        
        <div className="space-y-2">
          <Label>Statistics</Label>
          <div className="grid grid-cols-2 gap-2">
            <div className="bg-muted p-3 rounded-md">
              <div className="text-sm text-muted-foreground">Nodes</div>
              <div className="text-2xl font-bold">{workflow.nodes.length}</div>
            </div>
            <div className="bg-muted p-3 rounded-md">
              <div className="text-sm text-muted-foreground">Connections</div>
              <div className="text-2xl font-bold">{workflow.edges.length}</div>
            </div>
          </div>
        </div>
      </div>
    )
  }
  
  return (
    <Card className="h-full border-0 rounded-none">
      <CardHeader className="px-4 py-3">
        <CardTitle className="text-md flex items-center gap-2">
          <Settings className="h-4 w-4" />
          Properties
        </CardTitle>
        <CardDescription>
          {selectedNode ? 'Node Properties' : selectedEdge ? 'Connection Properties' : 'Workflow Properties'}
        </CardDescription>
      </CardHeader>
      <CardContent className="px-4 py-0">
        <ScrollArea className="h-[calc(100vh-20rem)]">
          {selectedNode && renderNodeProperties()}
          {selectedEdge && renderEdgeProperties()}
          {!selectedNode && !selectedEdge && renderWorkflowProperties()}
        </ScrollArea>
      </CardContent>
    </Card>
  )
}