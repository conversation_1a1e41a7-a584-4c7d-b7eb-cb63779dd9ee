import { memo } from 'react'
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Settings, 
  AlertCircle, 
  CheckCircle2 as CheckCircle, 
  Play, 
  Mail, 
  Globe, 
  Database, 
  Bell, 
  GitBranch, 
  Clock, 
  FileText, 
  Workflow,
  Webhook,
  Code,
  Filter,
  Layers,
  Box,
  Pencil,
  Search,
  Calculator,
  Wrench,
  ArrowRightLeft,
  Trash2,
  ClipboardCheck,
  ShoppingCart,
  Receipt,
  CheckSquare,
  Users,
  BarChart
} from 'lucide-react'

// Icon mapping
const iconMap = {
  Play,
  Mail,
  Globe,
  Database,
  Bell,
  GitBranch,
  Clock,
  FileText,
  Workflow,
  Webhook,
  Code,
  Filter,
  Layers,
  Box,
  Pencil,
  Search,
  Calculator,
  Wrench,
  ArrowRightLeft,
  Trash2,
  ClipboardCheck,
  ShoppingCart,
  Receipt,
  CheckSquare,
  Users,
  BarChart
}

// Base Node Component
const BaseNode = memo<NodeProps>(({ data, selected, id }) => {
  const IconComponent = data.icon ? iconMap[data.icon as keyof typeof iconMap] : Settings
  
  const categoryColors = {
    trigger: 'bg-chart-2',
    action: 'bg-primary',
    logic: 'bg-chart-4',
    data: 'bg-chart-5',
    integration: 'bg-destructive',
    asset: 'bg-chart-3',
    operations: 'bg-chart-1'
  }
  
  const nodeCategory = data.category || 'action'
  const headerColor = categoryColors[nodeCategory as keyof typeof categoryColors] || categoryColors.action
  
  const hasErrors = data.errors && data.errors.length > 0
  const isConfigured = data.isConfigured
  
  return (
    <div className={`
      relative min-w-[180px] max-w-[250px]
      ${selected ? 'ring-2 ring-primary ring-offset-2' : ''}
    `}>
      {/* Input Handles */}
      {data.inputs && data.inputs.map((input: any, index: number) => (
        <Handle
          key={`input-${input.id || index}`}
          type="target"
          position={Position.Left}
          id={input.id || `input-${index}`}
          style={{
            top: `${((index + 1) / (data.inputs.length + 1)) * 100}%`,
            background: 'hsl(var(--muted-foreground))',
            width: '8px',
            height: '8px'
          }}
        />
      ))}
      
      {/* Output Handles */}
      {data.outputs && data.outputs.map((output: any, index: number) => (
        <Handle
          key={`output-${output.id || index}`}
          type="source"
          position={Position.Right}
          id={output.id || `output-${index}`}
          style={{
            top: `${((index + 1) / (data.outputs.length + 1)) * 100}%`,
            background: 'hsl(var(--chart-2))',
            width: '8px',
            height: '8px'
          }}
        />
      ))}
      
      <Card className="border shadow-sm">
        {/* Header with color */}
        <div className={`h-1 ${headerColor} rounded-t-lg`} />
        
        <CardContent className="p-3">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <div className={`p-1 rounded-md ${headerColor} text-white`}>
                {IconComponent && <IconComponent className="h-3 w-3" />}
              </div>
              <div className="text-sm font-medium truncate">{data.label}</div>
            </div>
            
            <div className="flex items-center">
              {hasErrors && (
                <AlertCircle className="h-3 w-3 text-destructive mr-1" />
              )}
              {isConfigured && (
                <CheckCircle className="h-3 w-3 text-chart-2" />
              )}
            </div>
          </div>
          
          {data.description && (
            <div className="text-xs text-muted-foreground mb-2 line-clamp-2">
              {data.description}
            </div>
          )}
          
          {data.config && Object.keys(data.config).length > 0 && (
            <div className="text-xs text-muted-foreground border-t pt-1 mt-1">
              {Object.entries(data.config)
                .filter(([key]) => !key.startsWith('_'))
                .slice(0, 2)
                .map(([key, value]) => (
                  <div key={key} className="truncate">
                    <span className="opacity-70">{key}:</span>{' '}
                    <span className="font-mono">
                      {typeof value === 'string' 
                        ? value 
                        : typeof value === 'object' 
                          ? '{...}' 
                          : String(value)}
                    </span>
                  </div>
                ))}
            </div>
          )}
          
          {hasErrors && (
            <div className="mt-2 p-1 bg-destructive/10 border border-destructive/20 rounded text-xs text-destructive">
              {data.errors[0]}
              {data.errors.length > 1 && ` (+${data.errors.length - 1} more)`}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
})

BaseNode.displayName = 'BaseNode'

// Specialized Node Components
const TriggerNode = memo<NodeProps>((props) => {
  return <BaseNode {...props} />
})

const ActionNode = memo<NodeProps>((props) => {
  return <BaseNode {...props} />
})

const ConditionNode = memo<NodeProps>((props) => {
  return <BaseNode {...props} />
})

const AssetNode = memo<NodeProps>((props) => {
  return <BaseNode {...props} />
})

// Node type mapping for ReactFlow
export const nodeTypes = {
  default: BaseNode,
  trigger: TriggerNode,
  action: ActionNode,
  condition: ConditionNode,
  delay: BaseNode,
  webhook: BaseNode,
  function: BaseNode,
  transform: BaseNode,
  loop: BaseNode,
  decision: BaseNode,
  filter: BaseNode,
  merge: BaseNode,
  subflow: BaseNode,
  email: BaseNode,
  database: BaseNode,
  api: BaseNode,
  notification: BaseNode,
  file: BaseNode,
  
  // Asset nodes
  assetCreate: AssetNode,
  assetUpdate: AssetNode,
  assetQuery: AssetNode,
  assetDepreciation: AssetNode,
  assetMaintenance: AssetNode,
  assetTransfer: AssetNode,
  assetDisposal: AssetNode,
  inventoryCheck: AssetNode,
  purchaseOrder: AssetNode,
  invoiceProcess: AssetNode,
  approvalRequest: AssetNode,
  notifyStakeholders: AssetNode,
  generateReport: AssetNode
}

TriggerNode.displayName = 'TriggerNode'
ActionNode.displayName = 'ActionNode'
ConditionNode.displayName = 'ConditionNode'
AssetNode.displayName = 'AssetNode'