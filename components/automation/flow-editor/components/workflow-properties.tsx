import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import { Info, Settings, Code } from 'lucide-react'
import { FlowWorkflowDefinition } from '@/lib/advanced-features/automation/types'

interface WorkflowPropertiesProps {
  workflow: FlowWorkflowDefinition
  onUpdate: (data: Partial<FlowWorkflowDefinition>) => void
  readOnly?: boolean
}

export function WorkflowProperties({
  workflow,
  onUpdate,
  readOnly = false
}: WorkflowPropertiesProps) {
  const [activeTab, setActiveTab] = useState('general')
  
  // Handle workflow name change
  const handleNameChange = (value: string) => {
    if (readOnly) return
    
    onUpdate({
      name: value
    })
  }
  
  // Handle workflow description change
  const handleDescriptionChange = (value: string) => {
    if (readOnly) return
    
    onUpdate({
      description: value
    })
  }
  
  // Handle workflow active state change
  const handleActiveChange = (value: boolean) => {
    if (readOnly) return
    
    onUpdate({
      isActive: value
    })
  }
  
  return (
    <Card className="h-full border-0 rounded-none">
      <CardHeader className="px-4 py-3">
        <CardTitle className="text-md flex items-center gap-2">
          <Settings className="h-4 w-4" />
          Workflow Properties
        </CardTitle>
        <CardDescription>
          Configure the workflow settings
        </CardDescription>
      </CardHeader>
      <CardContent className="px-4 py-0">
        <ScrollArea className="h-[calc(100vh-20rem)]">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList>
              <TabsTrigger value="general">General</TabsTrigger>
              <TabsTrigger value="advanced">Advanced</TabsTrigger>
              <TabsTrigger value="json">JSON</TabsTrigger>
            </TabsList>
            
            <TabsContent value="general" className="space-y-4 pt-4">
              <div className="space-y-2">
                <Label htmlFor="workflow-name">Name</Label>
                <Input
                  id="workflow-name"
                  value={workflow.name || ''}
                  onChange={(e) => handleNameChange(e.target.value)}
                  disabled={readOnly}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="workflow-description">Description</Label>
                <Textarea
                  id="workflow-description"
                  value={workflow.description || ''}
                  onChange={(e) => handleDescriptionChange(e.target.value)}
                  disabled={readOnly}
                  rows={3}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <Label htmlFor="workflow-active">Active</Label>
                <Switch
                  id="workflow-active"
                  checked={workflow.isActive || false}
                  onCheckedChange={handleActiveChange}
                  disabled={readOnly}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="workflow-id">ID</Label>
                <Input
                  id="workflow-id"
                  value={workflow.id || ''}
                  disabled
                />
              </div>
              
              <div className="space-y-2">
                <Label>Statistics</Label>
                <div className="grid grid-cols-2 gap-2">
                  <div className="bg-muted p-3 rounded-md">
                    <div className="text-sm text-muted-foreground">Nodes</div>
                    <div className="text-2xl font-bold">{workflow.nodes.length}</div>
                  </div>
                  <div className="bg-muted p-3 rounded-md">
                    <div className="text-sm text-muted-foreground">Connections</div>
                    <div className="text-2xl font-bold">{workflow.edges.length}</div>
                  </div>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label>Metadata</Label>
                <div className="grid grid-cols-2 gap-2">
                  <div className="space-y-1">
                    <div className="text-xs text-muted-foreground">Created</div>
                    <div className="text-sm">{new Date(workflow.createdAt).toLocaleString()}</div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-xs text-muted-foreground">Updated</div>
                    <div className="text-sm">{new Date(workflow.updatedAt).toLocaleString()}</div>
                  </div>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="advanced" className="space-y-4 pt-4">
              <div className="space-y-2">
                <Label htmlFor="workflow-version">Version</Label>
                <Input
                  id="workflow-version"
                  value={workflow.version || '1.0.0'}
                  disabled
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="workflow-created-by">Created By</Label>
                <Input
                  id="workflow-created-by"
                  value={workflow.createdBy || 'Unknown'}
                  disabled
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="workflow-tags">Tags</Label>
                <div className="flex flex-wrap gap-2">
                  {workflow.tags?.map((tag, index) => (
                    <Badge key={index} variant="outline">
                      {tag}
                    </Badge>
                  ))}
                  {(!workflow.tags || workflow.tags.length === 0) && (
                    <div className="text-sm text-muted-foreground">No tags</div>
                  )}
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="workflow-execution-count">Execution Count</Label>
                <Input
                  id="workflow-execution-count"
                  value={workflow.executionCount || 0}
                  disabled
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="workflow-last-executed">Last Executed</Label>
                <Input
                  id="workflow-last-executed"
                  value={workflow.lastExecuted ? new Date(workflow.lastExecuted).toLocaleString() : 'Never'}
                  disabled
                />
              </div>
            </TabsContent>
            
            <TabsContent value="json" className="space-y-4 pt-4">
              <div className="space-y-2">
                <Label htmlFor="workflow-json">Workflow JSON</Label>
                <Textarea
                  id="workflow-json"
                  value={JSON.stringify(workflow, null, 2)}
                  className="font-mono text-sm"
                  rows={20}
                  disabled
                />
              </div>
            </TabsContent>
          </Tabs>
        </ScrollArea>
      </CardContent>
    </Card>
  )
}