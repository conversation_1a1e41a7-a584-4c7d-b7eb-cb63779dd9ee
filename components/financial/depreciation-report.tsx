"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { DatePickerWithRange } from "@/components/ui/date-range-picker"
import { DateRange } from "react-day-picker"
import { addDays } from "date-fns"
import { Download, Printer, FileText, <PERSON><PERSON>hart, <PERSON><PERSON>hart, Filter } from "lucide-react"
import type { FinancialAsset } from "@/lib/modules/financial/types"
import type { DepreciationSchedule } from "@/lib/modules/financial/depreciation-types"

interface DepreciationReportProps {
  assets: FinancialAsset[]
  depreciationSchedules: Record<string, DepreciationSchedule>
}

export function DepreciationReport({ assets, depreciationSchedules }: DepreciationReportProps) {
  const [reportType, setReportType] = useState("summary")
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(new Date().getFullYear(), 0, 1), // January 1st of current year
    to: new Date(new Date().getFullYear(), 11, 31), // December 31st of current year
  })
  const [assetCategory, setAssetCategory] = useState<string>("all")
  const [assetLocation, setAssetLocation] = useState<string>("all")

  // Get unique categories and locations for filtering
  const categories = Array.from(new Set(assets.map((asset) => asset.category)))
  const locations = Array.from(new Set(assets.map((asset) => asset.location)))

  // Filter assets based on selected filters
  const filteredAssets = assets.filter((asset) => {
    const matchesCategory = assetCategory === "all" || asset.category === assetCategory
    const matchesLocation = assetLocation === "all" || asset.location === assetLocation
    return matchesCategory && matchesLocation
  })

  // Calculate totals for summary report
  const totalAcquisitionCost = filteredAssets.reduce((sum, asset) => sum + asset.acquisitionCost, 0)
  const totalCurrentValue = filteredAssets.reduce((sum, asset) => sum + asset.currentValue, 0)
  const totalAccumulatedDepreciation = totalAcquisitionCost - totalCurrentValue
  
  // Calculate current year depreciation
  const currentYear = new Date().getFullYear()
  const currentYearDepreciation = filteredAssets.reduce((sum, asset) => {
    const schedule = depreciationSchedules[asset.id]
    if (!schedule) return sum
    
    const currentYearEntry = schedule.entries.find(
      (entry) => new Date(entry.periodEndDate).getFullYear() === currentYear
    )
    
    return sum + (currentYearEntry?.depreciationAmount || 0)
  }, 0)

  // Group assets by category for category breakdown
  const assetsByCategory = filteredAssets.reduce((acc, asset) => {
    if (!acc[asset.category]) {
      acc[asset.category] = {
        count: 0,
        acquisitionCost: 0,
        currentValue: 0,
        depreciation: 0,
      }
    }
    
    acc[asset.category].count += 1
    acc[asset.category].acquisitionCost += asset.acquisitionCost
    acc[asset.category].currentValue += asset.currentValue
    
    const schedule = depreciationSchedules[asset.id]
    if (schedule) {
      const currentYearEntry = schedule.entries.find(
        (entry) => new Date(entry.periodEndDate).getFullYear() === currentYear
      )
      acc[asset.category].depreciation += currentYearEntry?.depreciationAmount || 0
    }
    
    return acc
  }, {} as Record<string, { count: number; acquisitionCost: number; currentValue: number; depreciation: number }>)

  // Group assets by depreciation method
  const assetsByMethod = filteredAssets.reduce((acc, asset) => {
    if (!acc[asset.depreciationMethod]) {
      acc[asset.depreciationMethod] = {
        count: 0,
        acquisitionCost: 0,
        currentValue: 0,
        depreciation: 0,
      }
    }
    
    acc[asset.depreciationMethod].count += 1
    acc[asset.depreciationMethod].acquisitionCost += asset.acquisitionCost
    acc[asset.depreciationMethod].currentValue += asset.currentValue
    
    const schedule = depreciationSchedules[asset.id]
    if (schedule) {
      const currentYearEntry = schedule.entries.find(
        (entry) => new Date(entry.periodEndDate).getFullYear() === currentYear
      )
      acc[asset.depreciationMethod].depreciation += currentYearEntry?.depreciationAmount || 0
    }
    
    return acc
  }, {} as Record<string, { count: number; acquisitionCost: number; currentValue: number; depreciation: number }>)

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <CardTitle>Depreciation Report</CardTitle>
            <CardDescription>
              {dateRange?.from && dateRange?.to
                ? `Reporting period: ${dateRange.from.toLocaleDateString()} - ${dateRange.to.toLocaleDateString()}`
                : "Select a reporting period"}
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button variant="outline" size="sm">
              <Printer className="h-4 w-4 mr-2" />
              Print
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Report Controls */}
        <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Report Type</label>
            <Select value={reportType} onValueChange={setReportType}>
              <SelectTrigger>
                <SelectValue placeholder="Select report type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="summary">Summary Report</SelectItem>
                <SelectItem value="detailed">Detailed Report</SelectItem>
                <SelectItem value="category">Category Breakdown</SelectItem>
                <SelectItem value="method">Method Breakdown</SelectItem>
                <SelectItem value="tax">Tax Report</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">Date Range</label>
            <DatePickerWithRange date={dateRange} setDate={setDateRange} />
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">Asset Category</label>
            <Select value={assetCategory} onValueChange={setAssetCategory}>
              <SelectTrigger>
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">Asset Location</label>
            <Select value={assetLocation} onValueChange={setAssetLocation}>
              <SelectTrigger>
                <SelectValue placeholder="Select location" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Locations</SelectItem>
                {locations.map((location) => (
                  <SelectItem key={location} value={location}>
                    {location}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Report Content */}
        <div className="border rounded-md">
          {reportType === "summary" && (
            <div className="p-6 space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Depreciation Summary Report</h3>
                <div className="text-sm text-muted-foreground">
                  {filteredAssets.length} assets | Fiscal Year {currentYear}
                </div>
              </div>
              
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                  <CardHeader className="py-4">
                    <CardTitle className="text-sm font-medium">Total Acquisition Cost</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{formatCurrency(totalAcquisitionCost)}</div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader className="py-4">
                    <CardTitle className="text-sm font-medium">Current Book Value</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{formatCurrency(totalCurrentValue)}</div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader className="py-4">
                    <CardTitle className="text-sm font-medium">Accumulated Depreciation</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{formatCurrency(totalAccumulatedDepreciation)}</div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader className="py-4">
                    <CardTitle className="text-sm font-medium">Current Year Depreciation</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{formatCurrency(currentYearDepreciation)}</div>
                  </CardContent>
                </Card>
              </div>
              
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Asset Category</TableHead>
                    <TableHead>Count</TableHead>
                    <TableHead>Acquisition Cost</TableHead>
                    <TableHead>Current Value</TableHead>
                    <TableHead>Accumulated Depreciation</TableHead>
                    <TableHead>Current Year Depreciation</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {Object.entries(assetsByCategory).map(([category, data]) => (
                    <TableRow key={category}>
                      <TableCell className="font-medium">{category}</TableCell>
                      <TableCell>{data.count}</TableCell>
                      <TableCell>{formatCurrency(data.acquisitionCost)}</TableCell>
                      <TableCell>{formatCurrency(data.currentValue)}</TableCell>
                      <TableCell>{formatCurrency(data.acquisitionCost - data.currentValue)}</TableCell>
                      <TableCell>{formatCurrency(data.depreciation)}</TableCell>
                    </TableRow>
                  ))}
                  <TableRow className="font-semibold">
                    <TableCell>Total</TableCell>
                    <TableCell>{filteredAssets.length}</TableCell>
                    <TableCell>{formatCurrency(totalAcquisitionCost)}</TableCell>
                    <TableCell>{formatCurrency(totalCurrentValue)}</TableCell>
                    <TableCell>{formatCurrency(totalAccumulatedDepreciation)}</TableCell>
                    <TableCell>{formatCurrency(currentYearDepreciation)}</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
          )}

          {reportType === "detailed" && (
            <div className="p-6 space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Detailed Depreciation Report</h3>
                <div className="text-sm text-muted-foreground">
                  {filteredAssets.length} assets | Fiscal Year {currentYear}
                </div>
              </div>
              
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Asset ID</TableHead>
                    <TableHead>Asset Name</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Acquisition Date</TableHead>
                    <TableHead>Acquisition Cost</TableHead>
                    <TableHead>Current Value</TableHead>
                    <TableHead>Method</TableHead>
                    <TableHead>Current Year Depreciation</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAssets.map((asset) => {
                    const schedule = depreciationSchedules[asset.id]
                    const currentYearEntry = schedule?.entries.find(
                      (entry) => new Date(entry.periodEndDate).getFullYear() === currentYear
                    )
                    
                    return (
                      <TableRow key={asset.id}>
                        <TableCell className="font-medium">{asset.id}</TableCell>
                        <TableCell>{asset.name}</TableCell>
                        <TableCell>{asset.category}</TableCell>
                        <TableCell>{new Date(asset.acquisitionDate).toLocaleDateString()}</TableCell>
                        <TableCell>{formatCurrency(asset.acquisitionCost)}</TableCell>
                        <TableCell>{formatCurrency(asset.currentValue)}</TableCell>
                        <TableCell className="capitalize">{asset.depreciationMethod.replace(/-/g, " ")}</TableCell>
                        <TableCell>{formatCurrency(currentYearEntry?.depreciationAmount || 0)}</TableCell>
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            </div>
          )}

          {reportType === "category" && (
            <div className="p-6 space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Depreciation by Category</h3>
                <div className="text-sm text-muted-foreground">
                  {filteredAssets.length} assets | Fiscal Year {currentYear}
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Acquisition Cost by Category</CardTitle>
                  </CardHeader>
                  <CardContent className="h-[300px] flex items-center justify-center">
                    <div className="text-center text-muted-foreground">
                      <PieChart className="h-16 w-16 mx-auto mb-4" />
                      <p>Chart visualization would appear here</p>
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle>Current Year Depreciation by Category</CardTitle>
                  </CardHeader>
                  <CardContent className="h-[300px] flex items-center justify-center">
                    <div className="text-center text-muted-foreground">
                      <BarChart className="h-16 w-16 mx-auto mb-4" />
                      <p>Chart visualization would appear here</p>
                    </div>
                  </CardContent>
                </Card>
              </div>
              
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Category</TableHead>
                    <TableHead>Asset Count</TableHead>
                    <TableHead>Acquisition Cost</TableHead>
                    <TableHead>Current Value</TableHead>
                    <TableHead>Accumulated Depreciation</TableHead>
                    <TableHead>Current Year Depreciation</TableHead>
                    <TableHead>% of Total</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {Object.entries(assetsByCategory).map(([category, data]) => (
                    <TableRow key={category}>
                      <TableCell className="font-medium">{category}</TableCell>
                      <TableCell>{data.count}</TableCell>
                      <TableCell>{formatCurrency(data.acquisitionCost)}</TableCell>
                      <TableCell>{formatCurrency(data.currentValue)}</TableCell>
                      <TableCell>{formatCurrency(data.acquisitionCost - data.currentValue)}</TableCell>
                      <TableCell>{formatCurrency(data.depreciation)}</TableCell>
                      <TableCell>
                        {((data.acquisitionCost / totalAcquisitionCost) * 100).toFixed(1)}%
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          {reportType === "method" && (
            <div className="p-6 space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Depreciation by Method</h3>
                <div className="text-sm text-muted-foreground">
                  {filteredAssets.length} assets | Fiscal Year {currentYear}
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Assets by Depreciation Method</CardTitle>
                  </CardHeader>
                  <CardContent className="h-[300px] flex items-center justify-center">
                    <div className="text-center text-muted-foreground">
                      <PieChart className="h-16 w-16 mx-auto mb-4" />
                      <p>Chart visualization would appear here</p>
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle>Depreciation by Method</CardTitle>
                  </CardHeader>
                  <CardContent className="h-[300px] flex items-center justify-center">
                    <div className="text-center text-muted-foreground">
                      <BarChart className="h-16 w-16 mx-auto mb-4" />
                      <p>Chart visualization would appear here</p>
                    </div>
                  </CardContent>
                </Card>
              </div>
              
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Depreciation Method</TableHead>
                    <TableHead>Asset Count</TableHead>
                    <TableHead>Acquisition Cost</TableHead>
                    <TableHead>Current Value</TableHead>
                    <TableHead>Accumulated Depreciation</TableHead>
                    <TableHead>Current Year Depreciation</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {Object.entries(assetsByMethod).map(([method, data]) => (
                    <TableRow key={method}>
                      <TableCell className="font-medium capitalize">{method.replace(/-/g, " ")}</TableCell>
                      <TableCell>{data.count}</TableCell>
                      <TableCell>{formatCurrency(data.acquisitionCost)}</TableCell>
                      <TableCell>{formatCurrency(data.currentValue)}</TableCell>
                      <TableCell>{formatCurrency(data.acquisitionCost - data.currentValue)}</TableCell>
                      <TableCell>{formatCurrency(data.depreciation)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          {reportType === "tax" && (
            <div className="p-6 space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Tax Depreciation Report</h3>
                <div className="text-sm text-muted-foreground">
                  {filteredAssets.length} assets | Tax Year {currentYear}
                </div>
              </div>
              
              <div className="p-4 border rounded-lg bg-muted/50">
                <div className="flex items-start gap-3">
                  <FileText className="h-5 w-5 text-blue-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold mb-1">IRS Form 4562 Information</h4>
                    <p className="text-sm">
                      This report contains the information needed for completing IRS Form 4562 - Depreciation and Amortization.
                    </p>
                  </div>
                </div>
              </div>
              
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Asset Description</TableHead>
                    <TableHead>Date Placed in Service</TableHead>
                    <TableHead>Cost Basis</TableHead>
                    <TableHead>Recovery Period</TableHead>
                    <TableHead>Method</TableHead>
                    <TableHead>Prior Depreciation</TableHead>
                    <TableHead>Current Year Depreciation</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAssets.map((asset) => {
                    const schedule = depreciationSchedules[asset.id]
                    const currentYearEntry = schedule?.entries.find(
                      (entry) => new Date(entry.periodEndDate).getFullYear() === currentYear
                    )
                    const priorDepreciation = asset.acquisitionCost - asset.currentValue - (currentYearEntry?.depreciationAmount || 0)
                    
                    return (
                      <TableRow key={asset.id}>
                        <TableCell className="font-medium">{asset.name}</TableCell>
                        <TableCell>{new Date(asset.acquisitionDate).toLocaleDateString()}</TableCell>
                        <TableCell>{formatCurrency(asset.acquisitionCost)}</TableCell>
                        <TableCell>{asset.usefulLife} years</TableCell>
                        <TableCell className="capitalize">
                          {asset.depreciationMethod === "straight-line" 
                            ? "Straight Line" 
                            : asset.depreciationMethod === "declining-balance" 
                              ? "200% Declining Balance" 
                              : asset.depreciationMethod.replace(/-/g, " ")}
                        </TableCell>
                        <TableCell>{formatCurrency(priorDepreciation)}</TableCell>
                        <TableCell>{formatCurrency(currentYearEntry?.depreciationAmount || 0)}</TableCell>
                      </TableRow>
                    )
                  })}
                  <TableRow className="font-semibold">
                    <TableCell colSpan={5}>Total</TableCell>
                    <TableCell>
                      {formatCurrency(
                        filteredAssets.reduce((sum, asset) => {
                          const schedule = depreciationSchedules[asset.id]
                          const currentYearEntry = schedule?.entries.find(
                            (entry) => new Date(entry.periodEndDate).getFullYear() === currentYear
                          )
                          return sum + (asset.acquisitionCost - asset.currentValue - (currentYearEntry?.depreciationAmount || 0))
                        }, 0)
                      )}
                    </TableCell>
                    <TableCell>{formatCurrency(currentYearDepreciation)}</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
              
              <div className="grid gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Section 179 Deduction</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex justify-between">
                        <span>Maximum Section 179 Deduction:</span>
                        <span className="font-semibold">$1,220,000</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Eligible Property Placed in Service:</span>
                        <span>
                          {formatCurrency(
                            filteredAssets
                              .filter((asset) => new Date(asset.acquisitionDate).getFullYear() === currentYear)
                              .reduce((sum, asset) => sum + asset.acquisitionCost, 0)
                          )}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>Threshold Cost of Section 179 Property:</span>
                        <span>$3,050,000</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle>Bonus Depreciation</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex justify-between">
                        <span>Bonus Depreciation Percentage:</span>
                        <span className="font-semibold">60%</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Eligible Property Placed in Service:</span>
                        <span>
                          {formatCurrency(
                            filteredAssets
                              .filter((asset) => new Date(asset.acquisitionDate).getFullYear() === currentYear)
                              .reduce((sum, asset) => sum + asset.acquisitionCost, 0)
                          )}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>Potential Bonus Depreciation:</span>
                        <span>
                          {formatCurrency(
                            filteredAssets
                              .filter((asset) => new Date(asset.acquisitionDate).getFullYear() === currentYear)
                              .reduce((sum, asset) => sum + asset.acquisitionCost * 0.6, 0)
                          )}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

export default function DepreciationReportDemo() {
  // Sample data for demonstration
  const assets: FinancialAsset[] = [
    {
      id: "1",
      name: "Manufacturing Equipment A",
      assetType: "Equipment",
      acquisitionCost: 250000,
      currentValue: 180000,
      depreciationMethod: "straight-line",
      usefulLife: 10,
      salvageValue: 25000,
      acquisitionDate: new Date("2022-01-15"),
      category: "Manufacturing",
      location: "Plant 1",
      status: "active",
    },
    {
      id: "2",
      name: "Office Building",
      assetType: "Real Estate",
      acquisitionCost: 2500000,
      currentValue: 2800000,
      depreciationMethod: "straight-line",
      usefulLife: 39,
      salvageValue: 500000,
      acquisitionDate: new Date("2020-06-01"),
      category: "Real Estate",
      location: "Headquarters",
      status: "active",
    },
    {
      id: "3",
      name: "Fleet Vehicle 001",
      assetType: "Vehicle",
      acquisitionCost: 45000,
      currentValue: 32000,
      depreciationMethod: "declining-balance",
      usefulLife: 5,
      salvageValue: 8000,
      acquisitionDate: new Date("2023-03-10"),
      category: "Transportation",
      location: "Fleet Depot",
      status: "active",
    },
  ]
  
  // Sample depreciation schedules
  const depreciationSchedules: Record<string, DepreciationSchedule> = {
    "1": {
      assetId: "1",
      assetName: "Manufacturing Equipment A",
      method: "straight-line",
      acquisitionCost: 250000,
      salvageValue: 25000,
      usefulLife: 10,
      startDate: "2022-01-15",
      entries: Array.from({ length: 10 }, (_, i) => ({
        period: i + 1,
        periodEndDate: new Date(2022 + i, 0, 15).toISOString().split("T")[0],
        depreciationAmount: 22500,
        accumulatedDepreciation: 22500 * (i + 1),
        bookValue: 250000 - 22500 * (i + 1),
      })),
    },
    "2": {
      assetId: "2",
      assetName: "Office Building",
      method: "straight-line",
      acquisitionCost: 2500000,
      salvageValue: 500000,
      usefulLife: 39,
      startDate: "2020-06-01",
      entries: Array.from({ length: 39 }, (_, i) => ({
        period: i + 1,
        periodEndDate: new Date(2020 + i, 5, 1).toISOString().split("T")[0],
        depreciationAmount: 51282,
        accumulatedDepreciation: 51282 * (i + 1),
        bookValue: 2500000 - 51282 * (i + 1),
      })),
    },
    "3": {
      assetId: "3",
      assetName: "Fleet Vehicle 001",
      method: "declining-balance",
      acquisitionCost: 45000,
      salvageValue: 8000,
      usefulLife: 5,
      startDate: "2023-03-10",
      entries: [
        {
          period: 1,
          periodEndDate: "2024-03-10",
          depreciationAmount: 18000,
          accumulatedDepreciation: 18000,
          bookValue: 27000,
        },
        {
          period: 2,
          periodEndDate: "2025-03-10",
          depreciationAmount: 10800,
          accumulatedDepreciation: 28800,
          bookValue: 16200,
        },
        {
          period: 3,
          periodEndDate: "2026-03-10",
          depreciationAmount: 6480,
          accumulatedDepreciation: 35280,
          bookValue: 9720,
        },
        {
          period: 4,
          periodEndDate: "2027-03-10",
          depreciationAmount: 1720,
          accumulatedDepreciation: 37000,
          bookValue: 8000,
        },
        {
          period: 5,
          periodEndDate: "2028-03-10",
          depreciationAmount: 0,
          accumulatedDepreciation: 37000,
          bookValue: 8000,
        },
      ],
    },
  }
  
  return <DepreciationReport assets={assets} depreciationSchedules={depreciationSchedules} />
}