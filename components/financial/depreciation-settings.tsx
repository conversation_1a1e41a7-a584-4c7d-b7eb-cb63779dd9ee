"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { Switch } from "@/components/ui/switch"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Settings, Save, Plus, Trash2, HelpCircle } from "lucide-react"
import { DepreciationMethod, DepreciationSettings } from "@/lib/modules/financial/depreciation-types"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

interface DepreciationSettingsProps {
  settings: DepreciationSettings
  onSave: (settings: DepreciationSettings) => void
}

export function DepreciationSettingsDialog({ settings, onSave }: DepreciationSettingsProps) {
  const [currentSettings, setCurrentSettings] = useState<DepreciationSettings>(settings)
  const [activeTab, setActiveTab] = useState("general")

  const handleSave = () => {
    onSave(currentSettings)
  }

  const handleGeneralSettingChange = (key: keyof Omit<DepreciationSettings, "assetCategoryDefaults">, value: any) => {
    setCurrentSettings((prev) => ({
      ...prev,
      [key]: value,
    }))
  }

  const handleCategoryDefaultChange = (
    category: string,
    field: keyof DepreciationSettings["assetCategoryDefaults"][string],
    value: any
  ) => {
    setCurrentSettings((prev) => ({
      ...prev,
      assetCategoryDefaults: {
        ...prev.assetCategoryDefaults,
        [category]: {
          ...prev.assetCategoryDefaults[category],
          [field]: value,
        },
      },
    }))
  }

  const addCategoryDefault = () => {
    const newCategory = `category-${Object.keys(currentSettings.assetCategoryDefaults).length + 1}`
    setCurrentSettings((prev) => ({
      ...prev,
      assetCategoryDefaults: {
        ...prev.assetCategoryDefaults,
        [newCategory]: {
          method: "straight-line",
          usefulLife: 5,
          salvageValuePercent: 10,
        },
      },
    }))
  }

  const removeCategoryDefault = (category: string) => {
    setCurrentSettings((prev) => {
      const newDefaults = { ...prev.assetCategoryDefaults }
      delete newDefaults[category]
      return {
        ...prev,
        assetCategoryDefaults: newDefaults,
      }
    })
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Depreciation Settings</CardTitle>
            <CardDescription>Configure global depreciation settings and defaults</CardDescription>
          </div>
          <Settings className="h-5 w-5 text-muted-foreground" />
        </div>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="general">General Settings</TabsTrigger>
            <TabsTrigger value="categories">Category Defaults</TabsTrigger>
            <TabsTrigger value="tax">Tax Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="general" className="space-y-4">
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="default-method" className="text-right">
                  Default Method
                </Label>
                <Select
                  value={currentSettings.defaultMethod}
                  onValueChange={(value) =>
                    handleGeneralSettingChange("defaultMethod", value as DepreciationMethod)
                  }
                >
                  <SelectTrigger id="default-method" className="col-span-3">
                    <SelectValue placeholder="Select method" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="straight-line">Straight Line</SelectItem>
                    <SelectItem value="declining-balance">Declining Balance</SelectItem>
                    <SelectItem value="sum-of-years">Sum of Years Digits</SelectItem>
                    <SelectItem value="units-of-production">Units of Production</SelectItem>
                    <SelectItem value="macrs">MACRS</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="salvage-value-percent" className="text-right">
                  Default Salvage Value (%)
                </Label>
                <div className="col-span-3 flex items-center gap-2">
                  <Input
                    id="salvage-value-percent"
                    type="number"
                    value={currentSettings.defaultSalvageValuePercent}
                    onChange={(e) =>
                      handleGeneralSettingChange("defaultSalvageValuePercent", Number(e.target.value))
                    }
                    min="0"
                    max="100"
                  />
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <HelpCircle className="h-4 w-4" />
                          <span className="sr-only">Help</span>
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Default percentage of acquisition cost to use as salvage value</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="discount-rate" className="text-right">
                  Discount Rate (%)
                </Label>
                <Input
                  id="discount-rate"
                  type="number"
                  value={currentSettings.discountRate * 100}
                  onChange={(e) => handleGeneralSettingChange("discountRate", Number(e.target.value) / 100)}
                  className="col-span-3"
                  min="0"
                  max="100"
                  step="0.1"
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="fiscal-year-start" className="text-right">
                  Fiscal Year Start
                </Label>
                <Input
                  id="fiscal-year-start"
                  type="text"
                  value={currentSettings.fiscalYearStart}
                  onChange={(e) => handleGeneralSettingChange("fiscalYearStart", e.target.value)}
                  placeholder="MM-DD"
                  className="col-span-3"
                />
              </div>

              <Separator className="my-4" />

              <div className="grid grid-cols-4 items-center gap-4">
                <div className="col-span-4">
                  <h3 className="text-lg font-medium">Advanced Options</h3>
                </div>
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="auto-calculate" className="text-right">
                  Auto-calculate Depreciation
                </Label>
                <div className="flex items-center space-x-2 col-span-3">
                  <Switch id="auto-calculate" defaultChecked />
                  <Label htmlFor="auto-calculate">Calculate depreciation automatically for new assets</Label>
                </div>
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="journal-entries" className="text-right">
                  Generate Journal Entries
                </Label>
                <div className="flex items-center space-x-2 col-span-3">
                  <Switch id="journal-entries" defaultChecked />
                  <Label htmlFor="journal-entries">Automatically create journal entries for depreciation</Label>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="categories" className="space-y-4">
            <div className="flex justify-end">
              <Button onClick={addCategoryDefault} size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Category
              </Button>
            </div>

            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Asset Category</TableHead>
                  <TableHead>Depreciation Method</TableHead>
                  <TableHead>Useful Life (Years)</TableHead>
                  <TableHead>Salvage Value (%)</TableHead>
                  <TableHead className="w-[100px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {Object.entries(currentSettings.assetCategoryDefaults).map(([category, defaults]) => (
                  <TableRow key={category}>
                    <TableCell>
                      <Input
                        value={category}
                        onChange={(e) => {
                          const newDefaults = { ...currentSettings.assetCategoryDefaults }
                          const value = newDefaults[category]
                          delete newDefaults[category]
                          setCurrentSettings((prev) => ({
                            ...prev,
                            assetCategoryDefaults: {
                              ...newDefaults,
                              [e.target.value]: value,
                            },
                          }))
                        }}
                      />
                    </TableCell>
                    <TableCell>
                      <Select
                        value={defaults.method}
                        onValueChange={(value) =>
                          handleCategoryDefaultChange(category, "method", value as DepreciationMethod)
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select method" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="straight-line">Straight Line</SelectItem>
                          <SelectItem value="declining-balance">Declining Balance</SelectItem>
                          <SelectItem value="sum-of-years">Sum of Years Digits</SelectItem>
                          <SelectItem value="units-of-production">Units of Production</SelectItem>
                          <SelectItem value="macrs">MACRS</SelectItem>
                        </SelectContent>
                      </Select>
                    </TableCell>
                    <TableCell>
                      <Input
                        type="number"
                        value={defaults.usefulLife}
                        onChange={(e) =>
                          handleCategoryDefaultChange(category, "usefulLife", Number(e.target.value))
                        }
                        min="1"
                      />
                    </TableCell>
                    <TableCell>
                      <Input
                        type="number"
                        value={defaults.salvageValuePercent}
                        onChange={(e) =>
                          handleCategoryDefaultChange(
                            category,
                            "salvageValuePercent",
                            Number(e.target.value)
                          )
                        }
                        min="0"
                        max="100"
                      />
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => removeCategoryDefault(category)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TabsContent>

          <TabsContent value="tax" className="space-y-4">
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="tax-rate" className="text-right">
                  Default Tax Rate (%)
                </Label>
                <Input
                  id="tax-rate"
                  type="number"
                  value={currentSettings.defaultTaxRate * 100}
                  onChange={(e) => handleGeneralSettingChange("defaultTaxRate", Number(e.target.value) / 100)}
                  className="col-span-3"
                  min="0"
                  max="100"
                  step="0.1"
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="section-179" className="text-right">
                  Section 179 Limit
                </Label>
                <Input
                  id="section-179"
                  type="number"
                  value="1220000"
                  className="col-span-3"
                  readOnly
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="bonus-depreciation" className="text-right">
                  Bonus Depreciation Rate (%)
                </Label>
                <Input
                  id="bonus-depreciation"
                  type="number"
                  value="60"
                  className="col-span-3"
                  readOnly
                />
              </div>

              <Separator className="my-4" />

              <div className="grid grid-cols-4 items-center gap-4">
                <div className="col-span-4">
                  <h3 className="text-lg font-medium">Tax Compliance</h3>
                </div>
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="use-irs-tables" className="text-right">
                  Use IRS Tables
                </Label>
                <div className="flex items-center space-x-2 col-span-3">
                  <Switch id="use-irs-tables" defaultChecked />
                  <Label htmlFor="use-irs-tables">Use official IRS depreciation tables for tax calculations</Label>
                </div>
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="separate-books" className="text-right">
                  Separate Books
                </Label>
                <div className="flex items-center space-x-2 col-span-3">
                  <Switch id="separate-books" defaultChecked />
                  <Label htmlFor="separate-books">Maintain separate depreciation books for tax and financial reporting</Label>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline">Cancel</Button>
        <Button onClick={handleSave}>
          <Save className="mr-2 h-4 w-4" />
          Save Settings
        </Button>
      </CardFooter>
    </Card>
  )
}

export default function DepreciationSettings() {
  const defaultSettings: DepreciationSettings = {
    defaultMethod: "straight-line",
    defaultSalvageValuePercent: 10,
    defaultTaxRate: 0.21,
    discountRate: 0.08,
    fiscalYearStart: "01-01",
    assetCategoryDefaults: {
      "IT Equipment": {
        method: "declining-balance",
        usefulLife: 5,
        salvageValuePercent: 10,
      },
      "Furniture": {
        method: "straight-line",
        usefulLife: 7,
        salvageValuePercent: 5,
      },
      "Vehicles": {
        method: "declining-balance",
        usefulLife: 5,
        salvageValuePercent: 20,
      },
      "Machinery": {
        method: "units-of-production",
        usefulLife: 10,
        salvageValuePercent: 15,
      },
      "Buildings": {
        method: "straight-line",
        usefulLife: 39,
        salvageValuePercent: 30,
      },
    },
  }

  const handleSaveSettings = (settings: DepreciationSettings) => {
    console.log("Saving settings:", settings)
    // In a real implementation, this would save to the backend
  }

  return <DepreciationSettingsDialog settings={defaultSettings} onSave={handleSaveSettings} />
}