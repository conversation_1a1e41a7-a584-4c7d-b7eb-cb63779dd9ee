"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { DatePickerWithRange } from "@/components/ui/date-range-picker"
import { DateRange } from "react-day-picker"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  Download,
  Printer,
  FileText,
  BarChart,
  PieChart,
  Filter,
  DollarSign,
  TrendingDown,
  Calendar,
  Clock,
  Package,
  Building,
  Truck,
  Laptop,
  Wrench,
  Shield,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
} from "lucide-react"
import type { FinancialAsset } from "@/lib/modules/financial/types"
import { assetLifecycleService } from "@/lib/modules/financial/asset-lifecycle-service"
import { depreciationService } from "@/lib/modules/financial/depreciation-service"

interface AssetFinancialReportProps {
  assets: FinancialAsset[]
  dateRange?: DateRange
  onDateRangeChange?: (range: DateRange | undefined) => void
}

export function AssetFinancialReport({ 
  assets, 
  dateRange, 
  onDateRangeChange 
}: AssetFinancialReportProps) {
  const [reportType, setReportType] = useState("financial")
  const [assetCategory, setAssetCategory] = useState<string>("all")
  const [assetLocation, setAssetLocation] = useState<string>("all")
  const [assetCondition, setAssetCondition] = useState<string>("all")
  const [replacementPriority, setReplacementPriority] = useState<string>("all")

  // Get unique categories, locations, conditions, and priorities for filtering
  const categories = Array.from(new Set(assets.map((asset) => asset.category)))
  const locations = Array.from(new Set(assets.map((asset) => asset.location)))
  const conditions = Array.from(new Set(assets.filter(asset => asset.assetCondition).map((asset) => asset.assetCondition as string)))
  const priorities = Array.from(new Set(assets.filter(asset => asset.replacementPriority).map((asset) => asset.replacementPriority as string)))

  // Filter assets based on selected filters
  const filteredAssets = assets.filter((asset) => {
    const matchesCategory = assetCategory === "all" || asset.category === assetCategory
    const matchesLocation = assetLocation === "all" || asset.location === assetLocation
    const matchesCondition = assetCondition === "all" || asset.assetCondition === assetCondition
    const matchesPriority = replacementPriority === "all" || asset.replacementPriority === replacementPriority
    return matchesCategory && matchesLocation && matchesCondition && matchesPriority
  })

  // Generate financial reports
  const financialReports = assetLifecycleService.generateFinancialReports(filteredAssets.map(asset => asset.id))
  
  // Generate replacement forecast
  const replacementForecast = assetLifecycleService.generateReplacementForecast(5)

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`
  }

  const getAssetTypeIcon = (assetType: string) => {
    switch (assetType) {
      case "Equipment":
        return <Package className="h-4 w-4" />
      case "Real Estate":
        return <Building className="h-4 w-4" />
      case "Vehicle":
        return <Truck className="h-4 w-4" />
      case "Technology":
      case "IT Equipment":
        return <Laptop className="h-4 w-4" />
      default:
        return <Package className="h-4 w-4" />
    }
  }

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "active":
        return "default"
      case "disposed":
        return "destructive"
      case "impaired":
        return "warning"
      default:
        return "secondary"
    }
  }

  const getConditionBadgeVariant = (condition: string) => {
    switch (condition) {
      case "excellent":
        return "default"
      case "good":
        return "secondary"
      case "fair":
        return "warning"
      case "poor":
        return "destructive"
      default:
        return "secondary"
    }
  }

  const getPriorityBadgeVariant = (priority: string) => {
    switch (priority) {
      case "critical":
        return "destructive"
      case "high":
        return "warning"
      case "medium":
        return "default"
      case "low":
        return "secondary"
      default:
        return "secondary"
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <CardTitle>Asset Financial Report</CardTitle>
            <CardDescription>
              {dateRange?.from && dateRange?.to
                ? `Reporting period: ${dateRange.from.toLocaleDateString()} - ${dateRange.to.toLocaleDateString()}`
                : "All time financial data"}
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button variant="outline" size="sm">
              <Printer className="h-4 w-4 mr-2" />
              Print
            </Button>
            <Button variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Report Controls */}
        <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-5">
          <div className="space-y-2">
            <label className="text-sm font-medium">Report Type</label>
            <Select value={reportType} onValueChange={setReportType}>
              <SelectTrigger>
                <SelectValue placeholder="Select report type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="financial">Financial Summary</SelectItem>
                <SelectItem value="depreciation">Depreciation Analysis</SelectItem>
                <SelectItem value="maintenance">Maintenance Costs</SelectItem>
                <SelectItem value="replacement">Replacement Planning</SelectItem>
                <SelectItem value="lifecycle">Lifecycle Analysis</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">Date Range</label>
            <DatePickerWithRange date={dateRange} setDate={onDateRangeChange} />
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">Asset Category</label>
            <Select value={assetCategory} onValueChange={setAssetCategory}>
              <SelectTrigger>
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">Asset Location</label>
            <Select value={assetLocation} onValueChange={setAssetLocation}>
              <SelectTrigger>
                <SelectValue placeholder="Select location" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Locations</SelectItem>
                {locations.map((location) => (
                  <SelectItem key={location} value={location}>
                    {location}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">Asset Condition</label>
            <Select value={assetCondition} onValueChange={setAssetCondition}>
              <SelectTrigger>
                <SelectValue placeholder="Select condition" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Conditions</SelectItem>
                {conditions.map((condition) => (
                  <SelectItem key={condition} value={condition}>
                    <span className="capitalize">{condition}</span>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Report Summary Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="py-4">
              <CardTitle className="text-sm font-medium">Total Asset Value</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(financialReports.assetValuationReport.totalCurrentValue)}
              </div>
              <p className="text-xs text-muted-foreground">
                {filteredAssets.length} assets
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="py-4">
              <CardTitle className="text-sm font-medium">Accumulated Depreciation</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(financialReports.assetValuationReport.totalDepreciation)}
              </div>
              <p className="text-xs text-muted-foreground">
                {formatPercentage((financialReports.assetValuationReport.totalDepreciation / 
                  financialReports.assetValuationReport.totalAcquisitionCost) * 100)} of acquisition cost
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="py-4">
              <CardTitle className="text-sm font-medium">Annual Depreciation</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(financialReports.depreciationReport.totalDepreciationExpense)}
              </div>
              <p className="text-xs text-muted-foreground">
                Current fiscal year
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="py-4">
              <CardTitle className="text-sm font-medium">Maintenance Costs</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(financialReports.maintenanceReport.totalMaintenanceCost)}
              </div>
              <p className="text-xs text-muted-foreground">
                Year to date
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Report Content */}
        <Tabs value={reportType} onValueChange={setReportType} className="space-y-4">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="financial">Financial</TabsTrigger>
            <TabsTrigger value="depreciation">Depreciation</TabsTrigger>
            <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
            <TabsTrigger value="replacement">Replacement</TabsTrigger>
            <TabsTrigger value="lifecycle">Lifecycle</TabsTrigger>
          </TabsList>

          {/* Financial Summary Tab */}
          <TabsContent value="financial" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <h3 className="text-lg font-medium mb-4">Assets by Category</h3>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Category</TableHead>
                      <TableHead>Count</TableHead>
                      <TableHead>Value</TableHead>
                      <TableHead>% of Total</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {Object.entries(financialReports.assetValuationReport.assetsByCategory).map(([category, data]: [string, any]) => (
                      <TableRow key={category}>
                        <TableCell className="font-medium">{category}</TableCell>
                        <TableCell>{data.count}</TableCell>
                        <TableCell>{formatCurrency(data.value)}</TableCell>
                        <TableCell>
                          {formatPercentage((data.value / financialReports.assetValuationReport.totalCurrentValue) * 100)}
                        </TableCell>
                      </TableRow>
                    ))}
                    <TableRow className="font-semibold">
                      <TableCell>Total</TableCell>
                      <TableCell>{filteredAssets.length}</TableCell>
                      <TableCell>{formatCurrency(financialReports.assetValuationReport.totalCurrentValue)}</TableCell>
                      <TableCell>100%</TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </div>
              
              <div>
                <h3 className="text-lg font-medium mb-4">Assets by Status</h3>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Status</TableHead>
                      <TableHead>Count</TableHead>
                      <TableHead>Value</TableHead>
                      <TableHead>% of Total</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {Object.entries(financialReports.assetValuationReport.assetsByStatus).map(([status, data]: [string, any]) => (
                      <TableRow key={status}>
                        <TableCell className="font-medium">
                          <Badge variant={getStatusBadgeVariant(status)}>
                            {status}
                          </Badge>
                        </TableCell>
                        <TableCell>{data.count}</TableCell>
                        <TableCell>{formatCurrency(data.value)}</TableCell>
                        <TableCell>
                          {formatPercentage((data.value / financialReports.assetValuationReport.totalCurrentValue) * 100)}
                        </TableCell>
                      </TableRow>
                    ))}
                    <TableRow className="font-semibold">
                      <TableCell>Total</TableCell>
                      <TableCell>{filteredAssets.length}</TableCell>
                      <TableCell>{formatCurrency(financialReports.assetValuationReport.totalCurrentValue)}</TableCell>
                      <TableCell>100%</TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </div>
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-4">Asset Financial Details</h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Asset</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Acquisition Cost</TableHead>
                    <TableHead>Current Value</TableHead>
                    <TableHead>Depreciation</TableHead>
                    <TableHead>Book Ratio</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAssets.map((asset) => {
                    const depreciation = asset.acquisitionCost - asset.currentValue;
                    const bookRatio = asset.currentValue / asset.acquisitionCost;
                    
                    return (
                      <TableRow key={asset.id}>
                        <TableCell className="font-medium">
                          <div className="flex items-center">
                            {getAssetTypeIcon(asset.assetType)}
                            <span className="ml-2">{asset.name}</span>
                          </div>
                        </TableCell>
                        <TableCell>{asset.category}</TableCell>
                        <TableCell>{formatCurrency(asset.acquisitionCost)}</TableCell>
                        <TableCell>{formatCurrency(asset.currentValue)}</TableCell>
                        <TableCell>{formatCurrency(depreciation)}</TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Progress value={bookRatio * 100} className="h-2 w-20" />
                            <span>{formatPercentage(bookRatio * 100)}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={getStatusBadgeVariant(asset.status)}>
                            {asset.status}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          </TabsContent>

          {/* Depreciation Analysis Tab */}
          <TabsContent value="depreciation" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <h3 className="text-lg font-medium mb-4">Depreciation by Method</h3>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Method</TableHead>
                      <TableHead>Annual Expense</TableHead>
                      <TableHead>% of Total</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {Object.entries(financialReports.depreciationReport.depreciationByMethod).map(([method, amount]: [string, any]) => (
                      <TableRow key={method}>
                        <TableCell className="font-medium capitalize">{method.replace(/-/g, " ")}</TableCell>
                        <TableCell>{formatCurrency(amount)}</TableCell>
                        <TableCell>
                          {formatPercentage((amount / financialReports.depreciationReport.totalDepreciationExpense) * 100)}
                        </TableCell>
                      </TableRow>
                    ))}
                    <TableRow className="font-semibold">
                      <TableCell>Total</TableCell>
                      <TableCell>{formatCurrency(financialReports.depreciationReport.totalDepreciationExpense)}</TableCell>
                      <TableCell>100%</TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </div>
              
              <div>
                <h3 className="text-lg font-medium mb-4">Depreciation by Category</h3>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Category</TableHead>
                      <TableHead>Annual Expense</TableHead>
                      <TableHead>% of Total</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {Object.entries(financialReports.depreciationReport.depreciationByCategory).map(([category, amount]: [string, any]) => (
                      <TableRow key={category}>
                        <TableCell className="font-medium">{category}</TableCell>
                        <TableCell>{formatCurrency(amount)}</TableCell>
                        <TableCell>
                          {formatPercentage((amount / financialReports.depreciationReport.totalDepreciationExpense) * 100)}
                        </TableCell>
                      </TableRow>
                    ))}
                    <TableRow className="font-semibold">
                      <TableCell>Total</TableCell>
                      <TableCell>{formatCurrency(financialReports.depreciationReport.totalDepreciationExpense)}</TableCell>
                      <TableCell>100%</TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </div>
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-4">Asset Depreciation Details</h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Asset</TableHead>
                    <TableHead>Method</TableHead>
                    <TableHead>Acquisition Cost</TableHead>
                    <TableHead>Current Value</TableHead>
                    <TableHead>Annual Depreciation</TableHead>
                    <TableHead>Remaining Life</TableHead>
                    <TableHead>Salvage Value</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAssets.map((asset) => {
                    const annualDepreciation = (asset.acquisitionCost - asset.salvageValue) / asset.usefulLife;
                    const remainingLife = assetLifecycleService.calculateRemainingUsefulLife(asset.id);
                    
                    return (
                      <TableRow key={asset.id}>
                        <TableCell className="font-medium">
                          <div className="flex items-center">
                            {getAssetTypeIcon(asset.assetType)}
                            <span className="ml-2">{asset.name}</span>
                          </div>
                        </TableCell>
                        <TableCell className="capitalize">{asset.depreciationMethod.replace(/-/g, " ")}</TableCell>
                        <TableCell>{formatCurrency(asset.acquisitionCost)}</TableCell>
                        <TableCell>{formatCurrency(asset.currentValue)}</TableCell>
                        <TableCell>{formatCurrency(annualDepreciation)}</TableCell>
                        <TableCell>{remainingLife.toFixed(1)} years</TableCell>
                        <TableCell>{formatCurrency(asset.salvageValue)}</TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          </TabsContent>

          {/* Maintenance Costs Tab */}
          <TabsContent value="maintenance" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <h3 className="text-lg font-medium mb-4">Maintenance by Type</h3>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Maintenance Type</TableHead>
                      <TableHead>Cost</TableHead>
                      <TableHead>% of Total</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {Object.entries(financialReports.maintenanceReport.maintenanceByType).map(([type, cost]: [string, any]) => (
                      <TableRow key={type}>
                        <TableCell className="font-medium capitalize">{type.replace(/-/g, " ")}</TableCell>
                        <TableCell>{formatCurrency(cost)}</TableCell>
                        <TableCell>
                          {formatPercentage((cost / financialReports.maintenanceReport.totalMaintenanceCost) * 100)}
                        </TableCell>
                      </TableRow>
                    ))}
                    <TableRow className="font-semibold">
                      <TableCell>Total</TableCell>
                      <TableCell>{formatCurrency(financialReports.maintenanceReport.totalMaintenanceCost)}</TableCell>
                      <TableCell>100%</TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </div>
              
              <div>
                <h3 className="text-lg font-medium mb-4">Maintenance Cost Ratio</h3>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Asset</TableHead>
                      <TableHead>Maintenance Cost</TableHead>
                      <TableHead>Asset Value</TableHead>
                      <TableHead>Cost Ratio</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredAssets.map((asset) => {
                      const maintenanceCost = financialReports.maintenanceReport.maintenanceByAsset[asset.id] || 0;
                      const costRatio = maintenanceCost / asset.currentValue;
                      
                      return (
                        <TableRow key={asset.id}>
                          <TableCell className="font-medium">{asset.name}</TableCell>
                          <TableCell>{formatCurrency(maintenanceCost)}</TableCell>
                          <TableCell>{formatCurrency(asset.currentValue)}</TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Progress 
                                value={Math.min(costRatio * 100, 100)} 
                                className={`h-2 w-20 ${
                                  costRatio > 0.2 ? "bg-red-500" : 
                                  costRatio > 0.1 ? "bg-yellow-500" : ""
                                }`} 
                              />
                              <span>{formatPercentage(costRatio * 100)}</span>
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </div>
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-4">Maintenance Schedule</h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Asset</TableHead>
                    <TableHead>Last Maintenance</TableHead>
                    <TableHead>Next Scheduled</TableHead>
                    <TableHead>Frequency</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAssets.map((asset) => {
                    const lastMaintenance = asset.lastMaintenanceDate 
                      ? new Date(asset.lastMaintenanceDate).toLocaleDateString()
                      : "Not recorded";
                      
                    const nextMaintenance = asset.nextMaintenanceDate
                      ? new Date(asset.nextMaintenanceDate).toLocaleDateString()
                      : "Not scheduled";
                      
                    const isOverdue = asset.nextMaintenanceDate 
                      ? new Date(asset.nextMaintenanceDate) < new Date()
                      : false;
                    
                    return (
                      <TableRow key={asset.id}>
                        <TableCell className="font-medium">{asset.name}</TableCell>
                        <TableCell>{lastMaintenance}</TableCell>
                        <TableCell>{nextMaintenance}</TableCell>
                        <TableCell className="capitalize">{asset.maintenanceFrequency || "Not set"}</TableCell>
                        <TableCell>
                          {asset.nextMaintenanceDate ? (
                            <Badge variant={isOverdue ? "destructive" : "default"}>
                              {isOverdue ? "Overdue" : "Scheduled"}
                            </Badge>
                          ) : (
                            <Badge variant="secondary">Not scheduled</Badge>
                          )}
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          </TabsContent>

          {/* Replacement Planning Tab */}
          <TabsContent value="replacement" className="space-y-4">
            <div>
              <h3 className="text-lg font-medium mb-4">Replacement Forecast</h3>
              <div className="grid gap-4 md:grid-cols-5">
                {replacementForecast.map((forecast: any) => (
                  <Card key={forecast.year}>
                    <CardHeader className="py-4">
                      <CardTitle className="text-sm font-medium">{forecast.year}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-xl font-bold">
                        {formatCurrency(forecast.totalReplacementCost)}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {forecast.assetsToReplace.length} assets
                      </p>
                      {forecast.assetsToReplace.length > 0 && (
                        <div className="mt-2">
                          <Badge variant={
                            forecast.assetsToReplace.some((a: any) => a.replacementPriority === "critical")
                              ? "destructive"
                              : forecast.assetsToReplace.some((a: any) => a.replacementPriority === "high")
                              ? "warning"
                              : "default"
                          }>
                            {forecast.assetsToReplace.some((a: any) => a.replacementPriority === "critical")
                              ? "Critical"
                              : forecast.assetsToReplace.some((a: any) => a.replacementPriority === "high")
                              ? "High Priority"
                              : "Planned"
                            }
                          </Badge>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-4">Assets by Replacement Priority</h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Priority</TableHead>
                    <TableHead>Count</TableHead>
                    <TableHead>Replacement Cost</TableHead>
                    <TableHead>% of Total</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {priorities.map((priority) => {
                    const priorityAssets = filteredAssets.filter(asset => asset.replacementPriority === priority);
                    const replacementCost = priorityAssets.reduce((sum, asset) => {
                      // Estimate replacement cost with 3% annual inflation
                      const yearsSinceAcquisition = new Date().getFullYear() - new Date(asset.acquisitionDate).getFullYear();
                      return sum + (asset.acquisitionCost * Math.pow(1.03, yearsSinceAcquisition));
                    }, 0);
                    
                    const totalReplacementCost = filteredAssets.reduce((sum, asset) => {
                      const yearsSinceAcquisition = new Date().getFullYear() - new Date(asset.acquisitionDate).getFullYear();
                      return sum + (asset.acquisitionCost * Math.pow(1.03, yearsSinceAcquisition));
                    }, 0);
                    
                    return (
                      <TableRow key={priority}>
                        <TableCell className="font-medium">
                          <Badge variant={getPriorityBadgeVariant(priority)}>
                            {priority}
                          </Badge>
                        </TableCell>
                        <TableCell>{priorityAssets.length}</TableCell>
                        <TableCell>{formatCurrency(replacementCost)}</TableCell>
                        <TableCell>
                          {formatPercentage((replacementCost / totalReplacementCost) * 100)}
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-4">Asset Replacement Details</h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Asset</TableHead>
                    <TableHead>Acquisition Date</TableHead>
                    <TableHead>Useful Life</TableHead>
                    <TableHead>Remaining Life</TableHead>
                    <TableHead>Replacement Year</TableHead>
                    <TableHead>Estimated Cost</TableHead>
                    <TableHead>Priority</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAssets.map((asset) => {
                    const remainingLife = assetLifecycleService.calculateRemainingUsefulLife(asset.id);
                    const acquisitionYear = new Date(asset.acquisitionDate).getFullYear();
                    const replacementYear = asset.replacementYear || (acquisitionYear + asset.usefulLife);
                    
                    // Estimate replacement cost with 3% annual inflation
                    const yearsSinceAcquisition = replacementYear - acquisitionYear;
                    const estimatedReplacementCost = asset.acquisitionCost * Math.pow(1.03, yearsSinceAcquisition);
                    
                    return (
                      <TableRow key={asset.id}>
                        <TableCell className="font-medium">{asset.name}</TableCell>
                        <TableCell>{new Date(asset.acquisitionDate).toLocaleDateString()}</TableCell>
                        <TableCell>{asset.usefulLife} years</TableCell>
                        <TableCell>{remainingLife.toFixed(1)} years</TableCell>
                        <TableCell>{replacementYear}</TableCell>
                        <TableCell>{formatCurrency(estimatedReplacementCost)}</TableCell>
                        <TableCell>
                          <Badge variant={getPriorityBadgeVariant(asset.replacementPriority || "medium")}>
                            {asset.replacementPriority || "medium"}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          </TabsContent>

          {/* Lifecycle Analysis Tab */}
          <TabsContent value="lifecycle" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <h3 className="text-lg font-medium mb-4">Assets by Condition</h3>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Condition</TableHead>
                      <TableHead>Count</TableHead>
                      <TableHead>Value</TableHead>
                      <TableHead>% of Total</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {conditions.map((condition) => {
                      const conditionAssets = filteredAssets.filter(asset => asset.assetCondition === condition);
                      const conditionValue = conditionAssets.reduce((sum, asset) => sum + asset.currentValue, 0);
                      const totalValue = filteredAssets.reduce((sum, asset) => sum + asset.currentValue, 0);
                      
                      return (
                        <TableRow key={condition}>
                          <TableCell className="font-medium">
                            <Badge variant={getConditionBadgeVariant(condition)}>
                              {condition}
                            </Badge>
                          </TableCell>
                          <TableCell>{conditionAssets.length}</TableCell>
                          <TableCell>{formatCurrency(conditionValue)}</TableCell>
                          <TableCell>
                            {formatPercentage((conditionValue / totalValue) * 100)}
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </div>
              
              <div>
                <h3 className="text-lg font-medium mb-4">Asset Age Distribution</h3>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Age Range</TableHead>
                      <TableHead>Count</TableHead>
                      <TableHead>Value</TableHead>
                      <TableHead>% of Total</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {(() => {
                      const ageRanges = [
                        { name: "< 1 year", min: 0, max: 1 },
                        { name: "1-3 years", min: 1, max: 3 },
                        { name: "3-5 years", min: 3, max: 5 },
                        { name: "5-10 years", min: 5, max: 10 },
                        { name: "> 10 years", min: 10, max: 100 }
                      ];
                      
                      const totalValue = filteredAssets.reduce((sum, asset) => sum + asset.currentValue, 0);
                      
                      return ageRanges.map(range => {
                        const currentDate = new Date();
                        const rangeAssets = filteredAssets.filter(asset => {
                          const acquisitionDate = new Date(asset.acquisitionDate);
                          const ageInYears = (currentDate.getTime() - acquisitionDate.getTime()) / (1000 * 60 * 60 * 24 * 365);
                          return ageInYears >= range.min && ageInYears < range.max;
                        });
                        
                        const rangeValue = rangeAssets.reduce((sum, asset) => sum + asset.currentValue, 0);
                        
                        return (
                          <TableRow key={range.name}>
                            <TableCell className="font-medium">{range.name}</TableCell>
                            <TableCell>{rangeAssets.length}</TableCell>
                            <TableCell>{formatCurrency(rangeValue)}</TableCell>
                            <TableCell>
                              {formatPercentage((rangeValue / totalValue) * 100)}
                            </TableCell>
                          </TableRow>
                        );
                      });
                    })()}
                  </TableBody>
                </Table>
              </div>
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-4">Asset Lifecycle Details</h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Asset</TableHead>
                    <TableHead>Age</TableHead>
                    <TableHead>Condition</TableHead>
                    <TableHead>Utilization</TableHead>
                    <TableHead>Maintenance Ratio</TableHead>
                    <TableHead>ROI</TableHead>
                    <TableHead>Lifecycle Stage</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAssets.map((asset) => {
                    const currentDate = new Date();
                    const acquisitionDate = new Date(asset.acquisitionDate);
                    const ageInYears = (currentDate.getTime() - acquisitionDate.getTime()) / (1000 * 60 * 60 * 24 * 365);
                    const lifePercentage = ageInYears / asset.usefulLife;
                    
                    // Calculate metrics
                    const metrics = assetLifecycleService.calculateAssetPerformanceMetrics(asset.id);
                    const maintenanceCost = financialReports.maintenanceReport.maintenanceByAsset[asset.id] || 0;
                    const maintenanceRatio = maintenanceCost / asset.currentValue;
                    
                    // Determine lifecycle stage
                    let lifecycleStage = "";
                    if (lifePercentage < 0.25) {
                      lifecycleStage = "Acquisition";
                    } else if (lifePercentage < 0.75) {
                      lifecycleStage = "Utilization";
                    } else if (lifePercentage < 0.9) {
                      lifecycleStage = "Decline";
                    } else {
                      lifecycleStage = "End of Life";
                    }
                    
                    return (
                      <TableRow key={asset.id}>
                        <TableCell className="font-medium">{asset.name}</TableCell>
                        <TableCell>{ageInYears.toFixed(1)} years</TableCell>
                        <TableCell>
                          <Badge variant={getConditionBadgeVariant(asset.assetCondition || "fair")}>
                            {asset.assetCondition || "fair"}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Progress value={metrics.utilizationRate * 100} className="h-2 w-20" />
                            <span>{formatPercentage(metrics.utilizationRate * 100)}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Progress 
                              value={Math.min(maintenanceRatio * 100, 100)} 
                              className={`h-2 w-20 ${
                                maintenanceRatio > 0.2 ? "bg-red-500" : 
                                maintenanceRatio > 0.1 ? "bg-yellow-500" : ""
                              }`} 
                            />
                            <span>{formatPercentage(maintenanceRatio * 100)}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <span className={metrics.returnOnAsset >= 0 ? "text-green-600" : "text-red-600"}>
                            {formatPercentage(metrics.returnOnAsset * 100)}
                          </span>
                        </TableCell>
                        <TableCell>
                          <Badge variant={
                            lifecycleStage === "End of Life" ? "destructive" :
                            lifecycleStage === "Decline" ? "warning" :
                            lifecycleStage === "Utilization" ? "default" :
                            "secondary"
                          }>
                            {lifecycleStage}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between">
        <div className="text-sm text-muted-foreground">
          {filteredAssets.length} assets selected out of {assets.length} total assets
        </div>
        <Button variant="outline" size="sm">
          <FileText className="h-4 w-4 mr-2" />
          Generate Full Report
        </Button>
      </CardFooter>
    </Card>
  )
}

export default function AssetFinancialReportDemo() {
  // Sample data for demonstration
  const assets: FinancialAsset[] = [
    {
      id: "1",
      name: "Manufacturing Equipment A",
      assetType: "Equipment",
      acquisitionCost: 250000,
      currentValue: 180000,
      depreciationMethod: "straight-line",
      usefulLife: 10,
      salvageValue: 25000,
      acquisitionDate: new Date("2022-01-15"),
      category: "Manufacturing",
      location: "Plant 1",
      status: "active",
      assetCondition: "excellent",
      replacementPriority: "low",
    },
    {
      id: "2",
      name: "Office Building",
      assetType: "Real Estate",
      acquisitionCost: 2500000,
      currentValue: 2800000,
      depreciationMethod: "straight-line",
      usefulLife: 39,
      salvageValue: 500000,
      acquisitionDate: new Date("2020-06-01"),
      category: "Real Estate",
      location: "Headquarters",
      status: "active",
      assetCondition: "good",
      replacementPriority: "low",
    },
    {
      id: "3",
      name: "Fleet Vehicle 001",
      assetType: "Vehicle",
      acquisitionCost: 45000,
      currentValue: 32000,
      depreciationMethod: "declining-balance",
      usefulLife: 5,
      salvageValue: 8000,
      acquisitionDate: new Date("2023-03-10"),
      category: "Transportation",
      location: "Fleet Depot",
      status: "active",
      assetCondition: "good",
      replacementPriority: "medium",
    },
  ]
  
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(new Date().getFullYear(), 0, 1), // January 1st of current year
    to: new Date(), // Today
  })
  
  return <AssetFinancialReport assets={assets} dateRange={dateRange} onDateRangeChange={setDateRange} />
}