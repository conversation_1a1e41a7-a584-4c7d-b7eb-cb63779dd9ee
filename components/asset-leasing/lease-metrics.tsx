"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { AssetLeasingService } from "@/lib/modules/asset-leasing/services"
import type { LeaseMetrics } from "@/lib/modules/asset-leasing/types"
import { formatCurrency } from "@/lib/utils"
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Legend
} from "recharts"

export default function LeaseMetricsDisplay() {
  const [metrics, setMetrics] = useState<LeaseMetrics | null>(null)
  const [leasesByType, setLeasesByType] = useState<{ name: string; value: number }[]>([])
  const [leasesByStatus, setLeasesByStatus] = useState<{ name: string; value: number }[]>([])
  
  useEffect(() => {
    const leasingService = AssetLeasingService.getInstance()
    
    // Get metrics
    leasingService.getLeaseMetrics().then(fetchedMetrics => {
      setMetrics(fetchedMetrics)
    })
    
    // Get leases for charts
    const leases = leasingService.getLeases()
    
    // Count leases by type
    const typeCount: Record<string, number> = {}
    leases.forEach(lease => {
      typeCount[lease.leaseType] = (typeCount[lease.leaseType] || 0) + 1
    })
    
    setLeasesByType(
      Object.entries(typeCount).map(([name, value]) => ({ name, value }))
    )
    
    // Count leases by status
    const statusCount: Record<string, number> = {}
    leases.forEach(lease => {
      statusCount[lease.status] = (statusCount[lease.status] || 0) + 1
    })
    
    setLeasesByStatus(
      Object.entries(statusCount).map(([name, value]) => ({ name, value }))
    )
  }, [])
  
  // Colors for pie charts
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8']
  
  if (!metrics) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card className="h-24 animate-pulse bg-muted" />
        <Card className="h-24 animate-pulse bg-muted" />
        <Card className="h-24 animate-pulse bg-muted" />
        <Card className="h-24 animate-pulse bg-muted" />
      </div>
    )
  }
  
  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-6">
            <div className="text-2xl font-bold">{metrics.totalActiveLeases}</div>
            <p className="text-muted-foreground">Active Leases</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="text-2xl font-bold">{formatCurrency(metrics.monthlyRevenue)}</div>
            <p className="text-muted-foreground">Monthly Revenue</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="text-2xl font-bold">{metrics.averageLeaseLength.toFixed(1)} months</div>
            <p className="text-muted-foreground">Avg. Lease Length</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="text-2xl font-bold">{metrics.renewalRate.toFixed(1)}%</div>
            <p className="text-muted-foreground">Renewal Rate</p>
          </CardContent>
        </Card>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <Card>
          <CardContent className="p-6">
            <h3 className="font-medium mb-4">Leases by Type</h3>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={leasesByType}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {leasesByType.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => [`${value} leases`, 'Count']} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <h3 className="font-medium mb-4">Leases by Status</h3>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={leasesByStatus}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip formatter={(value) => [`${value} leases`, 'Count']} />
                  <Bar dataKey="value" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  )
}