"use client"

import { useState, useEffect } from "react"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Search, CreditCard } from "lucide-react"
import { AssetLeasingService } from "@/lib/modules/asset-leasing/services"
import type { PaymentSchedule } from "@/lib/modules/asset-leasing/types"
import { formatCurrency } from "@/lib/utils"
import { useToast } from "@/components/ui/use-toast"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export default function PaymentsList() {
  const [payments, setPayments] = useState<PaymentSchedule[]>([])
  const [filteredPayments, setFilteredPayments] = useState<PaymentSchedule[]>([])
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [paymentDialog, setPaymentDialog] = useState<{ open: boolean; payment: PaymentSchedule | null }>({
    open: false,
    payment: null
  })
  const [paymentAmount, setPaymentAmount] = useState<number>(0)
  const [paymentMethod, setPaymentMethod] = useState<string>("bank_transfer")
  const { toast } = useToast()
  
  // Load payments
  useEffect(() => {
    const leasingService = AssetLeasingService.getInstance()
    
    // Update late fees before fetching payments
    leasingService.calculateLateFees().then(() => {
      const fetchedPayments = leasingService.getPayments()
      setPayments(fetchedPayments)
      applyFilters(fetchedPayments, searchQuery, statusFilter)
    })
  }, [])
  
  // Apply filters when search query or status filter changes
  useEffect(() => {
    applyFilters(payments, searchQuery, statusFilter)
  }, [searchQuery, statusFilter])
  
  const applyFilters = (allPayments: PaymentSchedule[], query: string, status: string) => {
    let filtered = [...allPayments]
    
    // Apply status filter
    if (status !== "all") {
      filtered = filtered.filter(payment => payment.status === status)
    }
    
    // Apply search query
    if (query.trim() !== "") {
      const lowercaseQuery = query.toLowerCase()
      filtered = filtered.filter(payment => 
        payment.id.toLowerCase().includes(lowercaseQuery) ||
        payment.leaseId.toLowerCase().includes(lowercaseQuery)
      )
    }
    
    // Sort by due date (most recent first)
    filtered.sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime())
    
    setFilteredPayments(filtered)
  }
  
  const handleRecordPayment = async () => {
    if (!paymentDialog.payment) return
    
    try {
      const leasingService = AssetLeasingService.getInstance()
      const success = await leasingService.recordPayment(
        paymentDialog.payment.id,
        paymentAmount,
        paymentMethod
      )
      
      if (success) {
        toast({
          title: "Payment Recorded",
          description: `Payment of ${formatCurrency(paymentAmount)} has been recorded successfully.`,
        })
        
        // Refresh the payments list
        const updatedPayments = leasingService.getPayments()
        setPayments(updatedPayments)
        applyFilters(updatedPayments, searchQuery, statusFilter)
        
        // Close the dialog
        setPaymentDialog({ open: false, payment: null })
      } else {
        toast({
          title: "Error",
          description: "Failed to record the payment. Please try again.",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error recording payment:", error)
      toast({
        title: "Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      })
    }
  }
  
  const openPaymentDialog = (payment: PaymentSchedule) => {
    setPaymentAmount(payment.amount - payment.paidAmount)
    setPaymentMethod("bank_transfer")
    setPaymentDialog({ open: true, payment })
  }
  
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Paid":
        return <Badge className="bg-green-500">Paid</Badge>
      case "Pending":
        return <Badge variant="outline">Pending</Badge>
      case "Overdue":
        return <Badge variant="destructive">Overdue</Badge>
      case "Partial":
        return <Badge variant="secondary">Partial</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }
  
  return (
    <div>
      <div className="flex items-center justify-between mb-4">
        <div className="flex gap-4">
          <div className="relative w-72">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search payments..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="Pending">Pending</SelectItem>
              <SelectItem value="Paid">Paid</SelectItem>
              <SelectItem value="Overdue">Overdue</SelectItem>
              <SelectItem value="Partial">Partial</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="text-sm text-muted-foreground">
          {filteredPayments.length} payment{filteredPayments.length !== 1 ? 's' : ''} found
        </div>
      </div>
      
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Payment ID</TableHead>
              <TableHead>Lease ID</TableHead>
              <TableHead>Due Date</TableHead>
              <TableHead>Amount</TableHead>
              <TableHead>Paid Amount</TableHead>
              <TableHead>Late Fee</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredPayments.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="h-24 text-center">
                  No payments found.
                </TableCell>
              </TableRow>
            ) : (
              filteredPayments.map((payment) => (
                <TableRow key={payment.id}>
                  <TableCell className="font-medium">{payment.id}</TableCell>
                  <TableCell>{payment.leaseId}</TableCell>
                  <TableCell>{payment.dueDate}</TableCell>
                  <TableCell>{formatCurrency(payment.amount)}</TableCell>
                  <TableCell>{formatCurrency(payment.paidAmount)}</TableCell>
                  <TableCell>
                    {payment.lateFee > 0 && (
                      <span className="text-red-500">{formatCurrency(payment.lateFee)}</span>
                    )}
                  </TableCell>
                  <TableCell>{getStatusBadge(payment.status)}</TableCell>
                  <TableCell>
                    {(payment.status === "Pending" || payment.status === "Overdue" || payment.status === "Partial") && (
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => openPaymentDialog(payment)}
                      >
                        <CreditCard className="mr-2 h-4 w-4" />
                        Record Payment
                      </Button>
                    )}
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
      
      {/* Payment Dialog */}
      <Dialog 
        open={paymentDialog.open} 
        onOpenChange={(open) => !open && setPaymentDialog({ open: false, payment: null })}
      >
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Record Payment</DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            {paymentDialog.payment && (
              <>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Payment ID</Label>
                    <div className="font-medium">{paymentDialog.payment.id}</div>
                  </div>
                  <div>
                    <Label>Due Date</Label>
                    <div className="font-medium">{paymentDialog.payment.dueDate}</div>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Total Amount</Label>
                    <div className="font-medium">{formatCurrency(paymentDialog.payment.amount)}</div>
                  </div>
                  <div>
                    <Label>Remaining Balance</Label>
                    <div className="font-medium">
                      {formatCurrency(paymentDialog.payment.amount - paymentDialog.payment.paidAmount)}
                    </div>
                  </div>
                </div>
                
                {paymentDialog.payment.lateFee > 0 && (
                  <div>
                    <Label>Late Fee</Label>
                    <div className="font-medium text-red-500">
                      {formatCurrency(paymentDialog.payment.lateFee)}
                    </div>
                  </div>
                )}
                
                <div className="space-y-2">
                  <Label htmlFor="paymentAmount">Payment Amount</Label>
                  <Input 
                    id="paymentAmount" 
                    type="number"
                    value={paymentAmount}
                    onChange={(e) => setPaymentAmount(parseFloat(e.target.value))}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="paymentMethod">Payment Method</Label>
                  <Select value={paymentMethod} onValueChange={setPaymentMethod}>
                    <SelectTrigger id="paymentMethod">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                      <SelectItem value="credit_card">Credit Card</SelectItem>
                      <SelectItem value="check">Check</SelectItem>
                      <SelectItem value="cash">Cash</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </>
            )}
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setPaymentDialog({ open: false, payment: null })}>
              Cancel
            </Button>
            <Button onClick={handleRecordPayment}>Record Payment</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}