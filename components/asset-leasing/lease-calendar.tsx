"use client"

import { useState, useEffect } from "react"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { AssetLeasingService } from "@/lib/modules/asset-leasing/services"
import type { LeaseAgreement, PaymentSchedule } from "@/lib/modules/asset-leasing/types"
import { formatCurrency } from "@/lib/utils"

interface CalendarEvent {
  date: Date
  type: "leaseStart" | "leaseEnd" | "payment"
  title: string
  details: string
  leaseId?: string
  paymentId?: string
}

export default function LeaseCalendar() {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date())
  const [events, setEvents] = useState<CalendarEvent[]>([])
  const [selectedDateEvents, setSelectedDateEvents] = useState<CalendarEvent[]>([])
  
  // Load lease data and generate calendar events
  useEffect(() => {
    const leasingService = AssetLeasingService.getInstance()
    const leases = leasingService.getLeases()
    const payments = leasingService.getPayments()
    
    const calendarEvents: CalendarEvent[] = []
    
    // Add lease start and end dates
    leases.forEach(lease => {
      // Lease start date
      calendarEvents.push({
        date: new Date(lease.startDate),
        type: "leaseStart",
        title: `Lease Start: ${lease.assetName}`,
        details: `Lease agreement with ${lease.lesseeName} begins`,
        leaseId: lease.id
      })
      
      // Lease end date
      calendarEvents.push({
        date: new Date(lease.endDate),
        type: "leaseEnd",
        title: `Lease End: ${lease.assetName}`,
        details: `Lease agreement with ${lease.lesseeName} expires`,
        leaseId: lease.id
      })
    })
    
    // Add payment dates
    payments.forEach(payment => {
      calendarEvents.push({
        date: new Date(payment.dueDate),
        type: "payment",
        title: `Payment Due: ${payment.id}`,
        details: `Amount: ${formatCurrency(payment.amount)}`,
        paymentId: payment.id,
        leaseId: payment.leaseId
      })
    })
    
    setEvents(calendarEvents)
    
    // Update selected date events
    if (selectedDate) {
      updateSelectedDateEvents(selectedDate, calendarEvents)
    }
  }, [])
  
  // Update events for selected date when date changes
  useEffect(() => {
    if (selectedDate) {
      updateSelectedDateEvents(selectedDate, events)
    } else {
      setSelectedDateEvents([])
    }
  }, [selectedDate, events])
  
  const updateSelectedDateEvents = (date: Date, allEvents: CalendarEvent[]) => {
    const dateString = date.toDateString()
    const filtered = allEvents.filter(event => event.date.toDateString() === dateString)
    setSelectedDateEvents(filtered)
  }
  
  // Function to determine if a date has events
  const dateHasEvents = (date: Date) => {
    const dateString = date.toDateString()
    return events.some(event => event.date.toDateString() === dateString)
  }
  
  // Custom day render function for the calendar
  const renderDay = (day: Date) => {
    const hasEvents = dateHasEvents(day)
    
    return (
      <div className={`relative ${hasEvents ? 'font-bold' : ''}`}>
        {day.getDate()}
        {hasEvents && (
          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-primary rounded-full" />
        )}
      </div>
    )
  }
  
  // Get badge color based on event type
  const getEventBadge = (type: string) => {
    switch (type) {
      case "leaseStart":
        return <Badge className="bg-green-500">Start</Badge>
      case "leaseEnd":
        return <Badge className="bg-orange-500">End</Badge>
      case "payment":
        return <Badge className="bg-blue-500">Payment</Badge>
      default:
        return <Badge>Event</Badge>
    }
  }
  
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div className="md:col-span-2">
        <CalendarComponent
          mode="single"
          selected={selectedDate}
          onSelect={setSelectedDate}
          className="rounded-md border"
          components={{
            Day: ({ day }) => renderDay(day),
          }}
        />
      </div>
      
      <div className="space-y-4">
        <h3 className="font-medium text-lg">
          Events for {selectedDate?.toLocaleDateString(undefined, { month: 'long', day: 'numeric', year: 'numeric' })}
        </h3>
        
        {selectedDateEvents.length === 0 ? (
          <p className="text-muted-foreground">No events scheduled for this date.</p>
        ) : (
          selectedDateEvents.map((event, index) => (
            <Card key={index} className="overflow-hidden">
              <CardContent className="p-4">
                <div className="flex justify-between items-start">
                  <div className="font-medium">{event.title}</div>
                  {getEventBadge(event.type)}
                </div>
                <p className="text-sm text-muted-foreground mt-1">{event.details}</p>
                {event.leaseId && (
                  <p className="text-xs text-muted-foreground mt-2">Lease ID: {event.leaseId}</p>
                )}
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  )
}