"use client"

import { useState, useEffect } from "react"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { MoreHorizontal, Search, FileEdit, Trash, RefreshCw, Ban } from "lucide-react"
import { AssetLeasingService } from "@/lib/modules/asset-leasing/services"
import type { LeaseAgreement } from "@/lib/modules/asset-leasing/types"
import LeaseDialog from "./lease-dialog"
import LeaseRenewalDialog from "./lease-renewal-dialog"
import { formatCurrency } from "@/lib/utils"
import { useToast } from "@/components/ui/use-toast"

interface LeaseListProps {
  status?: string
}

export default function LeaseList({ status }: LeaseListProps) {
  const [leases, setLeases] = useState<LeaseAgreement[]>([])
  const [filteredLeases, setFilteredLeases] = useState<LeaseAgreement[]>([])
  const [searchQuery, setSearchQuery] = useState("")
  const [editLease, setEditLease] = useState<LeaseAgreement | null>(null)
  const [renewLease, setRenewLease] = useState<LeaseAgreement | null>(null)
  const { toast } = useToast()
  
  useEffect(() => {
    const leasingService = AssetLeasingService.getInstance()
    const fetchedLeases = leasingService.getLeases(status ? { status } : undefined)
    setLeases(fetchedLeases)
    setFilteredLeases(fetchedLeases)
  }, [status])
  
  useEffect(() => {
    if (searchQuery.trim() === "") {
      setFilteredLeases(leases)
      return
    }
    
    const query = searchQuery.toLowerCase()
    const filtered = leases.filter(lease => 
      lease.assetName.toLowerCase().includes(query) ||
      lease.lessorName.toLowerCase().includes(query) ||
      lease.lesseeName.toLowerCase().includes(query)
    )
    setFilteredLeases(filtered)
  }, [searchQuery, leases])
  
  const handleTerminateLease = async (lease: LeaseAgreement) => {
    if (confirm(`Are you sure you want to terminate the lease for ${lease.assetName}?`)) {
      const leasingService = AssetLeasingService.getInstance()
      const today = new Date().toISOString().split('T')[0]
      const success = await leasingService.terminateLease(lease.id, today, "Early termination")
      
      if (success) {
        toast({
          title: "Lease Terminated",
          description: `The lease for ${lease.assetName} has been terminated.`,
        })
        
        // Refresh the list
        const updatedLeases = leasingService.getLeases(status ? { status } : undefined)
        setLeases(updatedLeases)
        setFilteredLeases(updatedLeases)
      } else {
        toast({
          title: "Error",
          description: "Failed to terminate the lease. Please try again.",
          variant: "destructive",
        })
      }
    }
  }
  
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Active":
        return <Badge className="bg-green-500">Active</Badge>
      case "Draft":
        return <Badge variant="outline">Draft</Badge>
      case "Expired":
        return <Badge variant="secondary">Expired</Badge>
      case "Terminated":
        return <Badge variant="destructive">Terminated</Badge>
      case "Renewed":
        return <Badge className="bg-blue-500">Renewed</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }
  
  return (
    <div>
      <div className="flex items-center justify-between mb-4">
        <div className="relative w-72">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search leases..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="text-sm text-muted-foreground">
          {filteredLeases.length} lease{filteredLeases.length !== 1 ? 's' : ''} found
        </div>
      </div>
      
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Asset</TableHead>
              <TableHead>Lessor</TableHead>
              <TableHead>Lessee</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Start Date</TableHead>
              <TableHead>End Date</TableHead>
              <TableHead>Monthly Payment</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="w-[80px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredLeases.length === 0 ? (
              <TableRow>
                <TableCell colSpan={9} className="h-24 text-center">
                  No lease agreements found.
                </TableCell>
              </TableRow>
            ) : (
              filteredLeases.map((lease) => (
                <TableRow key={lease.id}>
                  <TableCell className="font-medium">{lease.assetName}</TableCell>
                  <TableCell>{lease.lessorName}</TableCell>
                  <TableCell>{lease.lesseeName}</TableCell>
                  <TableCell>{lease.leaseType}</TableCell>
                  <TableCell>{lease.startDate}</TableCell>
                  <TableCell>{lease.endDate}</TableCell>
                  <TableCell>{formatCurrency(lease.monthlyPayment)}</TableCell>
                  <TableCell>{getStatusBadge(lease.status)}</TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => setEditLease(lease)}>
                          <FileEdit className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        {lease.status === "Active" && (
                          <>
                            <DropdownMenuItem onClick={() => setRenewLease(lease)}>
                              <RefreshCw className="mr-2 h-4 w-4" />
                              Renew
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleTerminateLease(lease)}>
                              <Ban className="mr-2 h-4 w-4" />
                              Terminate
                            </DropdownMenuItem>
                          </>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
      
      {editLease && (
        <LeaseDialog 
          open={!!editLease} 
          onOpenChange={(open) => !open && setEditLease(null)} 
          lease={editLease}
        />
      )}
      
      {renewLease && (
        <LeaseRenewalDialog
          open={!!renewLease}
          onOpenChange={(open) => !open && setRenewLease(null)}
          lease={renewLease}
        />
      )}
    </div>
  )
}