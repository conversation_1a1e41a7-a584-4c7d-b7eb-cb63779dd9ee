"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>alogFooter } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { AssetLeasingService } from "@/lib/modules/asset-leasing/services"
import type { LeaseAgreement } from "@/lib/modules/asset-leasing/types"
import { useToast } from "@/components/ui/use-toast"
import { CalendarIcon } from "lucide-react"
import { format } from "date-fns"
import { Calendar } from "@/components/ui/calendar"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"

interface LeaseDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  lease?: LeaseAgreement
}

export default function LeaseDialog({ open, onOpenChange, lease }: LeaseDialogProps) {
  const isEditing = !!lease
  const { toast } = useToast()
  
  // Form state
  const [formData, setFormData] = useState({
    assetId: "",
    assetName: "",
    lessorId: "",
    lessorName: "",
    lesseeId: "",
    lesseeName: "",
    leaseType: "Operating",
    startDate: "",
    endDate: "",
    monthlyPayment: 0,
    totalValue: 0,
    securityDeposit: 0,
    renewalOptions: 0,
    earlyTerminationClause: false,
    maintenanceResponsibility: "Lessor",
    insuranceRequirement: false,
    terms: "",
    attachments: [] as string[],
  })
  
  // Assets list for dropdown
  const [assets, setAssets] = useState<{ id: string; name: string }[]>([])
  
  // Load assets (this would typically come from an API)
  useEffect(() => {
    // Mock data for assets
    setAssets([
      { id: "asset1", name: "Office Building A" },
      { id: "asset2", name: "Warehouse Equipment" },
      { id: "asset3", name: "Company Vehicle Fleet" },
      { id: "asset4", name: "Manufacturing Equipment" },
    ])
  }, [])
  
  // Initialize form with lease data if editing
  useEffect(() => {
    if (isEditing && lease) {
      setFormData({
        assetId: lease.assetId,
        assetName: lease.assetName,
        lessorId: lease.lessorId,
        lessorName: lease.lessorName,
        lesseeId: lease.lesseeId,
        lesseeName: lease.lesseeName,
        leaseType: lease.leaseType,
        startDate: lease.startDate,
        endDate: lease.endDate,
        monthlyPayment: lease.monthlyPayment,
        totalValue: lease.totalValue,
        securityDeposit: lease.securityDeposit,
        renewalOptions: lease.renewalOptions,
        earlyTerminationClause: lease.earlyTerminationClause,
        maintenanceResponsibility: lease.maintenanceResponsibility,
        insuranceRequirement: lease.insuranceRequirement,
        terms: lease.terms,
        attachments: lease.attachments,
      })
    } else {
      // Reset form for new lease
      setFormData({
        assetId: "",
        assetName: "",
        lessorId: "ORG-001", // Default organization ID
        lessorName: "Our Company", // Default organization name
        lesseeId: "",
        lesseeName: "",
        leaseType: "Operating",
        startDate: format(new Date(), "yyyy-MM-dd"),
        endDate: format(new Date(new Date().setFullYear(new Date().getFullYear() + 1)), "yyyy-MM-dd"),
        monthlyPayment: 0,
        totalValue: 0,
        securityDeposit: 0,
        renewalOptions: 0,
        earlyTerminationClause: false,
        maintenanceResponsibility: "Lessor",
        insuranceRequirement: false,
        terms: "",
        attachments: [],
      })
    }
  }, [isEditing, lease, open])
  
  // Handle form input changes
  const handleChange = (field: string, value: any) => {
    setFormData(prev => {
      const updated = { ...prev, [field]: value }
      
      // Auto-calculate total value when monthly payment changes
      if (field === "monthlyPayment") {
        const startDate = new Date(updated.startDate)
        const endDate = new Date(updated.endDate)
        const months = (endDate.getFullYear() - startDate.getFullYear()) * 12 + 
                      (endDate.getMonth() - startDate.getMonth())
        updated.totalValue = updated.monthlyPayment * months
      }
      
      return updated
    })
  }
  
  // Handle asset selection
  const handleAssetSelect = (assetId: string) => {
    const selectedAsset = assets.find(asset => asset.id === assetId)
    if (selectedAsset) {
      setFormData(prev => ({
        ...prev,
        assetId,
        assetName: selectedAsset.name
      }))
    }
  }
  
  // Handle form submission
  const handleSubmit = async () => {
    try {
      const leasingService = AssetLeasingService.getInstance()
      
      if (isEditing && lease) {
        // Update existing lease
        const updatedLease = await leasingService.updateLease(lease.id, formData)
        if (updatedLease) {
          toast({
            title: "Lease Updated",
            description: `Lease agreement for ${formData.assetName} has been updated.`,
          })
          onOpenChange(false)
        } else {
          throw new Error("Failed to update lease")
        }
      } else {
        // Create new lease
        const newLease = await leasingService.createLease(formData)
        if (newLease) {
          toast({
            title: "Lease Created",
            description: `New lease agreement for ${formData.assetName} has been created.`,
          })
          onOpenChange(false)
        } else {
          throw new Error("Failed to create lease")
        }
      }
    } catch (error) {
      console.error("Error saving lease:", error)
      toast({
        title: "Error",
        description: "Failed to save the lease agreement. Please try again.",
        variant: "destructive",
      })
    }
  }
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle>{isEditing ? "Edit Lease Agreement" : "Create New Lease Agreement"}</DialogTitle>
        </DialogHeader>
        
        <Tabs defaultValue="basic" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="basic">Basic Information</TabsTrigger>
            <TabsTrigger value="financial">Financial Details</TabsTrigger>
            <TabsTrigger value="terms">Terms & Conditions</TabsTrigger>
          </TabsList>
          
          <TabsContent value="basic" className="space-y-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="asset">Asset</Label>
                <Select 
                  value={formData.assetId} 
                  onValueChange={handleAssetSelect}
                >
                  <SelectTrigger id="asset">
                    <SelectValue placeholder="Select an asset" />
                  </SelectTrigger>
                  <SelectContent>
                    {assets.map(asset => (
                      <SelectItem key={asset.id} value={asset.id}>
                        {asset.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="leaseType">Lease Type</Label>
                <Select 
                  value={formData.leaseType} 
                  onValueChange={(value) => handleChange("leaseType", value)}
                >
                  <SelectTrigger id="leaseType">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Operating">Operating</SelectItem>
                    <SelectItem value="Finance">Finance</SelectItem>
                    <SelectItem value="Capital">Capital</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="lesseeName">Lessee Name</Label>
                <Input 
                  id="lesseeName" 
                  value={formData.lesseeName}
                  onChange={(e) => {
                    handleChange("lesseeName", e.target.value)
                    handleChange("lesseeId", `LESSEE-${Date.now()}`)
                  }}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="maintenanceResponsibility">Maintenance Responsibility</Label>
                <Select 
                  value={formData.maintenanceResponsibility} 
                  onValueChange={(value) => handleChange("maintenanceResponsibility", value)}
                >
                  <SelectTrigger id="maintenanceResponsibility">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Lessor">Lessor</SelectItem>
                    <SelectItem value="Lessee">Lessee</SelectItem>
                    <SelectItem value="Shared">Shared</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="startDate">Start Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !formData.startDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formData.startDate ? format(new Date(formData.startDate), "PPP") : "Select date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formData.startDate ? new Date(formData.startDate) : undefined}
                      onSelect={(date) => date && handleChange("startDate", format(date, "yyyy-MM-dd"))}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="endDate">End Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !formData.endDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formData.endDate ? format(new Date(formData.endDate), "PPP") : "Select date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formData.endDate ? new Date(formData.endDate) : undefined}
                      onSelect={(date) => date && handleChange("endDate", format(date, "yyyy-MM-dd"))}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="financial" className="space-y-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="monthlyPayment">Monthly Payment</Label>
                <Input 
                  id="monthlyPayment" 
                  type="number"
                  value={formData.monthlyPayment}
                  onChange={(e) => handleChange("monthlyPayment", parseFloat(e.target.value))}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="totalValue">Total Lease Value</Label>
                <Input 
                  id="totalValue" 
                  type="number"
                  value={formData.totalValue}
                  onChange={(e) => handleChange("totalValue", parseFloat(e.target.value))}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="securityDeposit">Security Deposit</Label>
                <Input 
                  id="securityDeposit" 
                  type="number"
                  value={formData.securityDeposit}
                  onChange={(e) => handleChange("securityDeposit", parseFloat(e.target.value))}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="renewalOptions">Renewal Options (Years)</Label>
                <Input 
                  id="renewalOptions" 
                  type="number"
                  value={formData.renewalOptions}
                  onChange={(e) => handleChange("renewalOptions", parseInt(e.target.value))}
                />
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="terms" className="space-y-4 py-4">
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch 
                  id="earlyTermination"
                  checked={formData.earlyTerminationClause}
                  onCheckedChange={(checked) => handleChange("earlyTerminationClause", checked)}
                />
                <Label htmlFor="earlyTermination">Early Termination Clause</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch 
                  id="insurance"
                  checked={formData.insuranceRequirement}
                  onCheckedChange={(checked) => handleChange("insuranceRequirement", checked)}
                />
                <Label htmlFor="insurance">Insurance Requirement</Label>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="terms">Terms & Conditions</Label>
                <Textarea 
                  id="terms" 
                  rows={5}
                  value={formData.terms}
                  onChange={(e) => handleChange("terms", e.target.value)}
                  placeholder="Enter the terms and conditions of the lease agreement..."
                />
              </div>
            </div>
          </TabsContent>
        </Tabs>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>Cancel</Button>
          <Button onClick={handleSubmit}>{isEditing ? "Update" : "Create"} Lease</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}