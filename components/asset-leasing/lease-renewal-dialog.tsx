"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { AssetLeasingService } from "@/lib/modules/asset-leasing/services"
import type { LeaseAgreement } from "@/lib/modules/asset-leasing/types"
import { useToast } from "@/components/ui/use-toast"
import { CalendarIcon } from "lucide-react"
import { format, addYears } from "date-fns"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"

interface LeaseRenewalDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  lease: LeaseAgreement
}

export default function LeaseRenewalD<PERSON>og({ open, onOpenChange, lease }: LeaseRenewalDialogProps) {
  const { toast } = useToast()
  
  // Calculate default new end date (1 year after current end date)
  const currentEndDate = new Date(lease.endDate)
  const defaultNewEndDate = format(addYears(currentEndDate, 1), "yyyy-MM-dd")
  
  // Form state
  const [formData, setFormData] = useState({
    newEndDate: defaultNewEndDate,
    rateAdjustment: 5, // Default 5% increase
    newTerms: lease.terms,
  })
  
  // Calculate new monthly payment based on rate adjustment
  const newMonthlyPayment = lease.monthlyPayment * (1 + formData.rateAdjustment / 100)
  
  // Handle form input changes
  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }
  
  // Handle form submission
  const handleSubmit = async () => {
    try {
      const leasingService = AssetLeasingService.getInstance()
      
      // Create renewal proposal
      const renewal = await leasingService.proposeRenewal(
        lease.id,
        formData.newTerms,
        formData.rateAdjustment
      )
      
      if (!renewal) {
        throw new Error("Failed to create renewal proposal")
      }
      
      // Auto-approve the renewal (in a real app, this might go through an approval workflow)
      const success = await leasingService.processRenewal(renewal.id, true)
      
      if (!success) {
        throw new Error("Failed to process renewal")
      }
      
      toast({
        title: "Lease Renewed",
        description: `The lease for ${lease.assetName} has been renewed successfully.`,
      })
      
      onOpenChange(false)
    } catch (error) {
      console.error("Error renewing lease:", error)
      toast({
        title: "Error",
        description: "Failed to renew the lease. Please try again.",
        variant: "destructive",
      })
    }
  }
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Renew Lease Agreement</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label>Asset</Label>
            <div className="font-medium">{lease.assetName}</div>
          </div>
          
          <div className="space-y-2">
            <Label>Current End Date</Label>
            <div className="font-medium">{format(new Date(lease.endDate), "PPP")}</div>
          </div>
          
          <div className="space-y-2">
            <Label>Current Monthly Payment</Label>
            <div className="font-medium">${lease.monthlyPayment.toFixed(2)}</div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="newEndDate">New End Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !formData.newEndDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {formData.newEndDate ? format(new Date(formData.newEndDate), "PPP") : "Select date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={formData.newEndDate ? new Date(formData.newEndDate) : undefined}
                  onSelect={(date) => date && handleChange("newEndDate", format(date, "yyyy-MM-dd"))}
                  initialFocus
                  disabled={(date) => date < currentEndDate}
                />
              </PopoverContent>
            </Popover>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="rateAdjustment">Rate Adjustment (%)</Label>
            <Input 
              id="rateAdjustment" 
              type="number"
              value={formData.rateAdjustment}
              onChange={(e) => handleChange("rateAdjustment", parseFloat(e.target.value))}
            />
          </div>
          
          <div className="space-y-2">
            <Label>New Monthly Payment</Label>
            <div className="font-medium text-green-600">${newMonthlyPayment.toFixed(2)}</div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="newTerms">Updated Terms</Label>
            <Textarea 
              id="newTerms" 
              rows={3}
              value={formData.newTerms}
              onChange={(e) => handleChange("newTerms", e.target.value)}
            />
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>Cancel</Button>
          <Button onClick={handleSubmit}>Renew Lease</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}