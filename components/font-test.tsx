import React from 'react'

export function FontTest() {
  return (
    <div className="p-8 space-y-6">
      <h1 className="text-3xl font-bold mb-6">Font Loading Test</h1>
      
      <div className="space-y-4">
        <div className="p-4 border rounded-lg">
          <h2 className="text-xl font-semibold mb-2">Outfit (Sans Serif)</h2>
          <p className="font-sans text-lg">
            This text uses the Outfit font family. It should display with the locally loaded Outfit variable font.
          </p>
          <p className="font-sans text-sm text-muted-foreground">
            Font weights: 100, 200, 300, 400, 500, 600, 700, 800, 900
          </p>
        </div>

        <div className="p-4 border rounded-lg">
          <h2 className="text-xl font-semibold mb-2">Montserrat (Serif)</h2>
          <p className="font-serif text-lg">
            This text uses the Montserrat font family. It should display with the locally loaded Montserrat variable font.
          </p>
          <p className="font-serif italic text-sm text-muted-foreground">
            This is italic text using Montserrat italic variant.
          </p>
        </div>

        <div className="p-4 border rounded-lg">
          <h2 className="text-xl font-semibold mb-2">JetBrains Mono (Monospace)</h2>
          <p className="font-mono text-lg">
            This text uses the JetBrains Mono font family. Perfect for code display.
          </p>
          <code className="font-mono text-sm bg-muted p-2 rounded block mt-2">
            const example = "This is code with JetBrains Mono";
          </code>
        </div>

        <div className="p-4 border rounded-lg">
          <h2 className="text-xl font-semibold mb-2">Manrope (Alternative Sans)</h2>
          <p className="font-manrope text-lg">
            This text uses the Manrope font family as an alternative sans-serif option.
          </p>
          <p className="font-manrope text-sm text-muted-foreground">
            Manrope provides a modern, clean alternative to Outfit.
          </p>
        </div>

        <div className="p-4 border rounded-lg">
          <h2 className="text-xl font-semibold mb-2">Font Weight Examples</h2>
          <div className="space-y-2">
            <p className="font-sans font-thin">Thin (100) - Outfit</p>
            <p className="font-sans font-light">Light (300) - Outfit</p>
            <p className="font-sans font-normal">Normal (400) - Outfit</p>
            <p className="font-sans font-medium">Medium (500) - Outfit</p>
            <p className="font-sans font-semibold">Semibold (600) - Outfit</p>
            <p className="font-sans font-bold">Bold (700) - Outfit</p>
            <p className="font-sans font-extrabold">Extra Bold (800) - Outfit</p>
            <p className="font-sans font-black">Black (900) - Outfit</p>
          </div>
        </div>
      </div>
    </div>
  )
}