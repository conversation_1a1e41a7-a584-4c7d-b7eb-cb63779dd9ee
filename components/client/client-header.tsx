"use client";

import React from "react";
import { Bell, Search, MessageSquare, HelpCircle } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Separator } from "@/components/ui/separator";
import { useRouter } from "next/navigation";

export function ClientHeader() {
  const router = useRouter();

  return (
    <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
      <SidebarTrigger className="-ml-1" />
      <Separator orientation="vertical" className="mr-2 h-4" />
      
      {/* Search */}
      <div className="flex-1 max-w-md">
        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search assets, requests, or help..."
            className="pl-8"
          />
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center gap-2">
        {/* Quick Help */}
        <Button
          variant="ghost"
          size="icon"
          onClick={() => router.push("/client/help")}
          title="Help Center"
        >
          <HelpCircle className="h-4 w-4" />
        </Button>

        {/* Live Chat */}
        <Button
          variant="ghost"
          size="icon"
          onClick={() => router.push("/client/support/chat")}
          title="Live Chat"
        >
          <MessageSquare className="h-4 w-4" />
        </Button>

        {/* Notifications */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="relative">
              <Bell className="h-4 w-4" />
              <Badge 
                variant="destructive" 
                className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs"
              >
                3
              </Badge>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-80">
            <DropdownMenuLabel>Notifications</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="flex flex-col items-start p-3">
              <div className="font-medium">Asset Request Approved</div>
              <div className="text-sm text-muted-foreground">
                Your laptop request has been approved and is being processed.
              </div>
              <div className="text-xs text-muted-foreground mt-1">2 hours ago</div>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="flex flex-col items-start p-3">
              <div className="font-medium">Payment Due</div>
              <div className="text-sm text-muted-foreground">
                Invoice #INV-2024-001 is due in 3 days.
              </div>
              <div className="text-xs text-muted-foreground mt-1">1 day ago</div>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="flex flex-col items-start p-3">
              <div className="font-medium">Support Ticket Updated</div>
              <div className="text-sm text-muted-foreground">
                Your support ticket #12345 has been updated.
              </div>
              <div className="text-xs text-muted-foreground mt-1">2 days ago</div>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem 
              className="text-center w-full"
              onClick={() => router.push("/client/notifications")}
            >
              View All Notifications
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
}