"use client";

import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  LayoutDashboard,
  Package,
  ShoppingCart,
  CreditCard,
  HelpCircle,
  MessageSquare,
  FileText,
  Settings,
  User,
  Bell,
  Search,
  Calendar,
  Truck,
  Receipt,
  Phone,
  Mail,
  ChevronUp,
} from "lucide-react";

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarInput,
  SidebarMenu,
  SidebarMenuBadge,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useSession } from "next-auth/react";

// Helper function to determine if a route is active
const isRouteActive = (pathname: string, itemUrl: string): boolean => {
  if (itemUrl === "/client" && pathname === "/client") {
    return true;
  }
  if (itemUrl !== "/client" && pathname.startsWith(itemUrl)) {
    return true;
  }
  return false;
};

// Menu items data
const menuData = {
  navMain: [
    {
      title: "Dashboard",
      items: [
        {
          title: "Overview",
          url: "/client",
          icon: LayoutDashboard,
        },
        {
          title: "My Assets",
          url: "/client/assets",
          icon: Package,
          badge: "12",
        },
        {
          title: "Notifications",
          url: "/client/notifications",
          icon: Bell,
          badge: "3",
        },
      ],
    },
    {
      title: "Asset Requests",
      items: [
        {
          title: "New Request",
          url: "/client/requests/new",
          icon: ShoppingCart,
        },
        {
          title: "My Requests",
          url: "/client/requests",
          icon: FileText,
          badge: "5",
        },
        {
          title: "Request History",
          url: "/client/requests/history",
          icon: Calendar,
        },
        {
          title: "Track Delivery",
          url: "/client/requests/tracking",
          icon: Truck,
        },
      ],
    },
    {
      title: "Billing & Payments",
      items: [
        {
          title: "Invoices",
          url: "/client/billing/invoices",
          icon: Receipt,
        },
        {
          title: "Payment Methods",
          url: "/client/billing/payment-methods",
          icon: CreditCard,
        },
        {
          title: "Billing History",
          url: "/client/billing/history",
          icon: FileText,
        },
        {
          title: "Subscription",
          url: "/client/billing/subscription",
          icon: Package,
        },
      ],
    },
    {
      title: "Support & Help",
      items: [
        {
          title: "Help Center",
          url: "/client/help",
          icon: HelpCircle,
        },
        {
          title: "Support Tickets",
          url: "/client/support/tickets",
          icon: MessageSquare,
          badge: "2",
        },
        {
          title: "Live Chat",
          url: "/client/support/chat",
          icon: MessageSquare,
        },
        {
          title: "Contact Support",
          url: "/client/support/contact",
          icon: Phone,
        },
        {
          title: "Knowledge Base",
          url: "/client/help/knowledge-base",
          icon: FileText,
        },
      ],
    },
  ],
};

export function ClientSidebar() {
  const pathname = usePathname();
  const { data: session } = useSession();

  return (
    <Sidebar variant="inset">
      <SidebarHeader>
        <div className="flex items-center gap-2 px-4 py-2">
          <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
            <Package className="size-4" />
          </div>
          <div className="grid flex-1 text-left text-sm leading-tight">
            <span className="truncate font-semibold">WizeAssets</span>
            <span className="truncate text-xs text-muted-foreground">Client Portal</span>
          </div>
        </div>
        <div className="px-4">
          <SidebarInput placeholder="Search..." />
        </div>
      </SidebarHeader>

      <SidebarContent>
        {menuData.navMain.map((group) => (
          <SidebarGroup key={group.title}>
            <SidebarGroupLabel>{group.title}</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                {group.items.map((item) => {
                  const Icon = item.icon;
                  const isActive = isRouteActive(pathname, item.url);
                  
                  return (
                    <SidebarMenuItem key={item.title}>
                      <SidebarMenuButton asChild isActive={isActive}>
                        <Link href={item.url}>
                          <Icon className="size-4" />
                          <span>{item.title}</span>
                          {item.badge && (
                            <SidebarMenuBadge>{item.badge}</SidebarMenuBadge>
                          )}
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  );
                })}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        ))}
      </SidebarContent>

      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton
                  size="lg"
                  className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                >
                  <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
                    <User className="size-4" />
                  </div>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-semibold">
                      {session?.user?.name || "Client User"}
                    </span>
                    <span className="truncate text-xs text-muted-foreground">
                      {session?.user?.email}
                    </span>
                  </div>
                  <ChevronUp className="ml-auto size-4" />
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
                side="bottom"
                align="end"
                sideOffset={4}
              >
                <DropdownMenuItem asChild>
                  <Link href="/client/profile">
                    <User className="mr-2 size-4" />
                    Profile Settings
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/client/settings">
                    <Settings className="mr-2 size-4" />
                    Account Settings
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/client/support/contact">
                    <Mail className="mr-2 size-4" />
                    Contact Support
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/logout">
                    <span className="mr-2 size-4">🚪</span>
                    Sign Out
                  </Link>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
}