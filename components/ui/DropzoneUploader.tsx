import { useState, useCallback, useEffect } from "react";
import { useDropzone } from "react-dropzone";
import { UploadCloud, AlertCircle, X } from "lucide-react";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { uploadFile, deleteFile, FileUpload } from "@/lib/appwrite";

interface DropzoneUploaderProps {
  onUpload: (files: FileUpload[]) => void;
  acceptedFileTypes?: Record<string, string[]>;
  maxFiles?: number;
  maxSize?: number; // in bytes
  disabled?: boolean;
}

interface LocalFile {
  file: File;
  preview: string;
  progress: number;
  uploadedFile: FileUpload | null;
}

export function DropzoneUploader({
  onUpload,
  acceptedFileTypes,
  maxFiles = 1,
  maxSize = 10 * 1024 * 1024, // 10MB default from Appwrite config
  disabled = false
}: DropzoneUploaderProps) {
  const [localFiles, setLocalFiles] = useState<LocalFile[]>([]);
  const [error, setError] = useState<string | null>(null);

  const handleUpload = useCallback(async (filesToUpload: File[]) => {
    const newLocalFiles: LocalFile[] = filesToUpload.map(file => ({
      file,
      preview: URL.createObjectURL(file),
      progress: 0,
      uploadedFile: null
    }));

    setLocalFiles(prev => [...prev, ...newLocalFiles]);

    const uploadPromises = newLocalFiles.map(async (localFile) => {
      try {
        const uploadedFile = await uploadFile(localFile.file, (progress) => {
          setLocalFiles(prev => prev.map(lf => 
            lf.file.name === localFile.file.name ? { ...lf, progress: progress.percentage } : lf
          ));
        });
        setLocalFiles(prev => prev.map(lf => 
          lf.file.name === localFile.file.name ? { ...lf, uploadedFile, progress: 100 } : lf
        ));
        return uploadedFile;
      } catch (uploadError) {
        setError(`Failed to upload ${localFile.file.name}.`);
        // Remove failed upload from local files
        setLocalFiles(prev => prev.filter(lf => lf.file.name !== localFile.file.name));
        return null;
      }
    });

    const uploadedFiles = (await Promise.all(uploadPromises)).filter(Boolean) as FileUpload[];
    if (uploadedFiles.length > 0) {
      onUpload(uploadedFiles);
    }

  }, [onUpload]);

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    setError(null);

    if (rejectedFiles.length > 0) {
      const rejectionReasons = rejectedFiles.map(rejection => {
        if (rejection.errors[0].code === 'file-too-large') {
          return `${rejection.file.name} is too large (max ${formatBytes(maxSize)})`;
        }
        if (rejection.errors[0].code === 'file-invalid-type') {
          return `${rejection.file.name} has an invalid file type`;
        }
        return `${rejection.file.name}: ${rejection.errors[0].message}`;
      });
      setError(rejectionReasons.join(', '));
      return;
    }

    if (localFiles.length + acceptedFiles.length > maxFiles) {
      setError(`Too many files. Maximum ${maxFiles} ${maxFiles === 1 ? 'file' : 'files'} allowed.`);
      return;
    }

    if (acceptedFiles.length > 0) {
      handleUpload(acceptedFiles);
    }
  }, [localFiles.length, maxFiles, maxSize, handleUpload]);

  const { 
    getRootProps, 
    getInputProps, 
    isDragActive,
    isDragAccept,
    isDragReject
  } = useDropzone({ 
    onDrop,
    accept: acceptedFileTypes,
    maxSize,
    disabled: disabled || localFiles.length >= maxFiles
  });

  const removeFile = async (fileName: string) => {
    const fileToRemove = localFiles.find(lf => lf.file.name === fileName);
    if (fileToRemove) {
      URL.revokeObjectURL(fileToRemove.preview);
      setLocalFiles(prev => prev.filter(lf => lf.file.name !== fileName));
      if (fileToRemove.uploadedFile) {
        try {
          await deleteFile(fileToRemove.uploadedFile.id);
        } catch (deleteError) {
          setError(`Failed to delete ${fileName} from storage.`);
        }
      }
    }
  };

  useEffect(() => {
    return () => localFiles.forEach(lf => URL.revokeObjectURL(lf.preview));
  }, [localFiles]);

  function formatBytes(bytes: number, decimals = 2) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  }

  const formatAcceptedTypes = () => {
    if (!acceptedFileTypes) return 'All files';
    const types = Object.values(acceptedFileTypes).flat().map(t => t.split('/')[1]?.toUpperCase() || t).join(', ');
    return types;
  };

  return (
    <div className="space-y-4">
      {localFiles.length < maxFiles && (
        <div
          {...getRootProps()}
          className={`
            border-2 border-dashed rounded-md p-6 text-center cursor-pointer transition-colors
            ${isDragActive ? 'bg-primary/5 border-primary/50' : 'border-muted-foreground/25 hover:border-primary/25 hover:bg-primary/5'}
            ${isDragAccept ? 'border-green-500/50 bg-green-500/5' : ''}
            ${isDragReject ? 'border-red-500/50 bg-red-500/5' : ''}
            ${(disabled || localFiles.length >= maxFiles) ? 'opacity-50 cursor-not-allowed' : ''}
          `}
        >
          <input {...getInputProps()} />
          <div className="flex flex-col items-center justify-center text-muted-foreground">
            <UploadCloud className="w-10 h-10 mb-2" />
            <p className="text-sm font-medium">
              {isDragActive
                ? isDragAccept
                  ? 'Drop the files here'
                  : 'This file type is not accepted'
                : `Drag & drop ${maxFiles > 1 ? 'files' : 'a file'} here, or click to select`}
            </p>
            <p className="text-xs mt-1">
              {formatAcceptedTypes()} ({maxFiles > 1 ? `Max ${maxFiles - localFiles.length} files` : 'Single file'}, max {formatBytes(maxSize)})
            </p>
          </div>
        </div>
      )}

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {localFiles.length > 0 && (
        <div className="space-y-2">
          {localFiles.map(localFile => (
            <div key={localFile.file.name} className="border rounded-md p-2 flex items-center space-x-2">
              <img src={localFile.preview} alt={localFile.file.name} className="h-10 w-10 object-cover rounded-md" />
              <div className="flex-1">
                <p className="text-sm font-medium truncate">{localFile.file.name}</p>
                <p className="text-xs text-muted-foreground">{formatBytes(localFile.file.size)}</p>
                <Progress value={localFile.progress} className="h-2 mt-1" />
              </div>
              <button onClick={() => removeFile(localFile.file.name)} className="text-muted-foreground hover:text-destructive">
                <X className="h-4 w-4" />
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}