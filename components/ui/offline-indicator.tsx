"use client"

import type React from "react"

import { useOffline } from "@/lib/hooks/use-offline"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { WifiOff } from "lucide-react"

export const OfflineIndicator: React.FC = () => {
  const isOffline = useOffline()

  if (!isOffline) return null

  return (
    <Alert className="border-chart-4/20 bg-chart-4/10">
      <WifiOff className="h-4 w-4" />
      <AlertDescription>You're currently offline. Some features may not be available.</AlertDescription>
    </Alert>
  )
}
