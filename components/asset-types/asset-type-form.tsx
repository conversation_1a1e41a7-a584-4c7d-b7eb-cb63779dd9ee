"use client"

import React, { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Plus, X, <PERSON>, <PERSON>ert<PERSON>ir<PERSON> } from "lucide-react"
import { useAssetCategories, useAssetTypes } from "@/hooks/use-asset-types"
import { CustomFieldEditor } from "./custom-field-editor"
import { LifecycleStageEditor } from "./lifecycle-stage-editor"
import { MaintenanceScheduleEditor } from "./maintenance-schedule-editor"
import { DepreciationSettingsEditor } from "./depreciation-settings-editor"
import type { AssetType, AssetCategory, CustomField, LifecycleStage, MaintenanceSchedule, DepreciationSettings } from "@/lib/modules/asset-types/types"

const assetTypeSchema = z.object({
  name: z.string().min(1, "Name is required"),
  code: z.string().min(1, "Code is required").max(20, "Code must be 20 characters or less"),
  description: z.string().min(1, "Description is required"),
  categoryId: z.string().min(1, "Category is required"),
  subcategory: z.string().optional(),
  icon: z.string().min(1, "Icon is required"),
  color: z.string().min(1, "Color is required"),
  isActive: z.boolean().default(true),
  tags: z.array(z.string()).default([]),
})

type AssetTypeFormData = z.infer<typeof assetTypeSchema>

interface AssetTypeFormProps {
  assetType?: AssetType
  onSave: (assetType: AssetType) => void
  onCancel?: () => void
}

const iconOptions = [
  { value: "laptop", label: "💻 Laptop" },
  { value: "desktop", label: "🖥️ Desktop" },
  { value: "server", label: "🖥️ Server" },
  { value: "phone", label: "📱 Phone" },
  { value: "car", label: "🚗 Vehicle" },
  { value: "truck", label: "🚚 Truck" },
  { value: "tools", label: "🔧 Tools" },
  { value: "machine", label: "⚙️ Machine" },
  { value: "chair", label: "🪑 Chair" },
  { value: "desk", label: "🗃️ Desk" },
  { value: "building", label: "🏢 Building" },
  { value: "other", label: "📦 Other" },
]

const colorOptions = [
  "#3B82F6", "#EF4444", "#10B981", "#F59E0B", "#8B5CF6",
  "#06B6D4", "#84CC16", "#F97316", "#EC4899", "#6B7280"
]

export function AssetTypeForm({ assetType, onSave, onCancel }: AssetTypeFormProps) {
  const [activeTab, setActiveTab] = useState("basic")
  const [tags, setTags] = useState<string[]>(assetType?.tags || [])
  const [customFields, setCustomFields] = useState<CustomField[]>(assetType?.customFields || [])
  const [lifecycleStages, setLifecycleStages] = useState<LifecycleStage[]>(assetType?.lifecycleStages || [])
  const [maintenanceSchedules, setMaintenanceSchedules] = useState<MaintenanceSchedule[]>(assetType?.maintenanceSchedules || [])
  const [depreciationSettings, setDepreciationSettings] = useState<DepreciationSettings>(
    assetType?.depreciationSettings || {
      method: "straight_line",
      usefulLife: 5,
      usefulLifeUnit: "years",
      salvageValue: 0,
      salvageValueType: "percentage",
      startDate: new Date().toISOString(),
      isActive: true,
    }
  )
  const [currentTag, setCurrentTag] = useState("")

  const { categories } = useAssetCategories()
  const { createAssetType, updateAssetType } = useAssetTypes()

  const form = useForm<AssetTypeFormData>({
    resolver: zodResolver(assetTypeSchema),
    defaultValues: {
      name: assetType?.name || "",
      code: assetType?.code || "",
      description: assetType?.description || "",
      categoryId: assetType?.category?.id || "",
      subcategory: assetType?.subcategory || "",
      icon: assetType?.icon || "other",
      color: assetType?.color || "#3B82F6",
      isActive: assetType?.isActive ?? true,
      tags: assetType?.tags || [],
    },
  })

  const onSubmit = async (data: AssetTypeFormData) => {
    try {
      const category = categories.find(cat => cat.id === data.categoryId)
      if (!category) {
        throw new Error("Selected category not found")
      }

      const assetTypeData = {
        ...data,
        category,
        tags,
        customFields,
        lifecycleStages,
        maintenanceSchedules,
        depreciationSettings,
        createdBy: "current-user", // Replace with actual user context
      }

      let result: AssetType
      if (assetType) {
        result = await updateAssetType(assetType.id, assetTypeData) as AssetType
      } else {
        result = await createAssetType(assetTypeData)
      }

      onSave(result)
    } catch (error) {
      console.error("Error saving asset type:", error)
      // Handle error (show toast, etc.)
    }
  }

  const addTag = () => {
    if (currentTag.trim() && !tags.includes(currentTag.trim())) {
      setTags([...tags, currentTag.trim()])
      setCurrentTag("")
    }
  }

  const removeTag = (tag: string) => {
    setTags(tags.filter(t => t !== tag))
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault()
      addTag()
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="basic">Basic Info</TabsTrigger>
            <TabsTrigger value="fields">Custom Fields</TabsTrigger>
            <TabsTrigger value="lifecycle">Lifecycle</TabsTrigger>
            <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
            <TabsTrigger value="depreciation">Depreciation</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
                <CardDescription>Define the basic properties of your asset type</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Laptop Computer" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="code"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Code</FormLabel>
                        <FormControl>
                          <Input placeholder="LAPTOP" {...field} />
                        </FormControl>
                        <FormDescription>Unique identifier for this asset type</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea placeholder="Portable computing devices for employees" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="categoryId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Category</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a category" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {categories.map((category) => (
                              <SelectItem key={category.id} value={category.id}>
                                {category.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="subcategory"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Subcategory</FormLabel>
                        <FormControl>
                          <Input placeholder="Optional subcategory" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="icon"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Icon</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select an icon" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {iconOptions.map((icon) => (
                              <SelectItem key={icon.value} value={icon.value}>
                                {icon.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="color"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Color</FormLabel>
                        <div className="flex gap-2 flex-wrap">
                          {colorOptions.map((color) => (
                            <button
                              key={color}
                              type="button"
                              className={`w-8 h-8 rounded-md border-2 ${
                                field.value === color ? "border-gray-900" : "border-gray-300"
                              }`}
                              style={{ backgroundColor: color }}
                              onClick={() => field.onChange(color)}
                            />
                          ))}
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="isActive"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Active</FormLabel>
                        <FormDescription>Enable this asset type for use</FormDescription>
                      </div>
                      <FormControl>
                        <Switch checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <div>
                  <FormLabel>Tags</FormLabel>
                  <div className="flex gap-2 mt-2 mb-2 flex-wrap">
                    {tags.map((tag) => (
                      <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                        {tag}
                        <X className="h-3 w-3 cursor-pointer" onClick={() => removeTag(tag)} />
                      </Badge>
                    ))}
                  </div>
                  <div className="flex gap-2">
                    <Input
                      placeholder="Add a tag"
                      value={currentTag}
                      onChange={(e) => setCurrentTag(e.target.value)}
                      onKeyPress={handleKeyPress}
                    />
                    <Button type="button" onClick={addTag} size="sm">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="fields" className="space-y-6">
            <CustomFieldEditor
              fields={customFields}
              onChange={setCustomFields}
            />
          </TabsContent>

          <TabsContent value="lifecycle" className="space-y-6">
            <LifecycleStageEditor
              stages={lifecycleStages}
              onChange={setLifecycleStages}
            />
          </TabsContent>

          <TabsContent value="maintenance" className="space-y-6">
            <MaintenanceScheduleEditor
              schedules={maintenanceSchedules}
              onChange={setMaintenanceSchedules}
            />
          </TabsContent>

          <TabsContent value="depreciation" className="space-y-6">
            <DepreciationSettingsEditor
              settings={depreciationSettings}
              onChange={setDepreciationSettings}
            />
          </TabsContent>
        </Tabs>

        <div className="flex justify-end space-x-2">
          {onCancel && (
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
          )}
          <Button type="submit">
            <Save className="w-4 h-4 mr-2" />
            {assetType ? "Update" : "Create"} Asset Type
          </Button>
        </div>
      </form>
    </Form>
  )
}