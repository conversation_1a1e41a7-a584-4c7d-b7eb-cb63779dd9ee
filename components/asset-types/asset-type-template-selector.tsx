"use client";

import React, { useState, useEffect } from "react";
import { AssetTypeTemplate } from "@/lib/templates/asset-type-templates";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Search,
  Filter,
  Laptop,
  Table,
  Car,
  Package,
  Settings,
  CheckCircle,
  AlertCircle,
  Info,
  Plus,
  Eye,
  Download
} from "lucide-react";

interface AssetTypeTemplateSelectorProps {
  onTemplateSelect: (template: AssetTypeTemplate, customizations?: any) => void;
  onCancel?: () => void;
  isOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
}

interface TemplateCustomization {
  name: string;
  code: string;
  description: string;
  categoryId?: string;
}

export function AssetTypeTemplateSelector({
  onTemplateSelect,
  onCancel,
  isOpen,
  onOpenChange,
}: AssetTypeTemplateSelectorProps) {
  const [templates, setTemplates] = useState<AssetTypeTemplate[]>([]);
  const [filteredTemplates, setFilteredTemplates] = useState<AssetTypeTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<AssetTypeTemplate | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [isLoading, setIsLoading] = useState(true);
  const [showCustomization, setShowCustomization] = useState(false);
  const [customizations, setCustomizations] = useState<TemplateCustomization>({
    name: "",
    code: "",
    description: "",
  });
  const [categories, setCategories] = useState<string[]>([]);

  useEffect(() => {
    loadTemplates();
  }, []);

  useEffect(() => {
    filterTemplates();
  }, [templates, searchTerm, selectedCategory]);

  const loadTemplates = async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/asset-type-templates");
      if (response.ok) {
        const data = await response.json();
        setTemplates(data.templates);
        
        // Extract unique categories
        const uniqueCategories = [...new Set(data.templates.map((t: AssetTypeTemplate) => t.category))];
        setCategories(uniqueCategories);
      }
    } catch (error) {
      console.error("Error loading templates:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const filterTemplates = () => {
    let filtered = templates;

    // Filter by search term
    if (searchTerm) {
      const search = searchTerm.toLowerCase();
      filtered = filtered.filter(template =>
        template.name.toLowerCase().includes(search) ||
        template.description.toLowerCase().includes(search) ||
        template.tags.some(tag => tag.toLowerCase().includes(search))
      );
    }

    // Filter by category
    if (selectedCategory !== "all") {
      filtered = filtered.filter(template => template.category === selectedCategory);
    }

    setFilteredTemplates(filtered);
  };

  const getTemplateIcon = (template: AssetTypeTemplate) => {
    switch (template.icon) {
      case "Laptop":
        return <Laptop className="h-6 w-6" />;
      case "Table":
        return <Table className="h-6 w-6" />;
      case "Car":
        return <Car className="h-6 w-6" />;
      default:
        return <Package className="h-6 w-6" />;
    }
  };

  const handleTemplateSelect = (template: AssetTypeTemplate) => {
    setSelectedTemplate(template);
    setCustomizations({
      name: template.assetType.name || "",
      code: template.assetType.code || "",
      description: template.assetType.description || "",
    });
    setShowCustomization(true);
  };

  const handleCreateAssetType = () => {
    if (selectedTemplate) {
      onTemplateSelect(selectedTemplate, customizations);
      setShowCustomization(false);
      setSelectedTemplate(null);
    }
  };

  const handleCancel = () => {
    setShowCustomization(false);
    setSelectedTemplate(null);
    if (onCancel) {
      onCancel();
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <span className="ml-2">Loading templates...</span>
      </div>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle>
            {showCustomization ? "Customize Asset Type" : "Select Asset Type Template"}
          </DialogTitle>
        </DialogHeader>

        {!showCustomization ? (
          <div className="space-y-4">
            {/* Search and Filter */}
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search templates..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Filter by category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  {categories.map(category => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Templates Grid */}
            <ScrollArea className="h-[60vh]">
              {filteredTemplates.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {filteredTemplates.map((template) => (
                    <Card
                      key={template.id}
                      className="cursor-pointer transition-all hover:shadow-md hover:border-primary/50"
                      onClick={() => handleTemplateSelect(template)}
                    >
                      <CardHeader className="pb-3">
                        <div className="flex items-center gap-3">
                          <div
                            className="p-2 rounded-lg"
                            style={{ backgroundColor: `${template.color}20`, color: template.color }}
                          >
                            {getTemplateIcon(template)}
                          </div>
                          <div className="flex-1">
                            <CardTitle className="text-sm">{template.name}</CardTitle>
                            <Badge variant="outline" className="text-xs">
                              {template.category}
                            </Badge>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <p className="text-sm text-muted-foreground mb-3">
                          {template.description}
                        </p>
                        
                        <div className="space-y-2">
                          <div className="flex items-center justify-between text-xs">
                            <span className="text-muted-foreground">Custom Fields:</span>
                            <Badge variant="secondary">{template.customFields.length}</Badge>
                          </div>
                          <div className="flex items-center justify-between text-xs">
                            <span className="text-muted-foreground">Lifecycle Stages:</span>
                            <Badge variant="secondary">{template.lifecycleStages.length}</Badge>
                          </div>
                          <div className="flex items-center justify-between text-xs">
                            <span className="text-muted-foreground">Maintenance Schedules:</span>
                            <Badge variant="secondary">{template.maintenanceSchedules.length}</Badge>
                          </div>
                        </div>

                        <div className="flex flex-wrap gap-1 mt-3">
                          {template.tags.slice(0, 3).map(tag => (
                            <Badge key={tag} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                          {template.tags.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{template.tags.length - 3}
                            </Badge>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="font-medium mb-2">No Templates Found</h3>
                  <p className="text-sm text-muted-foreground">
                    {searchTerm || selectedCategory !== "all" 
                      ? "Try adjusting your search or filter criteria"
                      : "No asset type templates are available"
                    }
                  </p>
                </div>
              )}
            </ScrollArea>

            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={handleCancel}>
                Cancel
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Template Preview */}
            {selectedTemplate && (
              <Card>
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <div
                      className="p-2 rounded-lg"
                      style={{ backgroundColor: `${selectedTemplate.color}20`, color: selectedTemplate.color }}
                    >
                      {getTemplateIcon(selectedTemplate)}
                    </div>
                    <div>
                      <CardTitle className="text-lg">{selectedTemplate.name}</CardTitle>
                      <p className="text-sm text-muted-foreground">{selectedTemplate.description}</p>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Category:</span>
                      <p className="text-muted-foreground">{selectedTemplate.category}</p>
                    </div>
                    <div>
                      <span className="font-medium">Custom Fields:</span>
                      <p className="text-muted-foreground">{selectedTemplate.customFields.length}</p>
                    </div>
                    <div>
                      <span className="font-medium">Lifecycle Stages:</span>
                      <p className="text-muted-foreground">{selectedTemplate.lifecycleStages.length}</p>
                    </div>
                    <div>
                      <span className="font-medium">Maintenance Schedules:</span>
                      <p className="text-muted-foreground">{selectedTemplate.maintenanceSchedules.length}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Customization Form */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Customize Asset Type</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name">Asset Type Name</Label>
                    <Input
                      id="name"
                      value={customizations.name}
                      onChange={(e) => setCustomizations(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="Enter asset type name"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="code">Asset Type Code</Label>
                    <Input
                      id="code"
                      value={customizations.code}
                      onChange={(e) => setCustomizations(prev => ({ ...prev, code: e.target.value.toUpperCase() }))}
                      placeholder="Enter asset type code"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={customizations.description}
                    onChange={(e) => setCustomizations(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Enter asset type description"
                    rows={3}
                  />
                </div>

                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    The template will be applied with all its custom fields, lifecycle stages, 
                    maintenance schedules, and depreciation settings. You can modify these after creation.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>

            {/* Template Details Preview */}
            {selectedTemplate && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Template Details</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Custom Fields */}
                    <div>
                      <h4 className="font-medium mb-2">Custom Fields ({selectedTemplate.customFields.length})</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        {selectedTemplate.customFields.slice(0, 6).map((field, index) => (
                          <div key={index} className="flex items-center gap-2 text-sm">
                            <Badge variant="outline" className="text-xs">{field.type}</Badge>
                            <span>{field.label}</span>
                            {field.isRequired && <span className="text-red-500">*</span>}
                          </div>
                        ))}
                        {selectedTemplate.customFields.length > 6 && (
                          <div className="text-sm text-muted-foreground">
                            +{selectedTemplate.customFields.length - 6} more fields
                          </div>
                        )}
                      </div>
                    </div>

                    <Separator />

                    {/* Lifecycle Stages */}
                    <div>
                      <h4 className="font-medium mb-2">Lifecycle Stages ({selectedTemplate.lifecycleStages.length})</h4>
                      <div className="flex flex-wrap gap-2">
                        {selectedTemplate.lifecycleStages.map((stage, index) => (
                          <Badge
                            key={index}
                            variant="outline"
                            className="text-xs"
                            style={{ borderColor: stage.color, color: stage.color }}
                          >
                            {stage.name}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <Separator />

                    {/* Maintenance Schedules */}
                    <div>
                      <h4 className="font-medium mb-2">Maintenance Schedules ({selectedTemplate.maintenanceSchedules.length})</h4>
                      <div className="space-y-2">
                        {selectedTemplate.maintenanceSchedules.map((schedule, index) => (
                          <div key={index} className="flex items-center justify-between text-sm">
                            <span>{schedule.name}</span>
                            <div className="flex items-center gap-2">
                              <Badge variant="outline" className="text-xs">{schedule.type}</Badge>
                              <Badge variant="outline" className="text-xs">{schedule.priority}</Badge>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowCustomization(false)}>
                Back
              </Button>
              <Button 
                onClick={handleCreateAssetType}
                disabled={!customizations.name || !customizations.code}
              >
                <Plus className="h-4 w-4 mr-2" />
                Create Asset Type
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}