"use client"

import React, { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Plus, Edit, Trash2, <PERSON><PERSON><PERSON>, Cal<PERSON><PERSON>, T<PERSON>dingDown, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react"
import type { 
  DepreciationSettings, 
  DepreciationMethod, 
  DepreciationRate, 
  AcceleratedDepreciation, 
  ImpairmentSettings, 
  ImpairmentTrigger 
} from "@/lib/modules/asset-types/types"

interface DepreciationSettingsEditorProps {
  settings: DepreciationSettings
  onChange: (settings: DepreciationSettings) => void
}

const depreciationMethodOptions: { value: DepreciationMethod; label: string; description: string }[] = [
  { value: "straight_line", label: "Straight Line", description: "Equal depreciation over useful life" },
  { value: "declining_balance", label: "Declining Balance", description: "Higher depreciation in early years" },
  { value: "double_declining_balance", label: "Double Declining Balance", description: "Accelerated depreciation method" },
  { value: "sum_of_years_digits", label: "Sum of Years Digits", description: "Weighted depreciation method" },
  { value: "units_of_production", label: "Units of Production", description: "Based on actual usage" },
  { value: "custom", label: "Custom", description: "Define custom depreciation rates" },
]

const usefulLifeUnitOptions = [
  { value: "years", label: "Years" },
  { value: "months", label: "Months" },
  { value: "hours", label: "Hours" },
  { value: "cycles", label: "Cycles" },
]

const salvageValueTypeOptions = [
  { value: "fixed", label: "Fixed Amount" },
  { value: "percentage", label: "Percentage of Cost" },
]

const acceleratedMethodOptions = [
  { value: "bonus", label: "Bonus Depreciation" },
  { value: "section179", label: "Section 179" },
  { value: "custom", label: "Custom" },
]

const impairmentTestFrequencyOptions = [
  { value: "annual", label: "Annual" },
  { value: "quarterly", label: "Quarterly" },
  { value: "monthly", label: "Monthly" },
  { value: "event_based", label: "Event Based" },
]

const impairmentActionOptions = [
  { value: "flag", label: "Flag for Review" },
  { value: "auto_adjust", label: "Auto Adjust Value" },
  { value: "notify", label: "Send Notification" },
]

export function DepreciationSettingsEditor({ settings, onChange }: DepreciationSettingsEditorProps) {
  const [activeTab, setActiveTab] = useState("basic")
  const [isCustomRateDialogOpen, setIsCustomRateDialogOpen] = useState(false)
  const [isImpairmentTriggerDialogOpen, setIsImpairmentTriggerDialogOpen] = useState(false)
  const [editingRateIndex, setEditingRateIndex] = useState<number | null>(null)
  const [editingTriggerIndex, setEditingTriggerIndex] = useState<number | null>(null)
  
  const [customRateForm, setCustomRateForm] = useState<DepreciationRate>({
    year: 1,
    rate: 0,
    amount: 0,
  })
  
  const [impairmentTriggerForm, setImpairmentTriggerForm] = useState<ImpairmentTrigger>({
    condition: "",
    threshold: 0,
    action: "flag",
  })

  const updateSettings = (updates: Partial<DepreciationSettings>) => {
    onChange({ ...settings, ...updates })
  }

  const handleMethodChange = (method: DepreciationMethod) => {
    updateSettings({ 
      method,
      customRates: method === "custom" ? settings.customRates || [] : undefined
    })
  }

  const addCustomRate = () => {
    const newRate = { ...customRateForm }
    const customRates = settings.customRates || []
    
    if (editingRateIndex !== null) {
      customRates[editingRateIndex] = newRate
    } else {
      customRates.push(newRate)
    }
    
    updateSettings({ customRates: customRates.sort((a, b) => a.year - b.year) })
    setCustomRateForm({ year: 1, rate: 0, amount: 0 })
    setEditingRateIndex(null)
    setIsCustomRateDialogOpen(false)
  }

  const editCustomRate = (index: number) => {
    const rate = settings.customRates?.[index]
    if (rate) {
      setCustomRateForm(rate)
      setEditingRateIndex(index)
      setIsCustomRateDialogOpen(true)
    }
  }

  const deleteCustomRate = (index: number) => {
    const customRates = settings.customRates?.filter((_, i) => i !== index) || []
    updateSettings({ customRates })
  }

  const addImpairmentTrigger = () => {
    const newTrigger = { ...impairmentTriggerForm }
    const impairmentSettings = settings.impairmentSettings || {
      isEnabled: true,
      triggers: [],
      testFrequency: "annual"
    }
    
    if (editingTriggerIndex !== null) {
      impairmentSettings.triggers[editingTriggerIndex] = newTrigger
    } else {
      impairmentSettings.triggers.push(newTrigger)
    }
    
    updateSettings({ impairmentSettings })
    setImpairmentTriggerForm({ condition: "", threshold: 0, action: "flag" })
    setEditingTriggerIndex(null)
    setIsImpairmentTriggerDialogOpen(false)
  }

  const editImpairmentTrigger = (index: number) => {
    const trigger = settings.impairmentSettings?.triggers[index]
    if (trigger) {
      setImpairmentTriggerForm(trigger)
      setEditingTriggerIndex(index)
      setIsImpairmentTriggerDialogOpen(true)
    }
  }

  const deleteImpairmentTrigger = (index: number) => {
    const impairmentSettings = settings.impairmentSettings
    if (impairmentSettings) {
      impairmentSettings.triggers = impairmentSettings.triggers.filter((_, i) => i !== index)
      updateSettings({ impairmentSettings })
    }
  }

  const calculateEstimatedDepreciation = () => {
    // Simple calculation for demonstration
    if (settings.method === "straight_line") {
      const annualDepreciation = (100 - settings.salvageValue) / settings.usefulLife
      return settings.salvageValueType === "percentage" 
        ? `${annualDepreciation.toFixed(2)}% per ${settings.usefulLifeUnit.slice(0, -1)}`
        : `${annualDepreciation.toFixed(2)} per ${settings.usefulLifeUnit.slice(0, -1)}`
    }
    return "Varies by method"
  }

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="basic">Basic Settings</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
          <TabsTrigger value="custom">Custom Rates</TabsTrigger>
          <TabsTrigger value="impairment">Impairment</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calculator className="h-5 w-5" />
                Depreciation Method
              </CardTitle>
              <CardDescription>
                Choose the depreciation method that best fits your accounting needs
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="method">Depreciation Method</Label>
                <Select value={settings.method} onValueChange={handleMethodChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select depreciation method" />
                  </SelectTrigger>
                  <SelectContent>
                    {depreciationMethodOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <div>
                          <div className="font-medium">{option.label}</div>
                          <div className="text-sm text-muted-foreground">{option.description}</div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="usefulLife">Useful Life</Label>
                  <Input
                    id="usefulLife"
                    type="number"
                    min="1"
                    value={settings.usefulLife}
                    onChange={(e) => updateSettings({ usefulLife: Number(e.target.value) })}
                  />
                </div>
                <div>
                  <Label htmlFor="usefulLifeUnit">Unit</Label>
                  <Select 
                    value={settings.usefulLifeUnit} 
                    onValueChange={(value: "years" | "months" | "hours" | "cycles") => 
                      updateSettings({ usefulLifeUnit: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {usefulLifeUnitOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="salvageValue">Salvage Value</Label>
                  <Input
                    id="salvageValue"
                    type="number"
                    min="0"
                    value={settings.salvageValue}
                    onChange={(e) => updateSettings({ salvageValue: Number(e.target.value) })}
                  />
                </div>
                <div>
                  <Label htmlFor="salvageValueType">Salvage Value Type</Label>
                  <Select 
                    value={settings.salvageValueType} 
                    onValueChange={(value: "fixed" | "percentage") => 
                      updateSettings({ salvageValueType: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {salvageValueTypeOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="startDate">Start Date</Label>
                <Input
                  id="startDate"
                  type="date"
                  value={settings.startDate.split('T')[0]}
                  onChange={(e) => updateSettings({ startDate: new Date(e.target.value).toISOString() })}
                />
              </div>

              <div className="flex items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <Label className="text-base">Active</Label>
                  <div className="text-sm text-muted-foreground">
                    Enable depreciation calculations for this asset type
                  </div>
                </div>
                <Switch
                  checked={settings.isActive}
                  onCheckedChange={(checked) => updateSettings({ isActive: checked })}
                />
              </div>

              <div className="bg-muted p-4 rounded-lg">
                <h4 className="font-medium mb-2">Estimated Annual Depreciation</h4>
                <p className="text-sm text-muted-foreground">
                  {calculateEstimatedDepreciation()}
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="advanced" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingDown className="h-5 w-5" />
                Accelerated Depreciation
              </CardTitle>
              <CardDescription>
                Configure accelerated depreciation options for tax benefits
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <Label className="text-base">Enable Accelerated Depreciation</Label>
                  <div className="text-sm text-muted-foreground">
                    Apply accelerated depreciation methods
                  </div>
                </div>
                <Switch
                  checked={settings.acceleratedDepreciation?.isEnabled || false}
                  onCheckedChange={(checked) => 
                    updateSettings({ 
                      acceleratedDepreciation: {
                        ...settings.acceleratedDepreciation,
                        isEnabled: checked,
                        method: "bonus",
                        percentage: 50,
                      }
                    })
                  }
                />
              </div>

              {settings.acceleratedDepreciation?.isEnabled && (
                <>
                  <div>
                    <Label htmlFor="acceleratedMethod">Accelerated Method</Label>
                    <Select 
                      value={settings.acceleratedDepreciation.method} 
                      onValueChange={(value: "bonus" | "section179" | "custom") => 
                        updateSettings({ 
                          acceleratedDepreciation: {
                            ...settings.acceleratedDepreciation!,
                            method: value
                          }
                        })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {acceleratedMethodOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="acceleratedPercentage">Percentage</Label>
                      <Input
                        id="acceleratedPercentage"
                        type="number"
                        min="0"
                        max="100"
                        value={settings.acceleratedDepreciation.percentage}
                        onChange={(e) => 
                          updateSettings({ 
                            acceleratedDepreciation: {
                              ...settings.acceleratedDepreciation!,
                              percentage: Number(e.target.value)
                            }
                          })
                        }
                      />
                    </div>
                    <div>
                      <Label htmlFor="maxAmount">Max Amount (Optional)</Label>
                      <Input
                        id="maxAmount"
                        type="number"
                        min="0"
                        value={settings.acceleratedDepreciation.maxAmount || ""}
                        onChange={(e) => 
                          updateSettings({ 
                            acceleratedDepreciation: {
                              ...settings.acceleratedDepreciation!,
                              maxAmount: e.target.value ? Number(e.target.value) : undefined
                            }
                          })
                        }
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="conditions">Conditions (Optional)</Label>
                    <Textarea
                      id="conditions"
                      placeholder="Enter conditions for accelerated depreciation..."
                      value={settings.acceleratedDepreciation.conditions?.join('\n') || ""}
                      onChange={(e) => 
                        updateSettings({ 
                          acceleratedDepreciation: {
                            ...settings.acceleratedDepreciation!,
                            conditions: e.target.value.split('\n').filter(c => c.trim())
                          }
                        })
                      }
                    />
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="custom" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Custom Depreciation Rates
              </CardTitle>
              <CardDescription>
                Define custom depreciation rates for each year (only applies to custom method)
              </CardDescription>
            </CardHeader>
            <CardContent>
              {settings.method === "custom" ? (
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <h4 className="font-medium">Depreciation Schedule</h4>
                    <Dialog open={isCustomRateDialogOpen} onOpenChange={setIsCustomRateDialogOpen}>
                      <DialogTrigger asChild>
                        <Button size="sm">
                          <Plus className="h-4 w-4 mr-2" />
                          Add Rate
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>
                            {editingRateIndex !== null ? "Edit" : "Add"} Depreciation Rate
                          </DialogTitle>
                          <DialogDescription>
                            Configure the depreciation rate for a specific year
                          </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-4">
                          <div>
                            <Label htmlFor="year">Year</Label>
                            <Input
                              id="year"
                              type="number"
                              min="1"
                              value={customRateForm.year}
                              onChange={(e) => setCustomRateForm({
                                ...customRateForm,
                                year: Number(e.target.value)
                              })}
                            />
                          </div>
                          <div>
                            <Label htmlFor="rate">Rate (%)</Label>
                            <Input
                              id="rate"
                              type="number"
                              min="0"
                              max="100"
                              step="0.01"
                              value={customRateForm.rate}
                              onChange={(e) => setCustomRateForm({
                                ...customRateForm,
                                rate: Number(e.target.value)
                              })}
                            />
                          </div>
                          <div>
                            <Label htmlFor="amount">Fixed Amount (Optional)</Label>
                            <Input
                              id="amount"
                              type="number"
                              min="0"
                              value={customRateForm.amount || ""}
                              onChange={(e) => setCustomRateForm({
                                ...customRateForm,
                                amount: e.target.value ? Number(e.target.value) : undefined
                              })}
                            />
                          </div>
                        </div>
                        <DialogFooter>
                          <Button variant="outline" onClick={() => setIsCustomRateDialogOpen(false)}>
                            Cancel
                          </Button>
                          <Button onClick={addCustomRate}>
                            {editingRateIndex !== null ? "Update" : "Add"} Rate
                          </Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                  </div>

                  {settings.customRates && settings.customRates.length > 0 ? (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Year</TableHead>
                          <TableHead>Rate (%)</TableHead>
                          <TableHead>Fixed Amount</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {settings.customRates.map((rate, index) => (
                          <TableRow key={index}>
                            <TableCell>{rate.year}</TableCell>
                            <TableCell>{rate.rate}%</TableCell>
                            <TableCell>{rate.amount || "-"}</TableCell>
                            <TableCell className="text-right">
                              <div className="flex justify-end gap-2">
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => editCustomRate(index)}
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => deleteCustomRate(index)}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      No custom rates defined. Click "Add Rate" to get started.
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  Custom rates are only available when using the "Custom" depreciation method.
                  Change the depreciation method to "Custom" in the Basic Settings tab.
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="impairment" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Impairment Settings
              </CardTitle>
              <CardDescription>
                Configure asset impairment testing and triggers
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <Label className="text-base">Enable Impairment Testing</Label>
                  <div className="text-sm text-muted-foreground">
                    Monitor assets for potential value impairment
                  </div>
                </div>
                <Switch
                  checked={settings.impairmentSettings?.isEnabled || false}
                  onCheckedChange={(checked) => 
                    updateSettings({ 
                      impairmentSettings: {
                        ...settings.impairmentSettings,
                        isEnabled: checked,
                        triggers: settings.impairmentSettings?.triggers || [],
                        testFrequency: settings.impairmentSettings?.testFrequency || "annual",
                      }
                    })
                  }
                />
              </div>

              {settings.impairmentSettings?.isEnabled && (
                <>
                  <div>
                    <Label htmlFor="testFrequency">Test Frequency</Label>
                    <Select 
                      value={settings.impairmentSettings.testFrequency} 
                      onValueChange={(value: "annual" | "quarterly" | "monthly" | "event_based") => 
                        updateSettings({ 
                          impairmentSettings: {
                            ...settings.impairmentSettings!,
                            testFrequency: value
                          }
                        })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {impairmentTestFrequencyOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <h4 className="font-medium">Impairment Triggers</h4>
                      <Dialog open={isImpairmentTriggerDialogOpen} onOpenChange={setIsImpairmentTriggerDialogOpen}>
                        <DialogTrigger asChild>
                          <Button size="sm">
                            <Plus className="h-4 w-4 mr-2" />
                            Add Trigger
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>
                              {editingTriggerIndex !== null ? "Edit" : "Add"} Impairment Trigger
                            </DialogTitle>
                            <DialogDescription>
                              Configure conditions that may indicate asset impairment
                            </DialogDescription>
                          </DialogHeader>
                          <div className="space-y-4">
                            <div>
                              <Label htmlFor="condition">Condition</Label>
                              <Input
                                id="condition"
                                placeholder="e.g., Market value decline > 20%"
                                value={impairmentTriggerForm.condition}
                                onChange={(e) => setImpairmentTriggerForm({
                                  ...impairmentTriggerForm,
                                  condition: e.target.value
                                })}
                              />
                            </div>
                            <div>
                              <Label htmlFor="threshold">Threshold</Label>
                              <Input
                                id="threshold"
                                type="number"
                                value={impairmentTriggerForm.threshold}
                                onChange={(e) => setImpairmentTriggerForm({
                                  ...impairmentTriggerForm,
                                  threshold: Number(e.target.value)
                                })}
                              />
                            </div>
                            <div>
                              <Label htmlFor="action">Action</Label>
                              <Select 
                                value={impairmentTriggerForm.action} 
                                onValueChange={(value: "flag" | "auto_adjust" | "notify") => 
                                  setImpairmentTriggerForm({
                                    ...impairmentTriggerForm,
                                    action: value
                                  })
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  {impairmentActionOptions.map((option) => (
                                    <SelectItem key={option.value} value={option.value}>
                                      {option.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                          <DialogFooter>
                            <Button variant="outline" onClick={() => setIsImpairmentTriggerDialogOpen(false)}>
                              Cancel
                            </Button>
                            <Button onClick={addImpairmentTrigger}>
                              {editingTriggerIndex !== null ? "Update" : "Add"} Trigger
                            </Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>
                    </div>

                    {settings.impairmentSettings.triggers.length > 0 ? (
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Condition</TableHead>
                            <TableHead>Threshold</TableHead>
                            <TableHead>Action</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {settings.impairmentSettings.triggers.map((trigger, index) => (
                            <TableRow key={index}>
                              <TableCell>{trigger.condition}</TableCell>
                              <TableCell>{trigger.threshold}</TableCell>
                              <TableCell>
                                <Badge variant="outline">
                                  {impairmentActionOptions.find(opt => opt.value === trigger.action)?.label}
                                </Badge>
                              </TableCell>
                              <TableCell className="text-right">
                                <div className="flex justify-end gap-2">
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => editImpairmentTrigger(index)}
                                  >
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => deleteImpairmentTrigger(index)}
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    ) : (
                      <div className="text-center py-8 text-muted-foreground">
                        No impairment triggers defined. Click "Add Trigger" to get started.
                      </div>
                    )}
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}