"use client"

import React, { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Plus, Edit, Trash2, Settings, X } from "lucide-react"
import type { CustomField, CustomFieldType, FieldOption, FieldValidation } from "@/lib/modules/asset-types/types"

interface CustomFieldEditorProps {
  fields: CustomField[]
  onChange: (fields: CustomField[]) => void
}

const fieldTypeOptions: { value: CustomFieldType; label: string; description: string }[] = [
  { value: "text", label: "Text", description: "Single line text input" },
  { value: "textarea", label: "Text Area", description: "Multi-line text input" },
  { value: "number", label: "Number", description: "Numeric input" },
  { value: "decimal", label: "Decimal", description: "Decimal number input" },
  { value: "date", label: "Date", description: "Date picker" },
  { value: "datetime", label: "Date Time", description: "Date and time picker" },
  { value: "boolean", label: "Boolean", description: "True/false checkbox" },
  { value: "select", label: "Select", description: "Single selection dropdown" },
  { value: "multiselect", label: "Multi Select", description: "Multiple selection dropdown" },
  { value: "email", label: "Email", description: "Email address input" },
  { value: "url", label: "URL", description: "Website URL input" },
  { value: "phone", label: "Phone", description: "Phone number input" },
  { value: "currency", label: "Currency", description: "Currency amount input" },
  { value: "percentage", label: "Percentage", description: "Percentage value input" },
  { value: "file", label: "File", description: "File upload" },
  { value: "image", label: "Image", description: "Image upload" },
  { value: "json", label: "JSON", description: "JSON data input" },
]

export function CustomFieldEditor({ fields, onChange }: CustomFieldEditorProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingField, setEditingField] = useState<CustomField | null>(null)
  const [fieldForm, setFieldForm] = useState<Partial<CustomField>>({
    name: "",
    label: "",
    type: "text",
    description: "",
    isRequired: false,
    isUnique: false,
    defaultValue: undefined,
    validation: { errorMessage: "Invalid value" },
    options: [],
    displayOrder: fields.length,
    groupName: "",
    isActive: true,
  })
  const [options, setOptions] = useState<FieldOption[]>([])
  const [currentOption, setCurrentOption] = useState({ value: "", label: "", description: "" })

  const openDialog = (field?: CustomField) => {
    if (field) {
      setEditingField(field)
      setFieldForm({
        ...field,
        validation: field.validation || { errorMessage: "Invalid value" },
      })
      setOptions(field.options || [])
    } else {
      setEditingField(null)
      setFieldForm({
        name: "",
        label: "",
        type: "text",
        description: "",
        isRequired: false,
        isUnique: false,
        defaultValue: undefined,
        validation: { errorMessage: "Invalid value" },
        options: [],
        displayOrder: fields.length,
        groupName: "",
        isActive: true,
      })
      setOptions([])
    }
    setIsDialogOpen(true)
  }

  const saveField = () => {
    if (!fieldForm.name || !fieldForm.label || !fieldForm.type) return

    const newField: CustomField = {
      id: editingField?.id || `cf-${Date.now()}`,
      name: fieldForm.name,
      label: fieldForm.label,
      type: fieldForm.type as CustomFieldType,
      description: fieldForm.description || "",
      isRequired: fieldForm.isRequired || false,
      isUnique: fieldForm.isUnique || false,
      defaultValue: fieldForm.defaultValue,
      validation: fieldForm.validation || { errorMessage: "Invalid value" },
      options: options.length > 0 ? options : undefined,
      conditionalLogic: fieldForm.conditionalLogic,
      displayOrder: fieldForm.displayOrder || fields.length,
      groupName: fieldForm.groupName || "",
      isActive: fieldForm.isActive !== false,
    }

    if (editingField) {
      const updatedFields = fields.map(f => f.id === editingField.id ? newField : f)
      onChange(updatedFields)
    } else {
      onChange([...fields, newField])
    }

    setIsDialogOpen(false)
  }

  const deleteField = (id: string) => {
    onChange(fields.filter(f => f.id !== id))
  }

  const addOption = () => {
    if (currentOption.value.trim() && currentOption.label.trim()) {
      const newOption: FieldOption = {
        value: currentOption.value.trim(),
        label: currentOption.label.trim(),
        description: currentOption.description.trim() || undefined,
        isActive: true,
      }
      setOptions([...options, newOption])
      setCurrentOption({ value: "", label: "", description: "" })
    }
  }

  const removeOption = (value: string) => {
    setOptions(options.filter(o => o.value !== value))
  }

  const needsOptions = fieldForm.type === "select" || fieldForm.type === "multiselect"

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Custom Fields</CardTitle>
            <CardDescription>Define custom fields for this asset type</CardDescription>
          </div>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={() => openDialog()}>
                <Plus className="w-4 h-4 mr-2" />
                Add Field
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>{editingField ? "Edit" : "Add"} Custom Field</DialogTitle>
                <DialogDescription>Configure the custom field properties</DialogDescription>
              </DialogHeader>

              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="field-name">Field Name</Label>
                    <Input
                      id="field-name"
                      placeholder="serial_number"
                      value={fieldForm.name || ""}
                      onChange={(e) => setFieldForm({ ...fieldForm, name: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="field-label">Field Label</Label>
                    <Input
                      id="field-label"
                      placeholder="Serial Number"
                      value={fieldForm.label || ""}
                      onChange={(e) => setFieldForm({ ...fieldForm, label: e.target.value })}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="field-type">Field Type</Label>
                    <Select
                      value={fieldForm.type}
                      onValueChange={(value) => setFieldForm({ ...fieldForm, type: value as CustomFieldType })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {fieldTypeOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            <div>
                              <div className="font-medium">{option.label}</div>
                              <div className="text-sm text-muted-foreground">{option.description}</div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="field-group">Group Name</Label>
                    <Input
                      id="field-group"
                      placeholder="Hardware Specs"
                      value={fieldForm.groupName || ""}
                      onChange={(e) => setFieldForm({ ...fieldForm, groupName: e.target.value })}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="field-description">Description</Label>
                  <Textarea
                    id="field-description"
                    placeholder="Additional information about this field"
                    value={fieldForm.description || ""}
                    onChange={(e) => setFieldForm({ ...fieldForm, description: e.target.value })}
                  />
                </div>

                {needsOptions && (
                  <div>
                    <Label>Options</Label>
                    <div className="space-y-2 mt-2">
                      <div className="flex gap-2 flex-wrap">
                        {options.map((option) => (
                          <Badge key={option.value} variant="secondary" className="flex items-center gap-1">
                            {option.label}
                            <X className="h-3 w-3 cursor-pointer" onClick={() => removeOption(option.value)} />
                          </Badge>
                        ))}
                      </div>
                      <div className="grid grid-cols-3 gap-2">
                        <Input
                          placeholder="Value"
                          value={currentOption.value}
                          onChange={(e) => setCurrentOption({ ...currentOption, value: e.target.value })}
                        />
                        <Input
                          placeholder="Label"
                          value={currentOption.label}
                          onChange={(e) => setCurrentOption({ ...currentOption, label: e.target.value })}
                        />
                        <div className="flex gap-1">
                          <Input
                            placeholder="Description"
                            value={currentOption.description}
                            onChange={(e) => setCurrentOption({ ...currentOption, description: e.target.value })}
                          />
                          <Button type="button" onClick={addOption} size="sm">
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="field-default">Default Value</Label>
                    <Input
                      id="field-default"
                      placeholder="Default value"
                      value={fieldForm.defaultValue || ""}
                      onChange={(e) => setFieldForm({ ...fieldForm, defaultValue: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="field-order">Display Order</Label>
                    <Input
                      id="field-order"
                      type="number"
                      value={fieldForm.displayOrder || 0}
                      onChange={(e) => setFieldForm({ ...fieldForm, displayOrder: parseInt(e.target.value) })}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={fieldForm.isRequired || false}
                      onCheckedChange={(checked) => setFieldForm({ ...fieldForm, isRequired: checked })}
                    />
                    <Label>Required Field</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={fieldForm.isUnique || false}
                      onCheckedChange={(checked) => setFieldForm({ ...fieldForm, isUnique: checked })}
                    />
                    <Label>Unique Values</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={fieldForm.isActive !== false}
                      onCheckedChange={(checked) => setFieldForm({ ...fieldForm, isActive: checked })}
                    />
                    <Label>Active</Label>
                  </div>
                </div>
              </div>

              <DialogFooter>
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={saveField}>
                  Save Field
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {fields.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Settings className="mx-auto h-12 w-12 mb-4 opacity-50" />
            <p>No custom fields defined yet</p>
            <p className="text-sm">Add custom fields to capture specific information for this asset type</p>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Group</TableHead>
                <TableHead>Required</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {fields
                .sort((a, b) => a.displayOrder - b.displayOrder)
                .map((field) => (
                  <TableRow key={field.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{field.label}</div>
                        <div className="text-sm text-muted-foreground">{field.name}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{field.type}</Badge>
                    </TableCell>
                    <TableCell>{field.groupName || "—"}</TableCell>
                    <TableCell>
                      {field.isRequired ? (
                        <Badge variant="destructive" className="text-xs">Required</Badge>
                      ) : (
                        <Badge variant="secondary" className="text-xs">Optional</Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      {field.isActive ? (
                        <Badge variant="default" className="text-xs">Active</Badge>
                      ) : (
                        <Badge variant="secondary" className="text-xs">Inactive</Badge>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => openDialog(field)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => deleteField(field.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  )
}