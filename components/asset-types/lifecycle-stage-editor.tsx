"use client"

import React, { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Plus, Edit, Trash2, Workflow, ArrowRight, X } from "lucide-react"
import type { LifecycleStage } from "@/lib/modules/asset-types/types"

interface LifecycleStageEditorProps {
  stages: LifecycleStage[]
  onChange: (stages: LifecycleStage[]) => void
}

const iconOptions = [
  { value: "package", label: "📦 Package" },
  { value: "truck", label: "🚚 Delivery" },
  { value: "check", label: "✅ Check" },
  { value: "play", label: "▶️ Start" },
  { value: "settings", label: "⚙️ Settings" },
  { value: "wrench", label: "🔧 Maintenance" },
  { value: "pause", label: "⏸️ Pause" },
  { value: "stop", label: "⏹️ Stop" },
  { value: "archive", label: "📁 Archive" },
  { value: "trash", label: "🗑️ Dispose" },
]

const colorOptions = [
  "#10B981", "#3B82F6", "#F59E0B", "#EF4444", "#8B5CF6",
  "#06B6D4", "#84CC16", "#F97316", "#EC4899", "#6B7280"
]

export function LifecycleStageEditor({ stages, onChange }: LifecycleStageEditorProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingStage, setEditingStage] = useState<LifecycleStage | null>(null)
  const [stageForm, setStageForm] = useState<Partial<LifecycleStage>>({
    name: "",
    code: "",
    description: "",
    order: stages.length + 1,
    isInitial: false,
    isFinal: false,
    color: "#10B981",
    icon: "package",
    allowedTransitions: [],
    requiredFields: [],
    automatedActions: [],
    notifications: [],
    isActive: true,
  })
  const [requiredFields, setRequiredFields] = useState<string[]>([])
  const [currentField, setCurrentField] = useState("")

  const openDialog = (stage?: LifecycleStage) => {
    if (stage) {
      setEditingStage(stage)
      setStageForm(stage)
      setRequiredFields(stage.requiredFields || [])
    } else {
      setEditingStage(null)
      setStageForm({
        name: "",
        code: "",
        description: "",
        order: stages.length + 1,
        isInitial: false,
        isFinal: false,
        color: "#10B981",
        icon: "package",
        allowedTransitions: [],
        requiredFields: [],
        automatedActions: [],
        notifications: [],
        isActive: true,
      })
      setRequiredFields([])
    }
    setIsDialogOpen(true)
  }

  const saveStage = () => {
    if (!stageForm.name || !stageForm.code) return

    const newStage: LifecycleStage = {
      id: editingStage?.id || `ls-${Date.now()}`,
      name: stageForm.name,
      code: stageForm.code,
      description: stageForm.description || "",
      order: stageForm.order || stages.length + 1,
      isInitial: stageForm.isInitial || false,
      isFinal: stageForm.isFinal || false,
      color: stageForm.color || "#10B981",
      icon: stageForm.icon || "package",
      allowedTransitions: stageForm.allowedTransitions || [],
      requiredFields,
      automatedActions: stageForm.automatedActions || [],
      notifications: stageForm.notifications || [],
      isActive: stageForm.isActive !== false,
    }

    if (editingStage) {
      const updatedStages = stages.map(s => s.id === editingStage.id ? newStage : s)
      onChange(updatedStages.sort((a, b) => a.order - b.order))
    } else {
      onChange([...stages, newStage].sort((a, b) => a.order - b.order))
    }

    setIsDialogOpen(false)
  }

  const deleteStage = (id: string) => {
    onChange(stages.filter(s => s.id !== id))
  }

  const addRequiredField = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && currentField.trim() && !requiredFields.includes(currentField.trim())) {
      setRequiredFields([...requiredFields, currentField.trim()])
      setCurrentField("")
    }
  }

  const removeRequiredField = (field: string) => {
    setRequiredFields(requiredFields.filter(f => f !== field))
  }

  const getInitialStages = () => stages.filter(s => s.isInitial)
  const getFinalStages = () => stages.filter(s => s.isFinal)

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Lifecycle Stages</CardTitle>
            <CardDescription>Define the lifecycle stages for this asset type</CardDescription>
          </div>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={() => openDialog()}>
                <Plus className="w-4 h-4 mr-2" />
                Add Stage
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>{editingStage ? "Edit" : "Add"} Lifecycle Stage</DialogTitle>
                <DialogDescription>Configure the lifecycle stage properties</DialogDescription>
              </DialogHeader>

              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="stage-name">Stage Name</Label>
                    <Input
                      id="stage-name"
                      placeholder="In Use"
                      value={stageForm.name || ""}
                      onChange={(e) => setStageForm({ ...stageForm, name: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="stage-code">Stage Code</Label>
                    <Input
                      id="stage-code"
                      placeholder="IN_USE"
                      value={stageForm.code || ""}
                      onChange={(e) => setStageForm({ ...stageForm, code: e.target.value.toUpperCase() })}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="stage-description">Description</Label>
                  <Textarea
                    id="stage-description"
                    placeholder="Asset is actively being used"
                    value={stageForm.description || ""}
                    onChange={(e) => setStageForm({ ...stageForm, description: e.target.value })}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="stage-order">Display Order</Label>
                    <Input
                      id="stage-order"
                      type="number"
                      value={stageForm.order || 1}
                      onChange={(e) => setStageForm({ ...stageForm, order: parseInt(e.target.value) })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="stage-icon">Icon</Label>
                    <Select
                      value={stageForm.icon}
                      onValueChange={(value) => setStageForm({ ...stageForm, icon: value })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {iconOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label>Color</Label>
                  <div className="flex gap-2 flex-wrap mt-2">
                    {colorOptions.map((color) => (
                      <button
                        key={color}
                        type="button"
                        className={`w-8 h-8 rounded-md border-2 ${
                          stageForm.color === color ? "border-gray-900" : "border-gray-300"
                        }`}
                        style={{ backgroundColor: color }}
                        onClick={() => setStageForm({ ...stageForm, color })}
                      />
                    ))}
                  </div>
                </div>

                <div>
                  <Label>Required Fields</Label>
                  <div className="space-y-2 mt-2">
                    <div className="flex gap-2 flex-wrap">
                      {requiredFields.map((field) => (
                        <Badge key={field} variant="secondary" className="flex items-center gap-1">
                          {field}
                          <X className="h-3 w-3 cursor-pointer" onClick={() => removeRequiredField(field)} />
                        </Badge>
                      ))}
                    </div>
                    <Input
                      placeholder="Add required field (press Enter)"
                      value={currentField}
                      onChange={(e) => setCurrentField(e.target.value)}
                      onKeyPress={addRequiredField}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={stageForm.isInitial || false}
                      onCheckedChange={(checked) => setStageForm({ ...stageForm, isInitial: checked })}
                    />
                    <Label>Initial Stage</Label>
                    <span className="text-sm text-muted-foreground">(Assets start in this stage)</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={stageForm.isFinal || false}
                      onCheckedChange={(checked) => setStageForm({ ...stageForm, isFinal: checked })}
                    />
                    <Label>Final Stage</Label>
                    <span className="text-sm text-muted-foreground">(Assets end in this stage)</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={stageForm.isActive !== false}
                      onCheckedChange={(checked) => setStageForm({ ...stageForm, isActive: checked })}
                    />
                    <Label>Active</Label>
                  </div>
                </div>
              </div>

              <DialogFooter>
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={saveStage}>
                  Save Stage
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {stages.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Workflow className="mx-auto h-12 w-12 mb-4 opacity-50" />
            <p>No lifecycle stages defined yet</p>
            <p className="text-sm">Add lifecycle stages to track asset status throughout its lifetime</p>
          </div>
        ) : (
          <>
            {/* Validation warnings */}
            {getInitialStages().length === 0 && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 mb-4">
                <div className="flex items-center text-yellow-800">
                  <div className="text-sm">
                    <strong>Warning:</strong> No initial stage defined. Assets need a starting point in their lifecycle.
                  </div>
                </div>
              </div>
            )}
            {getInitialStages().length > 1 && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3 mb-4">
                <div className="flex items-center text-red-800">
                  <div className="text-sm">
                    <strong>Error:</strong> Multiple initial stages defined. Only one initial stage is allowed.
                  </div>
                </div>
              </div>
            )}

            {/* Lifecycle Flow Visualization */}
            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium mb-3">Lifecycle Flow</h4>
              <div className="flex items-center space-x-2 flex-wrap">
                {stages
                  .sort((a, b) => a.order - b.order)
                  .map((stage, index) => (
                    <React.Fragment key={stage.id}>
                      <div
                        className="flex items-center space-x-2 px-3 py-2 rounded-md text-sm"
                        style={{ backgroundColor: stage.color + "20", color: stage.color }}
                      >
                        <span>{stage.name}</span>
                        {stage.isInitial && <Badge variant="outline" className="text-xs">Initial</Badge>}
                        {stage.isFinal && <Badge variant="outline" className="text-xs">Final</Badge>}
                      </div>
                      {index < stages.length - 1 && (
                        <ArrowRight className="h-4 w-4 text-gray-400" />
                      )}
                    </React.Fragment>
                  ))}
              </div>
            </div>

            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Stage</TableHead>
                  <TableHead>Code</TableHead>
                  <TableHead>Order</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Required Fields</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {stages
                  .sort((a, b) => a.order - b.order)
                  .map((stage) => (
                    <TableRow key={stage.id}>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <div
                            className="w-4 h-4 rounded-full"
                            style={{ backgroundColor: stage.color }}
                          />
                          <div>
                            <div className="font-medium">{stage.name}</div>
                            <div className="text-sm text-muted-foreground">{stage.description}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <code className="bg-gray-100 px-2 py-1 rounded text-sm">{stage.code}</code>
                      </TableCell>
                      <TableCell>{stage.order}</TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          {stage.isInitial && <Badge variant="default" className="text-xs">Initial</Badge>}
                          {stage.isFinal && <Badge variant="destructive" className="text-xs">Final</Badge>}
                          {!stage.isInitial && !stage.isFinal && <Badge variant="secondary" className="text-xs">Normal</Badge>}
                        </div>
                      </TableCell>
                      <TableCell>
                        {stage.requiredFields.length > 0 ? (
                          <div className="flex gap-1 flex-wrap">
                            {stage.requiredFields.slice(0, 2).map((field) => (
                              <Badge key={field} variant="outline" className="text-xs">{field}</Badge>
                            ))}
                            {stage.requiredFields.length > 2 && (
                              <Badge variant="outline" className="text-xs">+{stage.requiredFields.length - 2}</Badge>
                            )}
                          </div>
                        ) : (
                          "—"
                        )}
                      </TableCell>
                      <TableCell>
                        {stage.isActive ? (
                          <Badge variant="default" className="text-xs">Active</Badge>
                        ) : (
                          <Badge variant="secondary" className="text-xs">Inactive</Badge>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => openDialog(stage)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => deleteStage(stage.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
              </TableBody>
            </Table>
          </>
        )}
      </CardContent>
    </Card>
  )
}