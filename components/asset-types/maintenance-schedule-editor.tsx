"use client"

import React, { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Plus, Edit, Trash2, Wrench, X } from "lucide-react"
import type { MaintenanceSchedule, MaintenanceType, MaintenancePriority, MaintenanceFrequency, RequiredPart, ChecklistItem } from "@/lib/modules/asset-types/types"

interface MaintenanceScheduleEditorProps {
  schedules: MaintenanceSchedule[]
  onChange: (schedules: MaintenanceSchedule[]) => void
}

const maintenanceTypes: { value: MaintenanceType; label: string; description: string }[] = [
  { value: "preventive", label: "Preventive", description: "Scheduled maintenance to prevent failures" },
  { value: "predictive", label: "Predictive", description: "Condition-based maintenance using monitoring" },
  { value: "corrective", label: "Corrective", description: "Maintenance after failure occurs" },
  { value: "condition_based", label: "Condition Based", description: "Maintenance based on asset condition" },
  { value: "time_based", label: "Time Based", description: "Maintenance at regular time intervals" },
]

const priorities: { value: MaintenancePriority; label: string; color: string }[] = [
  { value: "low", label: "Low", color: "#10B981" },
  { value: "medium", label: "Medium", color: "#F59E0B" },
  { value: "high", label: "High", color: "#EF4444" },
  { value: "critical", label: "Critical", color: "#DC2626" },
]

const frequencyTypes = [
  { value: "days", label: "Days" },
  { value: "weeks", label: "Weeks" },
  { value: "months", label: "Months" },
  { value: "years", label: "Years" },
  { value: "hours", label: "Hours" },
  { value: "cycles", label: "Cycles" },
  { value: "condition", label: "Condition" },
]

export function MaintenanceScheduleEditor({ schedules, onChange }: MaintenanceScheduleEditorProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingSchedule, setEditingSchedule] = useState<MaintenanceSchedule | null>(null)
  const [scheduleForm, setScheduleForm] = useState<Partial<MaintenanceSchedule>>({
    name: "",
    description: "",
    type: "preventive",
    frequency: {
      type: "months",
      interval: 6,
    },
    priority: "medium",
    estimatedDuration: 60,
    estimatedCost: 0,
    requiredSkills: [],
    requiredParts: [],
    instructions: "",
    checklistItems: [],
    triggers: [],
    isActive: true,
  })
  const [requiredSkills, setRequiredSkills] = useState<string[]>([])
  const [requiredParts, setRequiredParts] = useState<RequiredPart[]>([])
  const [checklistItems, setChecklistItems] = useState<ChecklistItem[]>([])
  const [currentSkill, setCurrentSkill] = useState("")
  const [currentPart, setCurrentPart] = useState<Partial<RequiredPart>>({})
  const [currentChecklistItem, setCurrentChecklistItem] = useState<Partial<ChecklistItem>>({})

  const openDialog = (schedule?: MaintenanceSchedule) => {
    if (schedule) {
      setEditingSchedule(schedule)
      setScheduleForm(schedule)
      setRequiredSkills(schedule.requiredSkills || [])
      setRequiredParts(schedule.requiredParts || [])
      setChecklistItems(schedule.checklistItems || [])
    } else {
      setEditingSchedule(null)
      setScheduleForm({
        name: "",
        description: "",
        type: "preventive",
        frequency: {
          type: "months",
          interval: 6,
        },
        priority: "medium",
        estimatedDuration: 60,
        estimatedCost: 0,
        requiredSkills: [],
        requiredParts: [],
        instructions: "",
        checklistItems: [],
        triggers: [],
        isActive: true,
      })
      setRequiredSkills([])
      setRequiredParts([])
      setChecklistItems([])
    }
    setIsDialogOpen(true)
  }

  const saveSchedule = () => {
    if (!scheduleForm.name || !scheduleForm.description) return

    const newSchedule: MaintenanceSchedule = {
      id: editingSchedule?.id || `ms-${Date.now()}`,
      name: scheduleForm.name,
      description: scheduleForm.description,
      type: scheduleForm.type || "preventive",
      frequency: scheduleForm.frequency || { type: "months", interval: 6 },
      priority: scheduleForm.priority || "medium",
      estimatedDuration: scheduleForm.estimatedDuration || 60,
      estimatedCost: scheduleForm.estimatedCost || 0,
      requiredSkills,
      requiredParts,
      instructions: scheduleForm.instructions || "",
      checklistItems,
      triggers: scheduleForm.triggers || [],
      isActive: scheduleForm.isActive !== false,
    }

    if (editingSchedule) {
      const updatedSchedules = schedules.map(s => s.id === editingSchedule.id ? newSchedule : s)
      onChange(updatedSchedules)
    } else {
      onChange([...schedules, newSchedule])
    }

    setIsDialogOpen(false)
  }

  const deleteSchedule = (id: string) => {
    onChange(schedules.filter(s => s.id !== id))
  }

  const addSkill = () => {
    if (currentSkill.trim() && !requiredSkills.includes(currentSkill.trim())) {
      setRequiredSkills([...requiredSkills, currentSkill.trim()])
      setCurrentSkill("")
    }
  }

  const removeSkill = (skill: string) => {
    setRequiredSkills(requiredSkills.filter(s => s !== skill))
  }

  const addPart = () => {
    if (currentPart.partName && currentPart.quantity) {
      const newPart: RequiredPart = {
        partId: `part-${Date.now()}`,
        partName: currentPart.partName,
        quantity: currentPart.quantity,
        estimatedCost: currentPart.estimatedCost || 0,
        isOptional: currentPart.isOptional || false,
      }
      setRequiredParts([...requiredParts, newPart])
      setCurrentPart({})
    }
  }

  const removePart = (partId: string) => {
    setRequiredParts(requiredParts.filter(p => p.partId !== partId))
  }

  const addChecklistItem = () => {
    if (currentChecklistItem.description) {
      const newItem: ChecklistItem = {
        id: `item-${Date.now()}`,
        description: currentChecklistItem.description,
        isRequired: currentChecklistItem.isRequired || false,
        order: checklistItems.length + 1,
        category: currentChecklistItem.category || "",
      }
      setChecklistItems([...checklistItems, newItem])
      setCurrentChecklistItem({})
    }
  }

  const removeChecklistItem = (itemId: string) => {
    setChecklistItems(checklistItems.filter(i => i.id !== itemId))
  }

  const formatFrequency = (frequency: MaintenanceFrequency) => {
    if (frequency.type === "condition") {
      return "Condition-based"
    }
    return `Every ${frequency.interval} ${frequency.type}`
  }

  const getPriorityColor = (priority: MaintenancePriority) => {
    return priorities.find(p => p.value === priority)?.color || "#6B7280"
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Maintenance Schedules</CardTitle>
            <CardDescription>Define maintenance schedules for this asset type</CardDescription>
          </div>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={() => openDialog()}>
                <Plus className="w-4 h-4 mr-2" />
                Add Schedule
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>{editingSchedule ? "Edit" : "Add"} Maintenance Schedule</DialogTitle>
                <DialogDescription>Configure the maintenance schedule properties</DialogDescription>
              </DialogHeader>

              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="schedule-name">Schedule Name</Label>
                    <Input
                      id="schedule-name"
                      placeholder="Quarterly Inspection"
                      value={scheduleForm.name || ""}
                      onChange={(e) => setScheduleForm({ ...scheduleForm, name: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="schedule-type">Maintenance Type</Label>
                    <Select
                      value={scheduleForm.type}
                      onValueChange={(value) => setScheduleForm({ ...scheduleForm, type: value as MaintenanceType })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {maintenanceTypes.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            <div>
                              <div className="font-medium">{type.label}</div>
                              <div className="text-sm text-muted-foreground">{type.description}</div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="schedule-description">Description</Label>
                  <Textarea
                    id="schedule-description"
                    placeholder="Detailed description of the maintenance tasks"
                    value={scheduleForm.description || ""}
                    onChange={(e) => setScheduleForm({ ...scheduleForm, description: e.target.value })}
                  />
                </div>

                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="frequency-interval">Frequency Interval</Label>
                    <Input
                      id="frequency-interval"
                      type="number"
                      value={scheduleForm.frequency?.interval || 1}
                      onChange={(e) => setScheduleForm({
                        ...scheduleForm,
                        frequency: {
                          ...scheduleForm.frequency!,
                          interval: parseInt(e.target.value)
                        }
                      })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="frequency-type">Frequency Type</Label>
                    <Select
                      value={scheduleForm.frequency?.type}
                      onValueChange={(value) => setScheduleForm({
                        ...scheduleForm,
                        frequency: {
                          ...scheduleForm.frequency!,
                          type: value as "days" | "weeks" | "months" | "years" | "hours" | "cycles" | "condition"
                        }
                      })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {frequencyTypes.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="schedule-priority">Priority</Label>
                    <Select
                      value={scheduleForm.priority}
                      onValueChange={(value) => setScheduleForm({ ...scheduleForm, priority: value as MaintenancePriority })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {priorities.map((priority) => (
                          <SelectItem key={priority.value} value={priority.value}>
                            <div className="flex items-center space-x-2">
                              <div
                                className="w-3 h-3 rounded-full"
                                style={{ backgroundColor: priority.color }}
                              />
                              <span>{priority.label}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="estimated-duration">Estimated Duration (minutes)</Label>
                    <Input
                      id="estimated-duration"
                      type="number"
                      value={scheduleForm.estimatedDuration || 60}
                      onChange={(e) => setScheduleForm({ ...scheduleForm, estimatedDuration: parseInt(e.target.value) })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="estimated-cost">Estimated Cost</Label>
                    <Input
                      id="estimated-cost"
                      type="number"
                      step="0.01"
                      value={scheduleForm.estimatedCost || 0}
                      onChange={(e) => setScheduleForm({ ...scheduleForm, estimatedCost: parseFloat(e.target.value) })}
                    />
                  </div>
                </div>

                <div>
                  <Label>Required Skills</Label>
                  <div className="space-y-2 mt-2">
                    <div className="flex gap-2 flex-wrap">
                      {requiredSkills.map((skill) => (
                        <Badge key={skill} variant="secondary" className="flex items-center gap-1">
                          {skill}
                          <X className="h-3 w-3 cursor-pointer" onClick={() => removeSkill(skill)} />
                        </Badge>
                      ))}
                    </div>
                    <div className="flex gap-2">
                      <Input
                        placeholder="Add required skill"
                        value={currentSkill}
                        onChange={(e) => setCurrentSkill(e.target.value)}
                        onKeyPress={(e) => e.key === "Enter" && addSkill()}
                      />
                      <Button type="button" onClick={addSkill} size="sm">
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>

                <div>
                  <Label>Required Parts</Label>
                  <div className="space-y-2 mt-2">
                    {requiredParts.length > 0 && (
                      <div className="space-y-2">
                        {requiredParts.map((part) => (
                          <div key={part.partId} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                            <div>
                              <span className="font-medium">{part.partName}</span>
                              <span className="text-muted-foreground ml-2">Qty: {part.quantity}</span>
                              {part.estimatedCost > 0 && (
                                <span className="text-muted-foreground ml-2">${part.estimatedCost}</span>
                              )}
                              {part.isOptional && (
                                <Badge variant="outline" className="ml-2 text-xs">Optional</Badge>
                              )}
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removePart(part.partId)}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}
                    <div className="grid grid-cols-4 gap-2">
                      <Input
                        placeholder="Part name"
                        value={currentPart.partName || ""}
                        onChange={(e) => setCurrentPart({ ...currentPart, partName: e.target.value })}
                      />
                      <Input
                        placeholder="Quantity"
                        type="number"
                        value={currentPart.quantity || ""}
                        onChange={(e) => setCurrentPart({ ...currentPart, quantity: parseInt(e.target.value) })}
                      />
                      <Input
                        placeholder="Cost"
                        type="number"
                        step="0.01"
                        value={currentPart.estimatedCost || ""}
                        onChange={(e) => setCurrentPart({ ...currentPart, estimatedCost: parseFloat(e.target.value) })}
                      />
                      <Button type="button" onClick={addPart} size="sm">
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>

                <div>
                  <Label htmlFor="instructions">Instructions</Label>
                  <Textarea
                    id="instructions"
                    placeholder="Detailed maintenance instructions"
                    value={scheduleForm.instructions || ""}
                    onChange={(e) => setScheduleForm({ ...scheduleForm, instructions: e.target.value })}
                    rows={4}
                  />
                </div>

                <div>
                  <Label>Checklist Items</Label>
                  <div className="space-y-2 mt-2">
                    {checklistItems.length > 0 && (
                      <div className="space-y-2">
                        {checklistItems.map((item) => (
                          <div key={item.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                            <div className="flex items-center space-x-2">
                              <span>{item.description}</span>
                              {item.isRequired && (
                                <Badge variant="destructive" className="text-xs">Required</Badge>
                              )}
                              {item.category && (
                                <Badge variant="outline" className="text-xs">{item.category}</Badge>
                              )}
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeChecklistItem(item.id)}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}
                    <div className="grid grid-cols-4 gap-2">
                      <Input
                        placeholder="Checklist item description"
                        value={currentChecklistItem.description || ""}
                        onChange={(e) => setCurrentChecklistItem({ ...currentChecklistItem, description: e.target.value })}
                      />
                      <Input
                        placeholder="Category"
                        value={currentChecklistItem.category || ""}
                        onChange={(e) => setCurrentChecklistItem({ ...currentChecklistItem, category: e.target.value })}
                      />
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={currentChecklistItem.isRequired || false}
                          onCheckedChange={(checked) => setCurrentChecklistItem({ ...currentChecklistItem, isRequired: checked })}
                        />
                        <Label className="text-sm">Required</Label>
                      </div>
                      <Button type="button" onClick={addChecklistItem} size="sm">
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    checked={scheduleForm.isActive !== false}
                    onCheckedChange={(checked) => setScheduleForm({ ...scheduleForm, isActive: checked })}
                  />
                  <Label>Active</Label>
                </div>
              </div>

              <DialogFooter>
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={saveSchedule}>
                  Save Schedule
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {schedules.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Wrench className="mx-auto h-12 w-12 mb-4 opacity-50" />
            <p>No maintenance schedules defined yet</p>
            <p className="text-sm">Add maintenance schedules to ensure proper asset care</p>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Schedule</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Frequency</TableHead>
                <TableHead>Priority</TableHead>
                <TableHead>Duration</TableHead>
                <TableHead>Cost</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {schedules.map((schedule) => (
                <TableRow key={schedule.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{schedule.name}</div>
                      <div className="text-sm text-muted-foreground">{schedule.description}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{schedule.type}</Badge>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm">{formatFrequency(schedule.frequency)}</span>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: getPriorityColor(schedule.priority) }}
                      />
                      <span className="capitalize">{schedule.priority}</span>
                    </div>
                  </TableCell>
                  <TableCell>{schedule.estimatedDuration} min</TableCell>
                  <TableCell>${schedule.estimatedCost}</TableCell>
                  <TableCell>
                    {schedule.isActive ? (
                      <Badge variant="default" className="text-xs">Active</Badge>
                    ) : (
                      <Badge variant="secondary" className="text-xs">Inactive</Badge>
                    )}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end space-x-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openDialog(schedule)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => deleteSchedule(schedule.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  )
}