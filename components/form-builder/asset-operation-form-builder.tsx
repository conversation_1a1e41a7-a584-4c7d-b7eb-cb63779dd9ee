"use client";

import React, { useState, useEffect } from "react";
import { Form<PERSON>uilder, FormDefinition } from "./form-builder";
import { CustomField } from "@/lib/modules/asset-types/types";
import { AssetOperationType, ASSET_OPERATION_CONFIGS } from "@/lib/types/asset-type-forms";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { <PERSON>roll<PERSON><PERSON> } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { 
  Save, 
  Eye, 
  Settings, 
  Plus, 
  FileText, 
  Layers,
  CheckCircle,
  AlertCircle,
  Info
} from "lucide-react";

interface AssetOperationFormBuilderProps {
  assetTypeId: string;
  assetTypeName: string;
  availableFields: CustomField[];
  onSave: (form: FormDefinition, operationType: AssetOperationType) => Promise<void>;
  onPreview: (form: FormDefinition, operationType: AssetOperationType) => void;
  initialForms?: Record<AssetOperationType, FormDefinition>;
}

export function AssetOperationFormBuilder({
  assetTypeId,
  assetTypeName,
  availableFields,
  onSave,
  onPreview,
  initialForms = {},
}: AssetOperationFormBuilderProps) {
  const [selectedOperation, setSelectedOperation] = useState<AssetOperationType>("asset.create");
  const [forms, setForms] = useState<Record<AssetOperationType, FormDefinition>>(initialForms);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState<"idle" | "saving" | "saved" | "error">("idle");

  const operationConfig = ASSET_OPERATION_CONFIGS[selectedOperation];
  const currentForm = forms[selectedOperation];

  // Initialize form for selected operation if it doesn't exist
  useEffect(() => {
    if (!currentForm) {
      const defaultForm: FormDefinition = {
        id: `form-${assetTypeId}-${selectedOperation}-${Date.now()}`,
        name: `${assetTypeName} - ${operationConfig.displayName}`,
        description: operationConfig.description,
        sections: [
          {
            id: `section-${Date.now()}`,
            title: "Basic Information",
            description: "Required information for this operation",
            columns: 1,
            fields: operationConfig.requiredFields.slice(0, 3), // Start with first 3 required fields
          },
        ],
        settings: {
          ...operationConfig.defaultSettings,
          layout: "standard",
          labelPosition: "top",
          showProgressBar: true,
          allowSaveAsDraft: true,
          confirmOnCancel: true,
        },
      };

      setForms(prev => ({
        ...prev,
        [selectedOperation]: defaultForm,
      }));
    }
  }, [selectedOperation, assetTypeId, assetTypeName, operationConfig, currentForm]);

  const handleFormSave = (updatedForm: FormDefinition) => {
    setForms(prev => ({
      ...prev,
      [selectedOperation]: updatedForm,
    }));
  };

  const handleFormPreview = (form: FormDefinition) => {
    onPreview(form, selectedOperation);
    setIsPreviewOpen(true);
  };

  const handleFormExport = (form: FormDefinition) => {
    const dataStr = JSON.stringify(form, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const exportFileDefaultName = `${assetTypeName}-${selectedOperation}-form.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  const handleSaveToDatabase = async () => {
    if (!currentForm) return;

    setIsSaving(true);
    setSaveStatus("saving");

    try {
      await onSave(currentForm, selectedOperation);
      setSaveStatus("saved");
      setTimeout(() => setSaveStatus("idle"), 2000);
    } catch (error) {
      console.error("Error saving form:", error);
      setSaveStatus("error");
      setTimeout(() => setSaveStatus("idle"), 3000);
    } finally {
      setIsSaving(false);
    }
  };

  const handleAddField = () => {
    // This would open a field selector dialog
    console.log("Add field clicked");
  };

  const getOperationIcon = (operationType: AssetOperationType) => {
    const config = ASSET_OPERATION_CONFIGS[operationType];
    // Return appropriate icon based on config.icon
    return <FileText className="h-4 w-4" />;
  };

  const getOperationColor = (operationType: AssetOperationType) => {
    const config = ASSET_OPERATION_CONFIGS[operationType];
    return config.color;
  };

  const getSaveStatusIcon = () => {
    switch (saveStatus) {
      case "saving":
        return <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>;
      case "saved":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "error":
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return null;
    }
  };

  const getSaveStatusText = () => {
    switch (saveStatus) {
      case "saving":
        return "Saving...";
      case "saved":
        return "Saved";
      case "error":
        return "Error saving";
      default:
        return "Save Form";
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Asset Operation Forms</h2>
          <p className="text-muted-foreground">
            Configure forms for different asset operations for {assetTypeName}
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            onClick={handleSaveToDatabase}
            disabled={!currentForm || isSaving}
            className="flex items-center gap-2"
          >
            {getSaveStatusIcon()}
            {getSaveStatusText()}
          </Button>
        </div>
      </div>

      {/* Operation Selector */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Layers className="h-5 w-5" />
            Select Operation Type
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(ASSET_OPERATION_CONFIGS).map(([operationType, config]) => (
              <Card
                key={operationType}
                className={`cursor-pointer transition-all hover:shadow-md ${
                  selectedOperation === operationType
                    ? "ring-2 ring-primary border-primary"
                    : "hover:border-primary/50"
                }`}
                onClick={() => setSelectedOperation(operationType as AssetOperationType)}
              >
                <CardContent className="p-4">
                  <div className="flex items-start gap-3">
                    <div
                      className="p-2 rounded-lg"
                      style={{ backgroundColor: `${config.color}20`, color: config.color }}
                    >
                      {getOperationIcon(operationType as AssetOperationType)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium text-sm">{config.displayName}</h3>
                      <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                        {config.description}
                      </p>
                      <div className="flex items-center gap-2 mt-2">
                        <Badge variant="secondary" className="text-xs">
                          {config.requiredFields.length} required
                        </Badge>
                        {forms[operationType as AssetOperationType] && (
                          <Badge variant="outline" className="text-xs">
                            Configured
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Form Builder */}
      {currentForm && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <div
                    className="p-2 rounded-lg"
                    style={{ 
                      backgroundColor: `${operationConfig.color}20`, 
                      color: operationConfig.color 
                    }}
                  >
                    {getOperationIcon(selectedOperation)}
                  </div>
                  {operationConfig.displayName} Form
                </CardTitle>
                <p className="text-sm text-muted-foreground mt-1">
                  {operationConfig.description}
                </p>
              </div>
              
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleFormPreview(currentForm)}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  Preview
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleFormExport(currentForm)}
                >
                  <Save className="h-4 w-4 mr-2" />
                  Export
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {/* Operation Requirements Info */}
            <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
              <div className="flex items-start gap-3">
                <Info className="h-5 w-5 text-blue-600 mt-0.5" />
                <div className="flex-1">
                  <h4 className="font-medium text-blue-900 dark:text-blue-100">
                    Operation Requirements
                  </h4>
                  <div className="mt-2 space-y-2">
                    <div>
                      <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
                        Required Fields:
                      </span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {operationConfig.requiredFields.map(field => (
                          <Badge key={field} variant="secondary" className="text-xs">
                            {field}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    {operationConfig.optionalFields.length > 0 && (
                      <div>
                        <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
                          Optional Fields:
                        </span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {operationConfig.optionalFields.map(field => (
                            <Badge key={field} variant="outline" className="text-xs">
                              {field}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Form Builder Component */}
            <FormBuilder
              initialForm={currentForm}
              availableFields={availableFields}
              onSave={handleFormSave}
              onPreview={handleFormPreview}
              onExport={handleFormExport}
              onAddField={handleAddField}
            />
          </CardContent>
        </Card>
      )}

      {/* Form Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Configuration Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(ASSET_OPERATION_CONFIGS).map(([operationType, config]) => {
              const form = forms[operationType as AssetOperationType];
              const isConfigured = !!form;
              
              return (
                <div
                  key={operationType}
                  className="flex items-center justify-between p-3 border rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    <div
                      className="p-1.5 rounded"
                      style={{ 
                        backgroundColor: `${config.color}20`, 
                        color: config.color 
                      }}
                    >
                      {getOperationIcon(operationType as AssetOperationType)}
                    </div>
                    <div>
                      <p className="font-medium text-sm">{config.displayName}</p>
                      <p className="text-xs text-muted-foreground">
                        {form?.sections.length || 0} sections
                      </p>
                    </div>
                  </div>
                  
                  <Badge variant={isConfigured ? "default" : "secondary"}>
                    {isConfigured ? "Configured" : "Not Set"}
                  </Badge>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}