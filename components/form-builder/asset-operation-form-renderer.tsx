"use client";

import React, { useState, useEffect } from "react";
import { FormRenderer } from "./form-renderer";
import { FormDefinition } from "./form-builder";
import { AssetOperationType, FormContext, ASSET_OPERATION_CONFIGS } from "@/lib/types/asset-type-forms";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { 
  Save, 
  Send, 
  AlertCircle, 
  CheckCircle, 
  Info,
  FileText,
  Clock,
  User
} from "lucide-react";

interface AssetOperationFormRendererProps {
  assetTypeId: string;
  operationType: AssetOperationType;
  context: FormContext;
  onSubmit: (data: Record<string, any>) => Promise<void>;
  onSaveDraft?: (data: Record<string, any>) => Promise<void>;
  onCancel?: () => void;
  className?: string;
}

interface FormState {
  form: FormDefinition | null;
  prePopulationData: Record<string, any>;
  isLoading: boolean;
  isSubmitting: boolean;
  isSavingDraft: boolean;
  errors: string[];
  warnings: string[];
  validationErrors: Record<string, string[]>;
}

export function AssetOperationFormRenderer({
  assetTypeId,
  operationType,
  context,
  onSubmit,
  onSaveDraft,
  onCancel,
  className,
}: AssetOperationFormRendererProps) {
  const [formState, setFormState] = useState<FormState>({
    form: null,
    prePopulationData: {},
    isLoading: true,
    isSubmitting: false,
    isSavingDraft: false,
    errors: [],
    warnings: [],
    validationErrors: {},
  });

  const [formData, setFormData] = useState<Record<string, any>>({});
  const [isDirty, setIsDirty] = useState(false);

  const operationConfig = ASSET_OPERATION_CONFIGS[operationType];

  // Load form and pre-population data
  useEffect(() => {
    loadForm();
  }, [assetTypeId, operationType, context]);

  const loadForm = async () => {
    try {
      setFormState(prev => ({ ...prev, isLoading: true, errors: [] }));

      const params = new URLSearchParams({
        assetTypeId,
        operationType,
        userId: context.userId,
        userRole: context.userRole,
      });

      if (context.assetId) {
        params.append("assetId", context.assetId);
      }

      const response = await fetch(`/api/asset-type-forms?${params}`);
      
      if (!response.ok) {
        throw new Error("Failed to load form");
      }

      const { form, prePopulationData } = await response.json();

      setFormState(prev => ({
        ...prev,
        form,
        prePopulationData,
        isLoading: false,
      }));

      setFormData(prePopulationData);

    } catch (error) {
      console.error("Error loading form:", error);
      setFormState(prev => ({
        ...prev,
        isLoading: false,
        errors: ["Failed to load form. Please try again."],
      }));
    }
  };

  const handleFormDataChange = (data: Record<string, any>) => {
    setFormData(data);
    setIsDirty(true);
    
    // Clear validation errors for changed fields
    const changedFields = Object.keys(data).filter(
      key => data[key] !== formState.prePopulationData[key]
    );
    
    if (changedFields.length > 0) {
      setFormState(prev => ({
        ...prev,
        validationErrors: Object.fromEntries(
          Object.entries(prev.validationErrors).filter(
            ([key]) => !changedFields.includes(key)
          )
        ),
      }));
    }
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string[]> = {};
    let isValid = true;

    // Validate required fields
    operationConfig.requiredFields.forEach(fieldName => {
      if (!formData[fieldName] || formData[fieldName] === "") {
        errors[fieldName] = [`${fieldName} is required`];
        isValid = false;
      }
    });

    // Additional validation based on operation type
    switch (operationType) {
      case "asset.create":
        if (formData.purchasePrice && Number(formData.purchasePrice) <= 0) {
          errors.purchasePrice = ["Purchase price must be greater than 0"];
          isValid = false;
        }
        break;

      case "asset.transfer":
        if (formData.transferDate && new Date(formData.transferDate) > new Date()) {
          errors.transferDate = ["Transfer date cannot be in the future"];
          isValid = false;
        }
        break;

      case "maintenance.log":
        if (formData.completedDate && new Date(formData.completedDate) > new Date()) {
          errors.completedDate = ["Completion date cannot be in the future"];
          isValid = false;
        }
        break;
    }

    setFormState(prev => ({ ...prev, validationErrors: errors }));
    return isValid;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setFormState(prev => ({ ...prev, isSubmitting: true, errors: [] }));

    try {
      await onSubmit(formData);
      setIsDirty(false);
    } catch (error) {
      console.error("Error submitting form:", error);
      setFormState(prev => ({
        ...prev,
        errors: ["Failed to submit form. Please try again."],
      }));
    } finally {
      setFormState(prev => ({ ...prev, isSubmitting: false }));
    }
  };

  const handleSaveDraft = async () => {
    if (!onSaveDraft) return;

    setFormState(prev => ({ ...prev, isSavingDraft: true, errors: [] }));

    try {
      await onSaveDraft(formData);
      setIsDirty(false);
    } catch (error) {
      console.error("Error saving draft:", error);
      setFormState(prev => ({
        ...prev,
        errors: ["Failed to save draft. Please try again."],
      }));
    } finally {
      setFormState(prev => ({ ...prev, isSavingDraft: false }));
    }
  };

  const handleCancel = () => {
    if (isDirty && !confirm("You have unsaved changes. Are you sure you want to cancel?")) {
      return;
    }
    
    if (onCancel) {
      onCancel();
    }
  };

  const getOperationIcon = () => {
    return <FileText className="h-5 w-5" />;
  };

  if (formState.isLoading) {
    return (
      <Card className={className}>
        <CardContent className="p-8">
          <div className="flex items-center justify-center space-y-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <p className="text-muted-foreground">Loading form...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!formState.form) {
    return (
      <Card className={className}>
        <CardContent className="p-8">
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              No form is configured for this operation. Please contact your administrator.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div
                className="p-2 rounded-lg"
                style={{ 
                  backgroundColor: `${operationConfig.color}20`, 
                  color: operationConfig.color 
                }}
              >
                {getOperationIcon()}
              </div>
              <div>
                <CardTitle>{operationConfig.displayName}</CardTitle>
                <p className="text-sm text-muted-foreground">
                  {operationConfig.description}
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="flex items-center gap-1">
                <User className="h-3 w-3" />
                {context.userRole}
              </Badge>
              {context.assetId && (
                <Badge variant="secondary">
                  Editing Asset
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Errors and Warnings */}
      {formState.errors.length > 0 && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <ul className="list-disc list-inside space-y-1">
              {formState.errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      )}

      {formState.warnings.length > 0 && (
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            <ul className="list-disc list-inside space-y-1">
              {formState.warnings.map((warning, index) => (
                <li key={index}>{warning}</li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      )}

      {/* Validation Errors */}
      {Object.keys(formState.validationErrors).length > 0 && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <p className="font-medium mb-2">Please fix the following errors:</p>
            <ul className="list-disc list-inside space-y-1">
              {Object.entries(formState.validationErrors).map(([field, errors]) =>
                errors.map((error, index) => (
                  <li key={`${field}-${index}`}>{error}</li>
                ))
              )}
            </ul>
          </AlertDescription>
        </Alert>
      )}

      {/* Form */}
      <Card>
        <CardContent className="p-6">
          <FormRenderer
            form={formState.form}
            data={formData}
            onChange={handleFormDataChange}
            validationErrors={formState.validationErrors}
          />
        </CardContent>
      </Card>

      {/* Actions */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {isDirty && (
                <Badge variant="outline" className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  Unsaved changes
                </Badge>
              )}
            </div>
            
            <div className="flex items-center gap-2">
              {onCancel && (
                <Button
                  variant="outline"
                  onClick={handleCancel}
                  disabled={formState.isSubmitting || formState.isSavingDraft}
                >
                  {formState.form.settings.cancelButtonText || "Cancel"}
                </Button>
              )}
              
              {onSaveDraft && formState.form.settings.allowSaveAsDraft && (
                <Button
                  variant="outline"
                  onClick={handleSaveDraft}
                  disabled={formState.isSubmitting || formState.isSavingDraft}
                  className="flex items-center gap-2"
                >
                  {formState.isSavingDraft ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                  ) : (
                    <Save className="h-4 w-4" />
                  )}
                  Save Draft
                </Button>
              )}
              
              <Button
                onClick={handleSubmit}
                disabled={formState.isSubmitting || formState.isSavingDraft}
                className="flex items-center gap-2"
              >
                {formState.isSubmitting ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                ) : (
                  <Send className="h-4 w-4" />
                )}
                {formState.form.settings.submitButtonText || "Submit"}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}