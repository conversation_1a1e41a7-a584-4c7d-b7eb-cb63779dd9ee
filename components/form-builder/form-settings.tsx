"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { FormDefinition } from "./form-builder";

interface FormSettingsProps {
  settings: FormDefinition["settings"];
  onChange: (settings: FormDefinition["settings"]) => void;
}

export function FormSettings({ settings, onChange }: FormSettingsProps) {
  const handleChange = <K extends keyof FormDefinition["settings"]>(
    key: K,
    value: FormDefinition["settings"][K]
  ) => {
    onChange({
      ...settings,
      [key]: value,
    });
  };

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card className="h-fit">
          <CardHeader className="py-3">
            <CardTitle className="text-base">Form Appearance</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3 py-2">
            <div className="space-y-2">
              <Label htmlFor="layout" className="text-sm">Layout Style</Label>
              <Select
                value={settings.layout}
                onValueChange={(value) => handleChange("layout", value as any)}
              >
                <SelectTrigger id="layout" className="h-9">
                  <SelectValue placeholder="Select layout" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="standard">Standard</SelectItem>
                  <SelectItem value="compact">Compact</SelectItem>
                  <SelectItem value="spacious">Spacious</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="labelPosition" className="text-sm">Label Position</Label>
              <Select
                value={settings.labelPosition}
                onValueChange={(value) => handleChange("labelPosition", value as any)}
              >
                <SelectTrigger id="labelPosition" className="h-9">
                  <SelectValue placeholder="Select label position" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="top">Top</SelectItem>
                  <SelectItem value="left">Left</SelectItem>
                  <SelectItem value="floating">Floating</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        <Card className="h-fit">
          <CardHeader className="py-3">
            <CardTitle className="text-base">Button Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3 py-2">
            <div className="space-y-2">
              <Label htmlFor="submitButtonText" className="text-sm">Submit Button Text</Label>
              <Input
                id="submitButtonText"
                value={settings.submitButtonText}
                onChange={(e) => handleChange("submitButtonText", e.target.value)}
                className="h-9"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="cancelButtonText" className="text-sm">Cancel Button Text</Label>
              <Input
                id="cancelButtonText"
                value={settings.cancelButtonText}
                onChange={(e) => handleChange("cancelButtonText", e.target.value)}
                className="h-9"
              />
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader className="py-3">
          <CardTitle className="text-base">Form Behavior</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 py-2">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center justify-between border rounded-md p-3">
              <div>
                <Label htmlFor="showProgressBar" className="text-sm font-medium">Show Progress Bar</Label>
                <p className="text-xs text-muted-foreground">
                  Display a progress bar for multi-section forms
                </p>
              </div>
              <Switch
                id="showProgressBar"
                checked={settings.showProgressBar}
                onCheckedChange={(checked) => handleChange("showProgressBar", checked)}
              />
            </div>
            <div className="flex items-center justify-between border rounded-md p-3">
              <div>
                <Label htmlFor="allowSaveAsDraft" className="text-sm font-medium">Allow Save as Draft</Label>
                <p className="text-xs text-muted-foreground">
                  Enable users to save their progress and continue later
                </p>
              </div>
              <Switch
                id="allowSaveAsDraft"
                checked={settings.allowSaveAsDraft}
                onCheckedChange={(checked) => handleChange("allowSaveAsDraft", checked)}
              />
            </div>
            <div className="flex items-center justify-between border rounded-md p-3">
              <div>
                <Label htmlFor="confirmOnCancel" className="text-sm font-medium">Confirm on Cancel</Label>
                <p className="text-xs text-muted-foreground">
                  Ask for confirmation when users try to cancel the form
                </p>
              </div>
              <Switch
                id="confirmOnCancel"
                checked={settings.confirmOnCancel}
                onCheckedChange={(checked) => handleChange("confirmOnCancel", checked)}
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}