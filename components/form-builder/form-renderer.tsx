"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CustomField } from "@/lib/modules/asset-types/types";
import { FormDefinition } from "./form-builder";
import { DynamicField } from "@/components/custom-fields/form-generator";
import { CustomFieldsService } from "@/lib/modules/custom-fields/service";

interface FormRendererProps {
  form: FormDefinition;
  fields: CustomField[];
  values: Record<string, any>;
  onChange: (values: Record<string, any>) => void;
  onSubmit?: (values: Record<string, any>) => void;
  onCancel?: () => void;
  onSaveDraft?: (values: Record<string, any>) => void;
  readOnly?: boolean;
  errors?: Record<string, string>;
}

export function FormRenderer({
  form,
  fields,
  values,
  onChange,
  onSubmit,
  onCancel,
  onSaveDraft,
  readOnly = false,
  errors = {},
}: FormRendererProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [progress, setProgress] = useState(0);
  const [showConfirmCancel, setShowConfirmCancel] = useState(false);
  
  const service = CustomFieldsService.getInstance();
  
  // Calculate progress
  useEffect(() => {
    if (form.sections.length > 0) {
      setProgress(((currentStep + 1) / form.sections.length) * 100);
    }
  }, [currentStep, form.sections.length]);

  // Handle field value change
  const handleFieldChange = (fieldId: string, value: any) => {
    // Clear validation error when field is changed
    if (validationErrors[fieldId]) {
      setValidationErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[fieldId];
        return newErrors;
      });
    }
    
    onChange({
      ...values,
      [fieldId]: value,
    });
  };

  // Validate the current section
  const validateSection = (sectionIndex: number): boolean => {
    const section = form.sections[sectionIndex];
    if (!section) return true;
    
    const sectionFields = section.fields
      .map((fieldId) => fields.find((f) => f.id === fieldId))
      .filter((f): f is CustomField => f !== undefined);
    
    const errors: Record<string, string> = {};
    let isValid = true;
    
    sectionFields.forEach((field) => {
      if (field.isRequired && (values[field.id] === undefined || values[field.id] === null || values[field.id] === "")) {
        errors[field.id] = `${field.label} is required`;
        isValid = false;
        return;
      }
      
      if (values[field.id] !== undefined && values[field.id] !== null && values[field.id] !== "") {
        const validationResult = service.validateFieldValue(values[field.id], field);
        if (!validationResult.isValid) {
          errors[field.id] = validationResult.error || `Invalid value for ${field.label}`;
          isValid = false;
        }
      }
    });
    
    setValidationErrors(errors);
    return isValid;
  };

  // Handle next step
  const handleNext = () => {
    if (validateSection(currentStep)) {
      if (currentStep < form.sections.length - 1) {
        setCurrentStep(currentStep + 1);
      } else {
        handleSubmit();
      }
    }
  };

  // Handle previous step
  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Handle form submission
  const handleSubmit = () => {
    // Validate all sections
    let isValid = true;
    const allErrors: Record<string, string> = {};
    
    form.sections.forEach((section, index) => {
      if (!validateSection(index)) {
        isValid = false;
        Object.assign(allErrors, validationErrors);
      }
    });
    
    if (isValid) {
      onSubmit?.(values);
    } else {
      setValidationErrors(allErrors);
      // Find the first section with errors and navigate to it
      for (let i = 0; i < form.sections.length; i++) {
        const section = form.sections[i];
        const hasError = section.fields.some((fieldId) => allErrors[fieldId]);
        if (hasError) {
          setCurrentStep(i);
          break;
        }
      }
    }
  };

  // Handle cancel
  const handleCancel = () => {
    if (form.settings.confirmOnCancel) {
      setShowConfirmCancel(true);
    } else {
      onCancel?.();
    }
  };

  // Confirm cancel
  const confirmCancel = () => {
    setShowConfirmCancel(false);
    onCancel?.();
  };

  // Cancel the cancel confirmation
  const cancelConfirmation = () => {
    setShowConfirmCancel(false);
  };

  // Handle save as draft
  const handleSaveDraft = () => {
    onSaveDraft?.(values);
  };

  // Get fields for the current section
  const getCurrentSectionFields = (): CustomField[] => {
    if (currentStep >= form.sections.length) return [];
    
    const section = form.sections[currentStep];
    return section.fields
      .map((fieldId) => fields.find((f) => f.id === fieldId))
      .filter((f): f is CustomField => f !== undefined);
  };

  // Determine if we're on the last step
  const isLastStep = currentStep === form.sections.length - 1;

  // Apply form layout styles
  const getLayoutClasses = () => {
    switch (form.settings.layout) {
      case "compact":
        return "space-y-3";
      case "spacious":
        return "space-y-6";
      default:
        return "space-y-4";
    }
  };

  // Apply label position styles
  const getLabelPositionClasses = () => {
    switch (form.settings.labelPosition) {
      case "left":
        return "grid grid-cols-3 gap-4 items-start";
      case "floating":
        return "relative";
      default:
        return "";
    }
  };

  return (
    <div className="space-y-4">
      {form.settings.showProgressBar && form.sections.length > 1 && (
        <div className="space-y-1">
          <div className="flex justify-between text-xs">
            <span>Step {currentStep + 1} of {form.sections.length}</span>
            <span>{Math.round(progress)}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      )}

      {showConfirmCancel && (
        <Alert variant="destructive" className="py-2">
          <AlertDescription className="flex justify-between items-center text-sm">
            <span>Are you sure you want to cancel? All unsaved changes will be lost.</span>
            <div className="space-x-2">
              <Button variant="outline" size="sm" onClick={cancelConfirmation} className="h-7 text-xs">
                No, Continue
              </Button>
              <Button variant="destructive" size="sm" onClick={confirmCancel} className="h-7 text-xs">
                Yes, Cancel
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      )}

      <div>
        <h2 className="text-lg font-semibold">{form.sections[currentStep]?.title}</h2>
        {form.sections[currentStep]?.description && (
          <p className="text-sm text-muted-foreground mb-2">{form.sections[currentStep].description}</p>
        )}
        <Separator className="mb-4" />

        <div className={getLayoutClasses()}>
          <div className={`grid grid-cols-${form.sections[currentStep]?.columns || 1} gap-3`}>
            {getCurrentSectionFields().map((field) => (
              <div key={field.id} className={getLabelPositionClasses()}>
                <DynamicField
                  field={field}
                  value={values[field.id]}
                  onChange={(value) => handleFieldChange(field.id, value)}
                  error={validationErrors[field.id] || errors[field.id]}
                  readOnly={readOnly}
                />
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="flex justify-between pt-3">
        <div>
          {currentStep > 0 && (
            <Button variant="outline" size="sm" onClick={handlePrevious} className="h-9">
              Previous
            </Button>
          )}
        </div>
        <div className="space-x-2">
          {onCancel && (
            <Button variant="outline" size="sm" onClick={handleCancel} className="h-9">
              {form.settings.cancelButtonText}
            </Button>
          )}
          {form.settings.allowSaveAsDraft && onSaveDraft && (
            <Button variant="secondary" size="sm" onClick={handleSaveDraft} className="h-9">
              Save as Draft
            </Button>
          )}
          <Button size="sm" onClick={handleNext} className="h-9">
            {isLastStep ? form.settings.submitButtonText : "Next"}
          </Button>
        </div>
      </div>
    </div>
  );
}