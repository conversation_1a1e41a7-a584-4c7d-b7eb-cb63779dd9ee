"use client";

import React, { useState, useEffect } from "react";
import { AssetOperationFormRenderer } from "@/components/form-builder/asset-operation-form-renderer";
import { AssetOperationType, ASSET_OPERATION_CONFIGS } from "@/lib/types/asset-type-forms";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Scroll<PERSON><PERSON> } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { 
  Plus,
  FileText,
  Clock,
  CheckCircle,
  AlertCircle,
  TrendingUp,
  Activity,
  Search,
  Filter,
  Calendar,
  User,
  MapPin,
  Building
} from "lucide-react";

interface AssetOperationDashboardProps {
  assetId?: string;
  assetTypeId?: string;
  userId: string;
  userRole: string;
  location?: string;
  department?: string;
}

interface OperationHistory {
  id: string;
  operationType: AssetOperationType;
  status: string;
  performedAt: string;
  performedBy: string;
  asset: {
    id: string;
    name: string;
    category: string;
    assetType: {
      id: string;
      name: string;
      code: string;
    };
  };
  formData: Record<string, any>;
}

interface OperationStats {
  totalOperations: number;
  operationsByType: Record<AssetOperationType, number>;
  recentOperations: OperationHistory[];
  pendingOperations: number;
  completedOperations: number;
  failedOperations: number;
}

export function AssetOperationDashboard({
  assetId,
  assetTypeId,
  userId,
  userRole,
  location,
  department,
}: AssetOperationDashboardProps) {
  const [selectedOperation, setSelectedOperation] = useState<AssetOperationType | null>(null);
  const [selectedAssetType, setSelectedAssetType] = useState<string>(assetTypeId || "");
  const [operationHistory, setOperationHistory] = useState<OperationHistory[]>([]);
  const [operationStats, setOperationStats] = useState<OperationStats | null>(null);
  const [assetTypes, setAssetTypes] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState<AssetOperationType | "all">("all");

  useEffect(() => {
    loadData();
  }, [assetId, assetTypeId]);

  const loadData = async () => {
    try {
      setIsLoading(true);

      // Load asset types
      const assetTypesResponse = await fetch("/api/asset-types");
      if (assetTypesResponse.ok) {
        const assetTypesData = await assetTypesResponse.json();
        setAssetTypes(assetTypesData);
      }

      // Load operation history
      const historyParams = new URLSearchParams();
      if (assetId) historyParams.append("assetId", assetId);
      if (userId) historyParams.append("userId", userId);

      const historyResponse = await fetch(`/api/asset-operations?${historyParams}`);
      if (historyResponse.ok) {
        const historyData = await historyResponse.json();
        setOperationHistory(historyData.operations);
        
        // Calculate stats
        const stats = calculateStats(historyData.operations);
        setOperationStats(stats);
      }

    } catch (error) {
      console.error("Error loading dashboard data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const calculateStats = (operations: OperationHistory[]): OperationStats => {
    const operationsByType = {} as Record<AssetOperationType, number>;
    let completedOperations = 0;
    let failedOperations = 0;
    let pendingOperations = 0;

    operations.forEach(op => {
      operationsByType[op.operationType] = (operationsByType[op.operationType] || 0) + 1;
      
      switch (op.status) {
        case "completed":
          completedOperations++;
          break;
        case "failed":
          failedOperations++;
          break;
        case "pending":
        case "in_progress":
          pendingOperations++;
          break;
      }
    });

    return {
      totalOperations: operations.length,
      operationsByType,
      recentOperations: operations.slice(0, 10),
      pendingOperations,
      completedOperations,
      failedOperations,
    };
  };

  const handleOperationSubmit = async (data: Record<string, any>) => {
    if (!selectedOperation || !selectedAssetType) return;

    try {
      const response = await fetch("/api/asset-operations", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          operationType: selectedOperation,
          assetId,
          assetTypeId: selectedAssetType,
          formData: data,
          context: {
            assetId,
            assetTypeId: selectedAssetType,
            operationType: selectedOperation,
            userId,
            userRole,
            location,
            department,
          },
        }),
      });

      if (response.ok) {
        const result = await response.json();
        console.log("Operation completed:", result);
        
        // Refresh data
        await loadData();
        
        // Close form
        setIsFormOpen(false);
        setSelectedOperation(null);
      } else {
        const error = await response.json();
        console.error("Operation failed:", error);
      }

    } catch (error) {
      console.error("Error submitting operation:", error);
    }
  };

  const getOperationIcon = (operationType: AssetOperationType) => {
    const config = ASSET_OPERATION_CONFIGS[operationType];
    return <FileText className="h-4 w-4" style={{ color: config.color }} />;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "failed":
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      case "pending":
      case "in_progress":
        return <Clock className="h-4 w-4 text-yellow-600" />;
      default:
        return <Activity className="h-4 w-4 text-gray-600" />;
    }
  };

  const filteredHistory = operationHistory.filter(op => {
    const matchesSearch = searchTerm === "" || 
      op.asset.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      op.operationType.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFilter = filterType === "all" || op.operationType === filterType;
    
    return matchesSearch && matchesFilter;
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <span className="ml-2">Loading dashboard...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Asset Operations</h2>
          <p className="text-muted-foreground">
            Manage and track asset operations and workflows
          </p>
        </div>
        
        <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              New Operation
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[90vh]">
            <DialogHeader>
              <DialogTitle>Create New Operation</DialogTitle>
            </DialogHeader>
            
            {!selectedOperation ? (
              <div className="space-y-4">
                <div>
                  <Label htmlFor="assetType">Asset Type</Label>
                  <Select value={selectedAssetType} onValueChange={setSelectedAssetType}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select asset type" />
                    </SelectTrigger>
                    <SelectContent>
                      {assetTypes.map(type => (
                        <SelectItem key={type.id} value={type.id}>
                          {type.name} ({type.code})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label>Operation Type</Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-2">
                    {Object.entries(ASSET_OPERATION_CONFIGS).map(([operationType, config]) => (
                      <Card
                        key={operationType}
                        className="cursor-pointer transition-all hover:shadow-md hover:border-primary/50"
                        onClick={() => setSelectedOperation(operationType as AssetOperationType)}
                      >
                        <CardContent className="p-4">
                          <div className="flex items-center gap-3">
                            <div
                              className="p-2 rounded-lg"
                              style={{ backgroundColor: `${config.color}20`, color: config.color }}
                            >
                              {getOperationIcon(operationType as AssetOperationType)}
                            </div>
                            <div>
                              <h4 className="font-medium text-sm">{config.displayName}</h4>
                              <p className="text-xs text-muted-foreground">{config.description}</p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              </div>
            ) : (
              <ScrollArea className="max-h-[70vh]">
                <AssetOperationFormRenderer
                  assetTypeId={selectedAssetType}
                  operationType={selectedOperation}
                  context={{
                    assetId,
                    assetTypeId: selectedAssetType,
                    operationType: selectedOperation,
                    userId,
                    userRole,
                    location,
                    department,
                  }}
                  onSubmit={handleOperationSubmit}
                  onCancel={() => {
                    setSelectedOperation(null);
                    setIsFormOpen(false);
                  }}
                />
              </ScrollArea>
            )}
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Cards */}
      {operationStats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Activity className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="text-sm font-medium">Total Operations</p>
                  <p className="text-2xl font-bold">{operationStats.totalOperations}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <div>
                  <p className="text-sm font-medium">Completed</p>
                  <p className="text-2xl font-bold">{operationStats.completedOperations}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Clock className="h-5 w-5 text-yellow-600" />
                <div>
                  <p className="text-sm font-medium">Pending</p>
                  <p className="text-2xl font-bold">{operationStats.pendingOperations}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5 text-red-600" />
                <div>
                  <p className="text-sm font-medium">Failed</p>
                  <p className="text-2xl font-bold">{operationStats.failedOperations}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content */}
      <Tabs defaultValue="history" className="space-y-4">
        <TabsList>
          <TabsTrigger value="history">Operation History</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="workflows">Workflows</TabsTrigger>
        </TabsList>

        {/* Operation History Tab */}
        <TabsContent value="history" className="space-y-4">
          {/* Filters */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search operations..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                
                <Select value={filterType} onValueChange={(value) => setFilterType(value as AssetOperationType | "all")}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Filter by type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Operations</SelectItem>
                    {Object.entries(ASSET_OPERATION_CONFIGS).map(([operationType, config]) => (
                      <SelectItem key={operationType} value={operationType}>
                        {config.displayName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* History List */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Operations</CardTitle>
            </CardHeader>
            <CardContent>
              {filteredHistory.length > 0 ? (
                <div className="space-y-4">
                  {filteredHistory.map((operation) => (
                    <div key={operation.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-2">
                          {getOperationIcon(operation.operationType)}
                          {getStatusIcon(operation.status)}
                        </div>
                        
                        <div>
                          <h4 className="font-medium">
                            {ASSET_OPERATION_CONFIGS[operation.operationType].displayName}
                          </h4>
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <span className="flex items-center gap-1">
                              <FileText className="h-3 w-3" />
                              {operation.asset.name}
                            </span>
                            <span className="flex items-center gap-1">
                              <User className="h-3 w-3" />
                              {operation.performedBy}
                            </span>
                            <span className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              {new Date(operation.performedAt).toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Badge variant={
                          operation.status === "completed" ? "default" :
                          operation.status === "failed" ? "destructive" :
                          "secondary"
                        }>
                          {operation.status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="font-medium mb-2">No Operations Found</h3>
                  <p className="text-sm text-muted-foreground">
                    {searchTerm || filterType !== "all" 
                      ? "Try adjusting your search or filter criteria"
                      : "No operations have been performed yet"
                    }
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Operation Analytics</CardTitle>
            </CardHeader>
            <CardContent>
              {operationStats ? (
                <div className="space-y-6">
                  <div>
                    <h4 className="font-medium mb-4">Operations by Type</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {Object.entries(operationStats.operationsByType).map(([operationType, count]) => {
                        const config = ASSET_OPERATION_CONFIGS[operationType as AssetOperationType];
                        return (
                          <div key={operationType} className="flex items-center justify-between p-3 border rounded-lg">
                            <div className="flex items-center gap-2">
                              <div
                                className="p-1.5 rounded"
                                style={{ backgroundColor: `${config.color}20`, color: config.color }}
                              >
                                {getOperationIcon(operationType as AssetOperationType)}
                              </div>
                              <span className="text-sm font-medium">{config.displayName}</span>
                            </div>
                            <Badge variant="outline">{count}</Badge>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <TrendingUp className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="font-medium mb-2">No Analytics Data</h3>
                  <p className="text-sm text-muted-foreground">
                    Analytics will appear once operations are performed
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Workflows Tab */}
        <TabsContent value="workflows" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Workflow Integration</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="font-medium mb-2">Workflow Management</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Configure automated workflows for asset operations
                </p>
                <Button variant="outline">
                  Configure Workflows
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}