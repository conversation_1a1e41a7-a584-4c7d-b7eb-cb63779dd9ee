"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  Brain,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Clock,
  DollarSign,
  Wrench,
  BarChart3,
  Lightbulb,
  Target,
  Zap,
  RefreshCw,
  Eye,
  ChevronRight
} from 'lucide-react';
import { cn } from '@/lib/utils';
import type { AIInsight, PredictiveModel, SmartAlert } from '@/lib/advanced-features/ai-engine/types';
import { AIEngineService } from '@/lib/advanced-features/ai-engine/services';

interface AIInsightsDashboardProps {
  className?: string;
}

export function AIInsightsDashboard({ className }: AIInsightsDashboardProps) {
  const [insights, setInsights] = useState<AIInsight[]>([]);
  const [models, setModels] = useState<PredictiveModel[]>([]);
  const [alerts, setAlerts] = useState<SmartAlert[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const aiService = AIEngineService.getInstance();

  useEffect(() => {
    loadAIData();
  }, []);

  const loadAIData = async () => {
    setIsLoading(true);
    try {
      // Load existing insights, models, and alerts
      const currentInsights = aiService.getInsights();
      const currentModels = aiService.getModels();
      const currentAlerts = aiService.getAlerts(true);

      // Generate some sample data if none exists
      if (currentInsights.length === 0) {
        await generateSampleInsights();
      }

      setInsights(aiService.getInsights());
      setModels(aiService.getModels());
      setAlerts(aiService.getAlerts(true));
    } catch (error) {
      console.error('Failed to load AI data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const generateSampleInsights = async () => {
    try {
      // Generate sample maintenance predictions
      await aiService.generateMaintenancePredictions('asset-001', [
        { date: '2023-12-01', event: 'maintenance', cost: 150 },
        { date: '2023-09-15', event: 'repair', cost: 300 }
      ]);

      // Generate sample cost optimization
      await aiService.generateCostOptimization([
        { id: '1', name: 'Asset 1', category: 'IT Equipment', costs: { maintenance: 1200, operation: 800, depreciation: 500 } },
        { id: '2', name: 'Asset 2', category: 'Machinery', costs: { maintenance: 2500, operation: 1500, depreciation: 1000 } }
      ]);

      // Generate sample anomaly detection
      await aiService.detectAnomalies('maintenance', [
        { assetId: '1', cost: 150, date: '2024-01-15' },
        { assetId: '2', cost: 2500, date: '2024-01-20' }
      ]);
    } catch (error) {
      console.error('Failed to generate sample insights:', error);
    }
  };

  const getInsightIcon = (type: string) => {
    const iconMap: Record<string, React.ReactNode> = {
      prediction: <TrendingUp className="h-4 w-4" />,
      recommendation: <Lightbulb className="h-4 w-4" />,
      anomaly: <AlertTriangle className="h-4 w-4" />,
      optimization: <Target className="h-4 w-4" />,
      maintenance: <Wrench className="h-4 w-4" />,
      cost_analysis: <DollarSign className="h-4 w-4" />
    };
    return iconMap[type] || <Brain className="h-4 w-4" />;
  };

  const getImpactColor = (impact: string) => {
    const colorMap: Record<string, string> = {
      low: 'text-green-600 bg-green-100 dark:bg-green-900/20',
      medium: 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20',
      high: 'text-orange-600 bg-orange-100 dark:bg-orange-900/20',
      critical: 'text-red-600 bg-red-100 dark:bg-red-900/20'
    };
    return colorMap[impact] || 'text-gray-600 bg-gray-100 dark:bg-gray-900/20';
  };

  const getSeverityColor = (severity: string) => {
    const colorMap: Record<string, string> = {
      info: 'text-blue-600 bg-blue-100 dark:bg-blue-900/20',
      warning: 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20',
      error: 'text-red-600 bg-red-100 dark:bg-red-900/20',
      critical: 'text-red-600 bg-red-100 dark:bg-red-900/20'
    };
    return colorMap[severity] || 'text-gray-600 bg-gray-100 dark:bg-gray-900/20';
  };

  const filteredInsights = selectedCategory === 'all' 
    ? insights 
    : insights.filter(insight => insight.category === selectedCategory);

  const categories = ['all', ...Array.from(new Set(insights.map(insight => insight.category)))];

  const criticalInsights = insights.filter(insight => insight.impact === 'critical').length;
  const actionableInsights = insights.filter(insight => insight.actionable).length;
  const avgConfidence = insights.length > 0 
    ? Math.round(insights.reduce((sum, insight) => sum + insight.confidence, 0) / insights.length * 100)
    : 0;

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <div className="flex items-center gap-2">
            <Brain className="h-5 w-5 animate-pulse" />
            <CardTitle>AI Insights</CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32">
            <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Insights</CardTitle>
            <Brain className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{insights.length}</div>
            <p className="text-xs text-muted-foreground">
              {actionableInsights} actionable
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Critical Issues</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{criticalInsights}</div>
            <p className="text-xs text-muted-foreground">
              Require immediate attention
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">AI Confidence</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{avgConfidence}%</div>
            <Progress value={avgConfidence} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Models</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{models.length}</div>
            <p className="text-xs text-muted-foreground">
              Predictive models running
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Dashboard */}
      <Tabs defaultValue="insights" className="space-y-4">
        <TabsList>
          <TabsTrigger value="insights">Insights</TabsTrigger>
          <TabsTrigger value="models">Models</TabsTrigger>
          <TabsTrigger value="alerts">Alerts</TabsTrigger>
        </TabsList>

        <TabsContent value="insights" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>AI Insights</CardTitle>
                  <CardDescription>
                    AI-generated insights and recommendations for your assets
                  </CardDescription>
                </div>
                <Button onClick={loadAIData} variant="outline" size="sm">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {/* Category Filter */}
              <div className="flex flex-wrap gap-2 mb-4">
                {categories.map((category) => (
                  <Button
                    key={category}
                    variant={selectedCategory === category ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedCategory(category)}
                  >
                    {category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </Button>
                ))}
              </div>

              <ScrollArea className="h-[400px]">
                <div className="space-y-4">
                  {filteredInsights.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      <Brain className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No insights available for this category.</p>
                      <Button onClick={generateSampleInsights} variant="outline" className="mt-2">
                        Generate Sample Insights
                      </Button>
                    </div>
                  ) : (
                    filteredInsights.map((insight) => (
                      <Card key={insight.id} className="border-l-4 border-l-primary">
                        <CardHeader className="pb-3">
                          <div className="flex items-start justify-between">
                            <div className="flex items-center gap-2">
                              {getInsightIcon(insight.type)}
                              <div>
                                <CardTitle className="text-base">{insight.title}</CardTitle>
                                <div className="flex items-center gap-2 mt-1">
                                  <Badge variant="outline" className="text-xs">
                                    {insight.type.replace(/_/g, ' ')}
                                  </Badge>
                                  <Badge 
                                    variant="outline" 
                                    className={cn("text-xs", getImpactColor(insight.impact))}
                                  >
                                    {insight.impact} impact
                                  </Badge>
                                  <Badge variant="outline" className="text-xs">
                                    {Math.round(insight.confidence * 100)}% confidence
                                  </Badge>
                                </div>
                              </div>
                            </div>
                            <Button variant="ghost" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                          </div>
                        </CardHeader>
                        <CardContent className="pt-0">
                          <p className="text-sm text-muted-foreground mb-3">
                            {insight.description}
                          </p>
                          
                          {insight.actionable && insight.suggestedActions.length > 0 && (
                            <div className="space-y-2">
                              <h4 className="text-sm font-medium">Suggested Actions:</h4>
                              <ul className="space-y-1">
                                {insight.suggestedActions.map((action, index) => (
                                  <li key={index} className="flex items-center gap-2 text-sm">
                                    <ChevronRight className="h-3 w-3 text-muted-foreground" />
                                    {action}
                                  </li>
                                ))}
                              </ul>
                            </div>
                          )}
                          
                          <div className="flex items-center justify-between mt-4 pt-3 border-t">
                            <div className="text-xs text-muted-foreground">
                              {new Date(insight.createdAt).toLocaleDateString()} • {insight.source}
                            </div>
                            <Badge variant="outline" className="text-xs">
                              {insight.category}
                            </Badge>
                          </div>
                        </CardContent>
                      </Card>
                    ))
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="models" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Predictive Models</CardTitle>
              <CardDescription>
                AI models for predictive maintenance and asset optimization
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[400px]">
                <div className="space-y-4">
                  {models.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      <Zap className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No predictive models available.</p>
                    </div>
                  ) : (
                    models.map((model) => (
                      <Card key={model.id}>
                        <CardHeader className="pb-3">
                          <div className="flex items-center justify-between">
                            <div>
                              <CardTitle className="text-base">{model.name}</CardTitle>
                              <CardDescription className="text-sm">
                                {model.type.replace(/_/g, ' ')} model
                              </CardDescription>
                            </div>
                            <Badge variant="outline">
                              {Math.round(model.accuracy * 100)}% accuracy
                            </Badge>
                          </div>
                        </CardHeader>
                        <CardContent className="pt-0">
                          <div className="grid grid-cols-2 gap-4 mb-4">
                            <div>
                              <div className="text-sm font-medium">Performance</div>
                              <div className="text-xs text-muted-foreground">
                                Precision: {Math.round(model.performance.precision * 100)}%
                              </div>
                              <div className="text-xs text-muted-foreground">
                                Recall: {Math.round(model.performance.recall * 100)}%
                              </div>
                            </div>
                            <div>
                              <div className="text-sm font-medium">Training Data</div>
                              <div className="text-xs text-muted-foreground">
                                {model.trainingData.samples} samples
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {model.trainingData.features.length} features
                              </div>
                            </div>
                          </div>
                          
                          <div className="text-xs text-muted-foreground">
                            Last trained: {new Date(model.lastTrained).toLocaleDateString()}
                          </div>
                          
                          {model.predictions.length > 0 && (
                            <div className="mt-3 pt-3 border-t">
                              <div className="text-sm font-medium mb-2">
                                Recent Predictions ({model.predictions.length})
                              </div>
                              <div className="space-y-1">
                                {model.predictions.slice(0, 3).map((prediction) => (
                                  <div key={prediction.id} className="text-xs bg-muted p-2 rounded">
                                    Asset {prediction.assetId}: {Math.round(Number(prediction.value) * 100)}% confidence
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    ))
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="alerts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Smart Alerts</CardTitle>
              <CardDescription>
                AI-powered alerts and notifications
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[400px]">
                <div className="space-y-4">
                  {alerts.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      <CheckCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No active alerts.</p>
                    </div>
                  ) : (
                    alerts.map((alert) => (
                      <Card key={alert.id} className="border-l-4 border-l-orange-500">
                        <CardHeader className="pb-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <AlertTriangle className="h-4 w-4" />
                              <div>
                                <CardTitle className="text-base">{alert.title}</CardTitle>
                                <div className="flex items-center gap-2 mt-1">
                                  <Badge 
                                    variant="outline" 
                                    className={cn("text-xs", getSeverityColor(alert.severity))}
                                  >
                                    {alert.severity}
                                  </Badge>
                                  <Badge variant="outline" className="text-xs">
                                    {alert.type}
                                  </Badge>
                                </div>
                              </div>
                            </div>
                            <Clock className="h-4 w-4 text-muted-foreground" />
                          </div>
                        </CardHeader>
                        <CardContent className="pt-0">
                          <p className="text-sm text-muted-foreground mb-3">
                            {alert.message}
                          </p>
                          
                          <div className="text-xs text-muted-foreground">
                            Entity: {alert.entityType} ({alert.entityId})
                          </div>
                          <div className="text-xs text-muted-foreground">
                            Created: {new Date(alert.createdAt).toLocaleString()}
                          </div>
                          
                          {alert.actions.length > 0 && (
                            <div className="mt-3 pt-3 border-t">
                              <div className="text-sm font-medium mb-2">Actions</div>
                              <div className="space-y-1">
                                {alert.actions.map((action) => (
                                  <div key={action.id} className="flex items-center gap-2 text-xs">
                                    <div className={cn(
                                      "w-2 h-2 rounded-full",
                                      action.executed ? "bg-green-500" : "bg-gray-300"
                                    )} />
                                    {action.type} - {action.executed ? 'Completed' : 'Pending'}
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    ))
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}