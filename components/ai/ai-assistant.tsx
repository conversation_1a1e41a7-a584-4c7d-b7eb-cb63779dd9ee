"use client";

import { useState } from 'react';
import { useChat } from '@ai-sdk/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Bot, 
  User, 
  Send, 
  Loader2, 
  MessageSquare, 
  Wrench, 
  TrendingUp, 
  AlertTriangle,
  Search,
  Calendar,
  DollarSign,
  BarChart3,
  Minimize2,
  Maximize2,
  X
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface AIAssistantProps {
  className?: string;
  defaultOpen?: boolean;
}

export function AIAssistant({ className, defaultOpen = false }: AIAssistantProps) {
  const [isOpen, setIsOpen] = useState(defaultOpen);
  const [isMinimized, setIsMinimized] = useState(false);

  const { messages, input, handleInputChange, handleSubmit, isLoading } = useChat({
    api: '/api/ai/chat',
    maxSteps: 5,
  });

  const getMessageIcon = (role: string) => {
    return role === 'user' ? <User className="h-4 w-4" /> : <Bot className="h-4 w-4" />;
  };

  const getToolIcon = (toolName: string) => {
    const iconMap: Record<string, React.ReactNode> = {
      analyzeAsset: <BarChart3 className="h-4 w-4" />,
      predictMaintenance: <Wrench className="h-4 w-4" />,
      optimizeCosts: <DollarSign className="h-4 w-4" />,
      searchAssets: <Search className="h-4 w-4" />,
      getMaintenanceSchedule: <Calendar className="h-4 w-4" />,
      detectAnomalies: <AlertTriangle className="h-4 w-4" />
    };
    return iconMap[toolName] || <Bot className="h-4 w-4" />;
  };

  const renderToolInvocation = (toolInvocation: any) => {
    const { toolName, state, args, result } = toolInvocation;

    switch (state) {
      case 'call':
        return (
          <div className="flex items-center gap-2 p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
            {getToolIcon(toolName)}
            <div className="flex-1">
              <div className="font-medium text-blue-900 dark:text-blue-100">
                {toolName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
              </div>
              <div className="text-sm text-blue-700 dark:text-blue-300">
                {args && Object.keys(args).length > 0 && (
                  <div className="mt-1">
                    {Object.entries(args).map(([key, value]) => (
                      <span key={key} className="inline-block mr-2">
                        <strong>{key}:</strong> {String(value)}
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </div>
            <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
          </div>
        );

      case 'result':
        return (
          <div className="p-3 bg-green-50 dark:bg-green-950/20 rounded-lg border border-green-200 dark:border-green-800">
            <div className="flex items-center gap-2 mb-2">
              {getToolIcon(toolName)}
              <div className="font-medium text-green-900 dark:text-green-100">
                {toolName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())} - Complete
              </div>
            </div>
            
            {result && (
              <div className="text-sm text-green-800 dark:text-green-200">
                {result.success ? (
                  <div>
                    <Badge variant="outline" className="mb-2 border-green-300 text-green-700">
                      Success
                    </Badge>
                    {result.message && <p className="mb-2">{result.message}</p>}
                    
                    {/* Render specific result data based on tool */}
                    {toolName === 'searchAssets' && result.assets && (
                      <div className="space-y-1">
                        <p><strong>Found {result.total} assets:</strong></p>
                        {result.assets.slice(0, 3).map((asset: any) => (
                          <div key={asset.id} className="text-xs bg-white dark:bg-gray-800 p-2 rounded border">
                            <strong>{asset.name}</strong> - {asset.category} ({asset.status})
                          </div>
                        ))}
                        {result.assets.length > 3 && (
                          <p className="text-xs">...and {result.assets.length - 3} more</p>
                        )}
                      </div>
                    )}
                    
                    {toolName === 'getMaintenanceSchedule' && result.schedule && (
                      <div className="space-y-1">
                        <p><strong>Upcoming maintenance ({result.total} items):</strong></p>
                        {result.schedule.slice(0, 3).map((item: any) => (
                          <div key={item.id} className="text-xs bg-white dark:bg-gray-800 p-2 rounded border">
                            <strong>{item.assetName}</strong> - {item.task} (Due: {item.dueDate})
                            <Badge variant="outline" className="ml-2 text-xs">
                              {item.priority}
                            </Badge>
                          </div>
                        ))}
                      </div>
                    )}
                    
                    {toolName === 'optimizeCosts' && result.optimization && (
                      <div className="space-y-1">
                        <p><strong>Potential Savings:</strong> ${result.optimization.analysis.potentialSavings.toLocaleString()}</p>
                        <p><strong>Recommendations:</strong> {result.optimization.recommendations.length}</p>
                      </div>
                    )}
                  </div>
                ) : (
                  <div>
                    <Badge variant="destructive" className="mb-2">
                      Error
                    </Badge>
                    <p>{result.message || result.error}</p>
                  </div>
                )}
              </div>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  if (!isOpen) {
    return (
      <Button
        onClick={() => setIsOpen(true)}
        className={cn(
          "fixed bottom-4 right-4 h-12 w-12 rounded-full shadow-lg z-50",
          className
        )}
        size="icon"
      >
        <MessageSquare className="h-5 w-5" />
      </Button>
    );
  }

  return (
    <Card className={cn(
      "fixed bottom-4 right-4 w-96 h-[600px] shadow-xl z-50 flex flex-col",
      isMinimized && "h-14",
      className
    )}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 border-b">
        <div className="flex items-center gap-2">
          <Bot className="h-5 w-5 text-primary" />
          <div>
            <CardTitle className="text-sm">AI Assistant</CardTitle>
            <CardDescription className="text-xs">
              Asset management help
            </CardDescription>
          </div>
        </div>
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6"
            onClick={() => setIsMinimized(!isMinimized)}
          >
            {isMinimized ? <Maximize2 className="h-3 w-3" /> : <Minimize2 className="h-3 w-3" />}
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6"
            onClick={() => setIsOpen(false)}
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </CardHeader>

      {!isMinimized && (
        <>
          <CardContent className="flex-1 p-0">
            <ScrollArea className="h-full p-4">
              <div className="space-y-4">
                {messages.length === 0 && (
                  <div className="text-center text-muted-foreground py-8">
                    <Bot className="h-12 w-12 mx-auto mb-4 text-muted-foreground/50" />
                    <p className="text-sm mb-2">Hi! I'm your AI assistant.</p>
                    <p className="text-xs">Ask me about:</p>
                    <div className="flex flex-wrap gap-1 mt-2 justify-center">
                      <Badge variant="outline" className="text-xs">Asset analysis</Badge>
                      <Badge variant="outline" className="text-xs">Maintenance</Badge>
                      <Badge variant="outline" className="text-xs">Cost optimization</Badge>
                      <Badge variant="outline" className="text-xs">Search assets</Badge>
                    </div>
                  </div>
                )}

                {messages.map((message) => (
                  <div key={message.id} className="space-y-2">
                    <div className={cn(
                      "flex gap-3",
                      message.role === 'user' ? 'justify-end' : 'justify-start'
                    )}>
                      {message.role === 'assistant' && (
                        <div className="flex-shrink-0 w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                          {getMessageIcon(message.role)}
                        </div>
                      )}
                      
                      <div className={cn(
                        "max-w-[80%] space-y-2",
                        message.role === 'user' ? 'order-first' : ''
                      )}>
                        {message.parts.map((part, index) => {
                          switch (part.type) {
                            case 'text':
                              return (
                                <div
                                  key={index}
                                  className={cn(
                                    "rounded-lg px-3 py-2 text-sm",
                                    message.role === 'user'
                                      ? "bg-primary text-primary-foreground ml-auto"
                                      : "bg-muted"
                                  )}
                                >
                                  {part.text}
                                </div>
                              );
                            case 'tool-invocation':
                              return (
                                <div key={index}>
                                  {renderToolInvocation(part.toolInvocation)}
                                </div>
                              );
                            default:
                              return null;
                          }
                        })}
                      </div>

                      {message.role === 'user' && (
                        <div className="flex-shrink-0 w-8 h-8 rounded-full bg-muted flex items-center justify-center">
                          {getMessageIcon(message.role)}
                        </div>
                      )}
                    </div>
                  </div>
                ))}

                {isLoading && (
                  <div className="flex gap-3">
                    <div className="flex-shrink-0 w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                      <Bot className="h-4 w-4" />
                    </div>
                    <div className="bg-muted rounded-lg px-3 py-2 text-sm">
                      <div className="flex items-center gap-2">
                        <Loader2 className="h-3 w-3 animate-spin" />
                        Thinking...
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </ScrollArea>
          </CardContent>

          <Separator />

          <div className="p-4">
            <form onSubmit={handleSubmit} className="flex gap-2">
              <Input
                value={input}
                onChange={handleInputChange}
                placeholder="Ask about your assets..."
                disabled={isLoading}
                className="flex-1"
              />
              <Button type="submit" disabled={isLoading} size="icon">
                {isLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Send className="h-4 w-4" />
                )}
              </Button>
            </form>
            
            <div className="flex flex-wrap gap-1 mt-2">
              <Button
                variant="ghost"
                size="sm"
                className="h-6 text-xs"
                onClick={() => handleInputChange({ target: { value: "Show me all assets in maintenance" } } as any)}
                disabled={isLoading}
              >
                Assets in maintenance
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 text-xs"
                onClick={() => handleInputChange({ target: { value: "Analyze asset performance" } } as any)}
                disabled={isLoading}
              >
                Analyze performance
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 text-xs"
                onClick={() => handleInputChange({ target: { value: "Optimize costs" } } as any)}
                disabled={isLoading}
              >
                Cost optimization
              </Button>
            </div>
          </div>
        </>
      )}
    </Card>
  );
}