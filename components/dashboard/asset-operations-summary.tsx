"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Plus, 
  Search, 
  Package, 
  Wrench, 
  ArrowRightLeft, 
  ClipboardCheck,
  Calendar,
  TrendingUp,
  AlertTriangle,
  CheckCircle
} from "lucide-react";
import { QUICK_ACTIONS, DASHBOARD_METRICS } from "@/lib/config/dashboard-config";

interface OperationsSummary {
  totalAssets: number;
  activeAssets: number;
  maintenanceAssets: number;
  recentOperations: number;
  pendingAudits: number;
  scheduledMaintenance: number;
}

export function AssetOperationsSummary() {
  const [summary, setSummary] = useState<OperationsSummary>({
    totalAssets: 0,
    activeAssets: 0,
    maintenanceAssets: 0,
    recentOperations: 0,
    pendingAudits: 0,
    scheduledMaintenance: 0,
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadSummary();
  }, []);

  const loadSummary = async () => {
    try {
      setIsLoading(true);
      
      // Load asset statistics
      const assetsResponse = await fetch('/api/assets/statistics');
      const assetsData = assetsResponse.ok ? await assetsResponse.json() : {};
      
      // Load audit summary
      const auditResponse = await fetch('/api/inventory/audit-summary');
      const auditData = auditResponse.ok ? await auditResponse.json() : {};
      
      // Load maintenance tasks
      const maintenanceResponse = await fetch('/api/maintenance/tasks?upcoming=true');
      const maintenanceData = maintenanceResponse.ok ? await maintenanceResponse.json() : {};

      setSummary({
        totalAssets: assetsData.totalAssets || 0,
        activeAssets: assetsData.activeAssets || 0,
        maintenanceAssets: assetsData.maintenanceAssets || 0,
        recentOperations: assetsData.recentOperations || 0,
        pendingAudits: auditData.summary?.totalAssets - auditData.summary?.auditedAssets || 0,
        scheduledMaintenance: maintenanceData.tasks?.length || 0,
      });
    } catch (error) {
      console.error('Error loading operations summary:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {[...Array(6)].map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="h-4 w-20 bg-muted animate-pulse rounded" />
              <div className="h-4 w-4 bg-muted animate-pulse rounded" />
            </CardHeader>
            <CardContent>
              <div className="h-8 w-16 bg-muted animate-pulse rounded mb-2" />
              <div className="h-3 w-24 bg-muted animate-pulse rounded" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Operations Statistics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Assets</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{summary.totalAssets}</div>
            <p className="text-xs text-muted-foreground">Across all categories</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Assets</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{summary.activeAssets}</div>
            <p className="text-xs text-muted-foreground">Currently operational</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Maintenance</CardTitle>
            <Wrench className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{summary.maintenanceAssets}</div>
            <p className="text-xs text-muted-foreground">Under maintenance</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Recent Operations</CardTitle>
            <TrendingUp className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{summary.recentOperations}</div>
            <p className="text-xs text-muted-foreground">Last 30 days</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Audits</CardTitle>
            <AlertTriangle className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{summary.pendingAudits}</div>
            <p className="text-xs text-muted-foreground">Need verification</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Scheduled Tasks</CardTitle>
            <Calendar className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{summary.scheduledMaintenance}</div>
            <p className="text-xs text-muted-foreground">Next 30 days</p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Operations */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Operations</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {QUICK_ACTIONS.map((action) => {
              const IconComponent = action.icon;
              return (
                <Button 
                  key={action.id}
                  variant="outline" 
                  className="h-16 flex flex-col items-center justify-center gap-2"
                  onClick={() => window.location.href = action.href}
                  disabled={action.disabled}
                >
                  <IconComponent className="h-5 w-5" />
                  <span className="text-xs">{action.title}</span>
                </Button>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Plus className="h-4 w-4 text-green-600" />
                </div>
                <div>
                  <p className="text-sm font-medium">Assets Created</p>
                  <p className="text-xs text-muted-foreground">Last 7 days</p>
                </div>
              </div>
              <Badge variant="secondary">12</Badge>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <ArrowRightLeft className="h-4 w-4 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm font-medium">Transfers Completed</p>
                  <p className="text-xs text-muted-foreground">Last 7 days</p>
                </div>
              </div>
              <Badge variant="secondary">8</Badge>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <Wrench className="h-4 w-4 text-yellow-600" />
                </div>
                <div>
                  <p className="text-sm font-medium">Maintenance Logged</p>
                  <p className="text-xs text-muted-foreground">Last 7 days</p>
                </div>
              </div>
              <Badge variant="secondary">15</Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}