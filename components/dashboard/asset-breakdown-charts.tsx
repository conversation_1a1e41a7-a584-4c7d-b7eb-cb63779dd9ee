"use client";

import React from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle, CardDescription } from "@/components/ui/card";
import { getStatusBadge } from "@/lib/utils/asset-status";

interface Asset {
  id: string;
  category: string;
  status: string;
}

interface AssetBreakdownChartsProps {
  assets: Asset[];
  isLoading?: boolean;
}

export function AssetBreakdownCharts({ assets, isLoading }: AssetBreakdownChartsProps) {
  if (isLoading) {
    return (
      <div className="grid gap-6 md:grid-cols-2">
        {[...Array(2)].map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <div className="h-4 w-32 bg-muted animate-pulse rounded" />
              <div className="h-3 w-48 bg-muted animate-pulse rounded" />
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[...Array(4)].map((_, j) => (
                  <div key={j} className="flex items-center justify-between">
                    <div className="h-4 w-24 bg-muted animate-pulse rounded" />
                    <div className="h-4 w-16 bg-muted animate-pulse rounded" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  // Get unique categories
  const categories = [...new Set(assets.map(asset => asset.category))];
  const statuses = ["active", "maintenance", "disposed"];

  return (
    <div className="grid gap-6 md:grid-cols-2">
      <Card>
        <CardHeader>
          <CardTitle>Assets by Category</CardTitle>
          <CardDescription>Distribution of assets across categories</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {categories.map((category) => {
              const count = assets.filter((asset) => asset.category === category).length;
              const percentage = assets.length > 0 ? (count / assets.length) * 100 : 0;
              return (
                <div key={category} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">{category}</span>
                    <span className="text-sm text-muted-foreground">{count} assets</span>
                  </div>
                  <div className="w-full bg-secondary rounded-full h-2">
                    <div 
                      className="bg-primary h-2 rounded-full transition-all duration-300" 
                      style={{ width: `${percentage}%` }} 
                    />
                  </div>
                  <div className="text-xs text-muted-foreground text-right">
                    {percentage.toFixed(1)}%
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Asset Status</CardTitle>
          <CardDescription>Overall status of assets</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {statuses.map((status) => {
              const count = assets.filter((asset) => asset.status === status).length;
              const percentage = assets.length > 0 ? (count / assets.length) * 100 : 0;
              return (
                <div key={status} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      {getStatusBadge(status)}
                      <span className="text-sm">{count} assets</span>
                    </div>
                    <span className="text-sm text-muted-foreground">{percentage.toFixed(1)}%</span>
                  </div>
                  <div className="w-full bg-secondary rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full transition-all duration-300 ${
                        status === 'active' ? 'bg-green-500' :
                        status === 'maintenance' ? 'bg-yellow-500' :
                        status === 'disposed' ? 'bg-red-500' : 'bg-gray-500'
                      }`}
                      style={{ width: `${percentage}%` }} 
                    />
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}