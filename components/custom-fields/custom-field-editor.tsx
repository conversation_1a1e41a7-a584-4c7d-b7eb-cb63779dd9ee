"use client";

import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { AlertCircle, Info } from "lucide-react";
import { Al<PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { CustomFieldEditorProps } from "@/lib/modules/custom-fields/types";
import { CustomField, CustomFieldType } from "@/lib/modules/asset-types/types";
import { FIELD_TYPES, DEFAULT_FIELD } from "@/lib/modules/custom-fields/constants";
import { CustomFieldsService } from "@/lib/modules/custom-fields/service";
import { FieldOptionsEditor } from "./field-options-editor";
import { ValidationRulesEditor } from "./validation-rules-editor";
import { ConditionalLogicEditor } from "./conditional-logic-editor";

const formSchema = z.object({
  name: z
    .string()
    .min(1, { message: "Field name is required" })
    .regex(/^[a-zA-Z][a-zA-Z0-9_]*$/, {
      message: "Field name must start with a letter and contain only letters, numbers, and underscores",
    }),
  label: z.string().min(1, { message: "Field label is required" }),
  type: z.string().min(1, { message: "Field type is required" }),
  description: z.string().optional(),
  isRequired: z.boolean().default(false),
  isUnique: z.boolean().default(false),
  displayOrder: z.number().int().default(0),
  groupName: z.string().optional(),
  isActive: z.boolean().default(true),
});

export function CustomFieldEditor({ field, onSave, onCancel }: CustomFieldEditorProps) {
  const service = CustomFieldsService.getInstance();
  const [activeTab, setActiveTab] = useState("basic");
  const [options, setOptions] = useState(field?.options || []);
  const [validation, setValidation] = useState(field?.validation || {});
  const [conditionalLogic, setConditionalLogic] = useState(field?.conditionalLogic || []);
  const [fieldType, setFieldType] = useState<CustomFieldType>((field?.type as CustomFieldType) || "text");
  const [errors, setErrors] = useState<string[]>([]);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: field?.name || "",
      label: field?.label || "",
      type: field?.type || "text",
      description: field?.description || "",
      isRequired: field?.isRequired || false,
      isUnique: field?.isUnique || false,
      displayOrder: field?.displayOrder || 0,
      groupName: field?.groupName || "",
      isActive: field?.isActive !== undefined ? field?.isActive : true,
    },
  });

  // Update field type when it changes
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === "type") {
        setFieldType(value.type as CustomFieldType);
      }
    });
    return () => subscription.unsubscribe();
  }, [form]);

  const handleSubmit = (values: z.infer<typeof formSchema>) => {
    // Create the complete field object
    const updatedField: CustomField = {
      id: field?.id || `temp-${Date.now()}`,
      ...values,
      type: values.type as CustomFieldType,
      validation,
      options: ["select", "multiselect"].includes(values.type) ? options : undefined,
      conditionalLogic: conditionalLogic.length > 0 ? conditionalLogic : undefined,
    };

    // Validate the field
    const validationResult = service.validateField(updatedField);
    if (!validationResult.isValid) {
      setErrors(validationResult.errors);
      return;
    }

    onSave(updatedField);
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>{field ? "Edit Field" : "Create New Field"}</CardTitle>
        <CardDescription>
          Configure the field properties, validation rules, and conditional logic.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {errors.length > 0 && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Validation Error</AlertTitle>
            <AlertDescription>
              <ul className="list-disc pl-5">
                {errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="basic">Basic</TabsTrigger>
            <TabsTrigger value="options" disabled={!["select", "multiselect"].includes(fieldType)}>
              Options
            </TabsTrigger>
            <TabsTrigger value="validation">Validation</TabsTrigger>
            <TabsTrigger value="logic">Conditional Logic</TabsTrigger>
          </TabsList>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)}>
              <TabsContent value="basic" className="space-y-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Field Name</FormLabel>
                        <FormControl>
                          <Input placeholder="field_name" {...field} />
                        </FormControl>
                        <FormDescription>
                          Internal name used in code (no spaces, start with a letter)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="label"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Display Label</FormLabel>
                        <FormControl>
                          <Input placeholder="Field Label" {...field} />
                        </FormControl>
                        <FormDescription>Label shown to users</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Field Type</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select field type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {FIELD_TYPES.map((type) => (
                            <SelectItem key={type.value} value={type.value}>
                              {type.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        {FIELD_TYPES.find((t) => t.value === field.value)?.description || ""}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea placeholder="Field description" {...field} />
                      </FormControl>
                      <FormDescription>Help text shown to users</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="groupName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Group Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Group name (optional)" {...field} />
                        </FormControl>
                        <FormDescription>Group related fields together</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="displayOrder"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Display Order</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="0"
                            step="1"
                            {...field}
                            onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormDescription>Order in the form (lower numbers first)</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4 pt-2">
                  <FormField
                    control={form.control}
                    name="isRequired"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="space-y-0.5">
                          <FormLabel>Required Field</FormLabel>
                          <FormDescription>Users must provide a value</FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="isUnique"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="space-y-0.5">
                          <FormLabel>Unique Value</FormLabel>
                          <FormDescription>Value must be unique across all records</FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="isActive"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                      <div className="space-y-0.5">
                        <FormLabel>Active</FormLabel>
                        <FormDescription>Field is available for use</FormDescription>
                      </div>
                      <FormControl>
                        <Switch checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </TabsContent>

              <TabsContent value="options" className="py-4">
                <FieldOptionsEditor options={options} onChange={setOptions} />
              </TabsContent>

              <TabsContent value="validation" className="py-4">
                <ValidationRulesEditor
                  validation={validation}
                  fieldType={fieldType}
                  onChange={setValidation}
                />
              </TabsContent>

              <TabsContent value="logic" className="py-4">
                <ConditionalLogicEditor
                  logic={conditionalLogic}
                  availableFields={[]} // This would be populated from context or props
                  onChange={setConditionalLogic}
                />
              </TabsContent>

              <Separator className="my-4" />

              <div className="flex justify-end space-x-2">
                <Button variant="outline" type="button" onClick={onCancel}>
                  Cancel
                </Button>
                <Button type="submit">Save Field</Button>
              </div>
            </form>
          </Form>
        </Tabs>
      </CardContent>
    </Card>
  );
}