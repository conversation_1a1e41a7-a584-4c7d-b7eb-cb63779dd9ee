"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { CustomField, ConditionalLogic } from "@/lib/modules/asset-types/types";
import { FormGeneratorProps, FieldGroupProps, DynamicFieldProps } from "@/lib/modules/custom-fields/types";
import { CustomFieldsService } from "@/lib/modules/custom-fields/service";

export function FormGenerator({ fields, values, onChange, readOnly = false, showGroups = true }: FormGeneratorProps) {
  const [formValues, setFormValues] = useState<Record<string, any>>(values || {});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [fieldStates, setFieldStates] = useState<Record<string, { visible: boolean; required: boolean; disabled: boolean; value?: any }>>({});
  
  const service = CustomFieldsService.getInstance();

  // Initialize default values for fields
  useEffect(() => {
    const initialValues = { ...values };
    
    fields.forEach(field => {
      if (initialValues[field.name] === undefined) {
        initialValues[field.name] = service.getDefaultValue(field);
      }
    });
    
    setFormValues(initialValues);
  }, [fields, values, service]);

  // Process conditional logic
  useEffect(() => {
    // Collect all conditional logic rules
    const allLogic: ConditionalLogic[] = [];
    fields.forEach(field => {
      if (field.conditionalLogic && field.conditionalLogic.length > 0) {
        allLogic.push(...field.conditionalLogic);
      }
    });
    
    if (allLogic.length > 0) {
      const states = service.evaluateConditionalLogic(allLogic, formValues);
      setFieldStates(states);
      
      // Apply conditional values
      const updatedValues = { ...formValues };
      let hasChanges = false;
      
      Object.entries(states).forEach(([fieldId, state]) => {
        if (state.value !== undefined && updatedValues[fieldId] !== state.value) {
          updatedValues[fieldId] = state.value;
          hasChanges = true;
        }
      });
      
      if (hasChanges) {
        setFormValues(updatedValues);
        onChange(updatedValues);
      }
    }
  }, [fields, formValues, service, onChange]);

  const handleFieldChange = (fieldId: string, value: any) => {
    const updatedValues = { ...formValues, [fieldId]: value };
    setFormValues(updatedValues);
    
    // Validate the field
    const field = fields.find(f => f.name === fieldId);
    if (field) {
      const validation = service.validateFieldValue(value, field);
      if (!validation.isValid) {
        setErrors(prev => ({ ...prev, [fieldId]: validation.error || "Invalid value" }));
      } else {
        setErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors[fieldId];
          return newErrors;
        });
      }
    }
    
    onChange(updatedValues);
  };

  // Group fields by groupName
  const groupedFields: Record<string, CustomField[]> = {};
  fields.forEach(field => {
    const group = showGroups && field.groupName ? field.groupName : "default";
    if (!groupedFields[group]) {
      groupedFields[group] = [];
    }
    groupedFields[group].push(field);
  });

  // Sort groups and fields within groups by displayOrder
  Object.keys(groupedFields).forEach(group => {
    groupedFields[group].sort((a, b) => a.displayOrder - b.displayOrder);
  });

  // Sort group names with "default" always first
  const sortedGroupNames = Object.keys(groupedFields).sort((a, b) => {
    if (a === "default") return -1;
    if (b === "default") return 1;
    return a.localeCompare(b);
  });

  return (
    <div className="space-y-6">
      {sortedGroupNames.map(groupName => (
        <div key={groupName}>
          {showGroups && groupName !== "default" && (
            <h3 className="text-lg font-medium mb-4">{groupName}</h3>
          )}
          <div className="space-y-4">
            {groupedFields[groupName].map(field => {
              // Check if field should be visible based on conditional logic
              const fieldState = fieldStates[field.name] || { visible: true, required: field.isRequired, disabled: false };
              
              if (!fieldState.visible) return null;
              
              return (
                <DynamicField
                  key={field.id}
                  field={{
                    ...field,
                    isRequired: fieldState.required
                  }}
                  value={formValues[field.name]}
                  onChange={(value) => handleFieldChange(field.name, value)}
                  error={errors[field.name]}
                  readOnly={readOnly || fieldState.disabled}
                />
              );
            })}
          </div>
        </div>
      ))}
    </div>
  );
}

export function FieldGroup({ name, fields, values, onChange, readOnly = false }: FieldGroupProps) {
  return (
    <Card>
      <CardContent className="p-4">
        <h3 className="text-lg font-medium mb-4">{name}</h3>
        <div className="space-y-4">
          {fields.map(field => (
            <DynamicField
              key={field.id}
              field={field}
              value={values[field.name]}
              onChange={(value) => onChange(field.name, value)}
              readOnly={readOnly}
            />
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

export function DynamicField({ field, value, onChange, error, readOnly = false }: DynamicFieldProps) {
  const handleChange = (newValue: any) => {
    if (!readOnly) {
      onChange(newValue);
    }
  };

  return (
    <div className="space-y-2">
      <Label htmlFor={field.name} className={cn(field.isRequired && "after:content-['*'] after:ml-0.5 after:text-red-500")}>
        {field.label}
      </Label>

      {field.type === "text" && (
        <Input
          id={field.name}
          value={value || ""}
          onChange={(e) => handleChange(e.target.value)}
          placeholder={`Enter ${field.label.toLowerCase()}`}
          disabled={readOnly}
        />
      )}

      {field.type === "textarea" && (
        <Textarea
          id={field.name}
          value={value || ""}
          onChange={(e) => handleChange(e.target.value)}
          placeholder={`Enter ${field.label.toLowerCase()}`}
          disabled={readOnly}
        />
      )}

      {field.type === "number" && (
        <Input
          id={field.name}
          type="number"
          value={value ?? ""}
          onChange={(e) => handleChange(e.target.value === "" ? null : Number(e.target.value))}
          placeholder={`Enter ${field.label.toLowerCase()}`}
          disabled={readOnly}
        />
      )}

      {field.type === "decimal" && (
        <Input
          id={field.name}
          type="number"
          step="0.01"
          value={value ?? ""}
          onChange={(e) => handleChange(e.target.value === "" ? null : Number(e.target.value))}
          placeholder={`Enter ${field.label.toLowerCase()}`}
          disabled={readOnly}
        />
      )}

      {field.type === "date" && (
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                "w-full justify-start text-left font-normal",
                !value && "text-muted-foreground"
              )}
              disabled={readOnly}
            >
              {value ? format(new Date(value), "PPP") : `Select ${field.label.toLowerCase()}`}
              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              mode="single"
              selected={value ? new Date(value) : undefined}
              onSelect={handleChange}
              disabled={readOnly}
              initialFocus
            />
          </PopoverContent>
        </Popover>
      )}

      {field.type === "datetime" && (
        <div className="flex space-x-2">
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !value && "text-muted-foreground"
                )}
                disabled={readOnly}
              >
                {value ? format(new Date(value), "PPP") : `Select date`}
                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={value ? new Date(value) : undefined}
                onSelect={(date) => {
                  if (date) {
                    const currentDate = value ? new Date(value) : new Date();
                    date.setHours(currentDate.getHours(), currentDate.getMinutes());
                    handleChange(date.toISOString());
                  }
                }}
                disabled={readOnly}
                initialFocus
              />
            </PopoverContent>
          </Popover>
          <Input
            type="time"
            value={value ? format(new Date(value), "HH:mm") : ""}
            onChange={(e) => {
              if (e.target.value && value) {
                const [hours, minutes] = e.target.value.split(':').map(Number);
                const date = new Date(value);
                date.setHours(hours, minutes);
                handleChange(date.toISOString());
              } else if (e.target.value) {
                const [hours, minutes] = e.target.value.split(':').map(Number);
                const date = new Date();
                date.setHours(hours, minutes);
                handleChange(date.toISOString());
              }
            }}
            disabled={readOnly}
            className="w-24"
          />
        </div>
      )}

      {field.type === "boolean" && (
        <div className="flex items-center space-x-2">
          <Checkbox
            id={field.name}
            checked={!!value}
            onCheckedChange={handleChange}
            disabled={readOnly}
          />
          <label
            htmlFor={field.name}
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            Yes
          </label>
        </div>
      )}

      {field.type === "select" && (
        <Select
          value={value || ""}
          onValueChange={handleChange}
          disabled={readOnly}
        >
          <SelectTrigger>
            <SelectValue placeholder={`Select ${field.label.toLowerCase()}`} />
          </SelectTrigger>
          <SelectContent>
            {field.options?.map((option) => (
              <SelectItem key={option.value} value={option.value} disabled={!option.isActive}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      )}

      {field.type === "multiselect" && (
        <div className="border rounded-md p-2">
          <div className="flex flex-wrap gap-2">
            {field.options?.map((option) => (
              <div key={option.value} className="flex items-center space-x-2">
                <Checkbox
                  id={`${field.name}-${option.value}`}
                  checked={(value || []).includes(option.value)}
                  onCheckedChange={(checked) => {
                    const currentValues = Array.isArray(value) ? value : [];
                    if (checked) {
                      handleChange([...currentValues, option.value]);
                    } else {
                      handleChange(currentValues.filter((v) => v !== option.value));
                    }
                  }}
                  disabled={readOnly || !option.isActive}
                />
                <label
                  htmlFor={`${field.name}-${option.value}`}
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  {option.label}
                </label>
              </div>
            ))}
          </div>
        </div>
      )}

      {field.type === "email" && (
        <Input
          id={field.name}
          type="email"
          value={value || ""}
          onChange={(e) => handleChange(e.target.value)}
          placeholder="<EMAIL>"
          disabled={readOnly}
        />
      )}

      {field.type === "url" && (
        <Input
          id={field.name}
          type="url"
          value={value || ""}
          onChange={(e) => handleChange(e.target.value)}
          placeholder="https://example.com"
          disabled={readOnly}
        />
      )}

      {field.type === "phone" && (
        <Input
          id={field.name}
          type="tel"
          value={value || ""}
          onChange={(e) => handleChange(e.target.value)}
          placeholder="+****************"
          disabled={readOnly}
        />
      )}

      {field.type === "currency" && (
        <div className="relative">
          <span className="absolute left-3 top-1/2 -translate-y-1/2">$</span>
          <Input
            id={field.name}
            type="number"
            step="0.01"
            value={value ?? ""}
            onChange={(e) => handleChange(e.target.value === "" ? null : Number(e.target.value))}
            className="pl-7"
            placeholder="0.00"
            disabled={readOnly}
          />
        </div>
      )}

      {field.type === "percentage" && (
        <div className="relative">
          <Input
            id={field.name}
            type="number"
            step="0.01"
            value={value ?? ""}
            onChange={(e) => handleChange(e.target.value === "" ? null : Number(e.target.value))}
            className="pr-7"
            placeholder="0.00"
            disabled={readOnly}
          />
          <span className="absolute right-3 top-1/2 -translate-y-1/2">%</span>
        </div>
      )}

      {field.type === "file" && (
        <Input
          id={field.name}
          type="file"
          onChange={(e) => handleChange(e.target.files?.[0] || null)}
          disabled={readOnly}
        />
      )}

      {field.type === "image" && (
        <div className="space-y-2">
          <Input
            id={field.name}
            type="file"
            accept="image/*"
            onChange={(e) => handleChange(e.target.files?.[0] || null)}
            disabled={readOnly}
          />
          {value && typeof value === "string" && (
            <div className="mt-2">
              <img
                src={value}
                alt={field.label}
                className="max-w-full h-auto max-h-40 rounded-md"
              />
            </div>
          )}
        </div>
      )}

      {field.type === "json" && (
        <Textarea
          id={field.name}
          value={typeof value === "object" ? JSON.stringify(value, null, 2) : value || ""}
          onChange={(e) => {
            try {
              const jsonValue = e.target.value.trim() ? JSON.parse(e.target.value) : null;
              handleChange(jsonValue);
            } catch (err) {
              // Allow invalid JSON during editing, but don't update the value
              console.log("Invalid JSON:", err);
            }
          }}
          placeholder="{}"
          className="font-mono text-sm"
          rows={5}
          disabled={readOnly}
        />
      )}

      {field.description && (
        <p className="text-sm text-muted-foreground">{field.description}</p>
      )}

      {error && (
        <p className="text-sm font-medium text-destructive">{error}</p>
      )}
    </div>
  );
}