"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Search, Plus, MoreHorizontal, Edit, Trash2, Copy, Eye, ArrowUpDown, FileJson, FileDown, FileUp } from "lucide-react";
import { CustomField } from "@/lib/modules/asset-types/types";
import { CustomFieldsListProps } from "@/lib/modules/custom-fields/types";
import { FIELD_TYPES } from "@/lib/modules/custom-fields/constants";
import { CustomFieldsService } from "@/lib/modules/custom-fields/service";
import { Textarea } from "../ui/textarea";

export function CustomFieldsList({ fields, onEdit, onDelete, onAdd, onReorder }: CustomFieldsListProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState<"name" | "label" | "type" | "group" | "order">("order");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
  const [selectedField, setSelectedField] = useState<CustomField | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [exportFormat, setExportFormat] = useState<"json" | "schema" | "react" | "html">("json");
  const [importData, setImportData] = useState("");
  const [importFormat, setImportFormat] = useState<"json" | "schema">("json");
  const [previewField, setPreviewField] = useState<CustomField | null>(null);

  const service = CustomFieldsService.getInstance();

  const filteredFields = fields.filter((field) => {
    if (!searchQuery) return true;
    const query = searchQuery.toLowerCase();
    return (
      field.name.toLowerCase().includes(query) ||
      field.label.toLowerCase().includes(query) ||
      field.type.toLowerCase().includes(query) ||
      (field.groupName && field.groupName.toLowerCase().includes(query)) ||
      (field.description && field.description.toLowerCase().includes(query))
    );
  });

  const sortedFields = [...filteredFields].sort((a, b) => {
    let valueA, valueB;

    switch (sortBy) {
      case "name":
        valueA = a.name.toLowerCase();
        valueB = b.name.toLowerCase();
        break;
      case "label":
        valueA = a.label.toLowerCase();
        valueB = b.label.toLowerCase();
        break;
      case "type":
        valueA = a.type.toLowerCase();
        valueB = b.type.toLowerCase();
        break;
      case "group":
        valueA = (a.groupName || "").toLowerCase();
        valueB = (b.groupName || "").toLowerCase();
        break;
      case "order":
      default:
        valueA = a.displayOrder;
        valueB = b.displayOrder;
        break;
    }

    if (valueA < valueB) return sortDirection === "asc" ? -1 : 1;
    if (valueA > valueB) return sortDirection === "asc" ? 1 : -1;
    return 0;
  });

  const handleSort = (column: "name" | "label" | "type" | "group" | "order") => {
    if (sortBy === column) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortBy(column);
      setSortDirection("asc");
    }
  };

  const handleExport = () => {
    try {
      const exportedData = service.exportFields(fields, exportFormat);
      // In a real app, you might want to download this as a file
      console.log(exportedData);
      setShowExportDialog(false);
    } catch (error) {
      console.error("Export error:", error);
    }
  };

  const handleImport = () => {
    try {
      const importedFields = service.importFields(importData, importFormat);
      // Here you would handle the imported fields, perhaps merging them with existing ones
      console.log("Imported fields:", importedFields);
      setShowImportDialog(false);
    } catch (error) {
      console.error("Import error:", error);
    }
  };

  const getFieldTypeLabel = (type: string) => {
    return FIELD_TYPES.find((t) => t.value === type)?.label || type;
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <CardTitle>Custom Fields</CardTitle>
            <CardDescription>
              Manage metadata fields for assets, asset types, and workflow forms
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={() => setShowImportDialog(true)}>
              <FileUp className="h-4 w-4 mr-2" />
              Import
            </Button>
            <Button variant="outline" onClick={() => setShowExportDialog(true)}>
              <FileDown className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button onClick={onAdd}>
              <Plus className="h-4 w-4 mr-2" />
              Add Field
            </Button>
          </div>
        </div>
        <div className="flex items-center mt-2">
          <Search className="h-4 w-4 mr-2 text-muted-foreground" />
          <Input
            placeholder="Search fields..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="max-w-sm"
          />
        </div>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[50px]">#</TableHead>
                <TableHead>
                  <div
                    className="flex items-center cursor-pointer"
                    onClick={() => handleSort("name")}
                  >
                    Name
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                  </div>
                </TableHead>
                <TableHead>
                  <div
                    className="flex items-center cursor-pointer"
                    onClick={() => handleSort("label")}
                  >
                    Label
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                  </div>
                </TableHead>
                <TableHead>
                  <div
                    className="flex items-center cursor-pointer"
                    onClick={() => handleSort("type")}
                  >
                    Type
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                  </div>
                </TableHead>
                <TableHead>
                  <div
                    className="flex items-center cursor-pointer"
                    onClick={() => handleSort("group")}
                  >
                    Group
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                  </div>
                </TableHead>
                <TableHead className="text-center">Required</TableHead>
                <TableHead className="text-center">Unique</TableHead>
                <TableHead className="text-center">Active</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sortedFields.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                    {searchQuery
                      ? "No fields match your search query"
                      : "No custom fields defined. Click 'Add Field' to create one."}
                  </TableCell>
                </TableRow>
              ) : (
                sortedFields.map((field, index) => (
                  <TableRow key={field.id}>
                    <TableCell className="font-mono text-xs text-muted-foreground">
                      {field.displayOrder}
                    </TableCell>
                    <TableCell className="font-medium">{field.name}</TableCell>
                    <TableCell>{field.label}</TableCell>
                    <TableCell>
                      <Badge variant="outline">{getFieldTypeLabel(field.type)}</Badge>
                    </TableCell>
                    <TableCell>
                      {field.groupName ? (
                        <Badge variant="secondary">{field.groupName}</Badge>
                      ) : (
                        <span className="text-muted-foreground text-sm">—</span>
                      )}
                    </TableCell>
                    <TableCell className="text-center">
                      {field.isRequired ? "✓" : "—"}
                    </TableCell>
                    <TableCell className="text-center">
                      {field.isUnique ? "✓" : "—"}
                    </TableCell>
                    <TableCell className="text-center">
                      {field.isActive ? "✓" : "—"}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                            <span className="sr-only">Actions</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => onEdit(field)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => {
                            setPreviewField(field);
                          }}>
                            <Eye className="h-4 w-4 mr-2" />
                            Preview
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => {
                            // Clone the field
                            const clonedField = {
                              ...field,
                              id: `CF-${Date.now()}`,
                              name: `${field.name}_copy`,
                              label: `${field.label} (Copy)`,
                            };
                            onEdit(clonedField);
                          }}>
                            <Copy className="h-4 w-4 mr-2" />
                            Duplicate
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="text-destructive"
                            onClick={() => {
                              setSelectedField(field);
                              setShowDeleteDialog(true);
                            }}
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <div className="text-sm text-muted-foreground">
          Showing {filteredFields.length} of {fields.length} fields
        </div>
      </CardFooter>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the field "{selectedField?.label}". This action cannot be
              undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              onClick={() => {
                if (selectedField) {
                  onDelete(selectedField.id);
                }
                setShowDeleteDialog(false);
              }}
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Export Dialog */}
      <Dialog open={showExportDialog} onOpenChange={setShowExportDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Export Custom Fields</DialogTitle>
            <DialogDescription>
              Choose a format to export your custom fields.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Export Format</label>
              <div className="grid grid-cols-2 gap-2">
                <Button
                  variant={exportFormat === "json" ? "default" : "outline"}
                  onClick={() => setExportFormat("json")}
                  className="justify-start"
                >
                  <FileJson className="h-4 w-4 mr-2" />
                  JSON
                </Button>
                <Button
                  variant={exportFormat === "schema" ? "default" : "outline"}
                  onClick={() => setExportFormat("schema")}
                  className="justify-start"
                >
                  <FileJson className="h-4 w-4 mr-2" />
                  JSON Schema
                </Button>
                <Button
                  variant={exportFormat === "react" ? "default" : "outline"}
                  onClick={() => setExportFormat("react")}
                  className="justify-start"
                >
                  <FileJson className="h-4 w-4 mr-2" />
                  React Component
                </Button>
                <Button
                  variant={exportFormat === "html" ? "default" : "outline"}
                  onClick={() => setExportFormat("html")}
                  className="justify-start"
                >
                  <FileJson className="h-4 w-4 mr-2" />
                  HTML Form
                </Button>
              </div>
            </div>
            <Separator />
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setShowExportDialog(false)}>
                Cancel
              </Button>
              <Button onClick={handleExport}>Export</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Import Dialog */}
      <Dialog open={showImportDialog} onOpenChange={setShowImportDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Import Custom Fields</DialogTitle>
            <DialogDescription>
              Paste your custom fields definition to import.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Import Format</label>
              <div className="flex space-x-2">
                <Button
                  variant={importFormat === "json" ? "default" : "outline"}
                  onClick={() => setImportFormat("json")}
                  className="flex-1 justify-center"
                >
                  JSON
                </Button>
                <Button
                  variant={importFormat === "schema" ? "default" : "outline"}
                  onClick={() => setImportFormat("schema")}
                  className="flex-1 justify-center"
                >
                  JSON Schema
                </Button>
              </div>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Paste {importFormat.toUpperCase()} Data</label>
              <Textarea
                value={importData}
                onChange={(e) => setImportData(e.target.value)}
                placeholder={`Paste your ${importFormat.toUpperCase()} data here...`}
                className="font-mono text-sm"
                rows={10}
              />
            </div>
            <Separator />
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setShowImportDialog(false)}>
                Cancel
              </Button>
              <Button onClick={handleImport} disabled={!importData.trim()}>
                Import
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Field Preview Dialog */}
      <Dialog open={!!previewField} onOpenChange={(open) => !open && setPreviewField(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Field Preview: {previewField?.label}</DialogTitle>
            <DialogDescription>
              This is how the field will appear in forms.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            {previewField && (
              <div className="border rounded-md p-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">{previewField.label}{previewField.isRequired ? ' *' : ''}</label>
                  {previewField.type === 'text' && <Input placeholder={`Enter ${previewField.label.toLowerCase()}`} />}
                  {previewField.type === 'textarea' && <Textarea placeholder={`Enter ${previewField.label.toLowerCase()}`} />}
                  {previewField.type === 'number' && <Input type="number" placeholder={`Enter ${previewField.label.toLowerCase()}`} />}
                  {previewField.type === 'select' && (
                    <select className="w-full p-2 border rounded-md">
                      <option value="">Select {previewField.label.toLowerCase()}</option>
                      {previewField.options?.map(option => (
                        <option key={option.value} value={option.value}>{option.label}</option>
                      ))}
                    </select>
                  )}
                  {/* Add more field type previews as needed */}
                  {previewField.description && (
                    <p className="text-sm text-muted-foreground">{previewField.description}</p>
                  )}
                </div>
              </div>
            )}
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Field Details</h3>
              <div className="text-sm">
                <p><strong>Name:</strong> {previewField?.name}</p>
                <p><strong>Type:</strong> {getFieldTypeLabel(previewField?.type || '')}</p>
                <p><strong>Required:</strong> {previewField?.isRequired ? 'Yes' : 'No'}</p>
                <p><strong>Unique:</strong> {previewField?.isUnique ? 'Yes' : 'No'}</p>
                {previewField?.groupName && <p><strong>Group:</strong> {previewField.groupName}</p>}
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </Card>
  );
}