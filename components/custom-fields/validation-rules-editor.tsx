"use client";

import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Info } from "lucide-react";
import { ValidationRuleProps } from "@/lib/modules/custom-fields/types";
import { FieldValidation } from "@/lib/modules/asset-types/types";
import { FIELD_TYPE_VALIDATION_MAP } from "@/lib/modules/custom-fields/constants";

export function ValidationRulesEditor({ validation, fieldType, onChange }: ValidationRuleProps) {
  const handleChange = (field: keyof FieldValidation, value: any) => {
    onChange({ ...validation, [field]: value });
  };

  const availableValidations = FIELD_TYPE_VALIDATION_MAP[fieldType] || [];

  return (
    <div className="space-y-4">
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          Validation rules are specific to the field type. Only applicable rules are shown.
        </AlertDescription>
      </Alert>

      <Card>
        <CardContent className="p-4 space-y-4">
          {availableValidations.includes("minLength") && (
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="minLength">Minimum Length</Label>
                <Input
                  id="minLength"
                  type="number"
                  min="0"
                  value={validation.minLength || ""}
                  onChange={(e) => handleChange("minLength", e.target.value ? parseInt(e.target.value) : undefined)}
                  placeholder="Minimum length"
                />
                <p className="text-sm text-muted-foreground">
                  {fieldType === "multiselect"
                    ? "Minimum number of selections"
                    : "Minimum number of characters"}
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="maxLength">Maximum Length</Label>
                <Input
                  id="maxLength"
                  type="number"
                  min="0"
                  value={validation.maxLength || ""}
                  onChange={(e) => handleChange("maxLength", e.target.value ? parseInt(e.target.value) : undefined)}
                  placeholder="Maximum length"
                />
                <p className="text-sm text-muted-foreground">
                  {fieldType === "multiselect"
                    ? "Maximum number of selections"
                    : fieldType === "file" || fieldType === "image"
                    ? "Maximum file size in KB"
                    : "Maximum number of characters"}
                </p>
              </div>
            </div>
          )}

          {availableValidations.includes("minValue") && (
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="minValue">Minimum Value</Label>
                <Input
                  id="minValue"
                  type={fieldType === "date" || fieldType === "datetime" ? "date" : "number"}
                  step={fieldType === "decimal" || fieldType === "currency" || fieldType === "percentage" ? "0.01" : "1"}
                  value={validation.minValue || ""}
                  onChange={(e) =>
                    handleChange(
                      "minValue",
                      e.target.value
                        ? fieldType === "date" || fieldType === "datetime"
                          ? e.target.value
                          : parseFloat(e.target.value)
                        : undefined
                    )
                  }
                  placeholder="Minimum value"
                />
                <p className="text-sm text-muted-foreground">
                  {fieldType === "date" || fieldType === "datetime"
                    ? "Earliest allowed date"
                    : "Minimum allowed value"}
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="maxValue">Maximum Value</Label>
                <Input
                  id="maxValue"
                  type={fieldType === "date" || fieldType === "datetime" ? "date" : "number"}
                  step={fieldType === "decimal" || fieldType === "currency" || fieldType === "percentage" ? "0.01" : "1"}
                  value={validation.maxValue || ""}
                  onChange={(e) =>
                    handleChange(
                      "maxValue",
                      e.target.value
                        ? fieldType === "date" || fieldType === "datetime"
                          ? e.target.value
                          : parseFloat(e.target.value)
                        : undefined
                    )
                  }
                  placeholder="Maximum value"
                />
                <p className="text-sm text-muted-foreground">
                  {fieldType === "date" || fieldType === "datetime"
                    ? "Latest allowed date"
                    : "Maximum allowed value"}
                </p>
              </div>
            </div>
          )}

          {availableValidations.includes("pattern") && (
            <div className="space-y-2">
              <Label htmlFor="pattern">Validation Pattern (RegEx)</Label>
              <Input
                id="pattern"
                value={validation.pattern || ""}
                onChange={(e) => handleChange("pattern", e.target.value || undefined)}
                placeholder="Regular expression pattern"
              />
              <p className="text-sm text-muted-foreground">
                {fieldType === "email"
                  ? "Email validation pattern (default is standard email format)"
                  : fieldType === "url"
                  ? "URL validation pattern (default is standard URL format)"
                  : fieldType === "phone"
                  ? "Phone number validation pattern"
                  : "Regular expression pattern for validation"}
              </p>
            </div>
          )}

          {availableValidations.includes("customValidator") && (
            <div className="space-y-2">
              <Label htmlFor="customValidator">Custom Validator</Label>
              <Textarea
                id="customValidator"
                value={validation.customValidator || ""}
                onChange={(e) => handleChange("customValidator", e.target.value || undefined)}
                placeholder="JavaScript validation function"
                rows={5}
                className="font-mono text-sm"
              />
              <p className="text-sm text-muted-foreground">
                JavaScript function that returns true if valid, or an error message if invalid.
                <br />
                Example: <code>return value.hasOwnProperty('id') ? true : 'Missing id property';</code>
              </p>
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="errorMessage">Custom Error Message</Label>
            <Input
              id="errorMessage"
              value={validation.errorMessage || ""}
              onChange={(e) => handleChange("errorMessage", e.target.value || undefined)}
              placeholder="Custom error message"
            />
            <p className="text-sm text-muted-foreground">
              Custom message to display when validation fails
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}