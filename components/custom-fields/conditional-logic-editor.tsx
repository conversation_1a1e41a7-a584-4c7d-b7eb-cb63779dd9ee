"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Info, Plus, Trash2 } from "lucide-react";
import { ConditionalLogic, CustomField, LogicAction, LogicCondition } from "@/lib/modules/asset-types/types";
import { ConditionalLogicEditorProps } from "@/lib/modules/custom-fields/types";
import { VALIDATION_OPERATORS, LOGIC_ACTIONS } from "@/lib/modules/custom-fields/constants";

export function ConditionalLogicEditor({
  logic,
  availableFields,
  onChange,
}: ConditionalLogicEditorProps) {
  const [newLogic, setNewLogic] = useState<Partial<ConditionalLogic>>({
    condition: {
      fieldId: "",
      operator: "equals",
      value: "",
    } as LogicCondition,
    action: {
      type: "show",
      value: undefined,
    } as LogicAction,
    targetFieldId: "",
  });

  const handleAddLogic = () => {
    if (!newLogic.condition?.fieldId || !newLogic.targetFieldId) return;

    const updatedLogic = [
      ...logic,
      {
        condition: newLogic.condition as LogicCondition,
        action: newLogic.action as LogicAction,
        targetFieldId: newLogic.targetFieldId,
      },
    ];

    onChange(updatedLogic);
    setNewLogic({
      condition: {
        fieldId: "",
        operator: "equals",
        value: "",
      } as LogicCondition,
      action: {
        type: "show",
        value: undefined,
      } as LogicAction,
      targetFieldId: "",
    });
  };

  const handleUpdateLogic = (index: number, field: keyof ConditionalLogic, value: any) => {
    const updatedLogic = [...logic];
    updatedLogic[index] = { ...updatedLogic[index], [field]: value };
    onChange(updatedLogic);
  };

  const handleUpdateCondition = (index: number, field: keyof LogicCondition, value: any) => {
    const updatedLogic = [...logic];
    updatedLogic[index].condition = { ...updatedLogic[index].condition, [field]: value };
    onChange(updatedLogic);
  };

  const handleUpdateAction = (index: number, field: keyof LogicAction, value: any) => {
    const updatedLogic = [...logic];
    updatedLogic[index].action = { ...updatedLogic[index].action, [field]: value };
    onChange(updatedLogic);
  };

  const handleRemoveLogic = (index: number) => {
    const updatedLogic = logic.filter((_, i) => i !== index);
    onChange(updatedLogic);
  };

  const handleNewConditionChange = (field: keyof LogicCondition, value: any) => {
    setNewLogic({
      ...newLogic,
      condition: { ...newLogic.condition, [field]: value } as LogicCondition,
    });
  };

  const handleNewActionChange = (field: keyof LogicAction, value: any) => {
    setNewLogic({
      ...newLogic,
      action: { ...newLogic.action, [field]: value } as LogicAction,
    });
  };

  return (
    <div className="space-y-4">
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          Conditional logic allows you to show, hide, or modify fields based on the values of other fields.
        </AlertDescription>
      </Alert>

      <div className="bg-muted/50 p-4 rounded-md">
        <h3 className="text-sm font-medium mb-4">Add New Conditional Logic</h3>

        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label>If Field</Label>
              <Select
                value={newLogic.condition?.fieldId || ""}
                onValueChange={(value) => handleNewConditionChange("fieldId", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select field" />
                </SelectTrigger>
                <SelectContent>
                  {availableFields.map((field) => (
                    <SelectItem key={field.id} value={field.id}>
                      {field.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Operator</Label>
              <Select
                value={newLogic.condition?.operator || "equals"}
                onValueChange={(value) => handleNewConditionChange("operator", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select operator" />
                </SelectTrigger>
                <SelectContent>
                  {VALIDATION_OPERATORS.map((op) => (
                    <SelectItem key={op.value} value={op.value}>
                      {op.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Value</Label>
              <Input
                value={newLogic.condition?.value || ""}
                onChange={(e) => handleNewConditionChange("value", e.target.value)}
                placeholder="Comparison value"
                disabled={
                  newLogic.condition?.operator === "is_empty" ||
                  newLogic.condition?.operator === "is_not_empty"
                }
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label>Then</Label>
              <Select
                value={newLogic.action?.type || "show"}
                onValueChange={(value) => handleNewActionChange("type", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select action" />
                </SelectTrigger>
                <SelectContent>
                  {LOGIC_ACTIONS.map((action) => (
                    <SelectItem key={action.value} value={action.value}>
                      {action.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Target Field</Label>
              <Select
                value={newLogic.targetFieldId || ""}
                onValueChange={(value) => setNewLogic({ ...newLogic, targetFieldId: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select target field" />
                </SelectTrigger>
                <SelectContent>
                  {availableFields.map((field) => (
                    <SelectItem key={field.id} value={field.id}>
                      {field.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {newLogic.action?.type === "set_value" && (
              <div className="space-y-2">
                <Label>Set Value To</Label>
                <Input
                  value={newLogic.action?.value || ""}
                  onChange={(e) => handleNewActionChange("value", e.target.value)}
                  placeholder="New value"
                />
              </div>
            )}
          </div>

          <div className="flex justify-end">
            <Button
              onClick={handleAddLogic}
              disabled={!newLogic.condition?.fieldId || !newLogic.targetFieldId}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Logic Rule
            </Button>
          </div>
        </div>
      </div>

      <Separator />

      <div className="space-y-2">
        <h3 className="text-sm font-medium">Conditional Logic Rules</h3>
        {logic.length === 0 ? (
          <p className="text-sm text-muted-foreground">No conditional logic rules defined.</p>
        ) : (
          <div className="space-y-2">
            {logic.map((rule, index) => (
              <Card key={index}>
                <CardContent className="p-4">
                  <div className="flex flex-col space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label>If Field</Label>
                        <Select
                          value={rule.condition.fieldId}
                          onValueChange={(value) => handleUpdateCondition(index, "fieldId", value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select field" />
                          </SelectTrigger>
                          <SelectContent>
                            {availableFields.map((field) => (
                              <SelectItem key={field.id} value={field.id}>
                                {field.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label>Operator</Label>
                        <Select
                          value={rule.condition.operator}
                          onValueChange={(value) => handleUpdateCondition(index, "operator", value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select operator" />
                          </SelectTrigger>
                          <SelectContent>
                            {VALIDATION_OPERATORS.map((op) => (
                              <SelectItem key={op.value} value={op.value}>
                                {op.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label>Value</Label>
                        <Input
                          value={rule.condition.value || ""}
                          onChange={(e) => handleUpdateCondition(index, "value", e.target.value)}
                          placeholder="Comparison value"
                          disabled={
                            rule.condition.operator === "is_empty" ||
                            rule.condition.operator === "is_not_empty"
                          }
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label>Then</Label>
                        <Select
                          value={rule.action.type}
                          onValueChange={(value) => handleUpdateAction(index, "type", value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select action" />
                          </SelectTrigger>
                          <SelectContent>
                            {LOGIC_ACTIONS.map((action) => (
                              <SelectItem key={action.value} value={action.value}>
                                {action.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label>Target Field</Label>
                        <Select
                          value={rule.targetFieldId}
                          onValueChange={(value) => handleUpdateLogic(index, "targetFieldId", value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select target field" />
                          </SelectTrigger>
                          <SelectContent>
                            {availableFields.map((field) => (
                              <SelectItem key={field.id} value={field.id}>
                                {field.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      {rule.action.type === "set_value" && (
                        <div className="space-y-2">
                          <Label>Set Value To</Label>
                          <Input
                            value={rule.action.value || ""}
                            onChange={(e) => handleUpdateAction(index, "value", e.target.value)}
                            placeholder="New value"
                          />
                        </div>
                      )}
                    </div>

                    <div className="flex justify-end">
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleRemoveLogic(index)}
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Remove Rule
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}