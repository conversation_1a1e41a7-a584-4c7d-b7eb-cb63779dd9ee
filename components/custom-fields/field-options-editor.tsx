"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { GripVertical, Plus, Trash2 } from "lucide-react";
import { FieldOption } from "@/lib/modules/asset-types/types";
import { FieldOptionsEditorProps } from "@/lib/modules/custom-fields/types";

export function FieldOptionsEditor({ options, onChange }: FieldOptionsEditorProps) {
  const [newOption, setNewOption] = useState<Partial<FieldOption>>({
    value: "",
    label: "",
    description: "",
    isActive: true,
  });

  const handleAddOption = () => {
    if (!newOption.value || !newOption.label) return;

    const updatedOptions = [
      ...options,
      {
        value: newOption.value,
        label: newOption.label,
        description: newOption.description || "",
        isActive: newOption.isActive !== undefined ? newOption.isActive : true,
      },
    ];

    onChange(updatedOptions);
    setNewOption({ value: "", label: "", description: "", isActive: true });
  };

  const handleUpdateOption = (index: number, field: keyof FieldOption, value: any) => {
    const updatedOptions = [...options];
    updatedOptions[index] = { ...updatedOptions[index], [field]: value };
    onChange(updatedOptions);
  };

  const handleRemoveOption = (index: number) => {
    const updatedOptions = options.filter((_, i) => i !== index);
    onChange(updatedOptions);
  };

  const handleMoveOption = (fromIndex: number, toIndex: number) => {
    if (toIndex < 0 || toIndex >= options.length) return;

    const updatedOptions = [...options];
    const [movedOption] = updatedOptions.splice(fromIndex, 1);
    updatedOptions.splice(toIndex, 0, movedOption);
    onChange(updatedOptions);
  };

  return (
    <div className="space-y-4">
      <div className="bg-muted/50 p-4 rounded-md">
        <h3 className="text-sm font-medium mb-2">Add New Option</h3>
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="space-y-2">
            <Label htmlFor="new-option-value">Value</Label>
            <Input
              id="new-option-value"
              value={newOption.value}
              onChange={(e) => setNewOption({ ...newOption, value: e.target.value })}
              placeholder="Option value (used in code)"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="new-option-label">Label</Label>
            <Input
              id="new-option-label"
              value={newOption.label}
              onChange={(e) => setNewOption({ ...newOption, label: e.target.value })}
              placeholder="Option label (displayed to users)"
            />
          </div>
        </div>
        <div className="space-y-2 mb-4">
          <Label htmlFor="new-option-description">Description (optional)</Label>
          <Textarea
            id="new-option-description"
            value={newOption.description || ""}
            onChange={(e) => setNewOption({ ...newOption, description: e.target.value })}
            placeholder="Option description"
            rows={2}
          />
        </div>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Switch
              id="new-option-active"
              checked={newOption.isActive !== undefined ? newOption.isActive : true}
              onCheckedChange={(checked) => setNewOption({ ...newOption, isActive: checked })}
            />
            <Label htmlFor="new-option-active">Active</Label>
          </div>
          <Button onClick={handleAddOption} disabled={!newOption.value || !newOption.label}>
            <Plus className="h-4 w-4 mr-2" />
            Add Option
          </Button>
        </div>
      </div>

      <Separator />

      <div className="space-y-2">
        <h3 className="text-sm font-medium">Field Options</h3>
        {options.length === 0 ? (
          <p className="text-sm text-muted-foreground">No options defined. Add options above.</p>
        ) : (
          <div className="space-y-2">
            {options.map((option, index) => (
              <Card key={index} className="relative">
                <CardContent className="p-4">
                  <div className="flex items-start">
                    <div className="cursor-move mr-2 mt-1 text-muted-foreground">
                      <GripVertical className="h-5 w-5" />
                    </div>
                    <div className="flex-1 grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor={`option-${index}-value`}>Value</Label>
                        <Input
                          id={`option-${index}-value`}
                          value={option.value}
                          onChange={(e) => handleUpdateOption(index, "value", e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor={`option-${index}-label`}>Label</Label>
                        <Input
                          id={`option-${index}-label`}
                          value={option.label}
                          onChange={(e) => handleUpdateOption(index, "label", e.target.value)}
                        />
                      </div>
                      <div className="space-y-2 md:col-span-2">
                        <Label htmlFor={`option-${index}-description`}>Description</Label>
                        <Textarea
                          id={`option-${index}-description`}
                          value={option.description || ""}
                          onChange={(e) => handleUpdateOption(index, "description", e.target.value)}
                          rows={2}
                        />
                      </div>
                      <div className="flex items-center justify-between md:col-span-2">
                        <div className="flex items-center space-x-2">
                          <Switch
                            id={`option-${index}-active`}
                            checked={option.isActive}
                            onCheckedChange={(checked) => handleUpdateOption(index, "isActive", checked)}
                          />
                          <Label htmlFor={`option-${index}-active`}>Active</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleMoveOption(index, index - 1)}
                            disabled={index === 0}
                          >
                            ↑
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleMoveOption(index, index + 1)}
                            disabled={index === options.length - 1}
                          >
                            ↓
                          </Button>
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => handleRemoveOption(index)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}