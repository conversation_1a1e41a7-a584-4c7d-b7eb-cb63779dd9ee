# Local Fonts Configuration

This project uses locally hosted fonts loaded via Next.js `localFont` for better performance and reliability.

## Available Fonts

### 1. Outfit (Primary Sans Serif)
- **Variable Font**: `Outfit-VariableFont_wght.ttf`
- **Weight Range**: 100-900
- **Usage**: Primary UI font, body text
- **CSS Variable**: `--font-outfit`
- **Tailwind Class**: `font-sans`

### 2. <PERSON><PERSON><PERSON> (Serif/Display)
- **Variable Fonts**: 
  - `Montserrat-VariableFont_wght.ttf` (Regular)
  - `Montserrat-Italic-VariableFont_wght.ttf` (Italic)
- **Weight Range**: 100-900
- **Usage**: Headings, display text
- **CSS Variable**: `--font-montserrat`
- **Tailwind Classes**: `font-serif`, `font-heading`

### 3. JetBrains Mono (Monospace)
- **Variable Fonts**:
  - `JetBrainsMono-VariableFont_wght.ttf` (Regular)
  - `JetBrainsMono-Italic-VariableFont_wght.ttf` (Italic)
- **Weight Range**: 100-800
- **Usage**: Code blocks, technical text
- **CSS Variable**: `--font-jetbrains-mono`
- **Tailwind Class**: `font-mono`

### 4. Manrope (Alternative Sans Serif)
- **Variable Font**: `Manrope-VariableFont_wght.ttf`
- **Weight Range**: 200-800
- **Usage**: Alternative UI font, special sections
- **CSS Variable**: `--font-manrope`
- **Tailwind Class**: `font-manrope`

## Usage Examples

### In CSS
```css
/* Using CSS variables */
.my-element {
  font-family: var(--font-sans); /* Outfit */
  font-family: var(--font-serif); /* Montserrat */
  font-family: var(--font-mono); /* JetBrains Mono */
  font-family: var(--font-manrope); /* Manrope */
}
```

### In Tailwind CSS
```html
<!-- Primary sans-serif (Outfit) -->
<p class="font-sans">This uses Outfit font</p>

<!-- Serif/Display (Montserrat) -->
<h1 class="font-serif">This uses Montserrat font</h1>
<h2 class="font-heading">This also uses Montserrat font</h2>

<!-- Monospace (JetBrains Mono) -->
<code class="font-mono">This uses JetBrains Mono font</code>

<!-- Alternative sans-serif (Manrope) -->
<p class="font-manrope">This uses Manrope font</p>
```

### Font Weights
All fonts support variable weights. Use Tailwind's font weight classes:
- `font-thin` (100)
- `font-light` (300)
- `font-normal` (400)
- `font-medium` (500)
- `font-semibold` (600)
- `font-bold` (700)
- `font-extrabold` (800)
- `font-black` (900)

## Implementation Details

### Font Loading
Fonts are loaded using Next.js `localFont` in `/lib/fonts.ts` and applied to the document body in `/app/layout.tsx`.

### CSS Variables
Font CSS variables are defined in `/app/globals.css` for both light and dark themes.

### Tailwind Configuration
Font families are configured in `tailwind.config.ts` to use the CSS variables.

### Performance Benefits
- **No external requests**: Fonts are served locally
- **Variable fonts**: Single file per font family with all weights
- **Font display swap**: Ensures text remains visible during font load
- **Proper fallbacks**: System fonts used while custom fonts load

## Testing Fonts
Use the `<FontTest />` component to verify all fonts are loading correctly:

```tsx
import { FontTest } from '@/components/font-test'

export default function TestPage() {
  return <FontTest />
}
```

## File Structure
```
assets/fonts/
├── Outfit/
│   └── Outfit-VariableFont_wght.ttf
├── Montserrat/
│   ├── Montserrat-VariableFont_wght.ttf
│   └── Montserrat-Italic-VariableFont_wght.ttf
├── JetBrains_Mono/
│   ├── JetBrainsMono-VariableFont_wght.ttf
│   └── JetBrainsMono-Italic-VariableFont_wght.ttf
└── Manrope/
    └── Manrope-VariableFont_wght.ttf
```