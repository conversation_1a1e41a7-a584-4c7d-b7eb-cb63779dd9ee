// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model for authentication
model User {
  id         String   @id @default(cuid())
  name       String
  email      String   @unique
  password   String
  department String?
  role       String   @default("user") // "user", "admin", "manager", "client"
  status     String   @default("active") // "active", "inactive", "suspended", "pending_verification"
  joinDate   DateTime @default(now())
  lastActive DateTime @default(now())
  avatarUrl  String?
  phone      String?
  address    String?
  company    String?
  jobTitle   String?
  
  // Email verification
  emailVerified    DateTime?
  emailVerifyToken String?   @unique
  
  // Password reset
  passwordResetToken     String?   @unique
  passwordResetExpiresAt DateTime?
  
  // Two-factor authentication
  twoFactorEnabled Boolean @default(false)
  twoFactorSecret  String?
  
  // Client-specific fields
  clientId          String?
  billingAddress    Json?    // Structured billing address
  paymentMethods    Json?    // Array of payment method references
  preferences       Json?    // User preferences and settings
  
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // Relations
  clientProfile     ClientProfile?
  assetRequests     AssetRequest[]
  supportTickets    SupportTicket[]
  invoices          Invoice[]
  sessions          Session[]
  accounts          Account[]

  @@index([email])
  @@index([role])
  @@index([status])
  @@index([clientId])
}

// Client Profile for additional client information
model ClientProfile {
  id                String   @id @default(cuid())
  userId            String   @unique
  companyName       String?
  industry          String?
  companySize       String?  // "1-10", "11-50", "51-200", "201-500", "500+"
  website           String?
  taxId             String?
  billingContact    Json?    // Contact information for billing
  technicalContact  Json?    // Technical contact information
  accountManager    String?  // Assigned account manager
  subscriptionTier  String   @default("basic") // "basic", "professional", "enterprise"
  monthlySpend      Float    @default(0)
  creditLimit       Float?
  paymentTerms      String   @default("net_30") // "net_15", "net_30", "net_60"
  autoApprovalLimit Float    @default(0) // Auto-approve requests under this amount
  
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  
  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@index([companyName])
  @@index([subscriptionTier])
}

// Asset Request model for client requests
model AssetRequest {
  id                String   @id @default(cuid())
  requestNumber     String   @unique
  userId            String
  assetTypeId       String?
  assetName         String
  quantity          Int      @default(1)
  priority          String   @default("normal") // "low", "normal", "high", "critical"
  status            String   @default("pending") // "pending", "approved", "rejected", "processing", "shipped", "delivered", "cancelled"
  justification     String
  businessCase      String?
  specifications    String?
  estimatedCost     Float?
  actualCost        Float?
  budgetCode        String?
  department        String?
  location          String
  expectedDelivery  DateTime?
  actualDelivery    DateTime?
  approvedBy        String?
  approvedAt        DateTime?
  rejectionReason   String?
  trackingNumber    String?
  notes             String?
  attachments       String[] @default([])
  
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  
  // Relations
  user       User @relation(fields: [userId], references: [id])
  
  @@index([userId])
  @@index([status])
  @@index([priority])
  @@index([requestNumber])
}

// Support Ticket model
model SupportTicket {
  id           String   @id @default(cuid())
  ticketNumber String   @unique
  userId       String
  subject      String
  description  String
  category     String
  priority     String   @default("normal") // "low", "normal", "high", "critical"
  status       String   @default("open") // "open", "in_progress", "waiting_response", "resolved", "closed"
  assignedTo   String?
  resolution   String?
  
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  
  // Relations
  user     User @relation(fields: [userId], references: [id])
  messages SupportMessage[]
  
  @@index([userId])
  @@index([status])
  @@index([priority])
  @@index([ticketNumber])
}

// Support Messages for ticket conversations
model SupportMessage {
  id         String   @id @default(cuid())
  ticketId   String
  content    String
  sender     String   // User ID or "system"
  senderType String   // "client", "support", "system"
  attachments String[] @default([])
  
  createdAt  DateTime @default(now())
  
  // Relations
  ticket SupportTicket @relation(fields: [ticketId], references: [id], onDelete: Cascade)
  
  @@index([ticketId])
}

// Invoice model for billing
model Invoice {
  id            String   @id @default(cuid())
  invoiceNumber String   @unique
  userId        String
  amount        Float
  tax           Float    @default(0)
  total         Float
  currency      String   @default("USD")
  status        String   @default("pending") // "pending", "paid", "overdue", "cancelled"
  dueDate       DateTime
  paidDate      DateTime?
  paymentMethod String?
  description   String?
  items         Json     // Array of invoice items
  
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  
  // Relations
  user User @relation(fields: [userId], references: [id])
  
  @@index([userId])
  @@index([status])
  @@index([invoiceNumber])
}

// NextAuth.js required models
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// Workflow definitions
model Workflow {
  id          String   @id @default(cuid())
  name        String
  description String?
  type        String   // e.g., "asset-automation"
  nodes       Json     // Array of nodes
  edges       Json     // Array of edges
  webhooks    Json?    // Array of webhook configurations
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  createdBy   String?
  isActive    Boolean  @default(true)
  tags        String[] @default([])
  
  // Relations
  executions  WorkflowExecution[]
  
  @@index([type])
}

// Workflow executions
model WorkflowExecution {
  id          String    @id @default(cuid())
  workflowId  String
  status      String    // "queued", "running", "completed", "failed", "cancelled"
  input       String?   // JSON string of input data
  output      String?   // JSON string of output data
  startedAt   DateTime
  completedAt DateTime?
  context     String    // JSON string of execution context
  
  // Relations
  workflow    Workflow  @relation(fields: [workflowId], references: [id], onDelete: Cascade)
  
  @@index([workflowId])
  @@index([status])
}

// Assets
model Asset {
  id            String   @id @default(cuid())
  name          String
  category      String
  purchaseDate  DateTime
  purchasePrice Float
  location      String
  department    String?
  status        String   @default("active") // "active", "maintenance", "disposed"
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  serialNumber  String?  @unique
  assetImages   String[] @default([]) // Add this line for asset images
  assetTypeId   String?  // Foreign key for AssetType
  
  // Relations
  assetType           AssetType? @relation("AssetToAssetType", fields: [assetTypeId], references: [id])
  maintenances        AssetMaintenance[]
  transfers           AssetTransfer[]
  disposals           AssetDisposal[]
  depreciations       AssetDepreciation[]
  leases              LeaseAgreement[]
  operationHistory    AssetOperationHistory[]
  depreciationSchedule DepreciationSchedule[]
  maintenanceTasks    MaintenanceTask[]
  lifecycleState      AssetLifecycleState?
  allocations         AssetAllocation[]
  
  @@index([assetTypeId])
}

// Asset maintenance records
model AssetMaintenance {
  id            String   @id @default(cuid())
  assetId       String
  type          String
  scheduledDate DateTime
  completedDate DateTime?
  assignedTo    String?
  notes         String?
  status        String   // "scheduled", "in_progress", "completed", "cancelled"
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  
  // Relations
  asset         Asset    @relation(fields: [assetId], references: [id], onDelete: Cascade)
  
  @@index([assetId])
  @@index([status])
}

// Asset transfer records
model AssetTransfer {
  id                 String   @id @default(cuid())
  assetId            String
  previousLocation   String
  newLocation        String
  previousDepartment String?
  newDepartment      String?
  transferDate       DateTime
  reason             String?
  createdAt          DateTime @default(now())
  
  // Relations
  asset              Asset    @relation(fields: [assetId], references: [id], onDelete: Cascade)
  
  @@index([assetId])
}

// Asset disposal records
model AssetDisposal {
  id           String   @id @default(cuid())
  assetId      String
  method       String   // "sale", "donation", "recycling", "destruction"
  disposalDate DateTime
  bookValue    Float
  salePrice    Float?
  gainLoss     Float?
  reason       String?
  createdAt    DateTime @default(now())
  
  // Relations
  asset        Asset    @relation(fields: [assetId], references: [id], onDelete: Cascade)
  
  @@index([assetId])
}

// Asset depreciation records
model AssetDepreciation {
  id           String   @id @default(cuid())
  assetId      String
  method       String   // "straight-line", "double-declining", etc.
  usefulLife   Int
  salvageValue Float
  initialValue Float
  schedule     String   // JSON string of depreciation schedule
  createdAt    DateTime @default(now())
  
  // Relations
  asset        Asset    @relation(fields: [assetId], references: [id], onDelete: Cascade)
  
  @@index([assetId])
}

// Inventory check records
model InventoryCheck {
  id            String   @id @default(cuid())
  location      String
  department    String?
  checkDate     DateTime
  performedBy   String
  assetsChecked Int
  assetsFound   Int
  assetsMissing Int
  missingAssets String   // JSON string of missing assets
  createdAt     DateTime @default(now())
}

// Purchase orders
model PurchaseOrder {
  id              String   @id @default(cuid())
  supplier        String
  items           String   // JSON string of items
  total           Float
  requestedBy     String
  deliveryDate    DateTime?
  shippingAddress String?
  status          String   // "pending_approval", "approved", "rejected", "completed"
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  
  // Relations
  requisitions    RequisitionPurchaseOrder[]
  
  @@index([status])
}

// Supplier Invoices (different from client invoices)
model SupplierInvoice {
  id              String   @id @default(cuid())
  invoiceNumber   String   @unique
  supplier        String
  amount          Float
  purchaseOrderId String?
  items           String?  // JSON string of items
  dueDate         DateTime?
  status          String   // "pending_approval", "approved", "rejected", "paid"
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  
  @@index([status])
}

// Approval requests
model ApprovalRequest {
  id           String   @id @default(cuid())
  type         String   // "purchase", "disposal", "transfer", etc.
  requestedBy  String
  approver     String
  items        String?  // JSON string of items
  justification String?
  urgency      String   @default("normal") // "low", "normal", "high", "critical"
  status       String   // "pending", "approved", "rejected"
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  
  @@index([status])
}

// Notifications
model Notification {
  id        String   @id @default(cuid())
  recipient String
  subject   String
  message   String
  priority  String   @default("normal") // "low", "normal", "high", "critical"
  type      String   // "system", "asset", "maintenance", "approval", etc.
  status    String   // "sent", "delivered", "read"
  createdAt DateTime @default(now())
  readAt    DateTime?
  
  @@index([recipient])
  @@index([status])
}

// Reports
model Report {
  id         String   @id @default(cuid())
  type       String   // "asset-inventory", "depreciation", etc.
  name       String
  parameters String?  // JSON string of parameters
  format     String   // "PDF", "CSV", "XLSX", etc.
  delivery   String   // "download", "email", etc.
  status     String   // "pending", "completed", "failed"
  url        String?
  createdAt  DateTime @default(now())
  
  @@index([type])
  @@index([status])
}

// Lease Agreements
model LeaseAgreement {
  id                        String   @id @default(cuid())
  assetId                   String
  lessorId                  String
  lessorName                String
  lesseeId                  String
  lesseeName                String
  leaseType                 String   // "Operating", "Finance", "Capital"
  startDate                 DateTime
  endDate                   DateTime
  monthlyPayment            Float
  totalValue                Float
  securityDeposit           Float
  status                    String   @default("Draft") // "Draft", "Active", "Expired", "Terminated", "Renewed"
  renewalOptions            Int      @default(0)
  earlyTerminationClause    Boolean  @default(false)
  maintenanceResponsibility String   // "Lessor", "Lessee", "Shared"
  insuranceRequirement      Boolean  @default(false)
  terms                     String?
  attachments               String?  // JSON string of attachment URLs
  createdAt                 DateTime @default(now())
  updatedAt                 DateTime @updatedAt

  // Relations
  asset                     Asset    @relation(fields: [assetId], references: [id])
  paymentSchedule           PaymentSchedule[]
  renewals                  LeaseRenewal[]   @relation("OriginalLease")
  renewedFrom               LeaseRenewal[]   @relation("NewLease")
  requisitions              RequisitionLeaseAgreement[]

  @@index([assetId])
  @@index([status])
  @@index([lessorId])
  @@index([lesseeId])
}

// Payment Schedule
model PaymentSchedule {
  id             String         @id @default(cuid())
  leaseId        String
  dueDate        DateTime
  amount         Float
  status         String         // "Pending", "Paid", "Overdue", "Partial"
  paidAmount     Float          @default(0)
  paidDate       DateTime?
  paymentMethod  String?
  transactionId  String?
  lateFee        Float          @default(0)
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt

  // Relations
  lease          LeaseAgreement @relation(fields: [leaseId], references: [id], onDelete: Cascade)

  @@index([leaseId])
  @@index([status])
  @@index([dueDate])
}

// Lease Renewals
model LeaseRenewal {
  id              String         @id @default(cuid())
  originalLeaseId String
  newLeaseId      String?
  renewalDate     DateTime
  newTerms        String?
  rateAdjustment  Float
  status          String         // "Proposed", "Approved", "Rejected", "Completed"
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt

  // Relations
  originalLease   LeaseAgreement @relation("OriginalLease", fields: [originalLeaseId], references: [id])
  newLease        LeaseAgreement? @relation("NewLease", fields: [newLeaseId], references: [id])

  @@index([originalLeaseId])
  @@index([status])
}

// Asset Categories
model AssetCategory {
  id          String   @id @default(cuid())
  name        String
  description String
  parentId    String?
  level       Int
  path        String
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  parent      AssetCategory?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children    AssetCategory[] @relation("CategoryHierarchy")
  assetTypes  AssetType[]

  @@index([parentId])
  @@index([isActive])
}

// Asset Types
model AssetType {
  id                    String   @id @default(cuid())
  name                  String
  code                  String   @unique
  description           String
  categoryId            String
  subcategory           String?
  icon                  String
  color                 String
  isActive              Boolean  @default(true)
  tags                  String[] @default([])
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt
  createdBy             String
  version               Int      @default(1)

  // Relations
  category              AssetCategory           @relation(fields: [categoryId], references: [id])
  customFields          CustomField[]
  lifecycleStages       LifecycleStage[]
  maintenanceSchedules  MaintenanceSchedule[]
  depreciationSettings  DepreciationSettings?
  assets                Asset[]                 @relation("AssetToAssetType")
  assetTypeForms        AssetTypeForm[]
  requisitions          Requisition[]

  @@index([categoryId])
  @@index([isActive])
  @@index([code])
}

// Custom Fields
model CustomField {
  id               String   @id @default(cuid())
  assetTypeId      String
  name             String
  label            String
  type             String   // CustomFieldType enum as string
  description      String?
  isRequired       Boolean  @default(false)
  isUnique         Boolean  @default(false)
  defaultValue     String?  // JSON string
  validation       String?  // JSON string of FieldValidation
  options          String?  // JSON string of FieldOption[]
  conditionalLogic String?  // JSON string of ConditionalLogic[]
  displayOrder     Int
  groupName        String?
  isActive         Boolean  @default(true)
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  // Relations
  assetType        AssetType @relation(fields: [assetTypeId], references: [id], onDelete: Cascade)

  @@index([assetTypeId])
  @@index([isActive])
}

// Lifecycle Stages
model LifecycleStage {
  id                 String   @id @default(cuid())
  assetTypeId        String
  name               String
  code               String
  description        String
  order              Int
  isInitial          Boolean  @default(false)
  isFinal            Boolean  @default(false)
  color              String
  icon               String
  allowedTransitions String[] @default([]) // Array of stage IDs
  requiredFields     String[] @default([]) // Array of field names
  automatedActions   String?  // JSON string of AutomatedAction[]
  notifications      String?  // JSON string of NotificationRule[]
  isActive           Boolean  @default(true)
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt

  // Relations
  assetType          AssetType @relation(fields: [assetTypeId], references: [id], onDelete: Cascade)

  @@index([assetTypeId])
  @@index([isActive])
  @@unique([assetTypeId, code])
}

// Maintenance Schedules
model MaintenanceSchedule {
  id                 String   @id @default(cuid())
  assetTypeId        String
  name               String
  description        String
  type               String   // MaintenanceType enum as string
  frequency          String   // JSON string of MaintenanceFrequency
  priority           String   // MaintenancePriority enum as string
  estimatedDuration  Int      // in minutes
  estimatedCost      Float
  requiredSkills     String[] @default([])
  requiredParts      String?  // JSON string of RequiredPart[]
  instructions       String
  checklistItems     String?  // JSON string of ChecklistItem[]
  triggers           String?  // JSON string of MaintenanceTrigger[]
  isActive           Boolean  @default(true)
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt

  // Relations
  assetType          AssetType @relation(fields: [assetTypeId], references: [id], onDelete: Cascade)

  @@index([assetTypeId])
  @@index([isActive])
  @@index([type])
}

// Depreciation Settings
model DepreciationSettings {
  id                     String   @id @default(cuid())
  assetTypeId            String   @unique
  method                 String   // DepreciationMethod enum as string
  usefulLife             Int
  usefulLifeUnit         String   // "years", "months", "hours", "cycles"
  salvageValue           Float
  salvageValueType       String   // "fixed", "percentage"
  startDate              DateTime
  customRates            String?  // JSON string of DepreciationRate[]
  acceleratedDepreciation String? // JSON string of AcceleratedDepreciation
  impairmentSettings     String?  // JSON string of ImpairmentSettings
  isActive               Boolean  @default(true)
  createdAt              DateTime @default(now())
  updatedAt              DateTime @updatedAt

  // Relations
  assetType              AssetType @relation(fields: [assetTypeId], references: [id], onDelete: Cascade)

  @@index([method])
  @@index([isActive])
}

// Asset Type Templates
model AssetTypeTemplate {
  id          String   @id @default(cuid())
  name        String
  description String
  category    String
  assetType   String   // JSON string of Partial<AssetType>
  isPublic    Boolean  @default(false)
  createdBy   String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  usageCount  Int      @default(0)
  rating      Float    @default(0)
  tags        String[] @default([])

  @@index([category])
  @@index([isPublic])
  @@index([createdBy])
}

// Form Definitions for dynamic forms
model FormDefinition {
  id          String   @id @default(cuid())
  name        String
  description String?
  sections    String   // JSON string of FormSection[]
  settings    String   // JSON string of form settings
  version     Int      @default(1)
  isActive    Boolean  @default(true)
  createdBy   String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  assetTypeForms AssetTypeForm[]

  @@index([isActive])
  @@index([createdBy])
}

// Asset Type Form Associations
model AssetTypeForm {
  id            String   @id @default(cuid())
  assetTypeId   String
  formId        String
  operationType String   // "asset.create", "asset.update", "maintenance.log", etc.
  version       Int      @default(1)
  isDefault     Boolean  @default(false)
  isActive      Boolean  @default(true)
  createdBy     String
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  assetType     AssetType      @relation(fields: [assetTypeId], references: [id], onDelete: Cascade)
  form          FormDefinition @relation(fields: [formId], references: [id], onDelete: Cascade)

  @@unique([assetTypeId, operationType, isDefault])
  @@index([assetTypeId])
  @@index([operationType])
  @@index([isActive])
}

// Asset Operation History
model AssetOperationHistory {
  id            String   @id @default(cuid())
  assetId       String
  operationType String   // "create", "update", "maintenance", "transfer", "disposal", etc.
  formData      String   // JSON string of submitted form data
  performedBy   String
  performedAt   DateTime @default(now())
  notes         String?
  status        String   @default("completed") // "completed", "pending", "failed"
  metadata      String?  // JSON string of additional metadata

  // Relations
  asset         Asset    @relation(fields: [assetId], references: [id], onDelete: Cascade)

  @@index([assetId])
  @@index([operationType])
  @@index([performedAt])
  @@index([status])
}

// Depreciation Schedules (calculated and stored)
model DepreciationSchedule {
  id                String   @id @default(cuid())
  assetId           String
  year              Int
  month             Int
  depreciationAmount Float
  accumulatedDepreciation Float
  bookValue         Float
  method            String
  calculatedAt      DateTime @default(now())
  isActual          Boolean  @default(false) // true if actual, false if projected
  notes             String?

  // Relations
  asset             Asset    @relation(fields: [assetId], references: [id], onDelete: Cascade)

  @@unique([assetId, year, month])
  @@index([assetId])
  @@index([year, month])
}

// Maintenance Task Queue
model MaintenanceTask {
  id                String   @id @default(cuid())
  assetId           String
  scheduleId        String?  // Reference to MaintenanceSchedule
  title             String
  description       String?
  type              String   // "preventive", "predictive", "corrective", etc.
  priority          String   // "low", "medium", "high", "critical"
  status            String   @default("scheduled") // "scheduled", "in_progress", "completed", "cancelled", "overdue"
  scheduledDate     DateTime
  dueDate           DateTime
  completedDate     DateTime?
  assignedTo        String?
  estimatedDuration Int?     // in minutes
  actualDuration    Int?     // in minutes
  estimatedCost     Float?
  actualCost        Float?
  instructions      String?
  checklistItems    String?  // JSON string of ChecklistItem[]
  completionNotes   String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  asset             Asset    @relation(fields: [assetId], references: [id], onDelete: Cascade)

  @@index([assetId])
  @@index([status])
  @@index([scheduledDate])
  @@index([dueDate])
  @@index([assignedTo])
}

// Lifecycle State Tracking
model AssetLifecycleState {
  id              String   @id @default(cuid())
  assetId         String   @unique
  currentStageId  String
  previousStageId String?
  stageEnteredAt  DateTime @default(now())
  stageData       String?  // JSON string of stage-specific data
  nextScheduledTransition DateTime?
  isLocked        Boolean  @default(false) // prevents manual stage changes
  lockedReason    String?
  updatedBy       String
  updatedAt       DateTime @updatedAt

  // Relations
  asset           Asset    @relation(fields: [assetId], references: [id], onDelete: Cascade)

  @@index([currentStageId])
  @@index([stageEnteredAt])
  @@index([nextScheduledTransition])
}

// Asset Requisitions
model Requisition {
  id                String   @id @default(cuid())
  requestorId       String
  requestorName     String
  assetTypeId       String
  quantity          Int
  priority          String   @default("normal") // "low", "normal", "high", "critical"
  justification     String?
  businessCase      String?
  location          String?
  department        String?
  budgetCode        String?
  expectedDelivery  DateTime?
  status            String   @default("pending") // "pending", "approved", "rejected", "fulfilled", "cancelled", "partially_fulfilled"
  data              Json     // captured form data
  approvalHistory   Json?    // approval workflow history
  fulfillmentData   Json?    // fulfillment tracking data
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  approvedAt        DateTime?
  approvedBy        String?
  rejectedAt        DateTime?
  rejectedBy        String?
  rejectionReason   String?
  fulfilledAt       DateTime?
  fulfilledBy       String?

  // Relations
  assetType         AssetType @relation(fields: [assetTypeId], references: [id])
  allocations       AssetAllocation[]
  purchaseOrders    RequisitionPurchaseOrder[]
  leaseAgreements   RequisitionLeaseAgreement[]
  
  @@index([requestorId])
  @@index([assetTypeId])
  @@index([status])
  @@index([priority])
  @@index([createdAt])
  @@index([expectedDelivery])
}

// Asset Allocations (linking requisitions to actual assets)
model AssetAllocation {
  id            String   @id @default(cuid())
  requisitionId String
  assetId       String
  quantity      Int      @default(1)
  allocatedAt   DateTime @default(now())
  allocatedBy   String
  status        String   @default("allocated") // "allocated", "delivered", "returned"
  deliveredAt   DateTime?
  returnedAt    DateTime?
  notes         String?

  // Relations
  requisition   Requisition @relation(fields: [requisitionId], references: [id], onDelete: Cascade)
  asset         Asset       @relation(fields: [assetId], references: [id])

  @@index([requisitionId])
  @@index([assetId])
  @@index([status])
  @@unique([requisitionId, assetId])
}

// Link requisitions to purchase orders
model RequisitionPurchaseOrder {
  id              String   @id @default(cuid())
  requisitionId   String
  purchaseOrderId String
  quantity        Int
  createdAt       DateTime @default(now())

  // Relations
  requisition     Requisition   @relation(fields: [requisitionId], references: [id], onDelete: Cascade)
  purchaseOrder   PurchaseOrder @relation(fields: [purchaseOrderId], references: [id])

  @@index([requisitionId])
  @@index([purchaseOrderId])
  @@unique([requisitionId, purchaseOrderId])
}

// Link requisitions to lease agreements
model RequisitionLeaseAgreement {
  id              String   @id @default(cuid())
  requisitionId   String
  leaseId         String
  quantity        Int
  createdAt       DateTime @default(now())

  // Relations
  requisition     Requisition     @relation(fields: [requisitionId], references: [id], onDelete: Cascade)
  lease           LeaseAgreement  @relation(fields: [leaseId], references: [id])

  @@index([requisitionId])
  @@index([leaseId])
  @@unique([requisitionId, leaseId])
}

// Supply Chain Metrics (for dashboard)
model SupplyChainMetric {
  id          String   @id @default(cuid())
  metricType  String   // "requisition_volume", "fulfillment_rate", "avg_processing_time", etc.
  value       Float
  unit        String?  // "count", "percentage", "days", etc.
  period      String   // "daily", "weekly", "monthly", "quarterly"
  date        DateTime
  metadata    Json?    // additional metric data
  createdAt   DateTime @default(now())

  @@index([metricType])
  @@index([period])
  @@index([date])
  @@unique([metricType, period, date])
}