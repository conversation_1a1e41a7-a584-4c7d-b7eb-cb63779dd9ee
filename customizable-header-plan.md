# Customizable Header Component Plan

**Goal:** Create a reusable `AppHeader` component that can dynamically render titles, breadcrumbs, and action buttons.

**Component Location:** `components/layout/app-header.tsx`

**Component Structure:**

The component will be a React functional component that accepts props for `title`, `breadcrumbs`, and `actions`. It will use conditional rendering to display either the title or the breadcrumbs in the left section of the header and map over the `actions` array to render the action buttons in the right section.

```mermaid
graph TD
    AppHeaderComponent[AppHeader Component] --> Props
    Props --> title(string?)
    Props --> breadcrumbs({label: string, url: string}[])
    Props --> actions(React.ReactNode[])
    AppHeaderComponent --> HeaderElement(header)
    HeaderElement --> LeftSection(div)
    LeftSection -- if breadcrumbs --> RenderBreadcrumbs
    LeftSection -- else if title --> RenderTitle
    HeaderElement --> RightSection(div)
    RightSection --> RenderActions
    RenderBreadcrumbs --> BreadcrumbComponents
    RenderActions --> ActionNodes(React Nodes)
```

**Props Definition:**

We will define a TypeScript interface for the component's props:

```typescript
interface AppHeaderProps {
  title?: string;
  breadcrumbs?: { label: string; url: string }[];
  actions?: React.ReactNode[];
}
```

**Conditional Rendering (Title vs. Breadcrumbs):**

Inside the component, we will check if the `breadcrumbs` prop is provided. If it is, we will render the breadcrumbs using the existing Breadcrumb components. If `breadcrumbs` is not provided but `title` is, we will render the title.

**Rendering Actions:**

The `actions` prop will be an array of React nodes. We will simply map over this array and render each node within the right section of the header. This allows for flexibility in adding any type of React element as an action button (e.g., a standard `<Button>`, a button with an icon, a dropdown menu trigger, etc.).

**Styling:**

We will apply appropriate Tailwind CSS classes to the `header` element and its internal divs to achieve the desired layout (likely using flexbox), spacing, alignment, and responsiveness. We can reuse some of the styling from the current header in `app/layout.tsx`.

**Integration with `app/layout.tsx`:**

1.  Import the new `AppHeader` component into `app/layout.tsx`.
2.  Replace the existing `<header>` element (lines 74-93 in the provided content) with the new `<AppHeader>` component.
3.  Pass the necessary `title`, `breadcrumbs`, and `actions` props to the `AppHeader` component based on the specific page or layout being rendered. This step will need to be done for each route or layout where the header is used.