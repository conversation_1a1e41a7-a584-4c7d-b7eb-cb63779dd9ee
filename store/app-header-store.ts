import { create } from 'zustand';

export type HeaderVariant = 'default' | 'dashboard' | 'management' | 'analytics' | 'settings';

interface AppHeaderState {
  title?: string;
  description?: string;
  breadcrumbs?: { label: string; url: string }[];
  actions?: React.ReactNode[];
  variant?: HeaderVariant;
  isLoading?: boolean;
  showBackButton?: boolean;
  backUrl?: string;
}

interface AppHeaderActions {
  setHeaderContent: (content: AppHeaderState) => void;
  resetHeaderContent: () => void;
  setLoading: (loading: boolean) => void;
  updateActions: (actions: React.ReactNode[]) => void;
  setVariant: (variant: HeaderVariant) => void;
}

type AppHeaderStore = AppHeaderState & AppHeaderActions;

const initialState: AppHeaderState = {
  title: undefined,
  description: undefined,
  breadcrumbs: undefined,
  actions: undefined,
  variant: 'default',
  isLoading: false,
  showBackButton: false,
  backUrl: undefined,
};

export const useAppHeaderStore = create<AppHeaderStore>((set, get) => ({
  ...initialState,
  setHeaderContent: (content) => set({ ...content }),
  resetHeaderContent: () => set(initialState),
  setLoading: (loading) => set({ isLoading: loading }),
  updateActions: (actions) => set({ actions }),
  setVariant: (variant) => set({ variant }),
}));