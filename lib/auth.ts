import { SignJWT, jwtVerify } from 'jose';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { db } from '@/lib/db';
import { comparePassword } from '@/lib/utils';

// Define JWT secret key (should be in environment variables in production)
const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || 'your-secret-key-that-is-at-least-32-characters'
);

// Token expiration (24 hours)
export const EXPIRATION_TIME = 60 * 60 * 24;

// User session schema
export const UserSessionSchema = z.object({
  id: z.string(),
  email: z.string().email(),
  role: z.string(),
  name: z.string(),
});

export type UserSession = z.infer<typeof UserSessionSchema>;

/**
 * Creates a JWT token for a user
 */
export async function signToken(payload: UserSession) {
  const token = await new SignJWT(payload)
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime(Math.floor(Date.now() / 1000) + EXPIRATION_TIME)
    .sign(JWT_SECRET);

  return token;
}

/**
 * Verifies a JWT token and returns the payload
 */
export async function verifyToken(token: string) {
  try {
    const { payload } = await jwtVerify(token, JWT_SECRET);
    return UserSessionSchema.parse(payload);
  } catch (error) {
    return null;
  }
}

/**
 * Sets the auth cookie with JWT token
 */
export async function setAuthCookie(token: string) {
  // Get the cookies instance and wait for the promise to resolve
  const cookiesInstance = await cookies();

  // Now set the cookie
  cookiesInstance.set({
    name: 'auth-token',
    value: token,
    httpOnly: true,
    path: '/',
    secure: process.env.NODE_ENV === 'production',
    maxAge: EXPIRATION_TIME,
    sameSite: 'strict',
  });
}

/**
 * Removes the auth cookie
 */
export async function removeAuthCookie() {
  // Get the cookies instance and wait for the promise to resolve
  const cookiesInstance = await cookies();

  // Now remove the cookie by setting max-age to 0
  cookiesInstance.set({
    name: 'auth-token',
    value: '',
    httpOnly: true,
    path: '/',
    secure: process.env.NODE_ENV === 'production',
    maxAge: 0,
    sameSite: 'strict',
  });
}

/**
 * Gets the current user session from the auth cookie
 */
export async function getSession() {
  // Get the cookies instance and wait for the promise to resolve
  const cookiesInstance = await cookies();

  // Get the token from cookies
  const token = cookiesInstance.get('auth-token')?.value;

  if (!token) {
    return null;
  }

  return await verifyToken(token);
}

/**
 * Middleware function for protecting API routes
 */
export async function authorize(
  request: NextRequest,
  options: {
    roles?: string[];
  } = {}
) {
  const token = request.cookies.get('auth-token')?.value;

  if (!token) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const session = await verifyToken(token);

  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  if (options.roles && !options.roles.includes(session.role)) {
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
  }

  return session;
}

/**
 * Function to validate user credentials
 */
export async function validateCredentials(email: string, password: string) {
  try {
    const user = await db.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        name: true,
        password: true,
        role: true,
      },
    });

    if (!user || !user.password) {
      return null;
    }

    // Use comparePassword utility function from utils
    const isValid = await comparePassword(password, user.password);

    if (!isValid) {
      return null;
    }

    return {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role,
    };
  } catch (error) {
    console.error('Error validating credentials:', error);
    return null;
  }
}

