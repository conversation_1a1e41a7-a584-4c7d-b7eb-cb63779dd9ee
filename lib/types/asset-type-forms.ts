import { FormDefinition } from "@/components/form-builder";

// Asset operation types
export type AssetOperationType = 
  | "asset.create"
  | "asset.update" 
  | "asset.transfer"
  | "asset.disposal"
  | "maintenance.log"
  | "maintenance.schedule"
  | "depreciation.calculate"
  | "inventory.audit"
  | "lifecycle.transition"
  | "requisition.asset"
  | "requisition.approve"
  | "requisition.fulfill";

// Form context for runtime
export interface FormContext {
  assetId?: string;
  assetTypeId: string;
  operationType: AssetOperationType;
  userId: string;
  userRole: string;
  location?: string;
  department?: string;
  metadata?: Record<string, any>;
}

// Asset Type Form Association
export interface AssetTypeForm {
  id: string;
  assetTypeId: string;
  formId: string;
  operationType: AssetOperationType;
  version: number;
  isDefault: boolean;
  isActive: boolean;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  form?: FormDefinition;
}

// Form submission data
export interface FormSubmissionData {
  formId: string;
  assetTypeId: string;
  operationType: AssetOperationType;
  data: Record<string, any>;
  context: FormContext;
  validationResults?: ValidationResult[];
}

// Validation result
export interface ValidationResult {
  fieldId: string;
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// Form runtime configuration
export interface FormRuntimeConfig {
  prePopulateFields: boolean;
  validateOnChange: boolean;
  saveAsDraft: boolean;
  requireAllFields: boolean;
  customValidators: CustomValidator[];
  conditionalLogic: ConditionalLogicRule[];
}

// Custom validator
export interface CustomValidator {
  fieldId: string;
  validatorFunction: string; // JavaScript function as string
  errorMessage: string;
  dependencies: string[]; // Other field IDs this validator depends on
}

// Conditional logic rule
export interface ConditionalLogicRule {
  id: string;
  condition: LogicCondition;
  actions: LogicAction[];
  priority: number;
}

export interface LogicCondition {
  fieldId: string;
  operator: "equals" | "not_equals" | "contains" | "greater_than" | "less_than" | "is_empty" | "is_not_empty" | "in" | "not_in";
  value: any;
  logicalOperator?: "AND" | "OR";
  nestedConditions?: LogicCondition[];
}

export interface LogicAction {
  type: "show" | "hide" | "require" | "optional" | "set_value" | "disable" | "enable" | "validate";
  targetFieldId: string;
  value?: any;
  message?: string;
}

// Form template for reusability
export interface FormTemplate {
  id: string;
  name: string;
  description: string;
  operationType: AssetOperationType;
  formDefinition: FormDefinition;
  isPublic: boolean;
  category: string;
  tags: string[];
  usageCount: number;
  rating: number;
  createdBy: string;
  createdAt: Date;
}

// Form builder configuration for asset operations
export interface AssetOperationFormConfig {
  operationType: AssetOperationType;
  displayName: string;
  description: string;
  icon: string;
  color: string;
  requiredFields: string[];
  optionalFields: string[];
  defaultSettings: Partial<FormDefinition["settings"]>;
  validationRules: ValidationRule[];
}

export interface ValidationRule {
  fieldType: string;
  rules: {
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    pattern?: string;
    customValidator?: string;
  };
}

// Asset operation form registry
export const ASSET_OPERATION_CONFIGS: Record<AssetOperationType, AssetOperationFormConfig> = {
  "asset.create": {
    operationType: "asset.create",
    displayName: "Asset Creation",
    description: "Form for creating new assets",
    icon: "Plus",
    color: "#10B981",
    requiredFields: ["name", "category", "location", "purchasePrice", "purchaseDate"],
    optionalFields: ["serialNumber", "department", "description", "warranty"],
    defaultSettings: {
      submitButtonText: "Create Asset",
      cancelButtonText: "Cancel",
      showProgressBar: true,
      allowSaveAsDraft: true,
    },
    validationRules: [
      {
        fieldType: "text",
        rules: { required: true, minLength: 2, maxLength: 100 }
      },
      {
        fieldType: "number",
        rules: { required: true }
      }
    ]
  },
  "asset.update": {
    operationType: "asset.update",
    displayName: "Asset Update",
    description: "Form for updating existing assets",
    icon: "Edit",
    color: "#3B82F6",
    requiredFields: ["name"],
    optionalFields: ["category", "location", "department", "status"],
    defaultSettings: {
      submitButtonText: "Update Asset",
      cancelButtonText: "Cancel",
      showProgressBar: false,
      allowSaveAsDraft: true,
    },
    validationRules: []
  },
  "asset.transfer": {
    operationType: "asset.transfer",
    displayName: "Asset Transfer",
    description: "Form for transferring assets between locations/departments",
    icon: "ArrowRightLeft",
    color: "#F59E0B",
    requiredFields: ["newLocation", "transferDate", "reason"],
    optionalFields: ["newDepartment", "notes"],
    defaultSettings: {
      submitButtonText: "Transfer Asset",
      cancelButtonText: "Cancel",
      showProgressBar: false,
      allowSaveAsDraft: false,
    },
    validationRules: []
  },
  "asset.disposal": {
    operationType: "asset.disposal",
    displayName: "Asset Disposal",
    description: "Form for disposing of assets",
    icon: "Trash2",
    color: "#EF4444",
    requiredFields: ["method", "disposalDate", "reason"],
    optionalFields: ["salePrice", "notes"],
    defaultSettings: {
      submitButtonText: "Dispose Asset",
      cancelButtonText: "Cancel",
      showProgressBar: false,
      allowSaveAsDraft: false,
    },
    validationRules: []
  },
  "maintenance.log": {
    operationType: "maintenance.log",
    displayName: "Maintenance Log",
    description: "Form for logging maintenance activities",
    icon: "Wrench",
    color: "#8B5CF6",
    requiredFields: ["type", "completedDate", "performedBy"],
    optionalFields: ["notes", "cost", "parts"],
    defaultSettings: {
      submitButtonText: "Log Maintenance",
      cancelButtonText: "Cancel",
      showProgressBar: false,
      allowSaveAsDraft: true,
    },
    validationRules: []
  },
  "maintenance.schedule": {
    operationType: "maintenance.schedule",
    displayName: "Schedule Maintenance",
    description: "Form for scheduling maintenance tasks",
    icon: "Calendar",
    color: "#06B6D4",
    requiredFields: ["type", "scheduledDate", "assignedTo"],
    optionalFields: ["priority", "instructions", "estimatedCost"],
    defaultSettings: {
      submitButtonText: "Schedule Maintenance",
      cancelButtonText: "Cancel",
      showProgressBar: false,
      allowSaveAsDraft: true,
    },
    validationRules: []
  },
  "depreciation.calculate": {
    operationType: "depreciation.calculate",
    displayName: "Calculate Depreciation",
    description: "Form for calculating asset depreciation",
    icon: "TrendingDown",
    color: "#DC2626",
    requiredFields: ["method", "usefulLife", "salvageValue"],
    optionalFields: ["customRates", "startDate"],
    defaultSettings: {
      submitButtonText: "Calculate Depreciation",
      cancelButtonText: "Cancel",
      showProgressBar: true,
      allowSaveAsDraft: false,
    },
    validationRules: []
  },
  "inventory.audit": {
    operationType: "inventory.audit",
    displayName: "Inventory Audit",
    description: "Form for conducting inventory audits",
    icon: "ClipboardCheck",
    color: "#059669",
    requiredFields: ["auditDate", "location", "status"],
    optionalFields: ["notes", "discrepancies"],
    defaultSettings: {
      submitButtonText: "Complete Audit",
      cancelButtonText: "Cancel",
      showProgressBar: false,
      allowSaveAsDraft: true,
    },
    validationRules: []
  },
  "lifecycle.transition": {
    operationType: "lifecycle.transition",
    displayName: "Lifecycle Transition",
    description: "Form for transitioning asset lifecycle stages",
    icon: "GitBranch",
    color: "#7C3AED",
    requiredFields: ["newStage", "transitionDate"],
    optionalFields: ["reason", "notes"],
    defaultSettings: {
      submitButtonText: "Transition Stage",
      cancelButtonText: "Cancel",
      showProgressBar: false,
      allowSaveAsDraft: false,
    },
    validationRules: []
  },
  "requisition.asset": {
    operationType: "requisition.asset",
    displayName: "Asset Requisition",
    description: "Form for requesting new assets",
    icon: "ShoppingCart",
    color: "#16A34A",
    requiredFields: ["assetType", "quantity", "justification", "location", "expectedDelivery"],
    optionalFields: ["priority", "businessCase", "department", "budgetCode", "notes"],
    defaultSettings: {
      submitButtonText: "Submit Requisition",
      cancelButtonText: "Cancel",
      showProgressBar: true,
      allowSaveAsDraft: true,
    },
    validationRules: [
      {
        fieldType: "number",
        rules: { required: true }
      },
      {
        fieldType: "text",
        rules: { required: true, minLength: 10, maxLength: 500 }
      }
    ]
  },
  "requisition.approve": {
    operationType: "requisition.approve",
    displayName: "Approve Requisition",
    description: "Form for approving or rejecting asset requisitions",
    icon: "CheckCircle",
    color: "#2563EB",
    requiredFields: ["decision", "comments"],
    optionalFields: ["conditions", "alternativeOptions"],
    defaultSettings: {
      submitButtonText: "Submit Decision",
      cancelButtonText: "Cancel",
      showProgressBar: false,
      allowSaveAsDraft: false,
    },
    validationRules: []
  },
  "requisition.fulfill": {
    operationType: "requisition.fulfill",
    displayName: "Fulfill Requisition",
    description: "Form for fulfilling approved requisitions",
    icon: "Truck",
    color: "#DC2626",
    requiredFields: ["fulfillmentMethod", "assets"],
    optionalFields: ["deliveryDate", "notes", "trackingInfo"],
    defaultSettings: {
      submitButtonText: "Complete Fulfillment",
      cancelButtonText: "Cancel",
      showProgressBar: false,
      allowSaveAsDraft: true,
    },
    validationRules: []
  }
};