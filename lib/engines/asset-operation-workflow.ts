import { AssetOperationType, FormContext } from "@/lib/types/asset-type-forms";
import { FormRuntimeEngine } from "./form-runtime-engine";
import { DepreciationEngine } from "./depreciation-engine";
import { LifecycleEngine } from "./lifecycle-engine";
import { MaintenanceEngine } from "./maintenance-engine";
import prisma from "@/lib/prisma";

export interface AssetOperationWorkflowInput {
  operationType: AssetOperationType;
  assetId?: string;
  assetTypeId: string;
  formData: Record<string, any>;
  context: FormContext;
  skipValidation?: boolean;
  triggerWorkflows?: boolean;
}

export interface AssetOperationWorkflowResult {
  success: boolean;
  assetId?: string;
  data?: any;
  errors: string[];
  warnings: string[];
  triggeredWorkflows: string[];
  notifications: string[];
}

export class AssetOperationWorkflow {
  /**
   * Execute a complete asset operation workflow
   */
  static async executeOperation(
    input: AssetOperationWorkflowInput
  ): Promise<AssetOperationWorkflowResult> {
    const result: AssetOperationWorkflowResult = {
      success: false,
      errors: [],
      warnings: [],
      triggeredWorkflows: [],
      notifications: [],
    };

    try {
      // Step 1: Process the form submission
      const formResult = await FormRuntimeEngine.processFormSubmission({
        formId: `${input.assetTypeId}-${input.operationType}`,
        assetTypeId: input.assetTypeId,
        operationType: input.operationType,
        data: input.formData,
        context: input.context,
      });

      if (!formResult.success) {
        result.errors.push(...formResult.errors);
        return result;
      }

      result.data = formResult.data;
      result.warnings.push(...formResult.warnings);

      // Step 2: Execute operation-specific workflows
      const workflowResult = await this.executeOperationWorkflows(
        input.operationType,
        formResult.data,
        input.context
      );

      result.assetId = workflowResult.assetId || input.assetId;
      result.triggeredWorkflows.push(...workflowResult.triggeredWorkflows);
      result.notifications.push(...workflowResult.notifications);
      result.errors.push(...workflowResult.errors);
      result.warnings.push(...workflowResult.warnings);

      // Step 3: Trigger external workflows if enabled
      if (input.triggerWorkflows && result.assetId) {
        const externalWorkflows = await this.triggerExternalWorkflows(
          input.operationType,
          result.assetId,
          input.context
        );
        result.triggeredWorkflows.push(...externalWorkflows);
      }

      result.success = result.errors.length === 0;

    } catch (error) {
      result.errors.push(`Workflow execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return result;
  }

  /**
   * Execute operation-specific workflows
   */
  private static async executeOperationWorkflows(
    operationType: AssetOperationType,
    data: any,
    context: FormContext
  ): Promise<{
    assetId?: string;
    triggeredWorkflows: string[];
    notifications: string[];
    errors: string[];
    warnings: string[];
  }> {
    const result = {
      triggeredWorkflows: [] as string[],
      notifications: [] as string[],
      errors: [] as string[],
      warnings: [] as string[],
    };

    let assetId = context.assetId;

    switch (operationType) {
      case "asset.create":
        const createResult = await this.handleAssetCreation(data, context);
        assetId = createResult.assetId;
        result.triggeredWorkflows.push(...createResult.workflows);
        result.notifications.push(...createResult.notifications);
        result.errors.push(...createResult.errors);
        break;

      case "asset.update":
        if (!assetId) {
          result.errors.push("Asset ID required for update operation");
          break;
        }
        const updateResult = await this.handleAssetUpdate(assetId, data, context);
        result.triggeredWorkflows.push(...updateResult.workflows);
        result.notifications.push(...updateResult.notifications);
        result.errors.push(...updateResult.errors);
        break;

      case "asset.transfer":
        if (!assetId) {
          result.errors.push("Asset ID required for transfer operation");
          break;
        }
        const transferResult = await this.handleAssetTransfer(assetId, data, context);
        result.triggeredWorkflows.push(...transferResult.workflows);
        result.notifications.push(...transferResult.notifications);
        result.errors.push(...transferResult.errors);
        break;

      case "asset.disposal":
        if (!assetId) {
          result.errors.push("Asset ID required for disposal operation");
          break;
        }
        const disposalResult = await this.handleAssetDisposal(assetId, data, context);
        result.triggeredWorkflows.push(...disposalResult.workflows);
        result.notifications.push(...disposalResult.notifications);
        result.errors.push(...disposalResult.errors);
        break;

      case "maintenance.log":
        if (!assetId) {
          result.errors.push("Asset ID required for maintenance operation");
          break;
        }
        const maintenanceResult = await this.handleMaintenanceLog(assetId, data, context);
        result.triggeredWorkflows.push(...maintenanceResult.workflows);
        result.notifications.push(...maintenanceResult.notifications);
        result.errors.push(...maintenanceResult.errors);
        break;

      case "maintenance.schedule":
        if (!assetId) {
          result.errors.push("Asset ID required for maintenance scheduling");
          break;
        }
        const scheduleResult = await this.handleMaintenanceScheduling(assetId, data, context);
        result.triggeredWorkflows.push(...scheduleResult.workflows);
        result.notifications.push(...scheduleResult.notifications);
        result.errors.push(...scheduleResult.errors);
        break;

      case "depreciation.calculate":
        if (!assetId) {
          result.errors.push("Asset ID required for depreciation calculation");
          break;
        }
        const depreciationResult = await this.handleDepreciationCalculation(assetId, data, context);
        result.triggeredWorkflows.push(...depreciationResult.workflows);
        result.notifications.push(...depreciationResult.notifications);
        result.errors.push(...depreciationResult.errors);
        break;

      case "lifecycle.transition":
        if (!assetId) {
          result.errors.push("Asset ID required for lifecycle transition");
          break;
        }
        const lifecycleResult = await this.handleLifecycleTransition(assetId, data, context);
        result.triggeredWorkflows.push(...lifecycleResult.workflows);
        result.notifications.push(...lifecycleResult.notifications);
        result.errors.push(...lifecycleResult.errors);
        break;

      case "inventory.audit":
        const auditResult = await this.handleInventoryAudit(data, context);
        result.triggeredWorkflows.push(...auditResult.workflows);
        result.notifications.push(...auditResult.notifications);
        result.errors.push(...auditResult.errors);
        break;

      case "requisition.asset":
        const requisitionResult = await this.handleAssetRequisition(data, context);
        result.triggeredWorkflows.push(...requisitionResult.workflows);
        result.notifications.push(...requisitionResult.notifications);
        result.errors.push(...requisitionResult.errors);
        break;

      case "requisition.approve":
        const approvalResult = await this.handleRequisitionApproval(data, context);
        result.triggeredWorkflows.push(...approvalResult.workflows);
        result.notifications.push(...approvalResult.notifications);
        result.errors.push(...approvalResult.errors);
        break;

      case "requisition.fulfill":
        const fulfillmentResult = await this.handleRequisitionFulfillment(data, context);
        result.triggeredWorkflows.push(...fulfillmentResult.workflows);
        result.notifications.push(...fulfillmentResult.notifications);
        result.errors.push(...fulfillmentResult.errors);
        break;
    }

    return { assetId, ...result };
  }

  /**
   * Handle asset creation workflow
   */
  private static async handleAssetCreation(
    data: any,
    context: FormContext
  ): Promise<{
    assetId?: string;
    workflows: string[];
    notifications: string[];
    errors: string[];
  }> {
    const result = {
      workflows: [] as string[],
      notifications: [] as string[],
      errors: [] as string[],
    };

    try {
      // Asset is already created by FormRuntimeEngine
      const assetId = data.id;

      // Initialize lifecycle
      await LifecycleEngine.initializeAssetLifecycle(
        assetId,
        context.assetTypeId,
        context.userId
      );
      result.workflows.push("lifecycle.initialize");

      // Calculate initial depreciation
      const asset = await prisma.asset.findUnique({
        where: { id: assetId },
        include: {
          assetType: {
            include: {
              depreciationSettings: true,
            },
          },
        },
      });

      if (asset?.assetType?.depreciationSettings) {
        const depreciationResult = await DepreciationEngine.calculateDepreciation({
          assetId,
          purchasePrice: asset.purchasePrice,
          purchaseDate: asset.purchaseDate,
          settings: asset.assetType.depreciationSettings,
        });

        await DepreciationEngine.saveDepreciationSchedule(assetId, depreciationResult.schedule);
        result.workflows.push("depreciation.calculate");
      }

      // Generate initial maintenance tasks
      const maintenanceResult = await MaintenanceEngine.generateTasksForAssetType(context.assetTypeId);
      if (maintenanceResult.tasksGenerated > 0) {
        result.workflows.push("maintenance.schedule");
      }

      // Send creation notification
      await this.sendNotification({
        recipient: context.userId,
        subject: "Asset Created Successfully",
        message: `Asset "${data.name}" has been created and configured.`,
        type: "asset",
        priority: "normal",
      });
      result.notifications.push("asset.created");

      return { assetId, ...result };

    } catch (error) {
      result.errors.push(`Asset creation workflow failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return result;
    }
  }

  /**
   * Handle asset update workflow
   */
  private static async handleAssetUpdate(
    assetId: string,
    data: any,
    context: FormContext
  ): Promise<{
    workflows: string[];
    notifications: string[];
    errors: string[];
  }> {
    const result = {
      workflows: [] as string[],
      notifications: [] as string[],
      errors: [] as string[],
    };

    try {
      // Check if depreciation needs recalculation
      if (data.purchasePrice || data.purchaseDate) {
        const asset = await prisma.asset.findUnique({
          where: { id: assetId },
          include: {
            assetType: {
              include: {
                depreciationSettings: true,
              },
            },
          },
        });

        if (asset?.assetType?.depreciationSettings) {
          const depreciationResult = await DepreciationEngine.calculateDepreciation({
            assetId,
            purchasePrice: data.purchasePrice || asset.purchasePrice,
            purchaseDate: new Date(data.purchaseDate || asset.purchaseDate),
            settings: asset.assetType.depreciationSettings,
          });

          await DepreciationEngine.saveDepreciationSchedule(assetId, depreciationResult.schedule);
          result.workflows.push("depreciation.recalculate");
        }
      }

      // Send update notification
      await this.sendNotification({
        recipient: context.userId,
        subject: "Asset Updated",
        message: `Asset "${data.name || 'Unknown'}" has been updated.`,
        type: "asset",
        priority: "normal",
      });
      result.notifications.push("asset.updated");

      return result;

    } catch (error) {
      result.errors.push(`Asset update workflow failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return result;
    }
  }

  /**
   * Handle asset transfer workflow
   */
  private static async handleAssetTransfer(
    assetId: string,
    data: any,
    context: FormContext
  ): Promise<{
    workflows: string[];
    notifications: string[];
    errors: string[];
  }> {
    const result = {
      workflows: [] as string[],
      notifications: [] as string[],
      errors: [] as string[],
    };

    try {
      // Check if lifecycle transition is needed
      const availableTransitions = await LifecycleEngine.getAvailableTransitions(assetId);
      const transferTransition = availableTransitions.find(t => 
        t.code === "IN_USE" || t.name.toLowerCase().includes("transfer")
      );

      if (transferTransition) {
        const transitionResult = await LifecycleEngine.transitionAsset({
          assetId,
          toStageId: transferTransition.id,
          userId: context.userId,
          reason: `Asset transferred to ${data.newLocation}`,
          data: {
            newLocation: data.newLocation,
            newDepartment: data.newDepartment,
          },
        });

        if (transitionResult.success) {
          result.workflows.push("lifecycle.transition");
        }
      }

      // Notify relevant parties
      const notifications = [
        {
          recipient: context.userId,
          subject: "Asset Transfer Completed",
          message: `Asset has been transferred to ${data.newLocation}.`,
        },
      ];

      if (data.newDepartment && data.newDepartment !== context.department) {
        notifications.push({
          recipient: `department:${data.newDepartment}`,
          subject: "New Asset Assigned",
          message: `An asset has been transferred to your department.`,
        });
      }

      for (const notification of notifications) {
        await this.sendNotification({
          ...notification,
          type: "asset",
          priority: "normal",
        });
      }
      result.notifications.push("asset.transferred");

      return result;

    } catch (error) {
      result.errors.push(`Asset transfer workflow failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return result;
    }
  }

  /**
   * Handle asset disposal workflow
   */
  private static async handleAssetDisposal(
    assetId: string,
    data: any,
    context: FormContext
  ): Promise<{
    workflows: string[];
    notifications: string[];
    errors: string[];
  }> {
    const result = {
      workflows: [] as string[],
      notifications: [] as string[],
      errors: [] as string[],
    };

    try {
      // Transition to disposal lifecycle stage
      const availableTransitions = await LifecycleEngine.getAvailableTransitions(assetId);
      const disposalTransition = availableTransitions.find(t => 
        t.code === "DISPOSED" || t.name.toLowerCase().includes("disposal")
      );

      if (disposalTransition) {
        const transitionResult = await LifecycleEngine.transitionAsset({
          assetId,
          toStageId: disposalTransition.id,
          userId: context.userId,
          reason: `Asset disposed via ${data.method}`,
          data: {
            disposalMethod: data.method,
            disposalDate: data.disposalDate,
            salePrice: data.salePrice,
          },
        });

        if (transitionResult.success) {
          result.workflows.push("lifecycle.transition");
        }
      }

      // Cancel any pending maintenance tasks
      await prisma.maintenanceTask.updateMany({
        where: {
          assetId,
          status: {
            in: ["scheduled", "in_progress"],
          },
        },
        data: {
          status: "cancelled",
        },
      });
      result.workflows.push("maintenance.cancel");

      // Send disposal notification
      await this.sendNotification({
        recipient: context.userId,
        subject: "Asset Disposal Completed",
        message: `Asset has been disposed of via ${data.method}.`,
        type: "asset",
        priority: "normal",
      });
      result.notifications.push("asset.disposed");

      return result;

    } catch (error) {
      result.errors.push(`Asset disposal workflow failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return result;
    }
  }

  /**
   * Handle maintenance log workflow
   */
  private static async handleMaintenanceLog(
    assetId: string,
    data: any,
    context: FormContext
  ): Promise<{
    workflows: string[];
    notifications: string[];
    errors: string[];
  }> {
    const result = {
      workflows: [] as string[],
      notifications: [] as string[],
      errors: [] as string[],
    };

    try {
      // Complete any related maintenance task
      if (data.taskId) {
        await MaintenanceEngine.completeMaintenanceTask(data.taskId, {
          completedBy: context.userId,
          completionNotes: data.notes,
          actualDuration: data.actualDuration,
          actualCost: data.actualCost,
        });
        result.workflows.push("maintenance.complete");
      }

      // Generate next maintenance tasks if needed
      const maintenanceResult = await MaintenanceEngine.generateTasksForAssetType(context.assetTypeId);
      if (maintenanceResult.tasksGenerated > 0) {
        result.workflows.push("maintenance.schedule");
      }

      // Send maintenance notification
      await this.sendNotification({
        recipient: context.userId,
        subject: "Maintenance Logged",
        message: `Maintenance activity "${data.type}" has been logged for the asset.`,
        type: "maintenance",
        priority: "normal",
      });
      result.notifications.push("maintenance.logged");

      return result;

    } catch (error) {
      result.errors.push(`Maintenance log workflow failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return result;
    }
  }

  /**
   * Handle maintenance scheduling workflow
   */
  private static async handleMaintenanceScheduling(
    assetId: string,
    data: any,
    context: FormContext
  ): Promise<{
    workflows: string[];
    notifications: string[];
    errors: string[];
  }> {
    const result = {
      workflows: [] as string[],
      notifications: [] as string[],
      errors: [] as string[],
    };

    try {
      // Create maintenance task
      await prisma.maintenanceTask.create({
        data: {
          assetId,
          title: data.title || data.type,
          description: data.description,
          type: data.type,
          priority: data.priority || "medium",
          scheduledDate: new Date(data.scheduledDate),
          dueDate: new Date(data.dueDate || data.scheduledDate),
          assignedTo: data.assignedTo,
          estimatedDuration: data.estimatedDuration,
          estimatedCost: data.estimatedCost,
          instructions: data.instructions,
        },
      });
      result.workflows.push("maintenance.schedule");

      // Send scheduling notification
      if (data.assignedTo) {
        await this.sendNotification({
          recipient: data.assignedTo,
          subject: "Maintenance Task Assigned",
          message: `You have been assigned a maintenance task for ${data.scheduledDate}.`,
          type: "maintenance",
          priority: data.priority || "normal",
        });
      }
      result.notifications.push("maintenance.scheduled");

      return result;

    } catch (error) {
      result.errors.push(`Maintenance scheduling workflow failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return result;
    }
  }

  /**
   * Handle depreciation calculation workflow
   */
  private static async handleDepreciationCalculation(
    assetId: string,
    data: any,
    context: FormContext
  ): Promise<{
    workflows: string[];
    notifications: string[];
    errors: string[];
  }> {
    const result = {
      workflows: [] as string[],
      notifications: [] as string[],
      errors: [] as string[],
    };

    try {
      const asset = await prisma.asset.findUnique({
        where: { id: assetId },
        include: {
          assetType: {
            include: {
              depreciationSettings: true,
            },
          },
        },
      });

      if (!asset?.assetType?.depreciationSettings) {
        result.errors.push("No depreciation settings found for asset type");
        return result;
      }

      const depreciationResult = await DepreciationEngine.calculateDepreciation({
        assetId,
        purchasePrice: asset.purchasePrice,
        purchaseDate: asset.purchaseDate,
        settings: asset.assetType.depreciationSettings,
      });

      await DepreciationEngine.saveDepreciationSchedule(assetId, depreciationResult.schedule);
      result.workflows.push("depreciation.calculate");

      // Send calculation notification
      await this.sendNotification({
        recipient: context.userId,
        subject: "Depreciation Calculated",
        message: `Depreciation schedule has been calculated and updated for the asset.`,
        type: "asset",
        priority: "normal",
      });
      result.notifications.push("depreciation.calculated");

      return result;

    } catch (error) {
      result.errors.push(`Depreciation calculation workflow failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return result;
    }
  }

  /**
   * Handle lifecycle transition workflow
   */
  private static async handleLifecycleTransition(
    assetId: string,
    data: any,
    context: FormContext
  ): Promise<{
    workflows: string[];
    notifications: string[];
    errors: string[];
  }> {
    const result = {
      workflows: [] as string[],
      notifications: [] as string[],
      errors: [] as string[],
    };

    try {
      const transitionResult = await LifecycleEngine.transitionAsset({
        assetId,
        fromStageId: data.fromStageId,
        toStageId: data.toStageId,
        userId: context.userId,
        reason: data.reason,
        data: data.stageData,
      });

      if (transitionResult.success) {
        result.workflows.push("lifecycle.transition");
        result.workflows.push(...transitionResult.triggeredActions);
        result.notifications.push(...transitionResult.notifications);
      } else {
        result.errors.push(...transitionResult.errors);
      }

      return result;

    } catch (error) {
      result.errors.push(`Lifecycle transition workflow failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return result;
    }
  }

  /**
   * Handle inventory audit workflow
   */
  private static async handleInventoryAudit(
    data: any,
    context: FormContext
  ): Promise<{
    workflows: string[];
    notifications: string[];
    errors: string[];
  }> {
    const result = {
      workflows: [] as string[],
      notifications: [] as string[],
      errors: [] as string[],
    };

    try {
      // Create inventory check record
      await prisma.inventoryCheck.create({
        data: {
          location: data.location,
          department: data.department,
          checkDate: new Date(data.checkDate),
          performedBy: context.userId,
          assetsChecked: data.assetsChecked || 0,
          assetsFound: data.assetsFound || 0,
          assetsMissing: data.assetsMissing || 0,
          missingAssets: JSON.stringify(data.missingAssetsList || []),
        },
      });
      result.workflows.push("inventory.audit");

      // Send audit notification
      await this.sendNotification({
        recipient: context.userId,
        subject: "Inventory Audit Completed",
        message: `Inventory audit for ${data.location} has been completed.`,
        type: "inventory",
        priority: "normal",
      });
      result.notifications.push("inventory.audited");

      return result;

    } catch (error) {
      result.errors.push(`Inventory audit workflow failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return result;
    }
  }

  /**
   * Trigger external workflows
   */
  private static async triggerExternalWorkflows(
    operationType: AssetOperationType,
    assetId: string,
    context: FormContext
  ): Promise<string[]> {
    const triggeredWorkflows: string[] = [];

    try {
      // Find workflows that should be triggered for this operation
      const workflows = await prisma.workflow.findMany({
        where: {
          type: "asset-automation",
          isActive: true,
        },
      });

      for (const workflow of workflows) {
        // Check if workflow should be triggered for this operation
        const nodes = JSON.parse(workflow.nodes);
        const triggerNode = nodes.find((node: any) => 
          node.type === "trigger" && 
          node.data?.operationType === operationType
        );

        if (triggerNode) {
          // Execute workflow (simplified - would integrate with actual workflow engine)
          await this.executeWorkflow(workflow.id, {
            assetId,
            operationType,
            context,
          });
          triggeredWorkflows.push(workflow.name);
        }
      }

    } catch (error) {
      console.error("Error triggering external workflows:", error);
    }

    return triggeredWorkflows;
  }

  /**
   * Execute a workflow (placeholder for actual workflow engine integration)
   */
  private static async executeWorkflow(
    workflowId: string,
    data: Record<string, any>
  ): Promise<void> {
    // This would integrate with the actual workflow engine
    // For now, just create a workflow execution record
    await prisma.workflowExecution.create({
      data: {
        workflowId,
        status: "queued",
        input: JSON.stringify(data),
        startedAt: new Date(),
        context: JSON.stringify({
          triggeredBy: "asset-operation",
          timestamp: new Date().toISOString(),
        }),
      },
    });
  }

  /**
   * Handle asset requisition workflow
   */
  private static async handleAssetRequisition(
    data: any,
    context: FormContext
  ): Promise<{
    workflows: string[];
    notifications: string[];
    errors: string[];
  }> {
    const result = {
      workflows: [] as string[],
      notifications: [] as string[],
      errors: [] as string[],
    };

    try {
      // Import RequisitionService dynamically to avoid circular dependency
      const { RequisitionService } = await import("@/lib/services/requisition-service");

      const requisitionResult = await RequisitionService.createRequisition({
        requestorId: context.userId,
        requestorName: data.requestorName || "Unknown",
        assetTypeId: context.assetTypeId,
        quantity: data.quantity,
        priority: data.priority || "normal",
        justification: data.justification,
        businessCase: data.businessCase,
        location: data.location,
        department: data.department,
        budgetCode: data.budgetCode,
        expectedDelivery: data.expectedDelivery ? new Date(data.expectedDelivery) : undefined,
        data: data,
      });

      if (requisitionResult.success) {
        result.workflows.push("requisition.created");
        
        // Send notification to approvers
        await this.sendNotification({
          recipient: "role:manager",
          subject: "New Asset Requisition",
          message: `A new asset requisition has been submitted for ${data.quantity} ${data.assetTypeName || 'assets'}.`,
          type: "requisition",
          priority: data.priority === "critical" ? "high" : "normal",
        });
        result.notifications.push("requisition.submitted");
      } else {
        result.errors.push(requisitionResult.error || "Failed to create requisition");
      }

      return result;

    } catch (error) {
      result.errors.push(`Asset requisition workflow failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return result;
    }
  }

  /**
   * Handle requisition approval workflow
   */
  private static async handleRequisitionApproval(
    data: any,
    context: FormContext
  ): Promise<{
    workflows: string[];
    notifications: string[];
    errors: string[];
  }> {
    const result = {
      workflows: [] as string[],
      notifications: [] as string[],
      errors: [] as string[],
    };

    try {
      const { RequisitionService } = await import("@/lib/services/requisition-service");

      const approvalResult = await RequisitionService.processApproval(data.requisitionId, {
        approverId: context.userId,
        decision: data.decision,
        comments: data.comments,
        conditions: data.conditions,
        alternativeOptions: data.alternativeOptions,
      });

      if (approvalResult.success) {
        result.workflows.push(`requisition.${data.decision}`);
        
        // Send notification to requestor
        await this.sendNotification({
          recipient: approvalResult.requisition!.requestorId,
          subject: `Requisition ${data.decision === "approved" ? "Approved" : "Rejected"}`,
          message: `Your asset requisition has been ${data.decision}. ${data.comments}`,
          type: "requisition",
          priority: "normal",
        });
        result.notifications.push(`requisition.${data.decision}.notified`);

        // If approved, trigger fulfillment workflow
        if (data.decision === "approved") {
          result.workflows.push("fulfillment.triggered");
        }
      } else {
        result.errors.push(approvalResult.error || "Failed to process approval");
      }

      return result;

    } catch (error) {
      result.errors.push(`Requisition approval workflow failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return result;
    }
  }

  /**
   * Handle requisition fulfillment workflow
   */
  private static async handleRequisitionFulfillment(
    data: any,
    context: FormContext
  ): Promise<{
    workflows: string[];
    notifications: string[];
    errors: string[];
  }> {
    const result = {
      workflows: [] as string[],
      notifications: [] as string[],
      errors: [] as string[],
    };

    try {
      const { RequisitionService } = await import("@/lib/services/requisition-service");

      const fulfillmentResult = await RequisitionService.fulfillRequisition(data.requisitionId, {
        fulfillerId: context.userId,
        method: data.method,
        assetIds: data.assetIds,
        purchaseOrderId: data.purchaseOrderId,
        leaseAgreementId: data.leaseAgreementId,
        deliveryDate: data.deliveryDate ? new Date(data.deliveryDate) : undefined,
        notes: data.notes,
        trackingInfo: data.trackingInfo,
      });

      if (fulfillmentResult.success) {
        result.workflows.push(`fulfillment.${data.method}`);
        
        // Send notification to requestor
        await this.sendNotification({
          recipient: fulfillmentResult.requisition!.requestorId,
          subject: "Requisition Fulfilled",
          message: `Your asset requisition has been fulfilled via ${data.method}.`,
          type: "requisition",
          priority: "normal",
        });
        result.notifications.push("fulfillment.completed");

        // If fulfilled via inventory, update asset statuses
        if (data.method === "inventory" && data.assetIds?.length) {
          for (const assetId of data.assetIds) {
            await prisma.asset.update({
              where: { id: assetId },
              data: { status: "allocated" },
            });
          }
          result.workflows.push("assets.allocated");
        }
      } else {
        result.errors.push(fulfillmentResult.error || "Failed to fulfill requisition");
      }

      return result;

    } catch (error) {
      result.errors.push(`Requisition fulfillment workflow failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return result;
    }
  }

  /**
   * Send notification
   */
  private static async sendNotification(notification: {
    recipient: string;
    subject: string;
    message: string;
    type: string;
    priority: string;
  }): Promise<void> {
    await prisma.notification.create({
      data: {
        recipient: notification.recipient,
        subject: notification.subject,
        message: notification.message,
        type: notification.type,
        priority: notification.priority,
        status: "sent",
      },
    });
  }
}