import { LifecycleStage, AutomatedAction, NotificationRule } from "@/lib/modules/asset-types/types";
import prisma from "@/lib/prisma";

export interface LifecycleTransitionInput {
  assetId: string;
  fromStageId?: string;
  toStageId: string;
  userId: string;
  reason?: string;
  data?: Record<string, any>;
  skipValidation?: boolean;
}

export interface LifecycleTransitionResult {
  success: boolean;
  errors: string[];
  warnings: string[];
  triggeredActions: string[];
  notifications: string[];
}

export interface LifecycleValidationResult {
  canTransition: boolean;
  missingRequiredFields: string[];
  errors: string[];
  warnings: string[];
}

export class LifecycleEngine {
  /**
   * Transition an asset to a new lifecycle stage
   */
  static async transitionAsset(
    input: LifecycleTransitionInput
  ): Promise<LifecycleTransitionResult> {
    const { assetId, fromStageId, toStageId, userId, reason, data, skipValidation } = input;
    
    const result: LifecycleTransitionResult = {
      success: false,
      errors: [],
      warnings: [],
      triggeredActions: [],
      notifications: [],
    };
    
    try {
      // Get asset with current lifecycle state
      const asset = await prisma.asset.findUnique({
        where: { id: assetId },
        include: {
          assetType: {
            include: {
              lifecycleStages: true,
            },
          },
          lifecycleState: true,
        },
      });
      
      if (!asset) {
        result.errors.push("Asset not found");
        return result;
      }
      
      if (!asset.assetType) {
        result.errors.push("Asset type not found");
        return result;
      }
      
      // Find target stage
      const targetStage = asset.assetType.lifecycleStages.find(
        stage => stage.id === toStageId
      );
      
      if (!targetStage) {
        result.errors.push("Target lifecycle stage not found");
        return result;
      }
      
      // Validate transition if not skipped
      if (!skipValidation) {
        const validation = await this.validateTransition(
          asset,
          fromStageId,
          toStageId,
          data
        );
        
        if (!validation.canTransition) {
          result.errors.push(...validation.errors);
          return result;
        }
        
        result.warnings.push(...validation.warnings);
      }
      
      // Perform the transition
      await prisma.$transaction(async (tx) => {
        // Update or create lifecycle state
        await tx.assetLifecycleState.upsert({
          where: { assetId },
          update: {
            previousStageId: fromStageId || asset.lifecycleState?.currentStageId,
            currentStageId: toStageId,
            stageEnteredAt: new Date(),
            stageData: data ? JSON.stringify(data) : null,
            updatedBy: userId,
            updatedAt: new Date(),
          },
          create: {
            assetId,
            currentStageId: toStageId,
            previousStageId: fromStageId,
            stageEnteredAt: new Date(),
            stageData: data ? JSON.stringify(data) : null,
            updatedBy: userId,
          },
        });
        
        // Log the operation
        await tx.assetOperationHistory.create({
          data: {
            assetId,
            operationType: "lifecycle.transition",
            formData: JSON.stringify({
              fromStageId,
              toStageId,
              reason,
              data,
            }),
            performedBy: userId,
            status: "completed",
          },
        });
      });
      
      // Execute automated actions
      const actionResults = await this.executeAutomatedActions(
        asset,
        targetStage,
        "stage_enter",
        data
      );
      result.triggeredActions.push(...actionResults);
      
      // Send notifications
      const notificationResults = await this.sendNotifications(
        asset,
        targetStage,
        "stage_change",
        { fromStageId, toStageId, userId }
      );
      result.notifications.push(...notificationResults);
      
      result.success = true;
      
    } catch (error) {
      result.errors.push(`Transition failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
    
    return result;
  }
  
  /**
   * Validate if a lifecycle transition is allowed
   */
  static async validateTransition(
    asset: any,
    fromStageId: string | undefined,
    toStageId: string,
    data?: Record<string, any>
  ): Promise<LifecycleValidationResult> {
    const result: LifecycleValidationResult = {
      canTransition: false,
      missingRequiredFields: [],
      errors: [],
      warnings: [],
    };
    
    const lifecycleStages = asset.assetType.lifecycleStages;
    const currentStage = fromStageId 
      ? lifecycleStages.find((stage: any) => stage.id === fromStageId)
      : null;
    const targetStage = lifecycleStages.find((stage: any) => stage.id === toStageId);
    
    if (!targetStage) {
      result.errors.push("Target stage not found");
      return result;
    }
    
    // Check if transition is allowed
    if (currentStage && !currentStage.allowedTransitions.includes(toStageId)) {
      result.errors.push(`Transition from ${currentStage.name} to ${targetStage.name} is not allowed`);
      return result;
    }
    
    // Check required fields
    if (targetStage.requiredFields && targetStage.requiredFields.length > 0) {
      const missingFields = targetStage.requiredFields.filter((fieldId: string) => {
        return !data || !data[fieldId] || data[fieldId] === "";
      });
      
      if (missingFields.length > 0) {
        result.missingRequiredFields = missingFields;
        result.errors.push(`Missing required fields: ${missingFields.join(", ")}`);
        return result;
      }
    }
    
    // Check if stage is active
    if (!targetStage.isActive) {
      result.errors.push("Target stage is not active");
      return result;
    }
    
    result.canTransition = true;
    return result;
  }
  
  /**
   * Get available transitions for an asset
   */
  static async getAvailableTransitions(assetId: string): Promise<LifecycleStage[]> {
    const asset = await prisma.asset.findUnique({
      where: { id: assetId },
      include: {
        assetType: {
          include: {
            lifecycleStages: true,
          },
        },
        lifecycleState: true,
      },
    });
    
    if (!asset || !asset.assetType) {
      return [];
    }
    
    const currentStageId = asset.lifecycleState?.currentStageId;
    const currentStage = asset.assetType.lifecycleStages.find(
      stage => stage.id === currentStageId
    );
    
    if (!currentStage) {
      // If no current stage, return initial stages
      return asset.assetType.lifecycleStages.filter(stage => stage.isInitial && stage.isActive);
    }
    
    // Return allowed transitions
    return asset.assetType.lifecycleStages.filter(
      stage => currentStage.allowedTransitions.includes(stage.id) && stage.isActive
    );
  }
  
  /**
   * Execute automated actions for a lifecycle stage
   */
  private static async executeAutomatedActions(
    asset: any,
    stage: LifecycleStage,
    trigger: string,
    data?: Record<string, any>
  ): Promise<string[]> {
    const executedActions: string[] = [];
    
    if (!stage.automatedActions) return executedActions;
    
    for (const action of stage.automatedActions) {
      if (!action.isActive || action.trigger.event !== trigger) continue;
      
      try {
        // Check conditions
        if (action.conditions && action.conditions.length > 0) {
          const conditionsMet = this.evaluateConditions(action.conditions, data);
          if (!conditionsMet) continue;
        }
        
        // Execute action steps
        for (const step of action.actions) {
          await this.executeActionStep(asset, step, data);
        }
        
        executedActions.push(action.name);
        
      } catch (error) {
        console.error(`Failed to execute automated action ${action.name}:`, error);
      }
    }
    
    return executedActions;
  }
  
  /**
   * Execute a single action step
   */
  private static async executeActionStep(
    asset: any,
    step: any,
    data?: Record<string, any>
  ): Promise<void> {
    switch (step.type) {
      case "update_field":
        await prisma.asset.update({
          where: { id: asset.id },
          data: {
            [step.parameters.fieldName]: step.parameters.value,
          },
        });
        break;
        
      case "send_notification":
        await prisma.notification.create({
          data: {
            recipient: step.parameters.recipient,
            subject: step.parameters.subject,
            message: step.parameters.message,
            type: "lifecycle",
            priority: step.parameters.priority || "normal",
            status: "sent",
          },
        });
        break;
        
      case "create_task":
        await prisma.maintenanceTask.create({
          data: {
            assetId: asset.id,
            title: step.parameters.title,
            description: step.parameters.description,
            type: step.parameters.type || "corrective",
            priority: step.parameters.priority || "medium",
            scheduledDate: new Date(step.parameters.scheduledDate),
            dueDate: new Date(step.parameters.dueDate),
            assignedTo: step.parameters.assignedTo,
          },
        });
        break;
        
      case "call_api":
        // Implement API call logic
        break;
        
      case "run_calculation":
        // Implement calculation logic
        break;
    }
  }
  
  /**
   * Send notifications for lifecycle events
   */
  private static async sendNotifications(
    asset: any,
    stage: LifecycleStage,
    trigger: string,
    context: Record<string, any>
  ): Promise<string[]> {
    const sentNotifications: string[] = [];
    
    if (!stage.notifications) return sentNotifications;
    
    for (const notification of stage.notifications) {
      if (!notification.isActive || notification.trigger.event !== trigger) continue;
      
      try {
        // Check conditions
        if (notification.trigger.conditions && notification.trigger.conditions.length > 0) {
          const conditionsMet = this.evaluateConditions(notification.trigger.conditions, context);
          if (!conditionsMet) continue;
        }
        
        // Send to each recipient
        for (const recipient of notification.recipients) {
          await this.sendNotificationToRecipient(
            recipient,
            notification.template,
            asset,
            stage,
            context
          );
        }
        
        sentNotifications.push(notification.name);
        
      } catch (error) {
        console.error(`Failed to send notification ${notification.name}:`, error);
      }
    }
    
    return sentNotifications;
  }
  
  /**
   * Send notification to a specific recipient
   */
  private static async sendNotificationToRecipient(
    recipient: any,
    template: any,
    asset: any,
    stage: LifecycleStage,
    context: Record<string, any>
  ): Promise<void> {
    const subject = this.replaceTemplateVariables(template.subject, asset, stage, context);
    const message = this.replaceTemplateVariables(template.body, asset, stage, context);
    
    await prisma.notification.create({
      data: {
        recipient: recipient.identifier,
        subject,
        message,
        type: "lifecycle",
        priority: "normal",
        status: "sent",
      },
    });
  }
  
  /**
   * Replace template variables with actual values
   */
  private static replaceTemplateVariables(
    template: string,
    asset: any,
    stage: LifecycleStage,
    context: Record<string, any>
  ): string {
    return template
      .replace(/\{asset\.name\}/g, asset.name)
      .replace(/\{asset\.id\}/g, asset.id)
      .replace(/\{stage\.name\}/g, stage.name)
      .replace(/\{stage\.description\}/g, stage.description)
      .replace(/\{context\.(\w+)\}/g, (match, key) => context[key] || match);
  }
  
  /**
   * Evaluate conditions for actions and notifications
   */
  private static evaluateConditions(
    conditions: any[],
    data?: Record<string, any>
  ): boolean {
    if (!conditions || conditions.length === 0) return true;
    if (!data) return false;
    
    // Simple condition evaluation - can be enhanced
    return conditions.every(condition => {
      const value = data[condition.fieldId];
      
      switch (condition.operator) {
        case "equals":
          return value === condition.value;
        case "not_equals":
          return value !== condition.value;
        case "greater_than":
          return Number(value) > Number(condition.value);
        case "less_than":
          return Number(value) < Number(condition.value);
        case "is_empty":
          return !value || value === "";
        case "is_not_empty":
          return value && value !== "";
        default:
          return false;
      }
    });
  }
  
  /**
   * Initialize lifecycle state for a new asset
   */
  static async initializeAssetLifecycle(
    assetId: string,
    assetTypeId: string,
    userId: string
  ): Promise<void> {
    const assetType = await prisma.assetType.findUnique({
      where: { id: assetTypeId },
      include: {
        lifecycleStages: true,
      },
    });
    
    if (!assetType || !assetType.lifecycleStages) return;
    
    // Find initial stage
    const initialStage = assetType.lifecycleStages.find(stage => stage.isInitial);
    
    if (initialStage) {
      await prisma.assetLifecycleState.create({
        data: {
          assetId,
          currentStageId: initialStage.id,
          stageEnteredAt: new Date(),
          updatedBy: userId,
        },
      });
    }
  }
  
  /**
   * Get lifecycle history for an asset
   */
  static async getLifecycleHistory(assetId: string): Promise<any[]> {
    return prisma.assetOperationHistory.findMany({
      where: {
        assetId,
        operationType: "lifecycle.transition",
      },
      orderBy: {
        performedAt: "desc",
      },
    });
  }
}