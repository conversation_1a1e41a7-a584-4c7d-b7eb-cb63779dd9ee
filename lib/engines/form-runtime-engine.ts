import { FormDefinition } from "@/components/form-builder";
import { AssetOperationType, FormContext, FormSubmissionData, ValidationResult } from "@/lib/types/asset-type-forms";
import { CustomField } from "@/lib/modules/asset-types/types";
import prisma from "@/lib/prisma";

export interface FormRuntimeResult {
  success: boolean;
  data?: any;
  errors: string[];
  warnings: string[];
  validationResults: ValidationResult[];
}

export interface FormPrePopulationData {
  [fieldId: string]: any;
}

export class FormRuntimeEngine {
  /**
   * Get form definition for a specific asset type and operation
   */
  static async getFormForOperation(
    assetTypeId: string,
    operationType: AssetOperationType,
    context: FormContext
  ): Promise<FormDefinition | null> {
    try {
      // Find the form association
      const assetTypeForm = await prisma.assetTypeForm.findFirst({
        where: {
          assetTypeId,
          operationType,
          isActive: true,
          isDefault: true,
        },
        include: {
          form: true,
        },
      });
      
      if (!assetTypeForm?.form) {
        return null;
      }
      
      // Parse the form definition
      const formDefinition: FormDefinition = {
        id: assetTypeForm.form.id,
        name: assetTypeForm.form.name,
        description: assetTypeForm.form.description,
        sections: JSON.parse(assetTypeForm.form.sections),
        settings: JSON.parse(assetTypeForm.form.settings),
      };
      
      // Apply context-specific modifications
      return this.applyContextToForm(formDefinition, context);
      
    } catch (error) {
      console.error("Error getting form for operation:", error);
      return null;
    }
  }
  
  /**
   * Apply context-specific modifications to form
   */
  private static applyContextToForm(
    form: FormDefinition,
    context: FormContext
  ): FormDefinition {
    // Clone the form to avoid mutations
    const modifiedForm = JSON.parse(JSON.stringify(form));
    
    // Apply user role-based field visibility
    if (context.userRole) {
      modifiedForm.sections = modifiedForm.sections.map((section: any) => ({
        ...section,
        fields: section.fields.filter((fieldId: string) => 
          this.isFieldVisibleForRole(fieldId, context.userRole)
        ),
      }));
    }
    
    // Apply location-based restrictions
    if (context.location) {
      // Add location-specific validation or field modifications
    }
    
    return modifiedForm;
  }
  
  /**
   * Check if field is visible for user role
   */
  private static isFieldVisibleForRole(fieldId: string, userRole: string): boolean {
    // Define role-based field visibility rules
    const roleRestrictions: Record<string, string[]> = {
      "user": ["basic_info", "description", "location"],
      "manager": ["basic_info", "description", "location", "financial", "approval"],
      "admin": [], // Admin can see all fields
    };
    
    const restrictedFields = roleRestrictions[userRole];
    if (!restrictedFields) return true; // If no restrictions defined, show all
    if (restrictedFields.length === 0) return true; // Empty array means no restrictions
    
    return restrictedFields.includes(fieldId);
  }
  
  /**
   * Pre-populate form with existing data
   */
  static async getPrePopulationData(
    assetId: string | undefined,
    assetTypeId: string,
    operationType: AssetOperationType,
    context: FormContext
  ): Promise<FormPrePopulationData> {
    const data: FormPrePopulationData = {};
    
    try {
      // Get asset type with custom fields
      const assetType = await prisma.assetType.findUnique({
        where: { id: assetTypeId },
        include: {
          customFields: true,
        },
      });
      
      if (!assetType) return data;
      
      // Pre-populate with default values from custom fields
      for (const field of assetType.customFields) {
        if (field.defaultValue) {
          data[field.id] = JSON.parse(field.defaultValue);
        }
      }
      
      // If updating existing asset, get current values
      if (assetId && operationType === "asset.update") {
        const asset = await prisma.asset.findUnique({
          where: { id: assetId },
        });
        
        if (asset) {
          data.name = asset.name;
          data.category = asset.category;
          data.location = asset.location;
          data.department = asset.department;
          data.status = asset.status;
          data.serialNumber = asset.serialNumber;
          data.purchasePrice = asset.purchasePrice;
          data.purchaseDate = asset.purchaseDate.toISOString().split('T')[0];
          data.assetImages = asset.assetImages;
        }
      }
      
      // Add context-based pre-population
      if (context.location) {
        data.location = context.location;
      }
      
      if (context.department) {
        data.department = context.department;
      }
      
      // Add user information
      data.performedBy = context.userId;
      data.performedAt = new Date().toISOString();
      
    } catch (error) {
      console.error("Error getting pre-population data:", error);
    }
    
    return data;
  }
  
  /**
   * Validate form submission
   */
  static async validateFormSubmission(
    submission: FormSubmissionData
  ): Promise<ValidationResult[]> {
    const results: ValidationResult[] = [];
    
    try {
      // Get asset type with custom fields
      const assetType = await prisma.assetType.findUnique({
        where: { id: submission.assetTypeId },
        include: {
          customFields: true,
        },
      });
      
      if (!assetType) {
        results.push({
          fieldId: "assetType",
          isValid: false,
          errors: ["Asset type not found"],
          warnings: [],
        });
        return results;
      }
      
      // Validate each custom field
      for (const field of assetType.customFields) {
        const fieldResult = await this.validateField(field, submission.data[field.id]);
        results.push(fieldResult);
      }
      
      // Validate operation-specific requirements
      const operationValidation = await this.validateOperationRequirements(
        submission.operationType,
        submission.data,
        submission.context
      );
      results.push(...operationValidation);
      
    } catch (error) {
      console.error("Error validating form submission:", error);
      results.push({
        fieldId: "general",
        isValid: false,
        errors: ["Validation failed due to system error"],
        warnings: [],
      });
    }
    
    return results;
  }
  
  /**
   * Validate individual field
   */
  private static async validateField(
    field: CustomField,
    value: any
  ): Promise<ValidationResult> {
    const result: ValidationResult = {
      fieldId: field.id,
      isValid: true,
      errors: [],
      warnings: [],
    };
    
    // Check required fields
    if (field.isRequired && (value === undefined || value === null || value === "")) {
      result.isValid = false;
      result.errors.push(`${field.label} is required`);
      return result;
    }
    
    // Skip validation if field is empty and not required
    if (!field.isRequired && (value === undefined || value === null || value === "")) {
      return result;
    }
    
    // Type-specific validation
    switch (field.type) {
      case "text":
        if (typeof value !== "string") {
          result.isValid = false;
          result.errors.push(`${field.label} must be text`);
        }
        break;
        
      case "number":
        if (typeof value !== "number" && !Number.isFinite(Number(value))) {
          result.isValid = false;
          result.errors.push(`${field.label} must be a number`);
        }
        break;
        
      case "email":
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
          result.isValid = false;
          result.errors.push(`${field.label} must be a valid email address`);
        }
        break;
        
      case "url":
        try {
          new URL(value);
        } catch {
          result.isValid = false;
          result.errors.push(`${field.label} must be a valid URL`);
        }
        break;
        
      case "date":
        if (isNaN(Date.parse(value))) {
          result.isValid = false;
          result.errors.push(`${field.label} must be a valid date`);
        }
        break;
        
      case "boolean":
        if (typeof value !== "boolean") {
          result.isValid = false;
          result.errors.push(`${field.label} must be true or false`);
        }
        break;
        
      case "select":
        if (field.options && !field.options.some(option => option.value === value)) {
          result.isValid = false;
          result.errors.push(`${field.label} must be one of the available options`);
        }
        break;
        
      case "multiselect":
        if (!Array.isArray(value)) {
          result.isValid = false;
          result.errors.push(`${field.label} must be an array`);
        } else if (field.options) {
          const validValues = field.options.map(option => option.value);
          const invalidValues = value.filter(v => !validValues.includes(v));
          if (invalidValues.length > 0) {
            result.isValid = false;
            result.errors.push(`${field.label} contains invalid options: ${invalidValues.join(", ")}`);
          }
        }
        break;
    }
    
    // Apply validation rules
    if (field.validation && result.isValid) {
      const validationResult = this.applyValidationRules(field, value);
      if (!validationResult.isValid) {
        result.isValid = false;
        result.errors.push(...validationResult.errors);
      }
      result.warnings.push(...validationResult.warnings);
    }
    
    return result;
  }
  
  /**
   * Apply validation rules to field value
   */
  private static applyValidationRules(
    field: CustomField,
    value: any
  ): { isValid: boolean; errors: string[]; warnings: string[] } {
    const result = { isValid: true, errors: [] as string[], warnings: [] as string[] };
    const validation = field.validation;
    
    if (typeof value === "string") {
      if (validation.minLength && value.length < validation.minLength) {
        result.isValid = false;
        result.errors.push(`${field.label} must be at least ${validation.minLength} characters`);
      }
      
      if (validation.maxLength && value.length > validation.maxLength) {
        result.isValid = false;
        result.errors.push(`${field.label} must be no more than ${validation.maxLength} characters`);
      }
      
      if (validation.pattern) {
        const regex = new RegExp(validation.pattern);
        if (!regex.test(value)) {
          result.isValid = false;
          result.errors.push(validation.errorMessage || `${field.label} format is invalid`);
        }
      }
    }
    
    if (typeof value === "number") {
      if (validation.minValue !== undefined && value < validation.minValue) {
        result.isValid = false;
        result.errors.push(`${field.label} must be at least ${validation.minValue}`);
      }
      
      if (validation.maxValue !== undefined && value > validation.maxValue) {
        result.isValid = false;
        result.errors.push(`${field.label} must be no more than ${validation.maxValue}`);
      }
    }
    
    return result;
  }
  
  /**
   * Validate operation-specific requirements
   */
  private static async validateOperationRequirements(
    operationType: AssetOperationType,
    data: Record<string, any>,
    context: FormContext
  ): Promise<ValidationResult[]> {
    const results: ValidationResult[] = [];
    
    switch (operationType) {
      case "asset.create":
        // Validate required fields for asset creation
        const requiredFields = ["name", "category", "location", "purchasePrice", "purchaseDate"];
        for (const fieldName of requiredFields) {
          if (!data[fieldName]) {
            results.push({
              fieldId: fieldName,
              isValid: false,
              errors: [`${fieldName} is required for asset creation`],
              warnings: [],
            });
          }
        }
        
        // Validate serial number uniqueness if provided
        if (data.serialNumber) {
          const existingAsset = await prisma.asset.findUnique({
            where: { serialNumber: data.serialNumber },
          });
          
          if (existingAsset) {
            results.push({
              fieldId: "serialNumber",
              isValid: false,
              errors: ["Serial number already exists"],
              warnings: [],
            });
          }
        }
        break;
        
      case "asset.transfer":
        // Validate transfer-specific requirements
        if (!data.newLocation) {
          results.push({
            fieldId: "newLocation",
            isValid: false,
            errors: ["New location is required for transfer"],
            warnings: [],
          });
        }
        
        if (!data.transferDate) {
          results.push({
            fieldId: "transferDate",
            isValid: false,
            errors: ["Transfer date is required"],
            warnings: [],
          });
        }
        break;
        
      case "asset.disposal":
        // Validate disposal-specific requirements
        if (!data.method) {
          results.push({
            fieldId: "method",
            isValid: false,
            errors: ["Disposal method is required"],
            warnings: [],
          });
        }
        
        if (!data.disposalDate) {
          results.push({
            fieldId: "disposalDate",
            isValid: false,
            errors: ["Disposal date is required"],
            warnings: [],
          });
        }
        break;
        
      case "maintenance.log":
        // Validate maintenance log requirements
        if (!data.type) {
          results.push({
            fieldId: "type",
            isValid: false,
            errors: ["Maintenance type is required"],
            warnings: [],
          });
        }
        
        if (!data.completedDate) {
          results.push({
            fieldId: "completedDate",
            isValid: false,
            errors: ["Completion date is required"],
            warnings: [],
          });
        }
        break;
    }
    
    return results;
  }
  
  /**
   * Process form submission and execute operation
   */
  static async processFormSubmission(
    submission: FormSubmissionData
  ): Promise<FormRuntimeResult> {
    const result: FormRuntimeResult = {
      success: false,
      errors: [],
      warnings: [],
      validationResults: [],
    };
    
    try {
      // Validate submission
      const validationResults = await this.validateFormSubmission(submission);
      result.validationResults = validationResults;
      
      // Check if validation passed
      const hasErrors = validationResults.some(r => !r.isValid);
      if (hasErrors) {
        result.errors.push("Form validation failed");
        return result;
      }
      
      // Execute operation based on type
      const operationResult = await this.executeOperation(submission);
      
      result.success = operationResult.success;
      result.data = operationResult.data;
      result.errors.push(...operationResult.errors);
      result.warnings.push(...operationResult.warnings);
      
    } catch (error) {
      result.errors.push(`Form processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
    
    return result;
  }
  
  /**
   * Execute the specific operation
   */
  private static async executeOperation(
    submission: FormSubmissionData
  ): Promise<{ success: boolean; data?: any; errors: string[]; warnings: string[] }> {
    const { operationType, data, context } = submission;
    
    switch (operationType) {
      case "asset.create":
        return this.executeAssetCreation(data, context);
        
      case "asset.update":
        return this.executeAssetUpdate(data, context);
        
      case "asset.transfer":
        return this.executeAssetTransfer(data, context);
        
      case "asset.disposal":
        return this.executeAssetDisposal(data, context);
        
      case "maintenance.log":
        return this.executeMaintenanceLog(data, context);
        
      default:
        return {
          success: false,
          errors: [`Unsupported operation type: ${operationType}`],
          warnings: [],
        };
    }
  }
  
  /**
   * Execute asset creation
   */
  private static async executeAssetCreation(
    data: Record<string, any>,
    context: FormContext
  ): Promise<{ success: boolean; data?: any; errors: string[]; warnings: string[] }> {
    try {
      const asset = await prisma.asset.create({
        data: {
          name: data.name,
          category: data.category,
          location: data.location,
          department: data.department,
          status: data.status || "active",
          serialNumber: data.serialNumber,
          purchasePrice: Number(data.purchasePrice),
          purchaseDate: new Date(data.purchaseDate),
          assetImages: data.assetImages || [],
          assetTypeId: context.assetTypeId,
        },
      });
      
      // Log the operation
      await prisma.assetOperationHistory.create({
        data: {
          assetId: asset.id,
          operationType: "create",
          formData: JSON.stringify(data),
          performedBy: context.userId,
          status: "completed",
        },
      });
      
      return {
        success: true,
        data: asset,
        errors: [],
        warnings: [],
      };
      
    } catch (error) {
      return {
        success: false,
        errors: [`Asset creation failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
        warnings: [],
      };
    }
  }
  
  /**
   * Execute asset update
   */
  private static async executeAssetUpdate(
    data: Record<string, any>,
    context: FormContext
  ): Promise<{ success: boolean; data?: any; errors: string[]; warnings: string[] }> {
    try {
      if (!context.assetId) {
        return {
          success: false,
          errors: ["Asset ID is required for update"],
          warnings: [],
        };
      }
      
      const asset = await prisma.asset.update({
        where: { id: context.assetId },
        data: {
          name: data.name,
          category: data.category,
          location: data.location,
          department: data.department,
          status: data.status,
          serialNumber: data.serialNumber,
          purchasePrice: data.purchasePrice ? Number(data.purchasePrice) : undefined,
          purchaseDate: data.purchaseDate ? new Date(data.purchaseDate) : undefined,
          assetImages: data.assetImages,
        },
      });
      
      // Log the operation
      await prisma.assetOperationHistory.create({
        data: {
          assetId: asset.id,
          operationType: "update",
          formData: JSON.stringify(data),
          performedBy: context.userId,
          status: "completed",
        },
      });
      
      return {
        success: true,
        data: asset,
        errors: [],
        warnings: [],
      };
      
    } catch (error) {
      return {
        success: false,
        errors: [`Asset update failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
        warnings: [],
      };
    }
  }
  
  /**
   * Execute asset transfer
   */
  private static async executeAssetTransfer(
    data: Record<string, any>,
    context: FormContext
  ): Promise<{ success: boolean; data?: any; errors: string[]; warnings: string[] }> {
    try {
      if (!context.assetId) {
        return {
          success: false,
          errors: ["Asset ID is required for transfer"],
          warnings: [],
        };
      }
      
      // Get current asset data
      const currentAsset = await prisma.asset.findUnique({
        where: { id: context.assetId },
      });
      
      if (!currentAsset) {
        return {
          success: false,
          errors: ["Asset not found"],
          warnings: [],
        };
      }
      
      // Create transfer record and update asset
      const result = await prisma.$transaction(async (tx) => {
        // Create transfer record
        const transfer = await tx.assetTransfer.create({
          data: {
            assetId: context.assetId!,
            previousLocation: currentAsset.location,
            newLocation: data.newLocation,
            previousDepartment: currentAsset.department,
            newDepartment: data.newDepartment,
            transferDate: new Date(data.transferDate),
            reason: data.reason,
          },
        });
        
        // Update asset location
        const updatedAsset = await tx.asset.update({
          where: { id: context.assetId },
          data: {
            location: data.newLocation,
            department: data.newDepartment,
          },
        });
        
        // Log the operation
        await tx.assetOperationHistory.create({
          data: {
            assetId: context.assetId!,
            operationType: "transfer",
            formData: JSON.stringify(data),
            performedBy: context.userId,
            status: "completed",
          },
        });
        
        return { transfer, asset: updatedAsset };
      });
      
      return {
        success: true,
        data: result,
        errors: [],
        warnings: [],
      };
      
    } catch (error) {
      return {
        success: false,
        errors: [`Asset transfer failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
        warnings: [],
      };
    }
  }
  
  /**
   * Execute asset disposal
   */
  private static async executeAssetDisposal(
    data: Record<string, any>,
    context: FormContext
  ): Promise<{ success: boolean; data?: any; errors: string[]; warnings: string[] }> {
    try {
      if (!context.assetId) {
        return {
          success: false,
          errors: ["Asset ID is required for disposal"],
          warnings: [],
        };
      }
      
      // Get current book value
      const currentBookValue = await prisma.depreciationSchedule.findFirst({
        where: { assetId: context.assetId },
        orderBy: [{ year: 'desc' }, { month: 'desc' }],
      });
      
      const bookValue = currentBookValue?.bookValue || 0;
      const salePrice = data.salePrice ? Number(data.salePrice) : 0;
      const gainLoss = salePrice - bookValue;
      
      // Create disposal record and update asset status
      const result = await prisma.$transaction(async (tx) => {
        // Create disposal record
        const disposal = await tx.assetDisposal.create({
          data: {
            assetId: context.assetId!,
            method: data.method,
            disposalDate: new Date(data.disposalDate),
            bookValue,
            salePrice: data.salePrice ? Number(data.salePrice) : null,
            gainLoss,
            reason: data.reason,
          },
        });
        
        // Update asset status
        const updatedAsset = await tx.asset.update({
          where: { id: context.assetId },
          data: {
            status: "disposed",
          },
        });
        
        // Log the operation
        await tx.assetOperationHistory.create({
          data: {
            assetId: context.assetId!,
            operationType: "disposal",
            formData: JSON.stringify(data),
            performedBy: context.userId,
            status: "completed",
          },
        });
        
        return { disposal, asset: updatedAsset };
      });
      
      return {
        success: true,
        data: result,
        errors: [],
        warnings: [],
      };
      
    } catch (error) {
      return {
        success: false,
        errors: [`Asset disposal failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
        warnings: [],
      };
    }
  }
  
  /**
   * Execute maintenance log
   */
  private static async executeMaintenanceLog(
    data: Record<string, any>,
    context: FormContext
  ): Promise<{ success: boolean; data?: any; errors: string[]; warnings: string[] }> {
    try {
      if (!context.assetId) {
        return {
          success: false,
          errors: ["Asset ID is required for maintenance log"],
          warnings: [],
        };
      }
      
      // Create maintenance record
      const maintenance = await prisma.assetMaintenance.create({
        data: {
          assetId: context.assetId,
          type: data.type,
          scheduledDate: new Date(data.scheduledDate || data.completedDate),
          completedDate: new Date(data.completedDate),
          assignedTo: data.performedBy || context.userId,
          notes: data.notes,
          status: "completed",
        },
      });
      
      // Log the operation
      await prisma.assetOperationHistory.create({
        data: {
          assetId: context.assetId,
          operationType: "maintenance.log",
          formData: JSON.stringify(data),
          performedBy: context.userId,
          status: "completed",
        },
      });
      
      return {
        success: true,
        data: maintenance,
        errors: [],
        warnings: [],
      };
      
    } catch (error) {
      return {
        success: false,
        errors: [`Maintenance log failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
        warnings: [],
      };
    }
  }
}