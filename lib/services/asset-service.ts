import { prisma } from "@/lib/prisma";
import { Asset, AssetMaintenance, AssetTransfer, AssetDisposal, AssetDepreciation, LeaseAgreement } from "@prisma/client";
import { AssetCreateRequest, AssetUpdateRequest } from "@/lib/types/asset";
import { AssetCreateSchema, AssetUpdateSchema } from "@/lib/schemas/validation";
import { AssetFilter, AssetStatistics, Pagination } from "@/lib/schemas/api";
import { z } from "zod";

export type AssetWithRelations = Asset & {
  maintenances?: AssetMaintenance[];
  transfers?: AssetTransfer[];
  disposals?: AssetDisposal[];
  depreciations?: AssetDepreciation[];
  leases?: LeaseAgreement[];
};

export type AssetCreateInput = AssetCreateRequest;
export type AssetUpdateInput = AssetUpdateRequest;

export class AssetService {
  /**
   * Get all assets with optional relations
   */
  async getAllAssets(includeRelations = false): Promise<AssetWithRelations[]> {
    return prisma.asset.findMany({
      include: includeRelations
        ? {
            maintenances: true,
            transfers: true,
            disposals: true,
            depreciations: true,
            leases: true,
          }
        : undefined,
      orderBy: {
        createdAt: "desc",
      },
    });
  }

  /**
   * Get a single asset by ID with optional relations
   */
  async getAssetById(id: string, includeRelations = false): Promise<AssetWithRelations | null> {
    return prisma.asset.findUnique({
      where: { id },
      include: includeRelations
        ? {
            maintenances: true,
            transfers: true,
            disposals: true,
            depreciations: true,
            leases: true,
          }
        : undefined,
    });
  }

  /**
   * Create a new asset with validation
   */
  async createAsset(data: AssetCreateInput): Promise<Asset> {
    try {
      // Validate input data
      const validatedData = AssetCreateSchema.parse(data);

      return await prisma.asset.create({
        data: validatedData,
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new Error(`Validation failed: ${error.errors.map(e => e.message).join(", ")}`);
      }
      throw error;
    }
  }

  /**
   * Update an existing asset with validation
   */
  async updateAsset(id: string, data: AssetUpdateInput): Promise<Asset> {
    try {
      // Validate input data
      const validatedData = AssetUpdateSchema.parse(data);

      // Check if asset exists
      const existingAsset = await prisma.asset.findUnique({ where: { id } });
      if (!existingAsset) {
        throw new Error(`Asset with ID ${id} not found`);
      }

      return await prisma.asset.update({
        where: { id },
        data: validatedData,
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new Error(`Validation failed: ${error.errors.map(e => e.message).join(", ")}`);
      }
      throw error;
    }
  }

  /**
   * Delete an asset
   */
  async deleteAsset(id: string): Promise<Asset> {
    return prisma.asset.delete({
      where: { id },
    });
  }

  /**
   * Get assets by category
   */
  async getAssetsByCategory(category: string): Promise<Asset[]> {
    return prisma.asset.findMany({
      where: { category },
      orderBy: {
        createdAt: "desc",
      },
    });
  }

  /**
   * Get assets by status
   */
  async getAssetsByStatus(status: string): Promise<Asset[]> {
    return prisma.asset.findMany({
      where: { status },
      orderBy: {
        createdAt: "desc",
      },
    });
  }

  /**
   * Get assets by department
   */
  async getAssetsByDepartment(department: string): Promise<Asset[]> {
    return prisma.asset.findMany({
      where: { department },
      orderBy: {
        createdAt: "desc",
      },
    });
  }

  /**
   * Get assets by location
   */
  async getAssetsByLocation(location: string): Promise<Asset[]> {
    return prisma.asset.findMany({
      where: { location },
      orderBy: {
        createdAt: "desc",
      },
    });
  }

  /**
   * Search assets by name, category, or location
   */
  async searchAssets(searchTerm: string): Promise<Asset[]> {
    return prisma.asset.findMany({
      where: {
        OR: [
          { name: { contains: searchTerm, mode: "insensitive" } },
          { category: { contains: searchTerm, mode: "insensitive" } },
          { location: { contains: searchTerm, mode: "insensitive" } },
          { department: { contains: searchTerm, mode: "insensitive" } },
          { serialNumber: { contains: searchTerm, mode: "insensitive" } },
        ],
      },
      orderBy: {
        createdAt: "desc",
      },
    });
  }

  /**
   * Get assets with comprehensive filtering and pagination
   */
  async getAssetsWithFilter(
    filter: AssetFilter,
    pagination: Pagination
  ): Promise<{ assets: AssetWithRelations[]; total: number; totalPages: number }> {
    try {
      // Build where clause from filter
      const where: any = {};

      if (filter.category) {
        where.category = { contains: filter.category, mode: "insensitive" };
      }

      if (filter.status) {
        where.status = filter.status;
      }

      if (filter.department) {
        where.department = { contains: filter.department, mode: "insensitive" };
      }

      if (filter.location) {
        where.location = { contains: filter.location, mode: "insensitive" };
      }

      if (filter.assetTypeId) {
        where.assetTypeId = filter.assetTypeId;
      }

      if (filter.search) {
        where.OR = [
          { name: { contains: filter.search, mode: "insensitive" } },
          { category: { contains: filter.search, mode: "insensitive" } },
          { location: { contains: filter.search, mode: "insensitive" } },
          { department: { contains: filter.search, mode: "insensitive" } },
          { serialNumber: { contains: filter.search, mode: "insensitive" } },
        ];
      }

      if (filter.dateFrom || filter.dateTo) {
        where.purchaseDate = {};
        if (filter.dateFrom) {
          where.purchaseDate.gte = filter.dateFrom;
        }
        if (filter.dateTo) {
          where.purchaseDate.lte = filter.dateTo;
        }
      }

      if (filter.priceMin || filter.priceMax) {
        where.purchasePrice = {};
        if (filter.priceMin) {
          where.purchasePrice.gte = filter.priceMin;
        }
        if (filter.priceMax) {
          where.purchasePrice.lte = filter.priceMax;
        }
      }

      // Calculate pagination
      const skip = (pagination.page - 1) * pagination.limit;
      const take = pagination.limit;

      // Build order by clause
      const orderBy: any = {};
      if (pagination.sortBy) {
        orderBy[pagination.sortBy] = pagination.sortOrder;
      } else {
        orderBy.createdAt = "desc";
      }

      // Execute queries
      const [assets, total] = await Promise.all([
        prisma.asset.findMany({
          where,
          skip,
          take,
          orderBy,
          include: filter.includeRelations
            ? {
                maintenances: true,
                transfers: true,
                disposals: true,
                depreciations: true,
                leases: true,
                assetType: true,
              }
            : undefined,
        }),
        prisma.asset.count({ where }),
      ]);

      const totalPages = Math.ceil(total / pagination.limit);

      return {
        assets,
        total,
        totalPages,
      };
    } catch (error) {
      console.error("Error in getAssetsWithFilter:", error);
      throw new Error("Failed to fetch assets with filter");
    }
  }

  /**
   * Get comprehensive asset statistics
   */
  async getAssetStatistics(): Promise<AssetStatistics> {
    try {
      // Get basic counts
      const [
        totalAssets,
        activeAssets,
        maintenanceAssets,
        disposedAssets,
      ] = await Promise.all([
        prisma.asset.count(),
        prisma.asset.count({ where: { status: "active" } }),
        prisma.asset.count({ where: { status: "maintenance" } }),
        prisma.asset.count({ where: { status: "disposed" } }),
      ]);

      // Get recent operations count (last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const recentOperations = await prisma.assetOperationHistory.count({
        where: {
          performedAt: {
            gte: thirtyDaysAgo
          }
        }
      });

      // Get assets by category with percentages
      const categoryData = await prisma.asset.groupBy({
        by: ['category'],
        _count: {
          category: true,
        },
      });

      const assetsByCategory = categoryData.map(item => ({
        category: item.category,
        count: item._count.category,
        percentage: totalAssets > 0 ? (item._count.category / totalAssets) * 100 : 0,
      }));

      // Get assets by status with percentages
      const statusData = await prisma.asset.groupBy({
        by: ['status'],
        _count: {
          status: true,
        },
      });

      const assetsByStatus = statusData.map(item => ({
        status: item.status,
        count: item._count.status,
        percentage: totalAssets > 0 ? (item._count.status / totalAssets) * 100 : 0,
      }));

      // Get assets by location with percentages
      const locationData = await prisma.asset.groupBy({
        by: ['location'],
        _count: {
          location: true,
        },
      });

      const assetsByLocation = locationData.map(item => ({
        location: item.location,
        count: item._count.location,
        percentage: totalAssets > 0 ? (item._count.location / totalAssets) * 100 : 0,
      }));

      // Calculate total value and average age
      const assetAggregates = await prisma.asset.aggregate({
        _sum: {
          purchasePrice: true,
        },
        _avg: {
          purchasePrice: true,
        },
      });

      const totalValue = assetAggregates._sum.purchasePrice || 0;

      // Calculate average age in days
      const assetsWithDates = await prisma.asset.findMany({
        select: {
          purchaseDate: true,
        },
      });

      const currentDate = new Date();
      const totalAgeInDays = assetsWithDates.reduce((sum, asset) => {
        const ageInDays = Math.floor((currentDate.getTime() - asset.purchaseDate.getTime()) / (1000 * 60 * 60 * 24));
        return sum + ageInDays;
      }, 0);

      const averageAge = assetsWithDates.length > 0 ? totalAgeInDays / assetsWithDates.length : 0;

      return {
        totalAssets,
        activeAssets,
        maintenanceAssets,
        disposedAssets,
        recentOperations,
        assetsByCategory,
        assetsByStatus,
        assetsByLocation,
        totalValue,
        averageAge,
      };
    } catch (error) {
      console.error('Database error in getAssetStatistics:', error);

      // Instead of returning mock data, throw a more specific error
      if (error instanceof Error) {
        throw new Error(`Failed to fetch asset statistics: ${error.message}`);
      }
      throw new Error('Failed to fetch asset statistics due to database error');
    }
  }

  /**
   * Bulk update assets
   */
  async bulkUpdateAssets(assetIds: string[], updates: Partial<AssetUpdateInput>): Promise<{ count: number }> {
    try {
      // Validate update data
      const validatedUpdates = AssetUpdateSchema.parse(updates);

      const result = await prisma.asset.updateMany({
        where: {
          id: {
            in: assetIds,
          },
        },
        data: validatedUpdates,
      });

      return { count: result.count };
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new Error(`Validation failed: ${error.errors.map(e => e.message).join(", ")}`);
      }
      throw error;
    }
  }

  /**
   * Get assets that need maintenance
   */
  async getAssetsNeedingMaintenance(): Promise<Asset[]> {
    try {
      // Get assets with overdue maintenance tasks
      const assetsWithOverdueTasks = await prisma.asset.findMany({
        where: {
          maintenanceTasks: {
            some: {
              status: "overdue",
            },
          },
        },
        include: {
          maintenanceTasks: {
            where: {
              status: "overdue",
            },
          },
        },
      });

      return assetsWithOverdueTasks;
    } catch (error) {
      console.error("Error fetching assets needing maintenance:", error);
      throw new Error("Failed to fetch assets needing maintenance");
    }
  }

  /**
   * Get asset value trends over time
   */
  async getAssetValueTrends(months: number = 12): Promise<Array<{ month: string; totalValue: number; count: number }>> {
    try {
      const startDate = new Date();
      startDate.setMonth(startDate.getMonth() - months);

      const assets = await prisma.asset.findMany({
        where: {
          purchaseDate: {
            gte: startDate,
          },
        },
        select: {
          purchaseDate: true,
          purchasePrice: true,
        },
        orderBy: {
          purchaseDate: "asc",
        },
      });

      // Group by month
      const trends = new Map<string, { totalValue: number; count: number }>();

      assets.forEach(asset => {
        const monthKey = asset.purchaseDate.toISOString().substring(0, 7); // YYYY-MM format
        const existing = trends.get(monthKey) || { totalValue: 0, count: 0 };
        trends.set(monthKey, {
          totalValue: existing.totalValue + asset.purchasePrice,
          count: existing.count + 1,
        });
      });

      return Array.from(trends.entries()).map(([month, data]) => ({
        month,
        ...data,
      }));
    } catch (error) {
      console.error("Error fetching asset value trends:", error);
      throw new Error("Failed to fetch asset value trends");
    }
  }

  /**
   * Get assets by multiple criteria with OR logic
   */
  async getAssetsByMultipleCriteria(criteria: {
    categories?: string[];
    statuses?: string[];
    locations?: string[];
    departments?: string[];
  }): Promise<Asset[]> {
    try {
      const orConditions: any[] = [];

      if (criteria.categories?.length) {
        orConditions.push({ category: { in: criteria.categories } });
      }

      if (criteria.statuses?.length) {
        orConditions.push({ status: { in: criteria.statuses } });
      }

      if (criteria.locations?.length) {
        orConditions.push({ location: { in: criteria.locations } });
      }

      if (criteria.departments?.length) {
        orConditions.push({ department: { in: criteria.departments } });
      }

      if (orConditions.length === 0) {
        return [];
      }

      return await prisma.asset.findMany({
        where: {
          OR: orConditions,
        },
        orderBy: {
          createdAt: "desc",
        },
      });
    } catch (error) {
      console.error("Error fetching assets by multiple criteria:", error);
      throw new Error("Failed to fetch assets by multiple criteria");
    }
  }
}

// Export a singleton instance
export const assetService = new AssetService();