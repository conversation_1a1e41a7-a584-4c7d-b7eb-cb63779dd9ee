import { prisma } from "@/lib/prisma";
import { SupportTicket, SupportMessage } from "@prisma/client";
import { SupportTicketCreateSchema, SupportTicketUpdateSchema, SupportMessageCreateSchema } from "@/lib/schemas/validation";
import { SupportTicketFilter, Pagination } from "@/lib/schemas/api";
import { z } from "zod";

export type SupportTicketWithMessages = SupportTicket & {
  messages?: SupportMessage[];
  user?: {
    id: string;
    name: string;
    email: string;
    department?: string;
  };
};

export class SupportTicketService {
  /**
   * Get all support tickets with optional filtering and pagination
   */
  async getSupportTickets(
    filter: SupportTicketFilter,
    pagination: Pagination,
    userId?: string
  ): Promise<{ tickets: SupportTicketWithMessages[]; total: number; totalPages: number }> {
    try {
      // Build where clause from filter
      const where: any = {};

      // If userId is provided (for client users), filter by user
      if (userId) {
        where.userId = userId;
      }

      if (filter.status) {
        where.status = filter.status;
      }

      if (filter.priority) {
        where.priority = filter.priority;
      }

      if (filter.category) {
        where.category = { contains: filter.category, mode: "insensitive" };
      }

      if (filter.assignedTo) {
        where.assignedTo = filter.assignedTo;
      }

      if (filter.userId && !userId) {
        // Only allow admin users to filter by other users
        where.userId = filter.userId;
      }

      if (filter.search) {
        where.OR = [
          { ticketNumber: { contains: filter.search, mode: "insensitive" } },
          { subject: { contains: filter.search, mode: "insensitive" } },
          { description: { contains: filter.search, mode: "insensitive" } },
        ];
      }

      if (filter.dateFrom || filter.dateTo) {
        where.createdAt = {};
        if (filter.dateFrom) {
          where.createdAt.gte = filter.dateFrom;
        }
        if (filter.dateTo) {
          where.createdAt.lte = filter.dateTo;
        }
      }

      // Calculate pagination
      const skip = (pagination.page - 1) * pagination.limit;
      const take = pagination.limit;

      // Build order by clause
      const orderBy: any = {};
      if (pagination.sortBy) {
        orderBy[pagination.sortBy] = pagination.sortOrder;
      } else {
        orderBy.createdAt = "desc";
      }

      // Execute queries
      const [tickets, total] = await Promise.all([
        prisma.supportTicket.findMany({
          where,
          skip,
          take,
          orderBy,
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                department: true,
              },
            },
            messages: {
              orderBy: {
                createdAt: "asc",
              },
            },
          },
        }),
        prisma.supportTicket.count({ where }),
      ]);

      const totalPages = Math.ceil(total / pagination.limit);

      return {
        tickets,
        total,
        totalPages,
      };
    } catch (error) {
      console.error("Error in getSupportTickets:", error);
      throw new Error("Failed to fetch support tickets");
    }
  }

  /**
   * Get a single support ticket by ID
   */
  async getSupportTicketById(id: string, userId?: string): Promise<SupportTicketWithMessages | null> {
    try {
      const where: any = { id };
      
      // If userId is provided (for client users), ensure they can only access their own tickets
      if (userId) {
        where.userId = userId;
      }

      return await prisma.supportTicket.findUnique({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              department: true,
            },
          },
          messages: {
            orderBy: {
              createdAt: "asc",
            },
          },
        },
      });
    } catch (error) {
      console.error("Error in getSupportTicketById:", error);
      throw new Error("Failed to fetch support ticket");
    }
  }

  /**
   * Create a new support ticket
   */
  async createSupportTicket(data: any, userId: string): Promise<SupportTicket> {
    try {
      // Generate ticket number
      const ticketNumber = await this.generateTicketNumber();
      
      // Validate input data
      const validatedData = SupportTicketCreateSchema.parse({
        ...data,
        userId,
        ticketNumber,
      });

      return await prisma.supportTicket.create({
        data: validatedData,
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new Error(`Validation failed: ${error.errors.map(e => e.message).join(", ")}`);
      }
      console.error("Error in createSupportTicket:", error);
      throw new Error("Failed to create support ticket");
    }
  }

  /**
   * Update a support ticket
   */
  async updateSupportTicket(id: string, data: any, userId?: string): Promise<SupportTicket> {
    try {
      // Check if ticket exists and user has permission
      const existingTicket = await this.getSupportTicketById(id, userId);
      if (!existingTicket) {
        throw new Error("Support ticket not found or access denied");
      }

      // Validate input data
      const validatedData = SupportTicketUpdateSchema.parse(data);

      return await prisma.supportTicket.update({
        where: { id },
        data: validatedData,
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new Error(`Validation failed: ${error.errors.map(e => e.message).join(", ")}`);
      }
      console.error("Error in updateSupportTicket:", error);
      throw new Error("Failed to update support ticket");
    }
  }

  /**
   * Add a message to a support ticket
   */
  async addMessage(ticketId: string, data: any, userId: string, userName: string): Promise<SupportMessage> {
    try {
      // Check if ticket exists and user has permission
      const existingTicket = await this.getSupportTicketById(ticketId, userId);
      if (!existingTicket) {
        throw new Error("Support ticket not found or access denied");
      }

      // Validate input data
      const validatedData = SupportMessageCreateSchema.parse({
        ...data,
        ticketId,
        sender: userId,
        senderType: "client", // Assuming client for now, can be enhanced
      });

      const message = await prisma.supportMessage.create({
        data: validatedData,
      });

      // Update ticket's updatedAt timestamp
      await prisma.supportTicket.update({
        where: { id: ticketId },
        data: { updatedAt: new Date() },
      });

      return message;
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new Error(`Validation failed: ${error.errors.map(e => e.message).join(", ")}`);
      }
      console.error("Error in addMessage:", error);
      throw new Error("Failed to add message");
    }
  }

  /**
   * Get support ticket statistics
   */
  async getSupportTicketStatistics(userId?: string) {
    try {
      const where = userId ? { userId } : {};

      const [
        totalTickets,
        openTickets,
        inProgressTickets,
        resolvedTickets,
        closedTickets,
      ] = await Promise.all([
        prisma.supportTicket.count({ where }),
        prisma.supportTicket.count({ where: { ...where, status: "open" } }),
        prisma.supportTicket.count({ where: { ...where, status: "in_progress" } }),
        prisma.supportTicket.count({ where: { ...where, status: "resolved" } }),
        prisma.supportTicket.count({ where: { ...where, status: "closed" } }),
      ]);

      return {
        totalTickets,
        openTickets,
        inProgressTickets,
        resolvedTickets,
        closedTickets,
      };
    } catch (error) {
      console.error("Error in getSupportTicketStatistics:", error);
      throw new Error("Failed to fetch support ticket statistics");
    }
  }

  /**
   * Generate a unique ticket number
   */
  private async generateTicketNumber(): Promise<string> {
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, "0");
    
    // Get the count of tickets this month
    const startOfMonth = new Date(year, new Date().getMonth(), 1);
    const endOfMonth = new Date(year, new Date().getMonth() + 1, 0);
    
    const count = await prisma.supportTicket.count({
      where: {
        createdAt: {
          gte: startOfMonth,
          lte: endOfMonth,
        },
      },
    });

    const sequence = String(count + 1).padStart(3, "0");
    return `TICK-${year}${month}-${sequence}`;
  }
}

// Export a singleton instance
export const supportTicketService = new SupportTicketService();
