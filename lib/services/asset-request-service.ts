import { prisma } from "@/lib/prisma";
import { AssetRequest } from "@prisma/client";
import { AssetRequestCreateSchema, AssetRequestUpdateSchema } from "@/lib/schemas/validation";
import { AssetRequestFilter, Pagination } from "@/lib/schemas/api";
import { z } from "zod";

export type AssetRequestWithUser = AssetRequest & {
  user?: {
    id: string;
    name: string;
    email: string;
    department?: string;
  };
};

export class AssetRequestService {
  /**
   * Get all asset requests with optional filtering and pagination
   */
  async getAssetRequests(
    filter: AssetRequestFilter,
    pagination: Pagination,
    userId?: string
  ): Promise<{ requests: AssetRequestWithUser[]; total: number; totalPages: number }> {
    try {
      // Build where clause from filter
      const where: any = {};

      // If userId is provided (for client users), filter by user
      if (userId) {
        where.userId = userId;
      }

      if (filter.status) {
        where.status = filter.status;
      }

      if (filter.priority) {
        where.priority = filter.priority;
      }

      if (filter.userId && !userId) {
        // Only allow admin users to filter by other users
        where.userId = filter.userId;
      }

      if (filter.assetTypeId) {
        where.assetTypeId = filter.assetTypeId;
      }

      if (filter.department) {
        where.department = { contains: filter.department, mode: "insensitive" };
      }

      if (filter.search) {
        where.OR = [
          { requestNumber: { contains: filter.search, mode: "insensitive" } },
          { assetName: { contains: filter.search, mode: "insensitive" } },
          { justification: { contains: filter.search, mode: "insensitive" } },
          { location: { contains: filter.search, mode: "insensitive" } },
        ];
      }

      if (filter.dateFrom || filter.dateTo) {
        where.createdAt = {};
        if (filter.dateFrom) {
          where.createdAt.gte = filter.dateFrom;
        }
        if (filter.dateTo) {
          where.createdAt.lte = filter.dateTo;
        }
      }

      // Calculate pagination
      const skip = (pagination.page - 1) * pagination.limit;
      const take = pagination.limit;

      // Build order by clause
      const orderBy: any = {};
      if (pagination.sortBy) {
        orderBy[pagination.sortBy] = pagination.sortOrder;
      } else {
        orderBy.createdAt = "desc";
      }

      // Execute queries
      const [requests, total] = await Promise.all([
        prisma.assetRequest.findMany({
          where,
          skip,
          take,
          orderBy,
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                department: true,
              },
            },
          },
        }),
        prisma.assetRequest.count({ where }),
      ]);

      const totalPages = Math.ceil(total / pagination.limit);

      return {
        requests,
        total,
        totalPages,
      };
    } catch (error) {
      console.error("Error in getAssetRequests:", error);
      throw new Error("Failed to fetch asset requests");
    }
  }

  /**
   * Get a single asset request by ID
   */
  async getAssetRequestById(id: string, userId?: string): Promise<AssetRequestWithUser | null> {
    try {
      const where: any = { id };
      
      // If userId is provided (for client users), ensure they can only access their own requests
      if (userId) {
        where.userId = userId;
      }

      return await prisma.assetRequest.findUnique({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              department: true,
            },
          },
        },
      });
    } catch (error) {
      console.error("Error in getAssetRequestById:", error);
      throw new Error("Failed to fetch asset request");
    }
  }

  /**
   * Create a new asset request
   */
  async createAssetRequest(data: any, userId: string): Promise<AssetRequest> {
    try {
      // Generate request number
      const requestNumber = await this.generateRequestNumber();
      
      // Validate input data
      const validatedData = AssetRequestCreateSchema.parse({
        ...data,
        userId,
        requestNumber,
      });

      return await prisma.assetRequest.create({
        data: validatedData,
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new Error(`Validation failed: ${error.errors.map(e => e.message).join(", ")}`);
      }
      console.error("Error in createAssetRequest:", error);
      throw new Error("Failed to create asset request");
    }
  }

  /**
   * Update an asset request
   */
  async updateAssetRequest(id: string, data: any, userId?: string): Promise<AssetRequest> {
    try {
      // Check if request exists and user has permission
      const existingRequest = await this.getAssetRequestById(id, userId);
      if (!existingRequest) {
        throw new Error("Asset request not found or access denied");
      }

      // Validate input data
      const validatedData = AssetRequestUpdateSchema.parse(data);

      return await prisma.assetRequest.update({
        where: { id },
        data: validatedData,
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new Error(`Validation failed: ${error.errors.map(e => e.message).join(", ")}`);
      }
      console.error("Error in updateAssetRequest:", error);
      throw new Error("Failed to update asset request");
    }
  }

  /**
   * Delete an asset request
   */
  async deleteAssetRequest(id: string, userId?: string): Promise<AssetRequest> {
    try {
      // Check if request exists and user has permission
      const existingRequest = await this.getAssetRequestById(id, userId);
      if (!existingRequest) {
        throw new Error("Asset request not found or access denied");
      }

      // Only allow deletion if request is still pending
      if (existingRequest.status !== "pending") {
        throw new Error("Cannot delete asset request that is not pending");
      }

      return await prisma.assetRequest.delete({
        where: { id },
      });
    } catch (error) {
      console.error("Error in deleteAssetRequest:", error);
      throw new Error("Failed to delete asset request");
    }
  }

  /**
   * Approve an asset request (admin only)
   */
  async approveAssetRequest(id: string, approvedBy: string, notes?: string): Promise<AssetRequest> {
    try {
      return await prisma.assetRequest.update({
        where: { id },
        data: {
          status: "approved",
          approvedBy,
          approvedAt: new Date(),
          notes,
        },
      });
    } catch (error) {
      console.error("Error in approveAssetRequest:", error);
      throw new Error("Failed to approve asset request");
    }
  }

  /**
   * Reject an asset request (admin only)
   */
  async rejectAssetRequest(id: string, rejectionReason: string): Promise<AssetRequest> {
    try {
      return await prisma.assetRequest.update({
        where: { id },
        data: {
          status: "rejected",
          rejectionReason,
        },
      });
    } catch (error) {
      console.error("Error in rejectAssetRequest:", error);
      throw new Error("Failed to reject asset request");
    }
  }

  /**
   * Get asset request statistics
   */
  async getAssetRequestStatistics(userId?: string) {
    try {
      const where = userId ? { userId } : {};

      const [
        totalRequests,
        pendingRequests,
        approvedRequests,
        rejectedRequests,
        processingRequests,
        deliveredRequests,
      ] = await Promise.all([
        prisma.assetRequest.count({ where }),
        prisma.assetRequest.count({ where: { ...where, status: "pending" } }),
        prisma.assetRequest.count({ where: { ...where, status: "approved" } }),
        prisma.assetRequest.count({ where: { ...where, status: "rejected" } }),
        prisma.assetRequest.count({ where: { ...where, status: "processing" } }),
        prisma.assetRequest.count({ where: { ...where, status: "delivered" } }),
      ]);

      return {
        totalRequests,
        pendingRequests,
        approvedRequests,
        rejectedRequests,
        processingRequests,
        deliveredRequests,
      };
    } catch (error) {
      console.error("Error in getAssetRequestStatistics:", error);
      throw new Error("Failed to fetch asset request statistics");
    }
  }

  /**
   * Generate a unique request number
   */
  private async generateRequestNumber(): Promise<string> {
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, "0");
    
    // Get the count of requests this month
    const startOfMonth = new Date(year, new Date().getMonth(), 1);
    const endOfMonth = new Date(year, new Date().getMonth() + 1, 0);
    
    const count = await prisma.assetRequest.count({
      where: {
        createdAt: {
          gte: startOfMonth,
          lte: endOfMonth,
        },
      },
    });

    const sequence = String(count + 1).padStart(3, "0");
    return `REQ-${year}${month}-${sequence}`;
  }
}

// Export a singleton instance
export const assetRequestService = new AssetRequestService();
