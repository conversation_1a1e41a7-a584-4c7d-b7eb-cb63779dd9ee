import { 
  FlowWorkflowDefinition, 
  TriggerJobExecution, 
  WorkflowAnalytics,
  FlowNode,
  FlowEdge,
  WebhookConfig
} from '../advanced-features/automation/types'
import { AssetNodeType } from '../advanced-features/automation/asset-node-types'

// Asset Automation Service - handles all API calls related to asset automation workflows
export const AssetAutomationService = {
  // Get all workflows
  async getWorkflows(): Promise<FlowWorkflowDefinition[]> {
    try {
      const response = await fetch('/api/asset-automation/workflows')
      if (!response.ok) {
        throw new Error(`Failed to fetch workflows: ${response.statusText}`)
      }
      
      const data = await response.json()
      return data.workflows
    } catch (error) {
      console.error('Error fetching workflows:', error)
      throw error
    }
  },
  
  // Get workflow by ID
  async getWorkflow(id: string): Promise<FlowWorkflowDefinition> {
    try {
      const response = await fetch(`/api/asset-automation/workflows/${id}`)
      if (!response.ok) {
        throw new Error(`Failed to fetch workflow: ${response.statusText}`)
      }
      
      const data = await response.json()
      return data.workflow
    } catch (error) {
      console.error(`Error fetching workflow ${id}:`, error)
      throw error
    }
  },
  
  // Create new workflow
  async createWorkflow(workflow: Partial<FlowWorkflowDefinition>): Promise<FlowWorkflowDefinition> {
    try {
      const response = await fetch('/api/asset-automation/workflows', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ workflow })
      })
      
      if (!response.ok) {
        throw new Error(`Failed to create workflow: ${response.statusText}`)
      }
      
      const data = await response.json()
      return data.workflow
    } catch (error) {
      console.error('Error creating workflow:', error)
      throw error
    }
  },
  
  // Update workflow
  async updateWorkflow(workflow: FlowWorkflowDefinition): Promise<FlowWorkflowDefinition> {
    try {
      const response = await fetch(`/api/asset-automation/workflows/${workflow.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ workflow })
      })
      
      if (!response.ok) {
        throw new Error(`Failed to update workflow: ${response.statusText}`)
      }
      
      const data = await response.json()
      return data.workflow
    } catch (error) {
      console.error(`Error updating workflow ${workflow.id}:`, error)
      throw error
    }
  },
  
  // Delete workflow
  async deleteWorkflow(id: string): Promise<void> {
    try {
      const response = await fetch(`/api/asset-automation/workflows/${id}`, {
        method: 'DELETE'
      })
      
      if (!response.ok) {
        throw new Error(`Failed to delete workflow: ${response.statusText}`)
      }
    } catch (error) {
      console.error(`Error deleting workflow ${id}:`, error)
      throw error
    }
  },
  
  // Execute workflow
  async executeWorkflow(workflowId: string, input: any = {}): Promise<string> {
    try {
      const response = await fetch(`/api/asset-automation/workflows/${workflowId}/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ input })
      })
      
      if (!response.ok) {
        throw new Error(`Failed to execute workflow: ${response.statusText}`)
      }
      
      const data = await response.json()
      return data.executionId
    } catch (error) {
      console.error(`Error executing workflow ${workflowId}:`, error)
      throw error
    }
  },
  
  // Get workflow execution
  async getExecution(executionId: string): Promise<TriggerJobExecution> {
    try {
      const response = await fetch(`/api/asset-automation/executions/${executionId}`)
      if (!response.ok) {
        throw new Error(`Failed to fetch execution: ${response.statusText}`)
      }
      
      const data = await response.json()
      return data.execution
    } catch (error) {
      console.error(`Error fetching execution ${executionId}:`, error)
      throw error
    }
  },
  
  // Get workflow executions
  async getExecutions(workflowId: string): Promise<TriggerJobExecution[]> {
    try {
      const response = await fetch(`/api/asset-automation/workflows/${workflowId}/executions`)
      if (!response.ok) {
        throw new Error(`Failed to fetch executions: ${response.statusText}`)
      }
      
      const data = await response.json()
      return data.executions
    } catch (error) {
      console.error(`Error fetching executions for workflow ${workflowId}:`, error)
      throw error
    }
  },
  
  // Get workflow analytics
  async getAnalytics(workflowId: string): Promise<WorkflowAnalytics> {
    try {
      const response = await fetch(`/api/asset-automation/workflows/${workflowId}/analytics`)
      if (!response.ok) {
        throw new Error(`Failed to fetch analytics: ${response.statusText}`)
      }
      
      const data = await response.json()
      return data.analytics
    } catch (error) {
      console.error(`Error fetching analytics for workflow ${workflowId}:`, error)
      throw error
    }
  },

  // Create webhook for workflow
  async createWebhook(workflowId: string, webhook: Partial<WebhookConfig>): Promise<WebhookConfig> {
    try {
      const response = await fetch(`/api/asset-automation/workflows/${workflowId}/webhooks`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ webhook })
      })
      
      if (!response.ok) {
        throw new Error(`Failed to create webhook: ${response.statusText}`)
      }
      
      const data = await response.json()
      return data.webhook
    } catch (error) {
      console.error(`Error creating webhook for workflow ${workflowId}:`, error)
      throw error
    }
  },

  // Update webhook
  async updateWebhook(workflowId: string, webhookId: string, webhook: Partial<WebhookConfig>): Promise<WebhookConfig> {
    try {
      const response = await fetch(`/api/asset-automation/workflows/${workflowId}/webhooks/${webhookId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ webhook })
      })
      
      if (!response.ok) {
        throw new Error(`Failed to update webhook: ${response.statusText}`)
      }
      
      const data = await response.json()
      return data.webhook
    } catch (error) {
      console.error(`Error updating webhook ${webhookId}:`, error)
      throw error
    }
  },

  // Delete webhook
  async deleteWebhook(workflowId: string, webhookId: string): Promise<void> {
    try {
      const response = await fetch(`/api/asset-automation/workflows/${workflowId}/webhooks/${webhookId}`, {
        method: 'DELETE'
      })
      
      if (!response.ok) {
        throw new Error(`Failed to delete webhook: ${response.statusText}`)
      }
    } catch (error) {
      console.error(`Error deleting webhook ${webhookId}:`, error)
      throw error
    }
  },

  // Test webhook
  async testWebhook(workflowId: string, webhookId: string, payload: any = {}): Promise<any> {
    try {
      const response = await fetch(`/api/asset-automation/workflows/${workflowId}/webhooks/${webhookId}/test`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      })
      
      if (!response.ok) {
        throw new Error(`Failed to test webhook: ${response.statusText}`)
      }
      
      const data = await response.json()
      return data
    } catch (error) {
      console.error(`Error testing webhook ${webhookId}:`, error)
      throw error
    }
  },

  // Execute asset-specific node function
  async executeAssetNodeFunction(nodeType: AssetNodeType, input: any = {}): Promise<any> {
    try {
      const response = await fetch(`/api/asset-automation/functions/${nodeType}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ input })
      })
      
      if (!response.ok) {
        throw new Error(`Failed to execute asset function: ${response.statusText}`)
      }
      
      const data = await response.json()
      return data.result
    } catch (error) {
      console.error(`Error executing asset function ${nodeType}:`, error)
      throw error
    }
  },

  // Get workflow template by ID
  async getWorkflowTemplate(templateId: string): Promise<Partial<FlowWorkflowDefinition>> {
    try {
      const response = await fetch(`/api/asset-automation/templates/${templateId}`)
      if (!response.ok) {
        throw new Error(`Failed to fetch workflow template: ${response.statusText}`)
      }
      
      const data = await response.json()
      return data.template
    } catch (error) {
      console.error(`Error fetching workflow template ${templateId}:`, error)
      throw error
    }
  }
}