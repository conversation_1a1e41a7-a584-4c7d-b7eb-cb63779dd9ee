import { prisma } from "@/lib/prisma";
import { MaintenanceTask } from "@prisma/client";
import { MaintenanceTaskCreateSchema, MaintenanceTaskUpdateSchema } from "@/lib/schemas/validation";
import { MaintenanceTaskFilter, Pagination, MaintenanceStatistics } from "@/lib/schemas/api";
import { z } from "zod";

export type MaintenanceTaskWithAsset = MaintenanceTask & {
  asset?: {
    id: string;
    name: string;
    category: string;
    location: string;
  };
};

export class MaintenanceService {
  /**
   * Get all maintenance tasks with optional filtering and pagination
   */
  async getMaintenanceTasks(
    filter: MaintenanceTaskFilter,
    pagination: Pagination
  ): Promise<{ tasks: MaintenanceTaskWithAsset[]; total: number; totalPages: number }> {
    try {
      // Build where clause from filter
      const where: any = {};

      if (filter.status) {
        where.status = filter.status;
      }

      if (filter.priority) {
        where.priority = filter.priority;
      }

      if (filter.type) {
        where.type = { contains: filter.type, mode: "insensitive" };
      }

      if (filter.assetId) {
        where.assetId = filter.assetId;
      }

      if (filter.assignedTo) {
        where.assignedTo = filter.assignedTo;
      }

      if (filter.dateFrom || filter.dateTo) {
        where.scheduledDate = {};
        if (filter.dateFrom) {
          where.scheduledDate.gte = filter.dateFrom;
        }
        if (filter.dateTo) {
          where.scheduledDate.lte = filter.dateTo;
        }
      }

      if (filter.upcoming) {
        const now = new Date();
        const futureDate = new Date();
        futureDate.setDate(futureDate.getDate() + 30); // Next 30 days
        
        where.scheduledDate = {
          gte: now,
          lte: futureDate,
        };
        where.status = {
          in: ["scheduled", "in_progress"],
        };
      }

      if (filter.overdue) {
        const now = new Date();
        where.dueDate = {
          lt: now,
        };
        where.status = {
          not: "completed",
        };
      }

      // Calculate pagination
      const skip = (pagination.page - 1) * pagination.limit;
      const take = pagination.limit;

      // Build order by clause
      const orderBy: any = {};
      if (pagination.sortBy) {
        orderBy[pagination.sortBy] = pagination.sortOrder;
      } else {
        orderBy.scheduledDate = "asc";
      }

      // Execute queries
      const [tasks, total] = await Promise.all([
        prisma.maintenanceTask.findMany({
          where,
          skip,
          take,
          orderBy,
          include: {
            asset: {
              select: {
                id: true,
                name: true,
                category: true,
                location: true,
              },
            },
          },
        }),
        prisma.maintenanceTask.count({ where }),
      ]);

      const totalPages = Math.ceil(total / pagination.limit);

      return {
        tasks,
        total,
        totalPages,
      };
    } catch (error) {
      console.error("Error in getMaintenanceTasks:", error);
      throw new Error("Failed to fetch maintenance tasks");
    }
  }

  /**
   * Get a single maintenance task by ID
   */
  async getMaintenanceTaskById(id: string): Promise<MaintenanceTaskWithAsset | null> {
    try {
      return await prisma.maintenanceTask.findUnique({
        where: { id },
        include: {
          asset: {
            select: {
              id: true,
              name: true,
              category: true,
              location: true,
            },
          },
        },
      });
    } catch (error) {
      console.error("Error in getMaintenanceTaskById:", error);
      throw new Error("Failed to fetch maintenance task");
    }
  }

  /**
   * Create a new maintenance task
   */
  async createMaintenanceTask(data: any): Promise<MaintenanceTask> {
    try {
      // Validate input data
      const validatedData = MaintenanceTaskCreateSchema.parse(data);

      return await prisma.maintenanceTask.create({
        data: validatedData,
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new Error(`Validation failed: ${error.errors.map(e => e.message).join(", ")}`);
      }
      console.error("Error in createMaintenanceTask:", error);
      throw new Error("Failed to create maintenance task");
    }
  }

  /**
   * Update a maintenance task
   */
  async updateMaintenanceTask(id: string, data: any): Promise<MaintenanceTask> {
    try {
      // Check if task exists
      const existingTask = await this.getMaintenanceTaskById(id);
      if (!existingTask) {
        throw new Error("Maintenance task not found");
      }

      // Validate input data
      const validatedData = MaintenanceTaskUpdateSchema.parse(data);

      return await prisma.maintenanceTask.update({
        where: { id },
        data: validatedData,
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new Error(`Validation failed: ${error.errors.map(e => e.message).join(", ")}`);
      }
      console.error("Error in updateMaintenanceTask:", error);
      throw new Error("Failed to update maintenance task");
    }
  }

  /**
   * Delete a maintenance task
   */
  async deleteMaintenanceTask(id: string): Promise<MaintenanceTask> {
    try {
      // Check if task exists
      const existingTask = await this.getMaintenanceTaskById(id);
      if (!existingTask) {
        throw new Error("Maintenance task not found");
      }

      return await prisma.maintenanceTask.delete({
        where: { id },
      });
    } catch (error) {
      console.error("Error in deleteMaintenanceTask:", error);
      throw new Error("Failed to delete maintenance task");
    }
  }

  /**
   * Complete a maintenance task
   */
  async completeMaintenanceTask(id: string, completionData: {
    completionNotes?: string;
    actualDuration?: number;
    actualCost?: number;
  }): Promise<MaintenanceTask> {
    try {
      return await prisma.maintenanceTask.update({
        where: { id },
        data: {
          status: "completed",
          completedDate: new Date(),
          completionNotes: completionData.completionNotes,
          actualDuration: completionData.actualDuration,
          actualCost: completionData.actualCost,
        },
      });
    } catch (error) {
      console.error("Error in completeMaintenanceTask:", error);
      throw new Error("Failed to complete maintenance task");
    }
  }

  /**
   * Get maintenance statistics
   */
  async getMaintenanceStatistics(): Promise<MaintenanceStatistics> {
    try {
      const [
        totalTasks,
        scheduledTasks,
        inProgressTasks,
        completedTasks,
        overdueTasks,
      ] = await Promise.all([
        prisma.maintenanceTask.count(),
        prisma.maintenanceTask.count({ where: { status: "scheduled" } }),
        prisma.maintenanceTask.count({ where: { status: "in_progress" } }),
        prisma.maintenanceTask.count({ where: { status: "completed" } }),
        prisma.maintenanceTask.count({
          where: {
            dueDate: { lt: new Date() },
            status: { not: "completed" },
          },
        }),
      ]);

      // Get upcoming tasks (next 30 days)
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 30);
      
      const upcomingTasks = await prisma.maintenanceTask.count({
        where: {
          scheduledDate: {
            gte: new Date(),
            lte: futureDate,
          },
          status: { in: ["scheduled", "in_progress"] },
        },
      });

      // Calculate average completion time
      const completedTasksWithDuration = await prisma.maintenanceTask.findMany({
        where: {
          status: "completed",
          actualDuration: { not: null },
        },
        select: {
          actualDuration: true,
        },
      });

      const averageCompletionTime = completedTasksWithDuration.length > 0
        ? completedTasksWithDuration.reduce((sum, task) => sum + (task.actualDuration || 0), 0) / completedTasksWithDuration.length
        : 0;

      // Calculate total and average cost
      const completedTasksWithCost = await prisma.maintenanceTask.findMany({
        where: {
          status: "completed",
          actualCost: { not: null },
        },
        select: {
          actualCost: true,
        },
      });

      const totalCost = completedTasksWithCost.reduce((sum, task) => sum + (task.actualCost || 0), 0);
      const averageCost = completedTasksWithCost.length > 0 ? totalCost / completedTasksWithCost.length : 0;

      // Get tasks by priority
      const tasksByPriority = await prisma.maintenanceTask.groupBy({
        by: ['priority'],
        _count: {
          priority: true,
        },
      });

      // Get tasks by type
      const tasksByType = await prisma.maintenanceTask.groupBy({
        by: ['type'],
        _count: {
          type: true,
        },
      });

      return {
        totalTasks,
        scheduledTasks,
        inProgressTasks,
        completedTasks,
        overdueTasks,
        upcomingTasks,
        averageCompletionTime,
        totalCost,
        averageCost,
        tasksByPriority: tasksByPriority.map(item => ({
          priority: item.priority,
          count: item._count.priority,
        })),
        tasksByType: tasksByType.map(item => ({
          type: item.type,
          count: item._count.type,
        })),
      };
    } catch (error) {
      console.error("Error in getMaintenanceStatistics:", error);
      throw new Error("Failed to fetch maintenance statistics");
    }
  }

  /**
   * Get overdue maintenance tasks
   */
  async getOverdueTasks(): Promise<MaintenanceTaskWithAsset[]> {
    try {
      const now = new Date();
      
      return await prisma.maintenanceTask.findMany({
        where: {
          dueDate: { lt: now },
          status: { not: "completed" },
        },
        include: {
          asset: {
            select: {
              id: true,
              name: true,
              category: true,
              location: true,
            },
          },
        },
        orderBy: {
          dueDate: "asc",
        },
      });
    } catch (error) {
      console.error("Error in getOverdueTasks:", error);
      throw new Error("Failed to fetch overdue tasks");
    }
  }

  /**
   * Get upcoming maintenance tasks
   */
  async getUpcomingTasks(days: number = 30): Promise<MaintenanceTaskWithAsset[]> {
    try {
      const now = new Date();
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + days);
      
      return await prisma.maintenanceTask.findMany({
        where: {
          scheduledDate: {
            gte: now,
            lte: futureDate,
          },
          status: { in: ["scheduled", "in_progress"] },
        },
        include: {
          asset: {
            select: {
              id: true,
              name: true,
              category: true,
              location: true,
            },
          },
        },
        orderBy: {
          scheduledDate: "asc",
        },
      });
    } catch (error) {
      console.error("Error in getUpcomingTasks:", error);
      throw new Error("Failed to fetch upcoming tasks");
    }
  }
}

// Export a singleton instance
export const maintenanceService = new MaintenanceService();
