import prisma from "@/lib/prisma";
import { Prisma } from "@prisma/client";

export interface RequisitionCreateInput {
  requestorId: string;
  requestorName: string;
  assetTypeId: string;
  quantity: number;
  priority?: "low" | "normal" | "high" | "critical";
  justification?: string;
  businessCase?: string;
  location?: string;
  department?: string;
  budgetCode?: string;
  expectedDelivery?: Date;
  data: Record<string, any>;
}

export interface RequisitionUpdateInput {
  quantity?: number;
  priority?: "low" | "normal" | "high" | "critical";
  justification?: string;
  businessCase?: string;
  location?: string;
  department?: string;
  budgetCode?: string;
  expectedDelivery?: Date;
  data?: Record<string, any>;
}

export interface ApprovalInput {
  approverId: string;
  decision: "approved" | "rejected";
  comments: string;
  conditions?: string;
  alternativeOptions?: string;
}

export interface FulfillmentInput {
  fulfillerId: string;
  method: "inventory" | "purchase" | "lease";
  assetIds?: string[];
  purchaseOrderId?: string;
  leaseAgreementId?: string;
  deliveryDate?: Date;
  notes?: string;
  trackingInfo?: string;
}

export interface RequisitionFilters {
  status?: string[];
  priority?: string[];
  requestorId?: string;
  assetTypeId?: string;
  department?: string;
  dateFrom?: Date;
  dateTo?: Date;
  search?: string;
}

export class RequisitionService {
  /**
   * Create a new requisition
   */
  static async createRequisition(input: RequisitionCreateInput) {
    try {
      const requisition = await prisma.requisition.create({
        data: {
          requestorId: input.requestorId,
          requestorName: input.requestorName,
          assetTypeId: input.assetTypeId,
          quantity: input.quantity,
          priority: input.priority || "normal",
          justification: input.justification,
          businessCase: input.businessCase,
          location: input.location,
          department: input.department,
          budgetCode: input.budgetCode,
          expectedDelivery: input.expectedDelivery,
          data: input.data,
          status: "pending",
        },
        include: {
          assetType: {
            select: {
              id: true,
              name: true,
              code: true,
              icon: true,
              color: true,
            },
          },
        },
      });

      // Create supply chain metric
      await this.updateSupplyChainMetrics("requisition_created", 1);

      return {
        success: true,
        requisition,
      };
    } catch (error) {
      console.error("Error creating requisition:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to create requisition",
      };
    }
  }

  /**
   * Get requisition by ID
   */
  static async getRequisition(id: string) {
    try {
      const requisition = await prisma.requisition.findUnique({
        where: { id },
        include: {
          assetType: {
            select: {
              id: true,
              name: true,
              code: true,
              icon: true,
              color: true,
            },
          },
          allocations: {
            include: {
              asset: {
                select: {
                  id: true,
                  name: true,
                  serialNumber: true,
                  status: true,
                },
              },
            },
          },
          purchaseOrders: {
            include: {
              purchaseOrder: {
                select: {
                  id: true,
                  supplier: true,
                  status: true,
                  total: true,
                  deliveryDate: true,
                },
              },
            },
          },
          leaseAgreements: {
            include: {
              lease: {
                select: {
                  id: true,
                  lessorName: true,
                  status: true,
                  monthlyPayment: true,
                  startDate: true,
                  endDate: true,
                },
              },
            },
          },
        },
      });

      if (!requisition) {
        return {
          success: false,
          error: "Requisition not found",
        };
      }

      return {
        success: true,
        requisition,
      };
    } catch (error) {
      console.error("Error getting requisition:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get requisition",
      };
    }
  }

  /**
   * List requisitions with filters
   */
  static async listRequisitions(
    filters: RequisitionFilters = {},
    page = 1,
    limit = 50
  ) {
    try {
      const where: Prisma.RequisitionWhereInput = {};

      if (filters.status?.length) {
        where.status = { in: filters.status };
      }

      if (filters.priority?.length) {
        where.priority = { in: filters.priority };
      }

      if (filters.requestorId) {
        where.requestorId = filters.requestorId;
      }

      if (filters.assetTypeId) {
        where.assetTypeId = filters.assetTypeId;
      }

      if (filters.department) {
        where.department = filters.department;
      }

      if (filters.dateFrom || filters.dateTo) {
        where.createdAt = {};
        if (filters.dateFrom) {
          where.createdAt.gte = filters.dateFrom;
        }
        if (filters.dateTo) {
          where.createdAt.lte = filters.dateTo;
        }
      }

      if (filters.search) {
        where.OR = [
          { requestorName: { contains: filters.search, mode: "insensitive" } },
          { justification: { contains: filters.search, mode: "insensitive" } },
          { businessCase: { contains: filters.search, mode: "insensitive" } },
          {
            assetType: {
              name: { contains: filters.search, mode: "insensitive" },
            },
          },
        ];
      }

      const [requisitions, total] = await Promise.all([
        prisma.requisition.findMany({
          where,
          include: {
            assetType: {
              select: {
                id: true,
                name: true,
                code: true,
                icon: true,
                color: true,
              },
            },
          },
          orderBy: { createdAt: "desc" },
          skip: (page - 1) * limit,
          take: limit,
        }),
        prisma.requisition.count({ where }),
      ]);

      return {
        success: true,
        requisitions,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      console.error("Error listing requisitions:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to list requisitions",
      };
    }
  }

  /**
   * Update requisition
   */
  static async updateRequisition(id: string, input: RequisitionUpdateInput) {
    try {
      const requisition = await prisma.requisition.update({
        where: { id },
        data: {
          ...input,
          updatedAt: new Date(),
        },
        include: {
          assetType: {
            select: {
              id: true,
              name: true,
              code: true,
              icon: true,
              color: true,
            },
          },
        },
      });

      return {
        success: true,
        requisition,
      };
    } catch (error) {
      console.error("Error updating requisition:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to update requisition",
      };
    }
  }

  /**
   * Approve or reject requisition
   */
  static async processApproval(id: string, input: ApprovalInput) {
    try {
      const requisition = await prisma.requisition.findUnique({
        where: { id },
        include: { assetType: true },
      });

      if (!requisition) {
        return {
          success: false,
          error: "Requisition not found",
        };
      }

      if (requisition.status !== "pending") {
        return {
          success: false,
          error: "Requisition is not in pending status",
        };
      }

      const approvalHistory = Array.isArray(requisition.approvalHistory)
        ? requisition.approvalHistory
        : [];

      approvalHistory.push({
        approverId: input.approverId,
        decision: input.decision,
        comments: input.comments,
        conditions: input.conditions,
        alternativeOptions: input.alternativeOptions,
        timestamp: new Date().toISOString(),
      });

      const updateData: any = {
        status: input.decision === "approved" ? "approved" : "rejected",
        approvalHistory,
        updatedAt: new Date(),
      };

      if (input.decision === "approved") {
        updateData.approvedAt = new Date();
        updateData.approvedBy = input.approverId;
      } else {
        updateData.rejectedAt = new Date();
        updateData.rejectedBy = input.approverId;
        updateData.rejectionReason = input.comments;
      }

      const updatedRequisition = await prisma.requisition.update({
        where: { id },
        data: updateData,
        include: {
          assetType: {
            select: {
              id: true,
              name: true,
              code: true,
              icon: true,
              color: true,
            },
          },
        },
      });

      // If approved, trigger fulfillment process
      if (input.decision === "approved") {
        await this.triggerFulfillmentProcess(id);
      }

      // Update metrics
      await this.updateSupplyChainMetrics(
        input.decision === "approved" ? "requisition_approved" : "requisition_rejected",
        1
      );

      return {
        success: true,
        requisition: updatedRequisition,
      };
    } catch (error) {
      console.error("Error processing approval:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to process approval",
      };
    }
  }

  /**
   * Fulfill requisition
   */
  static async fulfillRequisition(id: string, input: FulfillmentInput) {
    try {
      const requisition = await prisma.requisition.findUnique({
        where: { id },
        include: { assetType: true },
      });

      if (!requisition) {
        return {
          success: false,
          error: "Requisition not found",
        };
      }

      if (requisition.status !== "approved") {
        return {
          success: false,
          error: "Requisition is not approved",
        };
      }

      const fulfillmentData = {
        method: input.method,
        fulfillerId: input.fulfillerId,
        deliveryDate: input.deliveryDate,
        notes: input.notes,
        trackingInfo: input.trackingInfo,
        timestamp: new Date().toISOString(),
      };

      let updateData: any = {
        fulfillmentData,
        fulfilledAt: new Date(),
        fulfilledBy: input.fulfillerId,
        updatedAt: new Date(),
      };

      // Handle different fulfillment methods
      if (input.method === "inventory" && input.assetIds?.length) {
        // Allocate existing assets
        const allocations = input.assetIds.map((assetId) => ({
          requisitionId: id,
          assetId,
          quantity: 1, // Assuming 1:1 mapping for now
          allocatedBy: input.fulfillerId,
        }));

        await prisma.assetAllocation.createMany({
          data: allocations,
        });

        updateData.status = "fulfilled";
      } else if (input.method === "purchase" && input.purchaseOrderId) {
        // Link to purchase order
        await prisma.requisitionPurchaseOrder.create({
          data: {
            requisitionId: id,
            purchaseOrderId: input.purchaseOrderId,
            quantity: requisition.quantity,
          },
        });

        updateData.status = "partially_fulfilled"; // Will be fulfilled when PO is completed
      } else if (input.method === "lease" && input.leaseAgreementId) {
        // Link to lease agreement
        await prisma.requisitionLeaseAgreement.create({
          data: {
            requisitionId: id,
            leaseId: input.leaseAgreementId,
            quantity: requisition.quantity,
          },
        });

        updateData.status = "fulfilled";
      }

      const updatedRequisition = await prisma.requisition.update({
        where: { id },
        data: updateData,
        include: {
          assetType: {
            select: {
              id: true,
              name: true,
              code: true,
              icon: true,
              color: true,
            },
          },
        },
      });

      // Update metrics
      await this.updateSupplyChainMetrics("requisition_fulfilled", 1);

      return {
        success: true,
        requisition: updatedRequisition,
      };
    } catch (error) {
      console.error("Error fulfilling requisition:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to fulfill requisition",
      };
    }
  }

  /**
   * Trigger automatic fulfillment process
   */
  private static async triggerFulfillmentProcess(requisitionId: string) {
    try {
      const requisition = await prisma.requisition.findUnique({
        where: { id: requisitionId },
        include: { assetType: true },
      });

      if (!requisition) return;

      // Check available inventory
      const availableAssets = await this.getAvailableAssets(requisition.assetTypeId);
      
      if (availableAssets.length >= requisition.quantity) {
        // Auto-allocate from inventory
        const assetsToAllocate = availableAssets.slice(0, requisition.quantity);
        
        await this.fulfillRequisition(requisitionId, {
          fulfillerId: "system",
          method: "inventory",
          assetIds: assetsToAllocate.map(a => a.id),
          notes: "Auto-fulfilled from available inventory",
        });
      } else {
        // Create purchase order for deficit
        const deficit = requisition.quantity - availableAssets.length;
        await this.createPurchaseOrderForRequisition(requisitionId, deficit);
      }
    } catch (error) {
      console.error("Error in auto-fulfillment:", error);
    }
  }

  /**
   * Get available assets of a specific type
   */
  private static async getAvailableAssets(assetTypeId: string) {
    return await prisma.asset.findMany({
      where: {
        assetTypeId,
        status: "active",
        allocations: {
          none: {
            status: "allocated",
          },
        },
      },
      select: {
        id: true,
        name: true,
        serialNumber: true,
        status: true,
      },
    });
  }

  /**
   * Create purchase order for requisition
   */
  private static async createPurchaseOrderForRequisition(
    requisitionId: string,
    quantity: number
  ) {
    try {
      const requisition = await prisma.requisition.findUnique({
        where: { id: requisitionId },
        include: { assetType: true },
      });

      if (!requisition) return;

      // Create purchase order (simplified)
      const purchaseOrder = await prisma.purchaseOrder.create({
        data: {
          supplier: "TBD", // To be determined by procurement team
          items: JSON.stringify([
            {
              assetTypeId: requisition.assetTypeId,
              assetTypeName: requisition.assetType.name,
              quantity,
              unitPrice: 0, // To be filled by procurement
              totalPrice: 0,
            },
          ]),
          total: 0,
          requestedBy: requisition.requestorId,
          status: "pending_approval",
        },
      });

      // Link requisition to purchase order
      await prisma.requisitionPurchaseOrder.create({
        data: {
          requisitionId,
          purchaseOrderId: purchaseOrder.id,
          quantity,
        },
      });

      // Update requisition status
      await prisma.requisition.update({
        where: { id: requisitionId },
        data: {
          status: "partially_fulfilled",
          fulfillmentData: {
            method: "purchase",
            purchaseOrderId: purchaseOrder.id,
            timestamp: new Date().toISOString(),
          },
        },
      });
    } catch (error) {
      console.error("Error creating purchase order:", error);
    }
  }

  /**
   * Get supply chain dashboard metrics
   */
  static async getDashboardMetrics() {
    try {
      const [
        totalRequisitions,
        pendingRequisitions,
        approvedRequisitions,
        fulfilledRequisitions,
        rejectedRequisitions,
        avgProcessingTime,
      ] = await Promise.all([
        prisma.requisition.count(),
        prisma.requisition.count({ where: { status: "pending" } }),
        prisma.requisition.count({ where: { status: "approved" } }),
        prisma.requisition.count({ where: { status: "fulfilled" } }),
        prisma.requisition.count({ where: { status: "rejected" } }),
        this.calculateAverageProcessingTime(),
      ]);

      const fulfillmentRate = totalRequisitions > 0 
        ? (fulfilledRequisitions / totalRequisitions) * 100 
        : 0;

      const approvalRate = totalRequisitions > 0 
        ? (approvedRequisitions / totalRequisitions) * 100 
        : 0;

      return {
        success: true,
        metrics: {
          totalRequisitions,
          pendingRequisitions,
          approvedRequisitions,
          fulfilledRequisitions,
          rejectedRequisitions,
          fulfillmentRate: Math.round(fulfillmentRate * 100) / 100,
          approvalRate: Math.round(approvalRate * 100) / 100,
          avgProcessingTime: Math.round(avgProcessingTime * 100) / 100,
        },
      };
    } catch (error) {
      console.error("Error getting dashboard metrics:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get metrics",
      };
    }
  }

  /**
   * Calculate average processing time
   */
  private static async calculateAverageProcessingTime(): Promise<number> {
    try {
      const completedRequisitions = await prisma.requisition.findMany({
        where: {
          status: { in: ["fulfilled", "rejected"] },
          createdAt: { not: null },
          OR: [
            { fulfilledAt: { not: null } },
            { rejectedAt: { not: null } },
          ],
        },
        select: {
          createdAt: true,
          fulfilledAt: true,
          rejectedAt: true,
        },
      });

      if (completedRequisitions.length === 0) return 0;

      const totalTime = completedRequisitions.reduce((sum, req) => {
        const endTime = req.fulfilledAt || req.rejectedAt;
        if (!endTime) return sum;
        
        const processingTime = endTime.getTime() - req.createdAt.getTime();
        return sum + processingTime;
      }, 0);

      // Return average in days
      return totalTime / completedRequisitions.length / (1000 * 60 * 60 * 24);
    } catch (error) {
      console.error("Error calculating processing time:", error);
      return 0;
    }
  }

  /**
   * Update supply chain metrics
   */
  private static async updateSupplyChainMetrics(metricType: string, value: number) {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      await prisma.supplyChainMetric.upsert({
        where: {
          metricType_period_date: {
            metricType,
            period: "daily",
            date: today,
          },
        },
        update: {
          value: { increment: value },
        },
        create: {
          metricType,
          value,
          unit: "count",
          period: "daily",
          date: today,
        },
      });
    } catch (error) {
      console.error("Error updating supply chain metrics:", error);
    }
  }

  /**
   * Cancel requisition
   */
  static async cancelRequisition(id: string, userId: string, reason: string) {
    try {
      const requisition = await prisma.requisition.findUnique({
        where: { id },
      });

      if (!requisition) {
        return {
          success: false,
          error: "Requisition not found",
        };
      }

      if (!["pending", "approved"].includes(requisition.status)) {
        return {
          success: false,
          error: "Cannot cancel requisition in current status",
        };
      }

      const updatedRequisition = await prisma.requisition.update({
        where: { id },
        data: {
          status: "cancelled",
          rejectionReason: reason,
          updatedAt: new Date(),
        },
        include: {
          assetType: {
            select: {
              id: true,
              name: true,
              code: true,
              icon: true,
              color: true,
            },
          },
        },
      });

      // Update metrics
      await this.updateSupplyChainMetrics("requisition_cancelled", 1);

      return {
        success: true,
        requisition: updatedRequisition,
      };
    } catch (error) {
      console.error("Error cancelling requisition:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to cancel requisition",
      };
    }
  }
}