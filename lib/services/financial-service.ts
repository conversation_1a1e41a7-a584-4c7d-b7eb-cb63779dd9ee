import { prisma } from "@/lib/prisma";
import { assetService } from "./asset-service";

export interface FinancialMetrics {
  totalAssetValue: number;
  totalDepreciation: number;
  currentValue: number;
  monthlyDepreciation: number;
  averageAge: number;
  assetsByCategory: Array<{
    category: string;
    value: number;
    count: number;
    percentage: number;
  }>;
  depreciationByCategory: Array<{
    category: string;
    originalValue: number;
    currentValue: number;
    depreciationAmount: number;
    depreciationPercentage: number;
  }>;
  valueByLocation: Array<{
    location: string;
    value: number;
    count: number;
  }>;
  ageDistribution: Array<{
    ageRange: string;
    count: number;
    value: number;
  }>;
}

export interface AssetValuation {
  id: string;
  name: string;
  category: string;
  originalValue: number;
  currentValue: number;
  depreciationAmount: number;
  depreciationPercentage: number;
  ageInMonths: number;
  estimatedRemainingLife: number;
}

export interface TCOAnalysis {
  assetId: string;
  assetName: string;
  acquisitionCost: number;
  operatingCosts: number;
  maintenanceCosts: number;
  disposalCosts: number;
  totalCostOfOwnership: number;
  costPerYear: number;
  costPerMonth: number;
}

export interface ROIAnalysis {
  assetId: string;
  assetName: string;
  initialInvestment: number;
  annualBenefits: number;
  cumulativeBenefits: number;
  netPresentValue: number;
  returnOnInvestment: number;
  paybackPeriod: number;
}

export class FinancialService {
  /**
   * Get comprehensive financial metrics for all assets
   */
  async getFinancialMetrics(): Promise<FinancialMetrics> {
    try {
      // Get all assets with their financial data
      const assets = await prisma.asset.findMany({
        select: {
          id: true,
          name: true,
          category: true,
          location: true,
          purchasePrice: true,
          purchaseDate: true,
          status: true,
        },
      });

      if (assets.length === 0) {
        return this.getEmptyMetrics();
      }

      const currentDate = new Date();
      let totalAssetValue = 0;
      let totalCurrentValue = 0;
      let totalAgeInMonths = 0;

      // Calculate current values and depreciation
      const assetValuations = assets.map(asset => {
        const ageInMonths = this.calculateAgeInMonths(asset.purchaseDate, currentDate);
        const currentValue = this.calculateCurrentValue(asset.purchasePrice, ageInMonths);
        
        totalAssetValue += asset.purchasePrice;
        totalCurrentValue += currentValue;
        totalAgeInMonths += ageInMonths;

        return {
          ...asset,
          ageInMonths,
          currentValue,
          depreciationAmount: asset.purchasePrice - currentValue,
        };
      });

      const totalDepreciation = totalAssetValue - totalCurrentValue;
      const averageAge = totalAgeInMonths / assets.length;
      const monthlyDepreciation = totalDepreciation / (averageAge || 1);

      // Group by category
      const categoryMap = new Map<string, { value: number; count: number; currentValue: number }>();
      assetValuations.forEach(asset => {
        const existing = categoryMap.get(asset.category) || { value: 0, count: 0, currentValue: 0 };
        categoryMap.set(asset.category, {
          value: existing.value + asset.purchasePrice,
          count: existing.count + 1,
          currentValue: existing.currentValue + asset.currentValue,
        });
      });

      const assetsByCategory = Array.from(categoryMap.entries()).map(([category, data]) => ({
        category,
        value: data.value,
        count: data.count,
        percentage: (data.value / totalAssetValue) * 100,
      }));

      const depreciationByCategory = Array.from(categoryMap.entries()).map(([category, data]) => ({
        category,
        originalValue: data.value,
        currentValue: data.currentValue,
        depreciationAmount: data.value - data.currentValue,
        depreciationPercentage: ((data.value - data.currentValue) / data.value) * 100,
      }));

      // Group by location
      const locationMap = new Map<string, { value: number; count: number }>();
      assetValuations.forEach(asset => {
        const existing = locationMap.get(asset.location) || { value: 0, count: 0 };
        locationMap.set(asset.location, {
          value: existing.value + asset.currentValue,
          count: existing.count + 1,
        });
      });

      const valueByLocation = Array.from(locationMap.entries()).map(([location, data]) => ({
        location,
        value: data.value,
        count: data.count,
      }));

      // Age distribution
      const ageDistribution = this.calculateAgeDistribution(assetValuations);

      return {
        totalAssetValue,
        totalDepreciation,
        currentValue: totalCurrentValue,
        monthlyDepreciation,
        averageAge,
        assetsByCategory,
        depreciationByCategory,
        valueByLocation,
        ageDistribution,
      };
    } catch (error) {
      console.error("Error calculating financial metrics:", error);
      throw new Error("Failed to calculate financial metrics");
    }
  }

  /**
   * Get asset valuations with depreciation calculations
   */
  async getAssetValuations(): Promise<AssetValuation[]> {
    try {
      const assets = await prisma.asset.findMany({
        select: {
          id: true,
          name: true,
          category: true,
          purchasePrice: true,
          purchaseDate: true,
        },
      });

      const currentDate = new Date();

      return assets.map(asset => {
        const ageInMonths = this.calculateAgeInMonths(asset.purchaseDate, currentDate);
        const currentValue = this.calculateCurrentValue(asset.purchasePrice, ageInMonths);
        const depreciationAmount = asset.purchasePrice - currentValue;
        const depreciationPercentage = (depreciationAmount / asset.purchasePrice) * 100;
        
        // Estimate remaining useful life (assuming 5-year average lifespan)
        const estimatedLifeInMonths = 60;
        const estimatedRemainingLife = Math.max(0, estimatedLifeInMonths - ageInMonths);

        return {
          id: asset.id,
          name: asset.name,
          category: asset.category,
          originalValue: asset.purchasePrice,
          currentValue,
          depreciationAmount,
          depreciationPercentage,
          ageInMonths,
          estimatedRemainingLife,
        };
      });
    } catch (error) {
      console.error("Error calculating asset valuations:", error);
      throw new Error("Failed to calculate asset valuations");
    }
  }

  /**
   * Calculate Total Cost of Ownership for an asset
   */
  async calculateTCO(assetId: string): Promise<TCOAnalysis | null> {
    try {
      const asset = await prisma.asset.findUnique({
        where: { id: assetId },
        include: {
          maintenanceTasks: true,
        },
      });

      if (!asset) {
        return null;
      }

      const acquisitionCost = asset.purchasePrice;
      
      // Calculate maintenance costs from completed tasks
      const maintenanceCosts = asset.maintenanceTasks
        .filter(task => task.status === "completed" && task.actualCost)
        .reduce((total, task) => total + (task.actualCost || 0), 0);

      // Estimate operating costs (simplified calculation)
      const ageInYears = this.calculateAgeInMonths(asset.purchaseDate, new Date()) / 12;
      const estimatedOperatingCosts = acquisitionCost * 0.1 * ageInYears; // 10% of acquisition cost per year

      // Estimate disposal costs (simplified)
      const estimatedDisposalCosts = acquisitionCost * 0.05; // 5% of acquisition cost

      const totalCostOfOwnership = acquisitionCost + estimatedOperatingCosts + maintenanceCosts + estimatedDisposalCosts;
      const costPerYear = ageInYears > 0 ? totalCostOfOwnership / ageInYears : totalCostOfOwnership;
      const costPerMonth = costPerYear / 12;

      return {
        assetId: asset.id,
        assetName: asset.name,
        acquisitionCost,
        operatingCosts: estimatedOperatingCosts,
        maintenanceCosts,
        disposalCosts: estimatedDisposalCosts,
        totalCostOfOwnership,
        costPerYear,
        costPerMonth,
      };
    } catch (error) {
      console.error("Error calculating TCO:", error);
      throw new Error("Failed to calculate TCO");
    }
  }

  /**
   * Calculate Return on Investment for an asset
   */
  async calculateROI(assetId: string, annualBenefits: number): Promise<ROIAnalysis | null> {
    try {
      const asset = await prisma.asset.findUnique({
        where: { id: assetId },
      });

      if (!asset) {
        return null;
      }

      const initialInvestment = asset.purchasePrice;
      const ageInYears = this.calculateAgeInMonths(asset.purchaseDate, new Date()) / 12;
      const cumulativeBenefits = annualBenefits * ageInYears;
      
      // Simplified NPV calculation (assuming 5% discount rate)
      const discountRate = 0.05;
      const netPresentValue = cumulativeBenefits / Math.pow(1 + discountRate, ageInYears) - initialInvestment;
      
      const returnOnInvestment = ((cumulativeBenefits - initialInvestment) / initialInvestment) * 100;
      const paybackPeriod = initialInvestment / annualBenefits;

      return {
        assetId: asset.id,
        assetName: asset.name,
        initialInvestment,
        annualBenefits,
        cumulativeBenefits,
        netPresentValue,
        returnOnInvestment,
        paybackPeriod,
      };
    } catch (error) {
      console.error("Error calculating ROI:", error);
      throw new Error("Failed to calculate ROI");
    }
  }

  /**
   * Calculate asset age in months
   */
  private calculateAgeInMonths(purchaseDate: Date, currentDate: Date): number {
    const diffTime = Math.abs(currentDate.getTime() - purchaseDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.floor(diffDays / 30.44); // Average days per month
  }

  /**
   * Calculate current value using straight-line depreciation
   */
  private calculateCurrentValue(originalValue: number, ageInMonths: number): number {
    // Assume 5-year useful life for simplicity
    const usefulLifeInMonths = 60;
    const monthlyDepreciation = originalValue / usefulLifeInMonths;
    const totalDepreciation = Math.min(monthlyDepreciation * ageInMonths, originalValue * 0.9); // Max 90% depreciation
    return Math.max(originalValue - totalDepreciation, originalValue * 0.1); // Min 10% residual value
  }

  /**
   * Calculate age distribution
   */
  private calculateAgeDistribution(assets: any[]): Array<{ ageRange: string; count: number; value: number }> {
    const ranges = [
      { label: "0-12 months", min: 0, max: 12 },
      { label: "1-2 years", min: 13, max: 24 },
      { label: "2-3 years", min: 25, max: 36 },
      { label: "3-5 years", min: 37, max: 60 },
      { label: "5+ years", min: 61, max: Infinity },
    ];

    return ranges.map(range => {
      const assetsInRange = assets.filter(asset => 
        asset.ageInMonths >= range.min && asset.ageInMonths <= range.max
      );
      
      return {
        ageRange: range.label,
        count: assetsInRange.length,
        value: assetsInRange.reduce((sum, asset) => sum + asset.currentValue, 0),
      };
    });
  }

  /**
   * Get empty metrics for when no assets exist
   */
  private getEmptyMetrics(): FinancialMetrics {
    return {
      totalAssetValue: 0,
      totalDepreciation: 0,
      currentValue: 0,
      monthlyDepreciation: 0,
      averageAge: 0,
      assetsByCategory: [],
      depreciationByCategory: [],
      valueByLocation: [],
      ageDistribution: [],
    };
  }
}

// Export a singleton instance
export const financialService = new FinancialService();
