import { db } from '@/lib/db'
import { FlowWorkflowDefinition, FlowNode, FlowEdge } from '../advanced-features/automation/types'

// Execute a workflow with the given input
export async function executeWorkflow(
  workflow: FlowWorkflowDefinition,
  executionId: string,
  input: any = {}
): Promise<any> {
  try {
    // Update execution status to running
    await db.workflowExecution.update({
      where: { id: executionId },
      data: {
        status: 'running'
      }
    })
    
    // Get execution context
    const execution = await db.workflowExecution.findUnique({
      where: { id: executionId }
    })
    
    if (!execution) {
      throw new Error(`Execution ${executionId} not found`)
    }
    
    // Parse context
    const context = JSON.parse(execution.context)
    
    // Initialize variables with input
    const variables = {
      ...context.variables,
      input
    }
    
    // Find start nodes (nodes with no incoming edges)
    const startNodes = findStartNodes(workflow.nodes, workflow.edges)
    
    if (startNodes.length === 0) {
      throw new Error('No start nodes found in workflow')
    }
    
    // Execute workflow starting from start nodes
    const results = await executeNodes(startNodes, workflow.nodes, workflow.edges, variables, context)
    
    // Update execution with results
    await db.workflowExecution.update({
      where: { id: executionId },
      data: {
        status: 'completed',
        completedAt: new Date().toISOString(),
        output: JSON.stringify(results),
        context: JSON.stringify({
          ...context,
          variables,
          endTime: new Date().toISOString()
        })
      }
    })
    
    return results
  } catch (error: any) {
    console.error(`Error executing workflow ${workflow.id}:`, error)
    
    // Update execution with error
    await db.workflowExecution.update({
      where: { id: executionId },
      data: {
        status: 'failed',
        completedAt: new Date().toISOString(),
        output: JSON.stringify({ success: false, error: error.message }),
        context: JSON.stringify({
          ...JSON.parse(execution.context),
          errors: [{ error: error.message }]
        })
      }
    })
    
    throw error
  }
}

// Find nodes with no incoming edges (start nodes)
function findStartNodes(nodes: FlowNode[], edges: FlowEdge[]): FlowNode[] {
  const nodesWithIncomingEdges = new Set(edges.map(edge => edge.target))
  return nodes.filter(node => !nodesWithIncomingEdges.has(node.id))
}

// Execute nodes in the workflow
async function executeNodes(
  currentNodes: FlowNode[],
  allNodes: FlowNode[],
  edges: FlowEdge[],
  variables: any,
  context: any
): Promise<any> {
  if (currentNodes.length === 0) {
    return { success: true, message: 'Workflow completed successfully' }
  }
  
  const nodeResults: Record<string, any> = context.nodeResults || {}
  const errors: any[] = context.errors || []
  
  // Execute current nodes
  for (const node of currentNodes) {
    try {
      // Skip already executed nodes
      if (nodeResults[node.id]) {
        continue
      }
      
      // Execute node
      const startTime = new Date().toISOString()
      const result = await executeNode(node, variables)
      const endTime = new Date().toISOString()
      
      // Store result
      nodeResults[node.id] = {
        ...result,
        startTime,
        endTime
      }
      
      // Update variables with node result
      variables[node.id] = result
    } catch (error: any) {
      console.error(`Error executing node ${node.id}:`, error)
      
      // Store error
      nodeResults[node.id] = {
        error: error.message,
        startTime: new Date().toISOString(),
        endTime: new Date().toISOString()
      }
      
      errors.push({
        nodeId: node.id,
        error: error.message
      })
    }
  }
  
  // Find next nodes to execute
  const nextNodes: FlowNode[] = []
  
  for (const node of currentNodes) {
    // Find outgoing edges
    const outgoingEdges = edges.filter(edge => edge.source === node.id)
    
    // Find target nodes
    for (const edge of outgoingEdges) {
      const targetNode = allNodes.find(n => n.id === edge.target)
      
      if (targetNode && !nextNodes.includes(targetNode)) {
        // Check if all incoming edges to this node have been processed
        const incomingEdges = edges.filter(e => e.target === targetNode.id)
        const allSourceNodesExecuted = incomingEdges.every(e => 
          nodeResults[e.source] !== undefined
        )
        
        if (allSourceNodesExecuted) {
          nextNodes.push(targetNode)
        }
      }
    }
  }
  
  // Update context
  context.nodeResults = nodeResults
  context.errors = errors
  
  // Execute next nodes
  return executeNodes(nextNodes, allNodes, edges, variables, context)
}

// Execute a single node
async function executeNode(node: FlowNode, variables: any): Promise<any> {
  // Handle different node types
  switch (node.type) {
    case 'function':
      return executeFunctionNode(node, variables)
    case 'condition':
      return executeConditionNode(node, variables)
    case 'delay':
      return executeDelayNode(node, variables)
    case 'webhook':
      return executeWebhookNode(node, variables)
    case 'transform':
      return executeTransformNode(node, variables)
    case 'loop':
      return executeLoopNode(node, variables)
    case 'decision':
      return executeDecisionNode(node, variables)
    case 'email':
      return executeEmailNode(node, variables)
    case 'notification':
      return executeNotificationNode(node, variables)
    case 'database':
      return executeDatabaseNode(node, variables)
    case 'api':
      return executeApiNode(node, variables)
    case 'subflow':
      return executeSubflowNode(node, variables)
    case 'filter':
      return executeFilterNode(node, variables)
    case 'merge':
      return executeMergeNode(node, variables)
    case 'file':
      return executeFileNode(node, variables)
    // Asset-specific node types
    case 'assetCreate':
    case 'assetUpdate':
    case 'assetQuery':
    case 'assetDepreciation':
    case 'assetMaintenance':
    case 'assetTransfer':
    case 'assetDisposal':
    case 'inventoryCheck':
    case 'purchaseOrder':
    case 'invoiceProcess':
    case 'approvalRequest':
    case 'notifyStakeholders':
    case 'generateReport':
      return executeAssetNode(node, variables)
    default:
      throw new Error(`Unsupported node type: ${node.type}`)
  }
}

// Execute function node
async function executeFunctionNode(node: FlowNode, variables: any): Promise<any> {
  const { functionBody } = node.data.config
  
  if (!functionBody) {
    throw new Error('Function body is missing')
  }
  
  // Create function from function body
  const fn = new Function('input', 'variables', functionBody)
  
  // Execute function
  return fn(variables.input, variables)
}

// Execute condition node
async function executeConditionNode(node: FlowNode, variables: any): Promise<any> {
  const { condition } = node.data.config
  
  if (!condition) {
    throw new Error('Condition is missing')
  }
  
  // Create function from condition
  const fn = new Function('input', 'variables', `return ${condition}`)
  
  // Execute condition
  const result = fn(variables.input, variables)
  
  return {
    result,
    condition,
    path: result ? 'true' : 'false'
  }
}

// Execute delay node
async function executeDelayNode(node: FlowNode, variables: any): Promise<any> {
  const { delay } = node.data.config
  
  if (!delay) {
    throw new Error('Delay is missing')
  }
  
  // Convert delay to milliseconds
  const delayMs = parseInt(delay) * 1000
  
  // Wait for delay
  await new Promise(resolve => setTimeout(resolve, delayMs))
  
  return {
    delay,
    delayMs,
    message: `Delayed execution for ${delay} seconds`
  }
}

// Execute webhook node
async function executeWebhookNode(node: FlowNode, variables: any): Promise<any> {
  const { url, method, headers, body } = node.data.config
  
  if (!url) {
    throw new Error('Webhook URL is missing')
  }
  
  // Prepare request options
  const options: RequestInit = {
    method: method || 'POST',
    headers: {
      'Content-Type': 'application/json',
      ...headers
    }
  }
  
  // Add body for POST, PUT methods
  if (method === 'POST' || method === 'PUT') {
    // Interpolate variables in body
    const interpolatedBody = interpolateVariables(body, variables)
    options.body = JSON.stringify(interpolatedBody)
  }
  
  // Send request
  const response = await fetch(url, options)
  
  // Parse response
  let responseData
  try {
    responseData = await response.json()
  } catch (error) {
    responseData = await response.text()
  }
  
  return {
    statusCode: response.status,
    statusText: response.statusText,
    data: responseData,
    headers: Object.fromEntries(response.headers.entries())
  }
}

// Execute transform node
async function executeTransformNode(node: FlowNode, variables: any): Promise<any> {
  const { transformations } = node.data.config
  
  if (!transformations || !Array.isArray(transformations)) {
    throw new Error('Transformations are missing or invalid')
  }
  
  // Apply transformations
  const result: Record<string, any> = {}
  
  for (const transformation of transformations) {
    const { target, source, type } = transformation
    
    if (!target || !source) {
      continue
    }
    
    // Get source value
    const sourceValue = getNestedValue(variables, source)
    
    // Apply transformation
    let transformedValue = sourceValue
    
    if (type === 'number') {
      transformedValue = Number(sourceValue)
    } else if (type === 'string') {
      transformedValue = String(sourceValue)
    } else if (type === 'boolean') {
      transformedValue = Boolean(sourceValue)
    } else if (type === 'date') {
      transformedValue = new Date(sourceValue).toISOString()
    }
    
    // Set target value
    result[target] = transformedValue
  }
  
  return result
}

// Helper function to get nested value from object
function getNestedValue(obj: any, path: string): any {
  const keys = path.split('.')
  let value = obj
  
  for (const key of keys) {
    if (value === undefined || value === null) {
      return undefined
    }
    
    value = value[key]
  }
  
  return value
}

// Execute loop node
async function executeLoopNode(node: FlowNode, variables: any): Promise<any> {
  const { array, itemName, maxIterations } = node.data.config
  
  if (!array) {
    throw new Error('Array path is missing')
  }
  
  // Get array from variables
  const arrayData = getNestedValue(variables, array)
  
  if (!Array.isArray(arrayData)) {
    throw new Error(`Value at path '${array}' is not an array`)
  }
  
  // Limit iterations if needed
  const limit = maxIterations ? Math.min(arrayData.length, parseInt(maxIterations)) : arrayData.length
  const results = []
  
  // Process each item
  for (let i = 0; i < limit; i++) {
    const item = arrayData[i]
    const itemVarName = itemName || 'currentItem'
    
    // Add item to variables
    const loopVariables = {
      ...variables,
      [itemVarName]: item,
      loopIndex: i,
      loopCount: limit
    }
    
    // Store result
    results.push({
      index: i,
      item,
      processed: true
    })
  }
  
  return {
    iterations: limit,
    totalItems: arrayData.length,
    results,
    complete: true
  }
}

// Execute decision node
async function executeDecisionNode(node: FlowNode, variables: any): Promise<any> {
  const { field, options } = node.data.config
  
  if (!field || !options || !Array.isArray(options)) {
    throw new Error('Decision configuration is invalid')
  }
  
  // Get field value
  const fieldValue = getNestedValue(variables, field)
  
  // Find matching option
  let selectedOption = null
  let selectedPath = 'default'
  
  for (const option of options) {
    if (!option.condition) continue
    
    // Create function from condition
    try {
      const fn = new Function('input', 'variables', 'value', `return ${option.condition}`)
      const result = fn(variables.input, variables, fieldValue)
      
      if (result) {
        selectedOption = option
        selectedPath = option.value
        break
      }
    } catch (error) {
      console.error(`Error evaluating condition for option ${option.label}:`, error)
    }
  }
  
  return {
    field,
    value: fieldValue,
    selectedOption,
    path: selectedPath
  }
}

// Execute email node
async function executeEmailNode(node: FlowNode, variables: any): Promise<any> {
  const { to, subject, body, template, attachments } = node.data.config
  
  if (!to || !subject || !body) {
    throw new Error('Email configuration is incomplete')
  }
  
  // Interpolate variables in email fields
  const interpolatedTo = interpolateString(to, variables)
  const interpolatedSubject = interpolateString(subject, variables)
  const interpolatedBody = interpolateString(body, variables)
  
  // In a real implementation, this would call an email service
  console.log(`[Email] To: ${interpolatedTo}, Subject: ${interpolatedSubject}`)
  
  return {
    to: interpolatedTo,
    subject: interpolatedSubject,
    body: interpolatedBody,
    template: template,
    attachments: attachments,
    sent: true,
    timestamp: new Date().toISOString()
  }
}

// Execute notification node
async function executeNotificationNode(node: FlowNode, variables: any): Promise<any> {
  const { title, message, type, recipients, channels } = node.data.config
  
  if (!title || !message || !recipients) {
    throw new Error('Notification configuration is incomplete')
  }
  
  // Interpolate variables in notification fields
  const interpolatedTitle = interpolateString(title, variables)
  const interpolatedMessage = interpolateString(message, variables)
  
  // In a real implementation, this would call a notification service
  console.log(`[Notification] Type: ${type}, Title: ${interpolatedTitle}`)
  
  return {
    title: interpolatedTitle,
    message: interpolatedMessage,
    type: type || 'info',
    recipients: recipients,
    channels: channels || ['in-app'],
    sent: true,
    timestamp: new Date().toISOString()
  }
}

// Execute database node
async function executeDatabaseNode(node: FlowNode, variables: any): Promise<any> {
  const { query, parameters } = node.data.config
  
  if (!query) {
    throw new Error('Database query is missing')
  }
  
  // Interpolate variables in query
  const interpolatedQuery = interpolateString(query, variables)
  
  // Prepare parameters
  const interpolatedParams = {}
  if (parameters) {
    for (const [key, value] of Object.entries(parameters)) {
      if (typeof value === 'string') {
        interpolatedParams[key] = interpolateString(value, variables)
      } else {
        interpolatedParams[key] = value
      }
    }
  }
  
  // In a real implementation, this would execute a database query
  console.log(`[Database] Query: ${interpolatedQuery}`)
  
  // Mock database result
  return {
    query: interpolatedQuery,
    parameters: interpolatedParams,
    success: true,
    results: [{ id: 1, name: 'Sample Result' }],
    rowCount: 1,
    timestamp: new Date().toISOString()
  }
}

// Execute API node
async function executeApiNode(node: FlowNode, variables: any): Promise<any> {
  const { url, method, headers, body, timeout, retries } = node.data.config
  
  if (!url) {
    throw new Error('API URL is missing')
  }
  
  // Interpolate variables in URL and body
  const interpolatedUrl = interpolateString(url, variables)
  
  // Prepare request options
  const options: RequestInit = {
    method: method || 'GET',
    headers: {
      'Content-Type': 'application/json',
      ...headers
    }
  }
  
  // Add body for POST, PUT methods
  if ((method === 'POST' || method === 'PUT' || method === 'PATCH') && body) {
    const interpolatedBody = typeof body === 'string'
      ? interpolateString(body, variables)
      : interpolateObject(body, variables)
    
    options.body = typeof interpolatedBody === 'string'
      ? interpolatedBody
      : JSON.stringify(interpolatedBody)
  }
  
  try {
    // Send request
    const response = await fetch(interpolatedUrl, options)
    
    // Parse response
    let responseData
    const contentType = response.headers.get('content-type')
    if (contentType && contentType.includes('application/json')) {
      responseData = await response.json()
    } else {
      responseData = await response.text()
    }
    
    return {
      statusCode: response.status,
      statusText: response.statusText,
      data: responseData,
      headers: Object.fromEntries(response.headers.entries()),
      success: response.ok
    }
  } catch (error) {
    return {
      success: false,
      error: error.message,
      statusCode: 0
    }
  }
}

// Execute subflow node
async function executeSubflowNode(node: FlowNode, variables: any): Promise<any> {
  const { workflowId, parameters, waitForCompletion } = node.data.config
  
  if (!workflowId) {
    throw new Error('Subflow workflow ID is missing')
  }
  
  // Prepare parameters for subflow
  const subflowInput = {}
  if (parameters) {
    for (const [key, value] of Object.entries(parameters)) {
      if (typeof value === 'string') {
        subflowInput[key] = interpolateString(value, variables)
      } else {
        subflowInput[key] = value
      }
    }
  }
  
  try {
    // In a real implementation, this would execute the subflow
    // For now, we'll just log the execution
    console.log(`[Subflow] Executing workflow ${workflowId} with parameters:`, subflowInput)
    
    // Mock subflow execution
    const executionId = `subflow-${Date.now()}`
    
    return {
      workflowId,
      executionId,
      parameters: subflowInput,
      success: true,
      waitForCompletion: waitForCompletion !== false,
      timestamp: new Date().toISOString()
    }
  } catch (error) {
    return {
      success: false,
      error: error.message,
      workflowId
    }
  }
}

// Execute filter node
async function executeFilterNode(node: FlowNode, variables: any): Promise<any> {
  const { array, conditions, logic } = node.data.config
  
  if (!array || !conditions || !Array.isArray(conditions)) {
    throw new Error('Filter configuration is invalid')
  }
  
  // Get array from variables
  const arrayData = getNestedValue(variables, array)
  
  if (!Array.isArray(arrayData)) {
    throw new Error(`Value at path '${array}' is not an array`)
  }
  
  // Filter array
  const filteredData = arrayData.filter(item => {
    // Check each condition
    const results = conditions.map(condition => {
      const { field, operator, value } = condition
      if (!field || !operator) return false
      
      // Get field value from item
      const fieldValue = getNestedValue(item, field)
      
      // Compare based on operator
      switch (operator) {
        case 'equals':
          return fieldValue == value
        case 'not_equals':
          return fieldValue != value
        case 'greater_than':
          return fieldValue > value
        case 'less_than':
          return fieldValue < value
        case 'contains':
          return String(fieldValue).includes(String(value))
        case 'exists':
          return fieldValue !== undefined && fieldValue !== null
        case 'in':
          return Array.isArray(value) && value.includes(fieldValue)
        case 'not_in':
          return Array.isArray(value) && !value.includes(fieldValue)
        default:
          return false
      }
    })
    
    // Combine results based on logic
    if (logic === 'OR') {
      return results.some(result => result)
    } else {
      return results.every(result => result)
    }
  })
  
  return {
    originalCount: arrayData.length,
    filteredCount: filteredData.length,
    filtered: filteredData,
    conditions,
    logic: logic || 'AND'
  }
}

// Execute merge node
async function executeMergeNode(node: FlowNode, variables: any): Promise<any> {
  const { strategy, key, overwrite } = node.data.config
  
  // Get input data
  const input1 = variables.input1 || {}
  const input2 = variables.input2 || {}
  const input3 = variables.input3 || {}
  
  let result
  
  switch (strategy) {
    case 'shallow':
      result = { ...input1, ...input2, ...input3 }
      break
      
    case 'deep':
      result = deepMerge(input1, input2, overwrite)
      if (input3 && typeof input3 === 'object') {
        result = deepMerge(result, input3, overwrite)
      }
      break
      
    case 'array_concat':
      if (!Array.isArray(input1) || !Array.isArray(input2)) {
        throw new Error('Inputs must be arrays for array_concat strategy')
      }
      result = [...input1]
      
      // Add items from input2 that don't exist in input1 (if key is provided)
      if (key) {
        const input1Keys = new Set(input1.map(item => item[key]))
        input2.forEach(item => {
          if (!input1Keys.has(item[key])) {
            result.push(item)
          }
        })
      } else {
        result = [...input1, ...input2]
      }
      
      // Add items from input3 if it exists
      if (Array.isArray(input3)) {
        if (key) {
          const resultKeys = new Set(result.map(item => item[key]))
          input3.forEach(item => {
            if (!resultKeys.has(item[key])) {
              result.push(item)
            }
          })
        } else {
          result = [...result, ...input3]
        }
      }
      break
      
    case 'object_merge':
      if (typeof input1 !== 'object' || typeof input2 !== 'object') {
        throw new Error('Inputs must be objects for object_merge strategy')
      }
      
      result = { ...input1 }
      
      // Merge properties from input2
      Object.entries(input2).forEach(([k, v]) => {
        if (overwrite || result[k] === undefined) {
          result[k] = v
        }
      })
      
      // Merge properties from input3 if it exists
      if (input3 && typeof input3 === 'object') {
        Object.entries(input3).forEach(([k, v]) => {
          if (overwrite || result[k] === undefined) {
            result[k] = v
          }
        })
      }
      break
      
    default:
      result = { ...input1, ...input2, ...input3 }
  }
  
  return {
    strategy,
    result,
    sources: {
      input1: !!input1,
      input2: !!input2,
      input3: !!input3
    }
  }
}

// Execute file node
async function executeFileNode(node: FlowNode, variables: any): Promise<any> {
  const { operation, path, content, format, encoding } = node.data.config
  
  if (!operation || !path) {
    throw new Error('File operation or path is missing')
  }
  
  // Interpolate variables in path
  const interpolatedPath = interpolateString(path, variables)
  
  // In a real implementation, this would perform file operations
  // For now, we'll just log the operation
  console.log(`[File] Operation: ${operation}, Path: ${interpolatedPath}`)
  
  // Mock file operation result
  switch (operation) {
    case 'read':
      return {
        operation,
        path: interpolatedPath,
        success: true,
        content: 'Sample file content',
        format: format || 'text',
        encoding: encoding || 'utf8'
      }
      
    case 'write':
    case 'append':
      const interpolatedContent = content ? interpolateString(content, variables) : ''
      return {
        operation,
        path: interpolatedPath,
        success: true,
        bytesWritten: interpolatedContent.length,
        format: format || 'text',
        encoding: encoding || 'utf8'
      }
      
    case 'delete':
      return {
        operation,
        path: interpolatedPath,
        success: true
      }
      
    case 'move':
    case 'copy':
      const destination = interpolateString(content, variables)
      return {
        operation,
        path: interpolatedPath,
        destination,
        success: true
      }
      
    default:
      throw new Error(`Unsupported file operation: ${operation}`)
  }
}

// Execute asset-specific node
async function executeAssetNode(node: FlowNode, variables: any): Promise<any> {
  // Get function body from node config
  const { functionBody } = node.data.config
  
  if (!functionBody) {
    throw new Error('Function body is missing for asset node')
  }
  
  // Create function from function body
  const fn = new Function('input', 'variables', functionBody)
  
  // Execute function
  return fn(variables.input, variables)
}

// Helper function for deep merge
function deepMerge(target: any, source: any, overwrite = true): any {
  if (!source) return target
  
  const result = { ...target }
  
  Object.keys(source).forEach(key => {
    if (source[key] instanceof Object && key in target && target[key] instanceof Object) {
      result[key] = deepMerge(target[key], source[key], overwrite)
    } else if (overwrite || !(key in target)) {
      result[key] = source[key]
    }
  })
  
  return result
}

// Helper function to interpolate variables in string
function interpolateString(template: string, variables: any): string {
  if (!template) return ''
  
  return template.replace(/\${([^}]+)}/g, (match, path) => {
    const value = getNestedValue(variables, path)
    return value !== undefined ? String(value) : match
  })
}

// Helper function to interpolate variables in object
function interpolateObject(obj: any, variables: any): any {
  if (!obj) return {}
  
  if (typeof obj === 'string') {
    return interpolateString(obj, variables)
  }
  
  if (Array.isArray(obj)) {
    return obj.map(item => interpolateObject(item, variables))
  }
  
  if (typeof obj === 'object') {
    const result = {}
    for (const [key, value] of Object.entries(obj)) {
      result[key] = interpolateObject(value, variables)
    }
    return result
  }
  
  return obj
}

// Helper function to interpolate variables in string
function interpolateVariables(template: string, variables: any): any {
  if (!template) {
    return {}
  }
  
  try {
    // Replace ${variable} with actual values
    const interpolated = template.replace(/\${([^}]+)}/g, (match, path) => {
      const value = getNestedValue(variables, path)
      return value !== undefined ? JSON.stringify(value) : match
    })
    
    // Parse as JSON
    return JSON.parse(interpolated)
  } catch (error) {
    console.error('Error interpolating variables:', error)
    return {}
  }
}