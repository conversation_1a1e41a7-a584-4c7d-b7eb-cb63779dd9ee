import { 
  Package, 
  Plus, 
  Search, 
  Wrench, 
  ArrowRightLeft, 
  ClipboardCheck,
  Calendar,
  Layers,
  TrendingUp,
  Alert<PERSON>riangle,
  CheckCircle
} from "lucide-react";

export interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: any;
  href: string;
  color: string;
  disabled?: boolean;
}

export interface DashboardMetric {
  id: string;
  title: string;
  icon: any;
  color: string;
  apiEndpoint: string;
  dataKey: string;
  description: string;
}

export const QUICK_ACTIONS: QuickAction[] = [
  {
    id: "create-asset",
    title: "Create Asset",
    description: "Add new assets to inventory",
    icon: Plus,
    href: "/admin/assets/new",
    color: "blue",
  },
  {
    id: "inventory-audit",
    title: "Inventory Audit",
    description: "Verify asset locations and status",
    icon: ClipboardCheck,
    href: "/inventory/check",
    color: "green",
  },
  {
    id: "view-assets",
    title: "View Assets",
    description: "Browse all assets",
    icon: Package,
    href: "/assets",
    color: "purple",
  },
  {
    id: "maintenance",
    title: "Maintenance",
    description: "Manage maintenance tasks",
    icon: Wrench,
    href: "/admin/maintenance",
    color: "orange",
  },
];

export const DASHBOARD_METRICS: DashboardMetric[] = [
  {
    id: "total-assets",
    title: "Total Assets",
    icon: Package,
    color: "blue",
    apiEndpoint: "/api/assets/statistics",
    dataKey: "totalAssets",
    description: "Across all categories",
  },
  {
    id: "active-assets",
    title: "Active Assets",
    icon: CheckCircle,
    color: "green",
    apiEndpoint: "/api/assets/statistics",
    dataKey: "activeAssets",
    description: "Currently operational",
  },
  {
    id: "maintenance-assets",
    title: "In Maintenance",
    icon: Wrench,
    color: "yellow",
    apiEndpoint: "/api/assets/statistics",
    dataKey: "maintenanceAssets",
    description: "Under maintenance",
  },
  {
    id: "recent-operations",
    title: "Recent Operations",
    icon: TrendingUp,
    color: "purple",
    apiEndpoint: "/api/assets/statistics",
    dataKey: "recentOperations",
    description: "Last 30 days",
  },
  {
    id: "pending-audits",
    title: "Pending Audits",
    icon: AlertTriangle,
    color: "orange",
    apiEndpoint: "/api/inventory/audit-summary",
    dataKey: "pendingAudits",
    description: "Need verification",
  },
  {
    id: "scheduled-maintenance",
    title: "Scheduled Tasks",
    icon: Calendar,
    color: "indigo",
    apiEndpoint: "/api/maintenance/tasks?upcoming=true",
    dataKey: "tasks.length",
    description: "Next 30 days",
  },
];

export const DASHBOARD_TABS = [
  {
    id: "overview",
    title: "Overview",
    description: "Asset operations summary and quick actions",
  },
  {
    id: "analytics",
    title: "Analytics",
    description: "Asset performance and trends",
  },
  {
    id: "list",
    title: "Asset List",
    description: "Detailed asset listing with filters",
  },
  {
    id: "ai-insights",
    title: "AI Insights",
    description: "AI-powered asset analysis",
  },
];

export const ASSET_STATUS_COLORS = {
  active: "green",
  maintenance: "yellow",
  disposed: "red",
  inactive: "gray",
} as const;

export const OPERATION_TYPE_COLORS = {
  "asset.create": "blue",
  "asset.update": "purple",
  "asset.transfer": "indigo",
  "asset.disposal": "red",
  "maintenance.log": "orange",
  "maintenance.schedule": "yellow",
  "inventory.audit": "green",
} as const;