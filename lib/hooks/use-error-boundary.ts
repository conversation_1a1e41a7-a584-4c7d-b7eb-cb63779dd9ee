"use client"

import { useEffect } from "react"
import { AppError, logError } from "@/lib/utils/error-handler"

export const useErrorBoundary = () => {
  const captureError = (error: Error, errorInfo?: any) => {
    const appError = error instanceof AppError ? error : new AppError(error.message)
    logError(appError, errorInfo)

    // In production, send to error reporting service
    if (process.env.NODE_ENV === "production") {
      // Example: Sentry.captureException(error)
    }
  }

  useEffect(() => {
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      captureError(new Error(event.reason))
    }

    const handleError = (event: ErrorEvent) => {
      captureError(new Error(event.message))
    }

    window.addEventListener("unhandledrejection", handleUnhandledRejection)
    window.addEventListener("error", handleError)

    return () => {
      window.removeEventListener("unhandledrejection", handleUnhandledRejection)
      window.removeEventListener("error", handleError)
    }
  }, [])

  return { captureError }
}
