import { useState, useEffect, useCallback } from 'react'
import { AssetAutomationService } from '../services/asset-automation-service'
import { 
  FlowWorkflowDefinition, 
  TriggerJobExecution, 
  WorkflowAnalytics,
  FlowNode,
  FlowEdge,
  WebhookConfig
} from '../advanced-features/automation/types'
import { AssetNodeType } from '../advanced-features/automation/asset-node-types'

// Hook for managing workflows
export function useWorkflows() {
  const [workflows, setWorkflows] = useState<FlowWorkflowDefinition[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  
  const fetchWorkflows = useCallback(async () => {
    setLoading(true)
    setError(null)
    
    try {
      const fetchedWorkflows = await AssetAutomationService.getWorkflows()
      setWorkflows(fetchedWorkflows)
    } catch (err: any) {
      setError(err)
      console.error('Error fetching workflows:', err)
    } finally {
      setLoading(false)
    }
  }, [])
  
  useEffect(() => {
    fetchWorkflows()
  }, [fetchWorkflows])
  
  const createWorkflow = useCallback(async (workflow: Partial<FlowWorkflowDefinition>) => {
    setLoading(true)
    setError(null)
    
    try {
      const newWorkflow = await AssetAutomationService.createWorkflow(workflow)
      setWorkflows(prev => [...prev, newWorkflow])
      return newWorkflow
    } catch (err: any) {
      setError(err)
      console.error('Error creating workflow:', err)
      throw err
    } finally {
      setLoading(false)
    }
  }, [])
  
  const updateWorkflow = useCallback(async (workflow: FlowWorkflowDefinition) => {
    setLoading(true)
    setError(null)
    
    try {
      const updatedWorkflow = await AssetAutomationService.updateWorkflow(workflow)
      setWorkflows(prev => 
        prev.map(w => w.id === updatedWorkflow.id ? updatedWorkflow : w)
      )
      return updatedWorkflow
    } catch (err: any) {
      setError(err)
      console.error('Error updating workflow:', err)
      throw err
    } finally {
      setLoading(false)
    }
  }, [])
  
  const deleteWorkflow = useCallback(async (id: string) => {
    setLoading(true)
    setError(null)
    
    try {
      await AssetAutomationService.deleteWorkflow(id)
      setWorkflows(prev => prev.filter(w => w.id !== id))
    } catch (err: any) {
      setError(err)
      console.error('Error deleting workflow:', err)
      throw err
    } finally {
      setLoading(false)
    }
  }, [])
  
  return {
    workflows,
    loading,
    error,
    fetchWorkflows,
    createWorkflow,
    updateWorkflow,
    deleteWorkflow
  }
}

// Hook for managing a single workflow
export function useWorkflow(workflowId?: string) {
  const [workflow, setWorkflow] = useState<FlowWorkflowDefinition | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  
  const fetchWorkflow = useCallback(async () => {
    if (!workflowId) return
    
    setLoading(true)
    setError(null)
    
    try {
      const fetchedWorkflow = await AssetAutomationService.getWorkflow(workflowId)
      setWorkflow(fetchedWorkflow)
    } catch (err: any) {
      setError(err)
      console.error(`Error fetching workflow ${workflowId}:`, err)
    } finally {
      setLoading(false)
    }
  }, [workflowId])
  
  useEffect(() => {
    fetchWorkflow()
  }, [fetchWorkflow])
  
  const updateWorkflow = useCallback(async (updatedWorkflow: FlowWorkflowDefinition) => {
    setLoading(true)
    setError(null)
    
    try {
      const result = await AssetAutomationService.updateWorkflow(updatedWorkflow)
      setWorkflow(result)
      return result
    } catch (err: any) {
      setError(err)
      console.error(`Error updating workflow ${workflowId}:`, err)
      throw err
    } finally {
      setLoading(false)
    }
  }, [workflowId])
  
  const executeWorkflow = useCallback(async (input: any = {}) => {
    if (!workflowId) throw new Error('Workflow ID is required')
    
    setLoading(true)
    setError(null)
    
    try {
      const executionId = await AssetAutomationService.executeWorkflow(workflowId, input)
      return executionId
    } catch (err: any) {
      setError(err)
      console.error(`Error executing workflow ${workflowId}:`, err)
      throw err
    } finally {
      setLoading(false)
    }
  }, [workflowId])
  
  return {
    workflow,
    loading,
    error,
    fetchWorkflow,
    updateWorkflow,
    executeWorkflow
  }
}

// Hook for managing workflow executions
export function useWorkflowExecutions(workflowId?: string) {
  const [executions, setExecutions] = useState<TriggerJobExecution[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  
  const fetchExecutions = useCallback(async () => {
    if (!workflowId) return
    
    setLoading(true)
    setError(null)
    
    try {
      const fetchedExecutions = await AssetAutomationService.getExecutions(workflowId)
      setExecutions(fetchedExecutions)
    } catch (err: any) {
      setError(err)
      console.error(`Error fetching executions for workflow ${workflowId}:`, err)
    } finally {
      setLoading(false)
    }
  }, [workflowId])
  
  useEffect(() => {
    fetchExecutions()
  }, [fetchExecutions])
  
  return {
    executions,
    loading,
    error,
    fetchExecutions
  }
}

// Hook for managing a single execution
export function useExecution(executionId?: string) {
  const [execution, setExecution] = useState<TriggerJobExecution | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  
  const fetchExecution = useCallback(async () => {
    if (!executionId) return
    
    setLoading(true)
    setError(null)
    
    try {
      const fetchedExecution = await AssetAutomationService.getExecution(executionId)
      setExecution(fetchedExecution)
    } catch (err: any) {
      setError(err)
      console.error(`Error fetching execution ${executionId}:`, err)
    } finally {
      setLoading(false)
    }
  }, [executionId])
  
  // Initial fetch when executionId changes
  useEffect(() => {
    fetchExecution()
  }, [fetchExecution])
  
  // Separate effect for polling running executions
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null
    
    if (execution && execution.status === 'running') {
      interval = setInterval(() => {
        fetchExecution()
      }, 2000)
    }
    
    return () => {
      if (interval) clearInterval(interval)
    }
  }, [execution?.status, fetchExecution])
  
  return {
    execution,
    loading,
    error,
    fetchExecution
  }
}

// Hook for managing workflow analytics
export function useWorkflowAnalytics(workflowId?: string) {
  const [analytics, setAnalytics] = useState<WorkflowAnalytics | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  
  const fetchAnalytics = useCallback(async () => {
    if (!workflowId) return
    
    setLoading(true)
    setError(null)
    
    try {
      const fetchedAnalytics = await AssetAutomationService.getAnalytics(workflowId)
      setAnalytics(fetchedAnalytics)
    } catch (err: any) {
      setError(err)
      console.error(`Error fetching analytics for workflow ${workflowId}:`, err)
    } finally {
      setLoading(false)
    }
  }, [workflowId])
  
  useEffect(() => {
    fetchAnalytics()
  }, [fetchAnalytics])
  
  return {
    analytics,
    loading,
    error,
    fetchAnalytics
  }
}

// Hook for managing webhooks
export function useWebhooks(workflowId?: string) {
  const [webhooks, setWebhooks] = useState<WebhookConfig[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  
  const fetchWebhooks = useCallback(async () => {
    if (!workflowId) return
    
    setLoading(true)
    setError(null)
    
    try {
      const workflow = await AssetAutomationService.getWorkflow(workflowId)
      setWebhooks(workflow.webhooks || [])
    } catch (err: any) {
      setError(err)
      console.error(`Error fetching webhooks for workflow ${workflowId}:`, err)
    } finally {
      setLoading(false)
    }
  }, [workflowId])
  
  useEffect(() => {
    fetchWebhooks()
  }, [fetchWebhooks])
  
  const createWebhook = useCallback(async (webhook: Partial<WebhookConfig>) => {
    if (!workflowId) throw new Error('Workflow ID is required')
    
    setLoading(true)
    setError(null)
    
    try {
      const newWebhook = await AssetAutomationService.createWebhook(workflowId, webhook)
      setWebhooks(prev => [...prev, newWebhook])
      return newWebhook
    } catch (err: any) {
      setError(err)
      console.error(`Error creating webhook for workflow ${workflowId}:`, err)
      throw err
    } finally {
      setLoading(false)
    }
  }, [workflowId])
  
  const updateWebhook = useCallback(async (webhookId: string, webhook: Partial<WebhookConfig>) => {
    if (!workflowId) throw new Error('Workflow ID is required')
    
    setLoading(true)
    setError(null)
    
    try {
      const updatedWebhook = await AssetAutomationService.updateWebhook(workflowId, webhookId, webhook)
      setWebhooks(prev => 
        prev.map(w => w.id === updatedWebhook.id ? updatedWebhook : w)
      )
      return updatedWebhook
    } catch (err: any) {
      setError(err)
      console.error(`Error updating webhook ${webhookId}:`, err)
      throw err
    } finally {
      setLoading(false)
    }
  }, [workflowId])
  
  const deleteWebhook = useCallback(async (webhookId: string) => {
    if (!workflowId) throw new Error('Workflow ID is required')
    
    setLoading(true)
    setError(null)
    
    try {
      await AssetAutomationService.deleteWebhook(workflowId, webhookId)
      setWebhooks(prev => prev.filter(w => w.id !== webhookId))
    } catch (err: any) {
      setError(err)
      console.error(`Error deleting webhook ${webhookId}:`, err)
      throw err
    } finally {
      setLoading(false)
    }
  }, [workflowId])
  
  const testWebhook = useCallback(async (webhookId: string, payload: any = {}) => {
    if (!workflowId) throw new Error('Workflow ID is required')
    
    setLoading(true)
    setError(null)
    
    try {
      const result = await AssetAutomationService.testWebhook(workflowId, webhookId, payload)
      return result
    } catch (err: any) {
      setError(err)
      console.error(`Error testing webhook ${webhookId}:`, err)
      throw err
    } finally {
      setLoading(false)
    }
  }, [workflowId])
  
  return {
    webhooks,
    loading,
    error,
    fetchWebhooks,
    createWebhook,
    updateWebhook,
    deleteWebhook,
    testWebhook
  }
}

// Hook for executing asset-specific node functions
export function useAssetNodeFunctions() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  
  const executeFunction = useCallback(async (nodeType: AssetNodeType, input: any = {}) => {
    setLoading(true)
    setError(null)
    
    try {
      const result = await AssetAutomationService.executeAssetNodeFunction(nodeType, input)
      return result
    } catch (err: any) {
      setError(err)
      console.error(`Error executing asset function ${nodeType}:`, err)
      throw err
    } finally {
      setLoading(false)
    }
  }, [])
  
  return {
    loading,
    error,
    executeFunction
  }
}

// Hook specifically for creating workflows
export function useCreateWorkflow() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  
  const createWorkflow = useCallback(async (workflowData: {
    name: string;
    description?: string;
    templateId?: string | null;
  }) => {
    setLoading(true)
    setError(null)
    
    try {
      // Create a basic workflow structure
      const workflowTemplate = workflowData.templateId 
        ? await AssetAutomationService.getWorkflowTemplate(workflowData.templateId)
        : {
            nodes: [],
            edges: [],
            viewport: { x: 0, y: 0, zoom: 1 },
            variables: [],
            webhooks: [],
            triggers: []
          };
      
      const newWorkflow = await AssetAutomationService.createWorkflow({
        name: workflowData.name,
        description: workflowData.description || '',
        ...workflowTemplate,
        isActive: false,
        executionCount: 0
      })
      
      return newWorkflow
    } catch (err: any) {
      setError(err)
      console.error('Error creating workflow:', err)
      throw err
    } finally {
      setLoading(false)
    }
  }, [])
  
  return {
    createWorkflow,
    loading,
    error
  }
}