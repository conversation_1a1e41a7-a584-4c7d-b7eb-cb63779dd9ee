import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import bcrypt from 'bcryptjs'
import { NextResponse } from 'next/server'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'ZAR',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount)
}

// Password utilities
export async function hashPassword(password: string): Promise<string> {
  return await bcrypt.hash(password, 10)
}

export async function comparePassword(password: string, hashedPassword: string): Promise<boolean> {
  return await bcrypt.compare(password, hashedPassword)
}

// API response utilities
export function apiResponse(data: any, status: number = 200) {
  return NextResponse.json(data, { status })
}

export function apiError(message: string, status: number = 400) {
  return NextResponse.json({ error: message }, { status })
}

// Authentication middleware
export function withAuth(handler: (req: Request, session: any) => Promise<Response>) {
  return async (req: Request) => {
    try {
      // Get token from Authorization header or cookies
      const authHeader = req.headers.get('authorization')
      const token = authHeader?.startsWith('Bearer ') 
        ? authHeader.substring(7)
        : req.headers.get('cookie')
            ?.split(';')
            .find(c => c.trim().startsWith('auth-token='))
            ?.split('=')[1]

      if (!token) {
        return apiError('Unauthorized', 401)
      }

      // Verify JWT token
      const jwt = require('jsonwebtoken')
      let session
      try {
        session = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key')
      } catch (err) {
        return apiError('Invalid token', 401)
      }

      // Call the actual handler with the session
      return await handler(req, { user: session })
    } catch (error) {
      console.error('Auth middleware error:', error)
      return apiError('Authentication failed', 401)
    }
  }
}
