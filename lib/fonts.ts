import localFont from 'next/font/local'

// Outfit - Sans Serif Font
export const outfit = localFont({
  src: [
    {
      path: '../assets/fonts/Outfit/Outfit-VariableFont_wght.ttf',
      weight: '100 900',
      style: 'normal',
    },
  ],
  variable: '--font-outfit',
  display: 'swap',
  fallback: ['system-ui', 'arial'],
})

// Montserrat - Serif Font
export const montserrat = localFont({
  src: [
    {
      path: '../assets/fonts/Montserrat/Montserrat-VariableFont_wght.ttf',
      weight: '100 900',
      style: 'normal',
    },
    {
      path: '../assets/fonts/Montserrat/Montserrat-Italic-VariableFont_wght.ttf',
      weight: '100 900',
      style: 'italic',
    },
  ],
  variable: '--font-montserrat',
  display: 'swap',
  fallback: ['Georgia', 'serif'],
})

// JetBrains Mono - Monospace Font
export const jetbrainsMono = localFont({
  src: [
    {
      path: '../assets/fonts/JetBrains_Mono/JetBrainsMono-VariableFont_wght.ttf',
      weight: '100 800',
      style: 'normal',
    },
    {
      path: '../assets/fonts/JetBrains_Mono/JetBrainsMono-Italic-VariableFont_wght.ttf',
      weight: '100 800',
      style: 'italic',
    },
  ],
  variable: '--font-jetbrains-mono',
  display: 'swap',
  fallback: ['Consolas', 'Monaco', 'monospace'],
})

// Manrope - Alternative Sans Serif Font
export const manrope = localFont({
  src: [
    {
      path: '../assets/fonts/Manrope/Manrope-VariableFont_wght.ttf',
      weight: '200 800',
      style: 'normal',
    },
  ],
  variable: '--font-manrope',
  display: 'swap',
  fallback: ['system-ui', 'arial'],
})