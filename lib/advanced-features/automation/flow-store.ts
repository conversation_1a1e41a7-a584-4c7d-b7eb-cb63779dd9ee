import { create } from 'zustand'
import { devtools, subscribeWithSelector } from 'zustand/middleware'
import { 
  FlowEditorState, 
  FlowWorkflowDefinition, 
  FlowNode, 
  FlowEdge, 
  NodeTypeDefinition,
  NodeType 
} from './types'
import { defaultNodeTypes } from './node-types'

interface FlowEditorActions {
  // Workflow management
  setWorkflow: (workflow: FlowWorkflowDefinition | null) => void
  updateWorkflow: (updates: Partial<FlowWorkflowDefinition>) => void
  
  // Node management
  addNode: (node: FlowNode) => void
  updateNode: (id: string, updates: Partial<FlowNode>) => void
  deleteNode: (id: string) => void
  duplicateNode: (id: string) => void
  
  // Edge management
  addEdge: (edge: FlowEdge) => void
  updateEdge: (id: string, updates: Partial<FlowEdge>) => void
  deleteEdge: (id: string) => void
  
  // Selection management
  setSelectedNodes: (nodeIds: string[]) => void
  setSelectedEdges: (edgeIds: string[]) => void
  selectNode: (nodeId: string, multi?: boolean) => void
  selectEdge: (edgeId: string, multi?: boolean) => void
  clearSelection: () => void
  
  // Clipboard operations
  copySelection: () => void
  pasteFromClipboard: (position?: { x: number; y: number }) => void
  
  // History management
  undo: () => void
  redo: () => void
  saveToHistory: () => void
  
  // Editor state
  setEditing: (isEditing: boolean) => void
  setDirty: (isDirty: boolean) => void
  setDraggedNodeType: (nodeType: NodeType | null) => void
  
  // Utility functions
  getNodeById: (id: string) => FlowNode | undefined
  getEdgeById: (id: string) => FlowEdge | undefined
  validateWorkflow: () => string[]
  autoLayout: () => void
}

export const useFlowEditorStore = create<FlowEditorState & FlowEditorActions>()(
  devtools(
    subscribeWithSelector((set, get) => ({
      // Initial state
      workflow: null,
      selectedNodes: [],
      selectedEdges: [],
      isEditing: false,
      isDirty: false,
      clipboard: { nodes: [], edges: [] },
      history: { past: [], future: [] },
      nodeTypes: defaultNodeTypes,
      draggedNodeType: null,

      // Workflow management
      setWorkflow: (workflow) => {
        set({ workflow, isDirty: false })
        if (workflow) {
          get().saveToHistory()
        }
      },

      updateWorkflow: (updates) => {
        const currentWorkflow = get().workflow
        if (!currentWorkflow) return

        const updatedWorkflow = { ...currentWorkflow, ...updates }
        set({ 
          workflow: updatedWorkflow, 
          isDirty: true 
        })
      },

      // Node management
      addNode: (node) => {
        const workflow = get().workflow
        if (!workflow) return

        const updatedWorkflow = {
          ...workflow,
          nodes: [...workflow.nodes, node]
        }
        
        set({ 
          workflow: updatedWorkflow, 
          isDirty: true 
        })
        get().saveToHistory()
      },

      updateNode: (id, updates) => {
        const workflow = get().workflow
        if (!workflow) return

        const updatedNodes = workflow.nodes.map(node =>
          node.id === id ? { ...node, ...updates } : node
        )

        set({
          workflow: { ...workflow, nodes: updatedNodes },
          isDirty: true
        })
      },

      deleteNode: (id) => {
        const workflow = get().workflow
        if (!workflow) return

        const updatedNodes = workflow.nodes.filter(node => node.id !== id)
        const updatedEdges = workflow.edges.filter(
          edge => edge.source !== id && edge.target !== id
        )

        set({
          workflow: {
            ...workflow,
            nodes: updatedNodes,
            edges: updatedEdges
          },
          selectedNodes: get().selectedNodes.filter(nodeId => nodeId !== id),
          isDirty: true
        })
        get().saveToHistory()
      },

      duplicateNode: (id) => {
        const workflow = get().workflow
        if (!workflow) return

        const originalNode = workflow.nodes.find(node => node.id === id)
        if (!originalNode) return

        const newNode: FlowNode = {
          ...originalNode,
          id: `${originalNode.id}-copy-${Date.now()}`,
          position: {
            x: originalNode.position.x + 50,
            y: originalNode.position.y + 50
          },
          data: {
            ...originalNode.data,
            label: `${originalNode.data.label} (Copy)`
          }
        }

        get().addNode(newNode)
      },

      // Edge management
      addEdge: (edge) => {
        const workflow = get().workflow
        if (!workflow) return

        // Check if edge already exists
        const edgeExists = workflow.edges.some(
          e => e.source === edge.source && e.target === edge.target
        )
        if (edgeExists) return

        const updatedWorkflow = {
          ...workflow,
          edges: [...workflow.edges, edge]
        }

        set({ 
          workflow: updatedWorkflow, 
          isDirty: true 
        })
        get().saveToHistory()
      },

      updateEdge: (id, updates) => {
        const workflow = get().workflow
        if (!workflow) return

        const updatedEdges = workflow.edges.map(edge =>
          edge.id === id ? { ...edge, ...updates } : edge
        )

        set({
          workflow: { ...workflow, edges: updatedEdges },
          isDirty: true
        })
      },

      deleteEdge: (id) => {
        const workflow = get().workflow
        if (!workflow) return

        const updatedEdges = workflow.edges.filter(edge => edge.id !== id)

        set({
          workflow: { ...workflow, edges: updatedEdges },
          selectedEdges: get().selectedEdges.filter(edgeId => edgeId !== id),
          isDirty: true
        })
        get().saveToHistory()
      },

      // Selection management
      setSelectedNodes: (nodeIds) => set({ selectedNodes: nodeIds }),
      setSelectedEdges: (edgeIds) => set({ selectedEdges: edgeIds }),

      selectNode: (nodeId, multi = false) => {
        const currentSelection = get().selectedNodes
        if (multi) {
          const newSelection = currentSelection.includes(nodeId)
            ? currentSelection.filter(id => id !== nodeId)
            : [...currentSelection, nodeId]
          set({ selectedNodes: newSelection })
        } else {
          set({ selectedNodes: [nodeId] })
        }
      },

      selectEdge: (edgeId, multi = false) => {
        const currentSelection = get().selectedEdges
        if (multi) {
          const newSelection = currentSelection.includes(edgeId)
            ? currentSelection.filter(id => id !== edgeId)
            : [...currentSelection, edgeId]
          set({ selectedEdges: newSelection })
        } else {
          set({ selectedEdges: [edgeId] })
        }
      },

      clearSelection: () => set({ selectedNodes: [], selectedEdges: [] }),

      // Clipboard operations
      copySelection: () => {
        const { workflow, selectedNodes, selectedEdges } = get()
        if (!workflow) return

        const nodesToCopy = workflow.nodes.filter(node => 
          selectedNodes.includes(node.id)
        )
        const edgesToCopy = workflow.edges.filter(edge => 
          selectedEdges.includes(edge.id)
        )

        set({
          clipboard: {
            nodes: nodesToCopy,
            edges: edgesToCopy
          }
        })
      },

      pasteFromClipboard: (position = { x: 100, y: 100 }) => {
        const { clipboard, workflow } = get()
        if (!workflow || clipboard.nodes.length === 0) return

        const idMapping: Record<string, string> = {}
        const timestamp = Date.now()

        // Create new nodes with new IDs
        const newNodes = clipboard.nodes.map((node, index) => {
          const newId = `${node.id}-paste-${timestamp}-${index}`
          idMapping[node.id] = newId
          
          return {
            ...node,
            id: newId,
            position: {
              x: position.x + (index * 20),
              y: position.y + (index * 20)
            }
          }
        })

        // Create new edges with updated IDs
        const newEdges = clipboard.edges
          .filter(edge => 
            idMapping[edge.source] && idMapping[edge.target]
          )
          .map((edge, index) => ({
            ...edge,
            id: `${edge.id}-paste-${timestamp}-${index}`,
            source: idMapping[edge.source],
            target: idMapping[edge.target]
          }))

        // Add to workflow
        const updatedWorkflow = {
          ...workflow,
          nodes: [...workflow.nodes, ...newNodes],
          edges: [...workflow.edges, ...newEdges]
        }

        set({ 
          workflow: updatedWorkflow, 
          isDirty: true,
          selectedNodes: newNodes.map(node => node.id)
        })
        get().saveToHistory()
      },

      // History management
      saveToHistory: () => {
        const { workflow, history } = get()
        if (!workflow) return

        const newHistory = {
          past: [...history.past, workflow].slice(-50), // Keep last 50 states
          future: []
        }

        set({ history: newHistory })
      },

      undo: () => {
        const { history, workflow } = get()
        if (history.past.length === 0 || !workflow) return

        const previous = history.past[history.past.length - 1]
        const newPast = history.past.slice(0, -1)

        set({
          workflow: previous,
          history: {
            past: newPast,
            future: [workflow, ...history.future]
          },
          isDirty: true
        })
      },

      redo: () => {
        const { history, workflow } = get()
        if (history.future.length === 0 || !workflow) return

        const next = history.future[0]
        const newFuture = history.future.slice(1)

        set({
          workflow: next,
          history: {
            past: [...history.past, workflow],
            future: newFuture
          },
          isDirty: true
        })
      },

      // Editor state
      setEditing: (isEditing) => set({ isEditing }),
      setDirty: (isDirty) => set({ isDirty }),
      setDraggedNodeType: (nodeType) => set({ draggedNodeType: nodeType }),

      // Utility functions
      getNodeById: (id) => {
        const workflow = get().workflow
        return workflow?.nodes.find(node => node.id === id)
      },

      getEdgeById: (id) => {
        const workflow = get().workflow
        return workflow?.edges.find(edge => edge.id === id)
      },

      validateWorkflow: () => {
        const { workflow } = get()
        if (!workflow) return ['No workflow loaded']

        const errors: string[] = []

        // Check for nodes without connections
        const isolatedNodes = workflow.nodes.filter(node => {
          const hasIncoming = workflow.edges.some(edge => edge.target === node.id)
          const hasOutgoing = workflow.edges.some(edge => edge.source === node.id)
          return !hasIncoming && !hasOutgoing && node.type !== 'trigger'
        })

        if (isolatedNodes.length > 0) {
          errors.push(`${isolatedNodes.length} isolated node(s) found`)
        }

        // Check for trigger nodes
        const triggerNodes = workflow.nodes.filter(node => node.type === 'trigger')
        if (triggerNodes.length === 0) {
          errors.push('Workflow must have at least one trigger node')
        }

        // Check for unconfigured nodes
        const unconfiguredNodes = workflow.nodes.filter(node => 
          !node.data.isConfigured
        )
        if (unconfiguredNodes.length > 0) {
          errors.push(`${unconfiguredNodes.length} unconfigured node(s) found`)
        }

        return errors
      },

      autoLayout: () => {
        // This would implement automatic layout using dagre
        // For now, we'll implement a simple grid layout
        const { workflow } = get()
        if (!workflow) return

        const updatedNodes = workflow.nodes.map((node, index) => ({
          ...node,
          position: {
            x: (index % 3) * 300 + 100,
            y: Math.floor(index / 3) * 200 + 100
          }
        }))

        set({
          workflow: { ...workflow, nodes: updatedNodes },
          isDirty: true
        })
      }
    })),
    { name: 'flow-editor-store' }
  )
)

// Selectors for commonly used derived state
export const useSelectedNodesData = () => {
  return useFlowEditorStore(state => {
    if (!state.workflow) return []
    return state.workflow.nodes.filter(node => 
      state.selectedNodes.includes(node.id)
    )
  })
}

export const useSelectedEdgesData = () => {
  return useFlowEditorStore(state => {
    if (!state.workflow) return []
    return state.workflow.edges.filter(edge => 
      state.selectedEdges.includes(edge.id)
    )
  })
}

export const useWorkflowStats = () => {
  return useFlowEditorStore(state => {
    if (!state.workflow) return { nodeCount: 0, edgeCount: 0, triggerCount: 0 }
    
    return {
      nodeCount: state.workflow.nodes.length,
      edgeCount: state.workflow.edges.length,
      triggerCount: state.workflow.nodes.filter(n => n.type === 'trigger').length
    }
  })
}