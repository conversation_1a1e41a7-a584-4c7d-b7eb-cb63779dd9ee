import { 
  Play, 
  Mail, 
  Globe, 
  Database, 
  Bell, 
  GitBranch, 
  Split, 
  RotateCcw, 
  Shuffle, 
  Filter, 
  Merge, 
  Clock, 
  FileText, 
  Workflow,
  Webhook,
  Code,
  Layers,
  Box,
  Pencil,
  Search,
  Calculator,
  Wrench,
  ArrowRightLeft,
  Trash2,
  ClipboardCheck,
  ShoppingCart,
  Receipt,
  CheckSquare,
  Users,
  BarChart
} from 'lucide-react'

export const nodeCategories = {
  basic: {
    id: 'basic',
    label: 'Basic',
    icon: Workflow,
    nodes: [
      {
        type: 'trigger',
        label: 'Manual Trigger',
        description: 'Manually start workflow execution',
        icon: Play,
        defaultData: {
          label: 'Manual Trigger',
          description: 'Start this workflow manually',
          inputs: [],
          outputs: [{ id: 'output', type: 'output', dataType: 'any' }]
        }
      },
      {
        type: 'action',
        label: 'Action',
        description: 'Perform a basic action',
        icon: Play,
        defaultData: {
          label: 'Action',
          description: 'Perform a basic action',
          inputs: [{ id: 'input', type: 'input', dataType: 'any', required: false }],
          outputs: [{ id: 'output', type: 'output', dataType: 'any' }]
        }
      },
      {
        type: 'condition',
        label: 'Condition',
        description: 'Evaluate conditional logic',
        icon: GitBranch,
        defaultData: {
          label: 'Condition',
          description: 'Evaluate conditional logic',
          inputs: [{ id: 'input', type: 'input', dataType: 'any', required: false }],
          outputs: [
            { id: 'true', type: 'output', dataType: 'any', label: 'True' },
            { id: 'false', type: 'output', dataType: 'any', label: 'False' }
          ]
        }
      }
    ]
  },
  triggers: {
    id: 'triggers',
    label: 'Triggers',
    icon: Play,
    nodes: [
      {
        type: 'webhook',
        label: 'Webhook Trigger',
        description: 'Trigger workflow via HTTP webhook',
        icon: Webhook,
        defaultData: {
          label: 'Webhook Trigger',
          description: 'Trigger workflow via HTTP webhook',
          inputs: [],
          outputs: [{ id: 'output', type: 'output', dataType: 'any' }]
        }
      },
      {
        type: 'schedule',
        label: 'Schedule Trigger',
        description: 'Trigger workflow on a schedule',
        icon: Clock,
        defaultData: {
          label: 'Schedule Trigger',
          description: 'Trigger workflow on a schedule',
          inputs: [],
          outputs: [{ id: 'output', type: 'output', dataType: 'any' }]
        }
      }
    ]
  },
  actions: {
    id: 'actions',
    label: 'Actions',
    icon: Play,
    nodes: [
      {
        type: 'email',
        label: 'Send Email',
        description: 'Send email notification',
        icon: Mail,
        defaultData: {
          label: 'Send Email',
          description: 'Send email notification',
          inputs: [{ id: 'input', type: 'input', dataType: 'any', required: false }],
          outputs: [{ id: 'output', type: 'output', dataType: 'any' }]
        }
      },
      {
        type: 'api',
        label: 'API Request',
        description: 'Make HTTP API request',
        icon: Globe,
        defaultData: {
          label: 'API Request',
          description: 'Make HTTP API request',
          inputs: [{ id: 'input', type: 'input', dataType: 'any', required: false }],
          outputs: [{ id: 'output', type: 'output', dataType: 'any' }]
        }
      },
      {
        type: 'database',
        label: 'Database Query',
        description: 'Execute database query',
        icon: Database,
        defaultData: {
          label: 'Database Query',
          description: 'Execute database query',
          inputs: [{ id: 'input', type: 'input', dataType: 'any', required: false }],
          outputs: [{ id: 'output', type: 'output', dataType: 'any' }]
        }
      },
      {
        type: 'notification',
        label: 'Send Notification',
        description: 'Send in-app notification',
        icon: Bell,
        defaultData: {
          label: 'Send Notification',
          description: 'Send in-app notification',
          inputs: [{ id: 'input', type: 'input', dataType: 'any', required: false }],
          outputs: [{ id: 'output', type: 'output', dataType: 'any' }]
        }
      }
    ]
  },
  logic: {
    id: 'logic',
    label: 'Logic',
    icon: GitBranch,
    nodes: [
      {
        type: 'decision',
        label: 'Decision',
        description: 'Multi-path decision node',
        icon: Split,
        defaultData: {
          label: 'Decision',
          description: 'Multi-path decision node',
          inputs: [{ id: 'input', type: 'input', dataType: 'any', required: false }],
          outputs: [
            { id: 'option1', type: 'output', dataType: 'any', label: 'Option 1' },
            { id: 'option2', type: 'output', dataType: 'any', label: 'Option 2' },
            { id: 'default', type: 'output', dataType: 'any', label: 'Default' }
          ]
        }
      },
      {
        type: 'loop',
        label: 'Loop',
        description: 'Iterate over array data',
        icon: RotateCcw,
        defaultData: {
          label: 'Loop',
          description: 'Iterate over array data',
          inputs: [{ id: 'input', type: 'input', dataType: 'any', required: false }],
          outputs: [
            { id: 'item', type: 'output', dataType: 'any', label: 'Current Item' },
            { id: 'complete', type: 'output', dataType: 'any', label: 'Complete' }
          ]
        }
      },
      {
        type: 'delay',
        label: 'Delay',
        description: 'Add delay before next action',
        icon: Clock,
        defaultData: {
          label: 'Delay',
          description: 'Add delay before next action',
          inputs: [{ id: 'input', type: 'input', dataType: 'any', required: false }],
          outputs: [{ id: 'output', type: 'output', dataType: 'any' }]
        }
      }
    ]
  },
  data: {
    id: 'data',
    label: 'Data',
    icon: Database,
    nodes: [
      {
        type: 'transform',
        label: 'Transform Data',
        description: 'Transform and manipulate data',
        icon: Shuffle,
        defaultData: {
          label: 'Transform Data',
          description: 'Transform and manipulate data',
          inputs: [{ id: 'input', type: 'input', dataType: 'any', required: false }],
          outputs: [{ id: 'output', type: 'output', dataType: 'any' }]
        }
      },
      {
        type: 'filter',
        label: 'Filter Data',
        description: 'Filter array data based on conditions',
        icon: Filter,
        defaultData: {
          label: 'Filter Data',
          description: 'Filter array data based on conditions',
          inputs: [{ id: 'input', type: 'input', dataType: 'any', required: false }],
          outputs: [{ id: 'output', type: 'output', dataType: 'any' }]
        }
      },
      {
        type: 'merge',
        label: 'Merge Data',
        description: 'Merge multiple data inputs',
        icon: Merge,
        defaultData: {
          label: 'Merge Data',
          description: 'Merge multiple data inputs',
          inputs: [
            { id: 'input1', type: 'input', dataType: 'any', required: true, label: 'Input 1' },
            { id: 'input2', type: 'input', dataType: 'any', required: true, label: 'Input 2' },
            { id: 'input3', type: 'input', dataType: 'any', required: false, label: 'Input 3' }
          ],
          outputs: [{ id: 'output', type: 'output', dataType: 'any' }]
        }
      }
    ]
  },
  asset: {
    id: 'asset',
    label: 'Asset Management',
    icon: Box,
    nodes: [
      {
        type: 'assetCreate',
        label: 'Create Asset',
        description: 'Create a new asset record',
        icon: Box,
        defaultData: {
          label: 'Create Asset',
          description: 'Create a new asset record',
          inputs: [{ id: 'input', type: 'input', dataType: 'any', required: false }],
          outputs: [{ id: 'output', type: 'output', dataType: 'any' }]
        }
      },
      {
        type: 'assetUpdate',
        label: 'Update Asset',
        description: 'Update an existing asset',
        icon: Pencil,
        defaultData: {
          label: 'Update Asset',
          description: 'Update an existing asset',
          inputs: [{ id: 'input', type: 'input', dataType: 'any', required: false }],
          outputs: [{ id: 'output', type: 'output', dataType: 'any' }]
        }
      },
      {
        type: 'assetQuery',
        label: 'Query Assets',
        description: 'Search for assets',
        icon: Search,
        defaultData: {
          label: 'Query Assets',
          description: 'Search for assets',
          inputs: [{ id: 'input', type: 'input', dataType: 'any', required: false }],
          outputs: [{ id: 'output', type: 'output', dataType: 'any' }]
        }
      }
    ]
  },
  operations: {
    id: 'operations',
    label: 'Operations',
    icon: Wrench,
    nodes: [
      {
        type: 'assetMaintenance',
        label: 'Schedule Maintenance',
        description: 'Schedule asset maintenance',
        icon: Wrench,
        defaultData: {
          label: 'Schedule Maintenance',
          description: 'Schedule asset maintenance',
          inputs: [{ id: 'input', type: 'input', dataType: 'any', required: false }],
          outputs: [{ id: 'output', type: 'output', dataType: 'any' }]
        }
      },
      {
        type: 'assetTransfer',
        label: 'Transfer Asset',
        description: 'Transfer asset between locations or departments',
        icon: ArrowRightLeft,
        defaultData: {
          label: 'Transfer Asset',
          description: 'Transfer asset between locations or departments',
          inputs: [{ id: 'input', type: 'input', dataType: 'any', required: false }],
          outputs: [{ id: 'output', type: 'output', dataType: 'any' }]
        }
      },
      {
        type: 'assetDisposal',
        label: 'Dispose Asset',
        description: 'Record asset disposal',
        icon: Trash2,
        defaultData: {
          label: 'Dispose Asset',
          description: 'Record asset disposal',
          inputs: [{ id: 'input', type: 'input', dataType: 'any', required: false }],
          outputs: [{ id: 'output', type: 'output', dataType: 'any' }]
        }
      },
      {
        type: 'inventoryCheck',
        label: 'Inventory Check',
        description: 'Perform inventory verification',
        icon: ClipboardCheck,
        defaultData: {
          label: 'Inventory Check',
          description: 'Perform inventory verification',
          inputs: [{ id: 'input', type: 'input', dataType: 'any', required: false }],
          outputs: [{ id: 'output', type: 'output', dataType: 'any' }]
        }
      }
    ]
  },
  finance: {
    id: 'finance',
    label: 'Finance',
    icon: Receipt,
    nodes: [
      {
        type: 'purchaseOrder',
        label: 'Purchase Order',
        description: 'Create or update purchase order',
        icon: ShoppingCart,
        defaultData: {
          label: 'Purchase Order',
          description: 'Create or update purchase order',
          inputs: [{ id: 'input', type: 'input', dataType: 'any', required: false }],
          outputs: [{ id: 'output', type: 'output', dataType: 'any' }]
        }
      },
      {
        type: 'invoiceProcess',
        label: 'Process Invoice',
        description: 'Process vendor invoice',
        icon: Receipt,
        defaultData: {
          label: 'Process Invoice',
          description: 'Process vendor invoice',
          inputs: [{ id: 'input', type: 'input', dataType: 'any', required: false }],
          outputs: [{ id: 'output', type: 'output', dataType: 'any' }]
        }
      },
      {
        type: 'assetDepreciation',
        label: 'Calculate Depreciation',
        description: 'Calculate asset depreciation',
        icon: Calculator,
        defaultData: {
          label: 'Calculate Depreciation',
          description: 'Calculate asset depreciation',
          inputs: [{ id: 'input', type: 'input', dataType: 'any', required: false }],
          outputs: [{ id: 'output', type: 'output', dataType: 'any' }]
        }
      }
    ]
  },
  workflow: {
    id: 'workflow',
    label: 'Workflow',
    icon: Workflow,
    nodes: [
      {
        type: 'approvalRequest',
        label: 'Approval Request',
        description: 'Request approval from stakeholders',
        icon: CheckSquare,
        defaultData: {
          label: 'Approval Request',
          description: 'Request approval from stakeholders',
          inputs: [{ id: 'input', type: 'input', dataType: 'any', required: false }],
          outputs: [
            { id: 'approved', type: 'output', dataType: 'any', label: 'Approved' },
            { id: 'rejected', type: 'output', dataType: 'any', label: 'Rejected' }
          ]
        }
      },
      {
        type: 'notifyStakeholders',
        label: 'Notify Stakeholders',
        description: 'Send notifications to stakeholders',
        icon: Users,
        defaultData: {
          label: 'Notify Stakeholders',
          description: 'Send notifications to stakeholders',
          inputs: [{ id: 'input', type: 'input', dataType: 'any', required: false }],
          outputs: [{ id: 'output', type: 'output', dataType: 'any' }]
        }
      },
      {
        type: 'generateReport',
        label: 'Generate Report',
        description: 'Generate asset management report',
        icon: BarChart,
        defaultData: {
          label: 'Generate Report',
          description: 'Generate asset management report',
          inputs: [{ id: 'input', type: 'input', dataType: 'any', required: false }],
          outputs: [{ id: 'output', type: 'output', dataType: 'any' }]
        }
      }
    ]
  }
}