import { NodeTypeDefinition, NodePort } from './types'

// Common port definitions
const anyInput: NodePort = { id: 'input', type: 'input', dataType: 'any', required: false }
const anyOutput: NodePort = { id: 'output', type: 'output', dataType: 'any' }
const booleanOutput: NodePort = { id: 'condition', type: 'output', dataType: 'boolean' }
const stringInput: NodePort = { id: 'text', type: 'input', dataType: 'string', required: true }
const objectInput: NodePort = { id: 'data', type: 'input', dataType: 'object', required: true }

export const defaultNodeTypes: NodeTypeDefinition[] = [
  // TRIGGER NODES
  {
    type: 'trigger',
    label: 'Manual Trigger',
    description: 'Manually start workflow execution',
    icon: 'Play',
    category: 'trigger',
    color: '#10b981',
    inputs: [],
    outputs: [anyOutput],
    configSchema: {
      name: { type: 'string', required: true, label: 'Trigger Name' },
      description: { type: 'string', required: false, label: 'Description' }
    },
    defaultConfig: {
      name: 'Manual Trigger',
      description: 'Start this workflow manually'
    }
  },
  {
    type: 'webhook',
    label: 'Webhook Trigger',
    description: 'Trigger workflow via HTTP webhook',
    icon: 'Webhook',
    category: 'trigger',
    color: '#10b981',
    inputs: [],
    outputs: [anyOutput],
    configSchema: {
      path: { type: 'string', required: true, label: 'Webhook Path' },
      method: { 
        type: 'select', 
        required: true, 
        label: 'HTTP Method',
        options: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']
      },
      authentication: {
        type: 'select',
        required: false,
        label: 'Authentication',
        options: ['none', 'basic', 'bearer', 'api_key']
      },
      headers: { type: 'object', required: false, label: 'Custom Headers' }
    },
    defaultConfig: {
      path: '/webhook/trigger',
      method: 'POST',
      authentication: 'none',
      headers: {}
    }
  },

  // ACTION NODES
  {
    type: 'email',
    label: 'Send Email',
    description: 'Send email notification',
    icon: 'Mail',
    category: 'action',
    color: '#3b82f6',
    inputs: [anyInput],
    outputs: [anyOutput],
    configSchema: {
      to: { type: 'string', required: true, label: 'To Email' },
      subject: { type: 'string', required: true, label: 'Subject' },
      body: { type: 'textarea', required: true, label: 'Message Body' },
      template: { type: 'string', required: false, label: 'Template ID' },
      attachments: { type: 'array', required: false, label: 'Attachments' }
    },
    defaultConfig: {
      to: '',
      subject: 'Workflow Notification',
      body: 'This is an automated notification from your workflow.',
      template: '',
      attachments: []
    }
  },
  {
    type: 'api',
    label: 'API Request',
    description: 'Make HTTP API request',
    icon: 'Globe',
    category: 'action',
    color: '#3b82f6',
    inputs: [anyInput],
    outputs: [anyOutput],
    configSchema: {
      url: { type: 'string', required: true, label: 'API URL' },
      method: { 
        type: 'select', 
        required: true, 
        label: 'HTTP Method',
        options: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']
      },
      headers: { type: 'object', required: false, label: 'Headers' },
      body: { type: 'textarea', required: false, label: 'Request Body' },
      timeout: { type: 'number', required: false, label: 'Timeout (ms)' },
      retries: { type: 'number', required: false, label: 'Max Retries' }
    },
    defaultConfig: {
      url: '',
      method: 'GET',
      headers: { 'Content-Type': 'application/json' },
      body: '',
      timeout: 30000,
      retries: 3
    }
  },
  {
    type: 'database',
    label: 'Database Query',
    description: 'Execute database query',
    icon: 'Database',
    category: 'action',
    color: '#3b82f6',
    inputs: [anyInput],
    outputs: [anyOutput],
    configSchema: {
      connection: { type: 'string', required: true, label: 'Connection String' },
      query: { type: 'textarea', required: true, label: 'SQL Query' },
      parameters: { type: 'object', required: false, label: 'Query Parameters' },
      timeout: { type: 'number', required: false, label: 'Timeout (ms)' }
    },
    defaultConfig: {
      connection: '',
      query: 'SELECT * FROM table WHERE id = ?',
      parameters: {},
      timeout: 30000
    }
  },
  {
    type: 'notification',
    label: 'Send Notification',
    description: 'Send in-app notification',
    icon: 'Bell',
    category: 'action',
    color: '#3b82f6',
    inputs: [anyInput],
    outputs: [anyOutput],
    configSchema: {
      title: { type: 'string', required: true, label: 'Notification Title' },
      message: { type: 'textarea', required: true, label: 'Message' },
      type: { 
        type: 'select', 
        required: true, 
        label: 'Notification Type',
        options: ['info', 'success', 'warning', 'error']
      },
      recipients: { type: 'array', required: true, label: 'Recipients' },
      channels: { 
        type: 'multiselect', 
        required: false, 
        label: 'Channels',
        options: ['email', 'sms', 'push', 'in-app']
      }
    },
    defaultConfig: {
      title: 'Workflow Notification',
      message: 'An automated workflow has completed.',
      type: 'info',
      recipients: [],
      channels: ['in-app']
    }
  },

  // LOGIC NODES
  {
    type: 'condition',
    label: 'Condition',
    description: 'Evaluate conditional logic',
    icon: 'GitBranch',
    category: 'logic',
    color: '#f59e0b',
    inputs: [anyInput],
    outputs: [
      { id: 'true', type: 'output', dataType: 'any', label: 'True' },
      { id: 'false', type: 'output', dataType: 'any', label: 'False' }
    ],
    configSchema: {
      field: { type: 'string', required: true, label: 'Field Path' },
      operator: { 
        type: 'select', 
        required: true, 
        label: 'Operator',
        options: ['equals', 'not_equals', 'greater_than', 'less_than', 'contains', 'exists', 'in', 'not_in']
      },
      value: { type: 'string', required: false, label: 'Compare Value' },
      values: { type: 'array', required: false, label: 'Compare Values (for in/not_in)' }
    },
    defaultConfig: {
      field: 'data.status',
      operator: 'equals',
      value: 'active',
      values: []
    }
  },
  {
    type: 'decision',
    label: 'Decision',
    description: 'Multi-path decision node',
    icon: 'Split',
    category: 'logic',
    color: '#f59e0b',
    inputs: [anyInput],
    outputs: [
      { id: 'option1', type: 'output', dataType: 'any', label: 'Option 1' },
      { id: 'option2', type: 'output', dataType: 'any', label: 'Option 2' },
      { id: 'default', type: 'output', dataType: 'any', label: 'Default' }
    ],
    configSchema: {
      field: { type: 'string', required: true, label: 'Decision Field' },
      options: { 
        type: 'array', 
        required: true, 
        label: 'Decision Options',
        items: {
          label: { type: 'string', required: true },
          value: { type: 'string', required: true },
          condition: { type: 'string', required: false }
        }
      }
    },
    defaultConfig: {
      field: 'data.category',
      options: [
        { label: 'High Priority', value: 'high', condition: 'priority > 8' },
        { label: 'Normal Priority', value: 'normal', condition: 'priority <= 8' }
      ]
    }
  },
  {
    type: 'loop',
    label: 'Loop',
    description: 'Iterate over array data',
    icon: 'RotateCcw',
    category: 'logic',
    color: '#f59e0b',
    inputs: [anyInput],
    outputs: [
      { id: 'item', type: 'output', dataType: 'any', label: 'Current Item' },
      { id: 'complete', type: 'output', dataType: 'any', label: 'Complete' }
    ],
    configSchema: {
      array: { type: 'string', required: true, label: 'Array Field Path' },
      itemName: { type: 'string', required: false, label: 'Item Variable Name' },
      maxIterations: { type: 'number', required: false, label: 'Max Iterations' },
      parallel: { type: 'boolean', required: false, label: 'Process in Parallel' }
    },
    defaultConfig: {
      array: 'data.items',
      itemName: 'currentItem',
      maxIterations: 1000,
      parallel: false
    }
  },

  // DATA NODES
  {
    type: 'transform',
    label: 'Transform Data',
    description: 'Transform and manipulate data',
    icon: 'Shuffle',
    category: 'data',
    color: '#8b5cf6',
    inputs: [anyInput],
    outputs: [anyOutput],
    configSchema: {
      transformations: {
        type: 'array',
        required: true,
        label: 'Transformations',
        items: {
          field: { type: 'string', required: true },
          operation: { 
            type: 'select', 
            required: true,
            options: ['set', 'rename', 'delete', 'format', 'calculate', 'map']
          },
          value: { type: 'string', required: false },
          format: { type: 'string', required: false }
        }
      },
      outputFormat: { 
        type: 'select', 
        required: false, 
        label: 'Output Format',
        options: ['json', 'xml', 'csv', 'yaml']
      }
    },
    defaultConfig: {
      transformations: [
        { field: 'name', operation: 'set', value: 'John Doe' }
      ],
      outputFormat: 'json'
    }
  },
  {
    type: 'filter',
    label: 'Filter Data',
    description: 'Filter array data based on conditions',
    icon: 'Filter',
    category: 'data',
    color: '#8b5cf6',
    inputs: [anyInput],
    outputs: [anyOutput],
    configSchema: {
      array: { type: 'string', required: true, label: 'Array Field Path' },
      conditions: {
        type: 'array',
        required: true,
        label: 'Filter Conditions',
        items: {
          field: { type: 'string', required: true },
          operator: { 
            type: 'select', 
            required: true,
            options: ['equals', 'not_equals', 'greater_than', 'less_than', 'contains']
          },
          value: { type: 'string', required: true }
        }
      },
      logic: { 
        type: 'select', 
        required: false, 
        label: 'Logic Operator',
        options: ['AND', 'OR']
      }
    },
    defaultConfig: {
      array: 'data.items',
      conditions: [
        { field: 'status', operator: 'equals', value: 'active' }
      ],
      logic: 'AND'
    }
  },
  {
    type: 'merge',
    label: 'Merge Data',
    description: 'Merge multiple data inputs',
    icon: 'Merge',
    category: 'data',
    color: '#8b5cf6',
    inputs: [
      { id: 'input1', type: 'input', dataType: 'any', required: true, label: 'Input 1' },
      { id: 'input2', type: 'input', dataType: 'any', required: true, label: 'Input 2' },
      { id: 'input3', type: 'input', dataType: 'any', required: false, label: 'Input 3' }
    ],
    outputs: [anyOutput],
    configSchema: {
      strategy: { 
        type: 'select', 
        required: true, 
        label: 'Merge Strategy',
        options: ['shallow', 'deep', 'array_concat', 'object_merge']
      },
      key: { type: 'string', required: false, label: 'Merge Key (for arrays)' },
      overwrite: { type: 'boolean', required: false, label: 'Overwrite Duplicates' }
    },
    defaultConfig: {
      strategy: 'object_merge',
      key: 'id',
      overwrite: true
    }
  },

  // UTILITY NODES
  {
    type: 'delay',
    label: 'Delay',
    description: 'Add delay before next action',
    icon: 'Clock',
    category: 'logic',
    color: '#6b7280',
    inputs: [anyInput],
    outputs: [anyOutput],
    configSchema: {
      duration: { type: 'number', required: true, label: 'Delay Duration' },
      unit: { 
        type: 'select', 
        required: true, 
        label: 'Time Unit',
        options: ['seconds', 'minutes', 'hours', 'days']
      },
      dynamic: { type: 'boolean', required: false, label: 'Dynamic Delay' },
      field: { type: 'string', required: false, label: 'Dynamic Field Path' }
    },
    defaultConfig: {
      duration: 30,
      unit: 'seconds',
      dynamic: false,
      field: ''
    }
  },
  {
    type: 'file',
    label: 'File Operations',
    description: 'Read, write, or process files',
    icon: 'FileText',
    category: 'integration',
    color: '#dc2626',
    inputs: [anyInput],
    outputs: [anyOutput],
    configSchema: {
      operation: { 
        type: 'select', 
        required: true, 
        label: 'File Operation',
        options: ['read', 'write', 'append', 'delete', 'move', 'copy']
      },
      path: { type: 'string', required: true, label: 'File Path' },
      content: { type: 'textarea', required: false, label: 'File Content' },
      format: { 
        type: 'select', 
        required: false, 
        label: 'File Format',
        options: ['text', 'json', 'csv', 'xml', 'binary']
      },
      encoding: { type: 'string', required: false, label: 'File Encoding' }
    },
    defaultConfig: {
      operation: 'read',
      path: '/path/to/file.txt',
      content: '',
      format: 'text',
      encoding: 'utf8'
    }
  },
  {
    type: 'subflow',
    label: 'Sub-workflow',
    description: 'Execute another workflow',
    icon: 'Workflow',
    category: 'logic',
    color: '#059669',
    inputs: [anyInput],
    outputs: [anyOutput],
    configSchema: {
      workflowId: { type: 'string', required: true, label: 'Workflow ID' },
      parameters: { type: 'object', required: false, label: 'Input Parameters' },
      waitForCompletion: { type: 'boolean', required: false, label: 'Wait for Completion' },
      timeout: { type: 'number', required: false, label: 'Timeout (ms)' }
    },
    defaultConfig: {
      workflowId: '',
      parameters: {},
      waitForCompletion: true,
      timeout: 300000
    }
  }
]

// Helper function to get node type definition
export const getNodeTypeDefinition = (type: string): NodeTypeDefinition | undefined => {
  return defaultNodeTypes.find(nodeType => nodeType.type === type)
}

// Helper function to get node types by category
export const getNodeTypesByCategory = (category: string): NodeTypeDefinition[] => {
  return defaultNodeTypes.filter(nodeType => nodeType.category === category)
}

// Helper function to create a new node instance
export const createNodeInstance = (
  type: string, 
  position: { x: number; y: number }, 
  id?: string
) => {
  const nodeType = getNodeTypeDefinition(type)
  if (!nodeType) throw new Error(`Unknown node type: ${type}`)

  return {
    id: id || `${type}-${Date.now()}`,
    type: nodeType.type,
    position,
    data: {
      label: nodeType.label,
      description: nodeType.description,
      icon: nodeType.icon,
      config: { ...nodeType.defaultConfig },
      inputs: nodeType.inputs,
      outputs: nodeType.outputs,
      isConfigured: false,
      errors: []
    },
    draggable: true,
    selectable: true,
    deletable: true
  }
}