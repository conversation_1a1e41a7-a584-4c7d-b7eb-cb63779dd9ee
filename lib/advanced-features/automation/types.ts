export interface WorkflowDefinition {
  id: string
  name: string
  description: string
  trigger: WorkflowTrigger
  conditions: WorkflowCondition[]
  actions: WorkflowAction[]
  isActive: boolean
  createdBy: string
  createdAt: string
  updatedAt: string
  executionCount: number
  lastExecuted?: string
}

export interface WorkflowTrigger {
  type: "event" | "schedule" | "webhook" | "manual"
  config: {
    eventType?: string
    schedule?: string // cron expression
    webhookUrl?: string
    conditions?: Record<string, any>
  }
}

export interface WorkflowCondition {
  field: string
  operator: "equals" | "not_equals" | "greater_than" | "less_than" | "contains" | "exists"
  value: any
  logicalOperator?: "AND" | "OR"
}

export interface WorkflowAction {
  type: "email" | "webhook" | "create_task" | "update_field" | "send_notification" | "run_script"
  config: Record<string, any>
  delay?: number // seconds
  retryCount?: number
}

export interface WorkflowExecution {
  id: string
  workflowId: string
  status: "pending" | "running" | "completed" | "failed" | "cancelled"
  startedAt: string
  completedAt?: string
  error?: string
  context: Record<string, any>
  actionResults: ActionResult[]
}

export interface ActionResult {
  actionType: string
  status: "success" | "failed" | "skipped"
  result?: any
  error?: string
  executedAt: string
}

export interface AutomationRule {
  id: string
  name: string
  entityType: string
  trigger: {
    event: string
    conditions: Record<string, any>
  }
  actions: {
    type: string
    config: Record<string, any>
  }[]
  isActive: boolean
  priority: number
}

export interface SmartSchedule {
  id: string
  name: string
  type: "maintenance" | "inventory" | "reporting" | "cleanup"
  frequency: "daily" | "weekly" | "monthly" | "quarterly" | "custom"
  cronExpression?: string
  nextRun: string
  lastRun?: string
  isActive: boolean
  config: Record<string, any>
}

// ReactFlow Node Types
export interface FlowNode {
  id: string
  type: 'trigger' | 'condition' | 'action' | 'delay' | 'webhook' | 'decision' | 'loop' | 'subflow'
  position: { x: number; y: number }
  data: NodeData
  draggable?: boolean
  selectable?: boolean
  deletable?: boolean
}

export interface NodeData {
  label: string
  description?: string
  icon?: string
  config: Record<string, any>
  inputs?: NodePort[]
  outputs?: NodePort[]
  isConfigured?: boolean
  errors?: string[]
}

export interface NodePort {
  id: string
  type: 'input' | 'output'
  dataType: 'any' | 'string' | 'number' | 'boolean' | 'object' | 'array'
  required?: boolean
  label?: string
}

export interface FlowEdge {
  id: string
  source: string
  target: string
  sourceHandle?: string
  targetHandle?: string
  type?: string
  animated?: boolean
  label?: string
  data?: EdgeData
}

export interface EdgeData {
  condition?: string
  label?: string
  color?: string
}

// Enhanced Workflow Definition for ReactFlow
export interface FlowWorkflowDefinition extends Omit<WorkflowDefinition, 'trigger' | 'conditions' | 'actions'> {
  nodes: FlowNode[]
  edges: FlowEdge[]
  viewport: { x: number; y: number; zoom: number }
  variables: WorkflowVariable[]
  webhooks: WebhookConfig[]
  triggers: FlowTrigger[]
  version?: string
  tags?: string[]
  lastExecuted?: string
}

export interface WorkflowVariable {
  id: string
  name: string
  type: 'string' | 'number' | 'boolean' | 'object' | 'array'
  value: any
  description?: string
  scope: 'global' | 'local'
}

export interface WebhookConfig {
  id: string
  name: string
  url: string
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  headers: Record<string, string>
  authentication?: {
    type: 'none' | 'basic' | 'bearer' | 'api_key'
    config: Record<string, string>
  }
  retryConfig?: {
    maxRetries: number
    retryDelay: number
    backoffMultiplier: number
  }
}

export interface FlowTrigger {
  id: string
  type: 'event' | 'schedule' | 'webhook' | 'manual' | 'api'
  name: string
  description?: string
  config: TriggerConfig
  isActive: boolean
}

export interface TriggerConfig {
  eventType?: string
  schedule?: string
  webhookUrl?: string
  apiEndpoint?: string
  conditions?: Record<string, any>
  triggerData?: Record<string, any>
}

// Node Type Definitions
export type NodeType = 
  | 'trigger'
  | 'condition' 
  | 'action'
  | 'delay'
  | 'webhook'
  | 'decision'
  | 'loop'
  | 'subflow'
  | 'email'
  | 'database'
  | 'api'
  | 'notification'
  | 'file'
  | 'transform'
  | 'filter'
  | 'merge'
  | 'split'

export interface NodeTypeDefinition {
  type: NodeType
  label: string
  description: string
  icon: string
  category: 'trigger' | 'action' | 'logic' | 'data' | 'integration'
  color: string
  inputs: NodePort[]
  outputs: NodePort[]
  configSchema: Record<string, any>
  defaultConfig: Record<string, any>
}

// Flow Editor State
export interface FlowEditorState {
  workflow: FlowWorkflowDefinition | null
  selectedNodes: string[]
  selectedEdges: string[]
  isEditing: boolean
  isDirty: boolean
  clipboard: {
    nodes: FlowNode[]
    edges: FlowEdge[]
  }
  history: {
    past: FlowWorkflowDefinition[]
    future: FlowWorkflowDefinition[]
  }
  nodeTypes: NodeTypeDefinition[]
  draggedNodeType: NodeType | null
}

// Execution Context
export interface FlowExecutionContext {
  workflowId: string
  executionId: string
  variables: Record<string, any>
  nodeResults: Record<string, any>
  startTime: Date
  currentNode?: string
  webhookResponses: Record<string, any>
  errors: ExecutionError[]
}

export interface ExecutionError {
  nodeId: string
  error: string
  timestamp: Date
  severity: 'warning' | 'error' | 'fatal'
}

// Trigger.dev Integration Types
export interface TriggerJobConfig {
  id: string
  name: string
  version: string
  trigger: {
    type: 'webhook' | 'schedule' | 'event'
    config: Record<string, any>
  }
  workflow: FlowWorkflowDefinition
  retries?: number
  concurrency?: number
}

export interface TriggerJobExecution {
  id: string
  jobId: string
  status: 'queued' | 'running' | 'completed' | 'failed' | 'cancelled'
  startedAt: Date
  completedAt?: Date
  output?: any
  error?: string
  context: FlowExecutionContext
}

// Analytics and Monitoring
export interface WorkflowAnalytics {
  workflowId: string
  totalExecutions: number
  successfulExecutions: number
  failedExecutions: number
  averageExecutionTime: number
  lastExecution?: Date
  errorRate: number
  nodeAnalytics: Record<string, NodeAnalytics>
}

export interface NodeAnalytics {
  nodeId: string
  executions: number
  failures: number
  averageExecutionTime: number
  lastExecuted?: Date
}
