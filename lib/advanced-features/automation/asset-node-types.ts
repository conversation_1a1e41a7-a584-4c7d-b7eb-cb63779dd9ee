import { NodeType, FlowNode } from './types'
import { createNodeInstance } from './node-types'
import { XYPosition } from 'reactflow'

// Asset Management specific node types
export type AssetNodeType = 
  | 'assetCreate'
  | 'assetUpdate'
  | 'assetQuery'
  | 'assetDepreciation'
  | 'assetMaintenance'
  | 'assetTransfer'
  | 'assetDisposal'
  | 'inventoryCheck'
  | 'purchaseOrder'
  | 'invoiceProcess'
  | 'approvalRequest'
  | 'notifyStakeholders'
  | 'generateReport'

// Create a specialized asset node
export const createAssetNode = (
  type: AssetNodeType,
  position: XYPosition
): FlowNode => {
  const id = `${type}-${Date.now()}`
  
  // Base node from standard node types
  const baseNode = createNodeInstance(type as NodeType, position)
  
  // Customize based on asset node type
  switch (type) {
    case 'assetCreate':
      return {
        ...baseNode,
        type: 'function',
        data: {
          ...baseNode.data,
          label: 'Create Asset',
          description: 'Create a new asset in the system',
          config: {
            ...baseNode.data.config,
            name: 'Create Asset',
            description: 'Create a new asset in the system',
            functionBody: `// Create a new asset
const { assetName, category, purchaseDate, purchasePrice, location } = input;

// Validate required fields
if (!assetName || !category || !purchaseDate || !purchasePrice) {
  throw new Error('Missing required asset information');
}

// In a real implementation, this would call the asset creation API
return {
  success: true,
  assetId: \`asset-\${Date.now()}\`,
  message: \`Asset "\${assetName}" created successfully\`
};`
          }
        }
      }
    
    case 'assetUpdate':
      return {
        ...baseNode,
        type: 'function',
        data: {
          ...baseNode.data,
          label: 'Update Asset',
          description: 'Update an existing asset',
          config: {
            ...baseNode.data.config,
            name: 'Update Asset',
            description: 'Update an existing asset',
            functionBody: `// Update an existing asset
const { assetId, updates } = input;

// Validate required fields
if (!assetId || !updates) {
  throw new Error('Missing asset ID or update data');
}

// In a real implementation, this would call the asset update API
return {
  success: true,
  assetId: assetId,
  message: \`Asset "\${assetId}" updated successfully\`
};`
          }
        }
      }
    
    case 'assetQuery':
      return {
        ...baseNode,
        type: 'function',
        data: {
          ...baseNode.data,
          label: 'Query Assets',
          description: 'Search for assets based on criteria',
          config: {
            ...baseNode.data.config,
            name: 'Query Assets',
            description: 'Search for assets based on criteria',
            functionBody: `// Query assets based on criteria
const { category, location, status, purchaseDateRange, minValue, maxValue } = input;

// Build query filters
const filters = {};
if (category) filters.category = category;
if (location) filters.location = location;
if (status) filters.status = status;
if (purchaseDateRange) filters.purchaseDateRange = purchaseDateRange;
if (minValue || maxValue) {
  filters.valueRange = {
    min: minValue || 0,
    max: maxValue || Infinity
  };
}

// In a real implementation, this would call the asset query API
return {
  success: true,
  filters: filters,
  results: [
    { id: 'asset-1', name: 'Sample Asset 1' },
    { id: 'asset-2', name: 'Sample Asset 2' }
  ],
  count: 2
};`
          }
        }
      }
    
    case 'assetDepreciation':
      return {
        ...baseNode,
        type: 'function',
        data: {
          ...baseNode.data,
          label: 'Calculate Depreciation',
          description: 'Calculate asset depreciation',
          config: {
            ...baseNode.data.config,
            name: 'Calculate Depreciation',
            description: 'Calculate asset depreciation',
            functionBody: `// Calculate asset depreciation
const { assetId, method, usefulLife, salvageValue } = input;

// Validate required fields
if (!assetId || !method || !usefulLife) {
  throw new Error('Missing required depreciation parameters');
}

// In a real implementation, this would call the depreciation calculation API
return {
  success: true,
  assetId: assetId,
  depreciationSchedule: [
    { year: 1, amount: 1000, bookValue: 9000 },
    { year: 2, amount: 1000, bookValue: 8000 },
    { year: 3, amount: 1000, bookValue: 7000 }
  ],
  method: method,
  totalDepreciation: 3000
};`
          }
        }
      }
    
    case 'assetMaintenance':
      return {
        ...baseNode,
        type: 'function',
        data: {
          ...baseNode.data,
          label: 'Schedule Maintenance',
          description: 'Schedule maintenance for an asset',
          config: {
            ...baseNode.data.config,
            name: 'Schedule Maintenance',
            description: 'Schedule maintenance for an asset',
            functionBody: `// Schedule maintenance for an asset
const { assetId, maintenanceType, scheduledDate, assignedTo, notes } = input;

// Validate required fields
if (!assetId || !maintenanceType || !scheduledDate) {
  throw new Error('Missing required maintenance information');
}

// In a real implementation, this would call the maintenance scheduling API
return {
  success: true,
  maintenanceId: \`maint-\${Date.now()}\`,
  assetId: assetId,
  scheduledDate: scheduledDate,
  message: \`Maintenance scheduled for asset "\${assetId}"\`
};`
          }
        }
      }
    
    case 'assetTransfer':
      return {
        ...baseNode,
        type: 'function',
        data: {
          ...baseNode.data,
          label: 'Transfer Asset',
          description: 'Transfer asset to a new location or department',
          config: {
            ...baseNode.data.config,
            name: 'Transfer Asset',
            description: 'Transfer asset to a new location or department',
            functionBody: `// Transfer asset to a new location or department
const { assetId, newLocation, newDepartment, transferDate, reason } = input;

// Validate required fields
if (!assetId || (!newLocation && !newDepartment)) {
  throw new Error('Missing asset ID or transfer destination');
}

// In a real implementation, this would call the asset transfer API
return {
  success: true,
  assetId: assetId,
  transferId: \`transfer-\${Date.now()}\`,
  previousLocation: 'Warehouse A',
  newLocation: newLocation || 'Same',
  previousDepartment: 'IT',
  newDepartment: newDepartment || 'Same',
  message: \`Asset "\${assetId}" transferred successfully\`
};`
          }
        }
      }
    
    case 'assetDisposal':
      return {
        ...baseNode,
        type: 'function',
        data: {
          ...baseNode.data,
          label: 'Dispose Asset',
          description: 'Record the disposal of an asset',
          config: {
            ...baseNode.data.config,
            name: 'Dispose Asset',
            description: 'Record the disposal of an asset',
            functionBody: `// Record the disposal of an asset
const { assetId, disposalMethod, disposalDate, salePrice, reason } = input;

// Validate required fields
if (!assetId || !disposalMethod || !disposalDate) {
  throw new Error('Missing required disposal information');
}

// In a real implementation, this would call the asset disposal API
return {
  success: true,
  assetId: assetId,
  disposalId: \`disposal-\${Date.now()}\`,
  disposalMethod: disposalMethod,
  bookValue: 5000,
  salePrice: salePrice || 0,
  gainLoss: (salePrice || 0) - 5000,
  message: \`Asset "\${assetId}" disposed successfully\`
};`
          }
        }
      }
    
    case 'inventoryCheck':
      return {
        ...baseNode,
        type: 'function',
        data: {
          ...baseNode.data,
          label: 'Inventory Check',
          description: 'Perform an inventory check on assets',
          config: {
            ...baseNode.data.config,
            name: 'Inventory Check',
            description: 'Perform an inventory check on assets',
            functionBody: `// Perform an inventory check on assets
const { location, department, checkDate, performedBy } = input;

// Validate required fields
if (!location && !department) {
  throw new Error('Must specify location or department for inventory check');
}

// In a real implementation, this would call the inventory check API
return {
  success: true,
  inventoryCheckId: \`inv-check-\${Date.now()}\`,
  location: location || 'All',
  department: department || 'All',
  assetsChecked: 42,
  assetsFound: 40,
  assetsMissing: 2,
  missingAssets: [
    { id: 'asset-123', name: 'Laptop XPS 15' },
    { id: 'asset-456', name: 'Monitor Dell U2720Q' }
  ],
  message: \`Inventory check completed with \${2} discrepancies\`
};`
          }
        }
      }
    
    case 'purchaseOrder':
      return {
        ...baseNode,
        type: 'function',
        data: {
          ...baseNode.data,
          label: 'Create Purchase Order',
          description: 'Create a purchase order for new assets',
          config: {
            ...baseNode.data.config,
            name: 'Create Purchase Order',
            description: 'Create a purchase order for new assets',
            functionBody: `// Create a purchase order for new assets
const { supplier, items, requestedBy, deliveryDate, shippingAddress } = input;

// Validate required fields
if (!supplier || !items || items.length === 0) {
  throw new Error('Missing supplier or items for purchase order');
}

// Calculate total
const total = items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);

// In a real implementation, this would call the purchase order API
return {
  success: true,
  purchaseOrderId: \`po-\${Date.now()}\`,
  supplier: supplier,
  items: items,
  total: total,
  status: 'pending_approval',
  message: \`Purchase order created for \${items.length} items totaling \${total}\`
};`
          }
        }
      }
    
    case 'invoiceProcess':
      return {
        ...baseNode,
        type: 'function',
        data: {
          ...baseNode.data,
          label: 'Process Invoice',
          description: 'Process an invoice for asset purchase or maintenance',
          config: {
            ...baseNode.data.config,
            name: 'Process Invoice',
            description: 'Process an invoice for asset purchase or maintenance',
            functionBody: `// Process an invoice for asset purchase or maintenance
const { invoiceNumber, supplier, amount, purchaseOrderId, items, dueDate } = input;

// Validate required fields
if (!invoiceNumber || !supplier || !amount) {
  throw new Error('Missing required invoice information');
}

// In a real implementation, this would call the invoice processing API
return {
  success: true,
  invoiceId: \`inv-\${Date.now()}\`,
  invoiceNumber: invoiceNumber,
  supplier: supplier,
  amount: amount,
  status: 'pending_approval',
  message: \`Invoice \${invoiceNumber} processed successfully\`
};`
          }
        }
      }
    
    case 'approvalRequest':
      return {
        ...baseNode,
        type: 'function',
        data: {
          ...baseNode.data,
          label: 'Request Approval',
          description: 'Request approval for asset-related actions',
          config: {
            ...baseNode.data.config,
            name: 'Request Approval',
            description: 'Request approval for asset-related actions',
            functionBody: `// Request approval for asset-related actions
const { requestType, requestedBy, approver, items, justification, urgency } = input;

// Validate required fields
if (!requestType || !approver) {
  throw new Error('Missing request type or approver');
}

// In a real implementation, this would call the approval request API
return {
  success: true,
  approvalRequestId: \`approval-\${Date.now()}\`,
  requestType: requestType,
  approver: approver,
  status: 'pending',
  message: \`Approval request sent to \${approver}\`
};`
          }
        }
      }
    
    case 'notifyStakeholders':
      return {
        ...baseNode,
        type: 'function',
        data: {
          ...baseNode.data,
          label: 'Notify Stakeholders',
          description: 'Send notifications to asset stakeholders',
          config: {
            ...baseNode.data.config,
            name: 'Notify Stakeholders',
            description: 'Send notifications to asset stakeholders',
            functionBody: `// Send notifications to asset stakeholders
const { recipients, subject, message, priority, notificationType } = input;

// Validate required fields
if (!recipients || recipients.length === 0 || !subject || !message) {
  throw new Error('Missing recipients, subject, or message');
}

// In a real implementation, this would call the notification API
return {
  success: true,
  notificationId: \`notif-\${Date.now()}\`,
  recipients: recipients,
  deliveredTo: recipients.length,
  message: \`Notification sent to \${recipients.length} recipients\`
};`
          }
        }
      }
    
    case 'generateReport':
      return {
        ...baseNode,
        type: 'function',
        data: {
          ...baseNode.data,
          label: 'Generate Report',
          description: 'Generate asset management reports',
          config: {
            ...baseNode.data.config,
            name: 'Generate Report',
            description: 'Generate asset management reports',
            functionBody: `// Generate asset management reports
const { reportType, parameters, format, delivery } = input;

// Validate required fields
if (!reportType) {
  throw new Error('Missing report type');
}

// Define report types and their descriptions
const reportTypes = {
  'asset-inventory': 'Current inventory of all assets',
  'depreciation': 'Asset depreciation schedule',
  'maintenance-history': 'Asset maintenance history',
  'acquisition': 'Asset acquisition report',
  'disposal': 'Asset disposal report',
  'valuation': 'Current asset valuation'
};

// In a real implementation, this would call the report generation API
return {
  success: true,
  reportId: \`report-\${Date.now()}\`,
  reportType: reportType,
  reportName: reportTypes[reportType] || reportType,
  format: format || 'PDF',
  url: \`https://example.com/reports/\${reportType}-\${Date.now()}.\${format || 'pdf'}\`,
  message: \`\${reportTypes[reportType] || reportType} report generated successfully\`
};`
          }
        }
      }
    
    default:
      return baseNode
  }
}

// Asset Management node categories for the palette
export const assetNodeCategories = [
  {
    id: 'asset-management',
    name: 'Asset Management',
    nodes: [
      {
        type: 'assetCreate',
        name: 'Create Asset',
        description: 'Create a new asset in the system'
      },
      {
        type: 'assetUpdate',
        name: 'Update Asset',
        description: 'Update an existing asset'
      },
      {
        type: 'assetQuery',
        name: 'Query Assets',
        description: 'Search for assets based on criteria'
      },
      {
        type: 'assetTransfer',
        name: 'Transfer Asset',
        description: 'Transfer asset to a new location or department'
      },
      {
        type: 'assetDisposal',
        name: 'Dispose Asset',
        description: 'Record the disposal of an asset'
      }
    ]
  },
  {
    id: 'financial',
    name: 'Financial',
    nodes: [
      {
        type: 'assetDepreciation',
        name: 'Calculate Depreciation',
        description: 'Calculate asset depreciation'
      },
      {
        type: 'purchaseOrder',
        name: 'Create Purchase Order',
        description: 'Create a purchase order for new assets'
      },
      {
        type: 'invoiceProcess',
        name: 'Process Invoice',
        description: 'Process an invoice for asset purchase or maintenance'
      },
      {
        type: 'generateReport',
        name: 'Generate Report',
        description: 'Generate asset management reports'
      }
    ]
  },
  {
    id: 'operations',
    name: 'Operations',
    nodes: [
      {
        type: 'assetMaintenance',
        name: 'Schedule Maintenance',
        description: 'Schedule maintenance for an asset'
      },
      {
        type: 'inventoryCheck',
        name: 'Inventory Check',
        description: 'Perform an inventory check on assets'
      },
      {
        type: 'approvalRequest',
        name: 'Request Approval',
        description: 'Request approval for asset-related actions'
      },
      {
        type: 'notifyStakeholders',
        name: 'Notify Stakeholders',
        description: 'Send notifications to asset stakeholders'
      }
    ]
  }
]

// Asset workflow templates
export const assetWorkflowTemplates = [
  {
    id: 'asset-acquisition',
    name: 'Asset Acquisition Process',
    description: 'Complete workflow for acquiring new assets',
    nodes: [
      { type: 'purchaseOrder', position: { x: 100, y: 100 } },
      { type: 'approvalRequest', position: { x: 100, y: 250 } },
      { type: 'invoiceProcess', position: { x: 100, y: 400 } },
      { type: 'assetCreate', position: { x: 100, y: 550 } },
      { type: 'notifyStakeholders', position: { x: 100, y: 700 } }
    ],
    edges: [
      { source: 'purchaseOrder', target: 'approvalRequest' },
      { source: 'approvalRequest', target: 'invoiceProcess' },
      { source: 'invoiceProcess', target: 'assetCreate' },
      { source: 'assetCreate', target: 'notifyStakeholders' }
    ]
  },
  {
    id: 'asset-maintenance',
    name: 'Asset Maintenance Workflow',
    description: 'Workflow for scheduling and tracking asset maintenance',
    nodes: [
      { type: 'assetQuery', position: { x: 100, y: 100 } },
      { type: 'assetMaintenance', position: { x: 100, y: 250 } },
      { type: 'approvalRequest', position: { x: 100, y: 400 } },
      { type: 'notifyStakeholders', position: { x: 100, y: 550 } },
      { type: 'assetUpdate', position: { x: 100, y: 700 } }
    ],
    edges: [
      { source: 'assetQuery', target: 'assetMaintenance' },
      { source: 'assetMaintenance', target: 'approvalRequest' },
      { source: 'approvalRequest', target: 'notifyStakeholders' },
      { source: 'notifyStakeholders', target: 'assetUpdate' }
    ]
  },
  {
    id: 'asset-disposal',
    name: 'Asset Disposal Process',
    description: 'Workflow for properly disposing of assets',
    nodes: [
      { type: 'assetQuery', position: { x: 100, y: 100 } },
      { type: 'assetDepreciation', position: { x: 100, y: 250 } },
      { type: 'approvalRequest', position: { x: 100, y: 400 } },
      { type: 'assetDisposal', position: { x: 100, y: 550 } },
      { type: 'generateReport', position: { x: 100, y: 700 } },
      { type: 'notifyStakeholders', position: { x: 100, y: 850 } }
    ],
    edges: [
      { source: 'assetQuery', target: 'assetDepreciation' },
      { source: 'assetDepreciation', target: 'approvalRequest' },
      { source: 'approvalRequest', target: 'assetDisposal' },
      { source: 'assetDisposal', target: 'generateReport' },
      { source: 'generateReport', target: 'notifyStakeholders' }
    ]
  },
  {
    id: 'inventory-audit',
    name: 'Inventory Audit Process',
    description: 'Workflow for conducting inventory audits',
    nodes: [
      { type: 'inventoryCheck', position: { x: 100, y: 100 } },
      { type: 'assetQuery', position: { x: 100, y: 250 } },
      { type: 'condition', position: { x: 100, y: 400 } },
      { type: 'notifyStakeholders', position: { x: 250, y: 550 } },
      { type: 'generateReport', position: { x: 100, y: 700 } }
    ],
    edges: [
      { source: 'inventoryCheck', target: 'assetQuery' },
      { source: 'assetQuery', target: 'condition' },
      { source: 'condition', target: 'notifyStakeholders', sourceHandle: 'true' },
      { source: 'condition', target: 'generateReport', sourceHandle: 'false' },
      { source: 'notifyStakeholders', target: 'generateReport' }
    ]
  }
]