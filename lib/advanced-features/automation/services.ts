import type { WorkflowDefinition, WorkflowExecution, AutomationRule, SmartSchedule, ActionResult } from "./types"

export class AutomationService {
  private static instance: AutomationService
  private workflows: WorkflowDefinition[] = []
  private executions: WorkflowExecution[] = []
  private rules: AutomationRule[] = []
  private schedules: SmartSchedule[] = []

  static getInstance(): AutomationService {
    if (!AutomationService.instance) {
      AutomationService.instance = new AutomationService()
    }
    return AutomationService.instance
  }

  // Workflow Management
  async createWorkflow(
    workflow: Omit<WorkflowDefinition, "id" | "createdAt" | "updatedAt" | "executionCount">,
  ): Promise<WorkflowDefinition> {
    const newWorkflow: WorkflowDefinition = {
      ...workflow,
      id: `workflow-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      executionCount: 0,
    }

    this.workflows.push(newWorkflow)
    return newWorkflow
  }

  async executeWorkflow(workflowId: string, context: Record<string, any> = {}): Promise<WorkflowExecution> {
    const workflow = this.workflows.find((w) => w.id === workflowId)
    if (!workflow) {
      throw new Error("Workflow not found")
    }

    if (!workflow.isActive) {
      throw new Error("Workflow is not active")
    }

    const execution: WorkflowExecution = {
      id: `exec-${Date.now()}`,
      workflowId,
      status: "running",
      startedAt: new Date().toISOString(),
      context,
      actionResults: [],
    }

    this.executions.push(execution)

    try {
      // Check conditions
      const conditionsMet = await this.evaluateConditions(workflow.conditions, context)

      if (!conditionsMet) {
        execution.status = "completed"
        execution.completedAt = new Date().toISOString()
        return execution
      }

      // Execute actions
      for (const action of workflow.actions) {
        const result = await this.executeAction(action, context)
        execution.actionResults.push(result)

        if (result.status === "failed" && !action.retryCount) {
          execution.status = "failed"
          execution.error = result.error
          execution.completedAt = new Date().toISOString()
          return execution
        }
      }

      execution.status = "completed"
      execution.completedAt = new Date().toISOString()

      // Update workflow execution count
      workflow.executionCount++
      workflow.lastExecuted = new Date().toISOString()
    } catch (error) {
      execution.status = "failed"
      execution.error = error instanceof Error ? error.message : "Unknown error"
      execution.completedAt = new Date().toISOString()
    }

    return execution
  }

  private async evaluateConditions(conditions: any[], context: Record<string, any>): Promise<boolean> {
    if (conditions.length === 0) return true

    let result = true
    let currentLogicalOperator = "AND"

    for (const condition of conditions) {
      const conditionResult = this.evaluateCondition(condition, context)

      if (currentLogicalOperator === "AND") {
        result = result && conditionResult
      } else {
        result = result || conditionResult
      }

      currentLogicalOperator = condition.logicalOperator || "AND"
    }

    return result
  }

  private evaluateCondition(condition: any, context: Record<string, any>): boolean {
    const fieldValue = this.getNestedValue(context, condition.field)

    switch (condition.operator) {
      case "equals":
        return fieldValue === condition.value
      case "not_equals":
        return fieldValue !== condition.value
      case "greater_than":
        return Number(fieldValue) > Number(condition.value)
      case "less_than":
        return Number(fieldValue) < Number(condition.value)
      case "contains":
        return String(fieldValue).includes(String(condition.value))
      case "exists":
        return fieldValue !== undefined && fieldValue !== null
      default:
        return false
    }
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split(".").reduce((current, key) => current?.[key], obj)
  }

  private async executeAction(action: any, context: Record<string, any>): Promise<ActionResult> {
    const result: ActionResult = {
      actionType: action.type,
      status: "success",
      executedAt: new Date().toISOString(),
    }

    try {
      // Add delay if specified
      if (action.delay) {
        await new Promise((resolve) => setTimeout(resolve, action.delay * 1000))
      }

      switch (action.type) {
        case "email":
          result.result = await this.sendEmail(action.config, context)
          break
        case "webhook":
          result.result = await this.callWebhook(action.config, context)
          break
        case "create_task":
          result.result = await this.createTask(action.config, context)
          break
        case "update_field":
          result.result = await this.updateField(action.config, context)
          break
        case "send_notification":
          result.result = await this.sendNotification(action.config, context)
          break
        case "run_script":
          result.result = await this.runScript(action.config, context)
          break
        default:
          throw new Error(`Unknown action type: ${action.type}`)
      }
    } catch (error) {
      result.status = "failed"
      result.error = error instanceof Error ? error.message : "Unknown error"
    }

    return result
  }

  private async sendEmail(config: any, context: Record<string, any>): Promise<any> {
    // Simulate email sending
    return {
      to: config.to,
      subject: this.interpolateTemplate(config.subject, context),
      body: this.interpolateTemplate(config.body, context),
      sent: true,
      timestamp: new Date().toISOString(),
    }
  }

  private async callWebhook(config: any, context: Record<string, any>): Promise<any> {
    // Simulate webhook call
    return {
      url: config.url,
      method: config.method || "POST",
      payload: context,
      status: 200,
      response: { success: true },
    }
  }

  private async createTask(config: any, context: Record<string, any>): Promise<any> {
    // Simulate task creation
    return {
      id: `task-${Date.now()}`,
      title: this.interpolateTemplate(config.title, context),
      description: this.interpolateTemplate(config.description, context),
      assignee: config.assignee,
      priority: config.priority || "medium",
      createdAt: new Date().toISOString(),
    }
  }

  private async updateField(config: any, context: Record<string, any>): Promise<any> {
    // Simulate field update
    return {
      entityId: config.entityId,
      field: config.field,
      oldValue: context[config.field],
      newValue: config.value,
      updatedAt: new Date().toISOString(),
    }
  }

  private async sendNotification(config: any, context: Record<string, any>): Promise<any> {
    // Simulate notification sending
    return {
      userId: config.userId,
      title: this.interpolateTemplate(config.title, context),
      message: this.interpolateTemplate(config.message, context),
      type: config.type || "info",
      sent: true,
      timestamp: new Date().toISOString(),
    }
  }

  private async runScript(config: any, context: Record<string, any>): Promise<any> {
    // Simulate script execution
    return {
      script: config.script,
      context,
      result: "Script executed successfully",
      executedAt: new Date().toISOString(),
    }
  }

  private interpolateTemplate(template: string, context: Record<string, any>): string {
    return template.replace(/\{\{(\w+(?:\.\w+)*)\}\}/g, (match, path) => {
      const value = this.getNestedValue(context, path)
      return value !== undefined ? String(value) : match
    })
  }

  // Smart Scheduling
  async createSmartSchedule(schedule: Omit<SmartSchedule, "id" | "nextRun">): Promise<SmartSchedule> {
    const nextRun = this.calculateNextRun(schedule.frequency, schedule.cronExpression)

    const newSchedule: SmartSchedule = {
      ...schedule,
      id: `schedule-${Date.now()}`,
      nextRun,
    }

    this.schedules.push(newSchedule)
    return newSchedule
  }

  private calculateNextRun(frequency: string, cronExpression?: string): string {
    const now = new Date()

    if (cronExpression) {
      // In a real implementation, you'd use a cron parser
      return new Date(now.getTime() + 24 * 60 * 60 * 1000).toISOString()
    }

    switch (frequency) {
      case "daily":
        return new Date(now.getTime() + 24 * 60 * 60 * 1000).toISOString()
      case "weekly":
        return new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString()
      case "monthly":
        return new Date(now.getFullYear(), now.getMonth() + 1, now.getDate()).toISOString()
      case "quarterly":
        return new Date(now.getFullYear(), now.getMonth() + 3, now.getDate()).toISOString()
      default:
        return new Date(now.getTime() + 60 * 60 * 1000).toISOString() // 1 hour default
    }
  }

  // Automation Rules
  async createAutomationRule(rule: Omit<AutomationRule, "id">): Promise<AutomationRule> {
    const newRule: AutomationRule = {
      ...rule,
      id: `rule-${Date.now()}`,
    }

    this.rules.push(newRule)
    return newRule
  }

  async triggerAutomationRules(entityType: string, event: string, data: any): Promise<void> {
    const applicableRules = this.rules
      .filter((rule) => rule.isActive && rule.entityType === entityType && rule.trigger.event === event)
      .sort((a, b) => b.priority - a.priority)

    for (const rule of applicableRules) {
      try {
        // Check if rule conditions are met
        const conditionsMet = this.evaluateRuleConditions(rule.trigger.conditions, data)

        if (conditionsMet) {
          // Execute rule actions
          for (const action of rule.actions) {
            await this.executeAction(action, data)
          }
        }
      } catch (error) {
        console.error(`Error executing automation rule ${rule.id}:`, error)
      }
    }
  }

  private evaluateRuleConditions(conditions: Record<string, any>, data: any): boolean {
    if (!conditions) return true

    for (const [field, expectedValue] of Object.entries(conditions)) {
      const actualValue = this.getNestedValue(data, field)
      if (actualValue !== expectedValue) {
        return false
      }
    }

    return true
  }

  // Getters
  getWorkflows(): WorkflowDefinition[] {
    return this.workflows
  }

  getWorkflowExecutions(workflowId?: string): WorkflowExecution[] {
    return workflowId ? this.executions.filter((e) => e.workflowId === workflowId) : this.executions
  }

  getAutomationRules(entityType?: string): AutomationRule[] {
    return entityType ? this.rules.filter((r) => r.entityType === entityType) : this.rules
  }

  getSmartSchedules(): SmartSchedule[] {
    return this.schedules
  }

  // Analytics
  async getAutomationMetrics() {
    const now = new Date()
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)

    const recentExecutions = this.executions.filter((e) => new Date(e.startedAt) > oneWeekAgo)

    return {
      totalWorkflows: this.workflows.length,
      activeWorkflows: this.workflows.filter((w) => w.isActive).length,
      totalExecutions: this.executions.length,
      recentExecutions: recentExecutions.length,
      successfulExecutions: recentExecutions.filter((e) => e.status === "completed").length,
      failedExecutions: recentExecutions.filter((e) => e.status === "failed").length,
      activeRules: this.rules.filter((r) => r.isActive).length,
      activeSchedules: this.schedules.filter((s) => s.isActive).length,
    }
  }
}
