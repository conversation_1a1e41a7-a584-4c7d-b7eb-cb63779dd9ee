import { google } from '@ai-sdk/google';
import { generateObject, generateText, streamText, tool } from 'ai';
import { z } from 'zod';
import { assetService } from '@/lib/services/asset-service';
import type { 
  AIInsight, 
  PredictiveModel, 
  SmartAlert, 
  NLPQuery, 
  AssetAnalysis,
  MaintenancePrediction,
  CostOptimization,
  AIServiceConfig,
  AIChatMessage,
  ToolInvocation
} from "./types";
import { 
  AssetAnalysisSchema, 
  MaintenancePredictionSchema, 
  CostOptimizationSchema 
} from "./types";

export class AIEngineService {
  private static instance: AIEngineService;
  private insights: AIInsight[] = [];
  private models: PredictiveModel[] = [];
  private alerts: SmartAlert[] = [];
  private config: AIServiceConfig;
  private cache: Map<string, { data: any; timestamp: number }> = new Map();

  constructor() {
    this.config = {
      google: {
        apiKey: process.env.GOOGLE_GENERATIVE_AI_API_KEY || '',
        model: 'gemini-2.0-flash-exp',
        temperature: 0.3,
        maxTokens: 8192
      },
      features: {
        predictiveMaintenance: true,
        costOptimization: true,
        anomalyDetection: true,
        nlpQueries: true,
        smartAlerts: true
      },
      limits: {
        maxQueriesPerHour: 100,
        maxTokensPerQuery: 8000,
        cacheTimeout: 300000 // 5 minutes
      }
    };
  }

  static getInstance(): AIEngineService {
    if (!AIEngineService.instance) {
      AIEngineService.instance = new AIEngineService();
    }
    return AIEngineService.instance;
  }

  private getCachedResult<T>(key: string): T | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.config.limits.cacheTimeout) {
      return cached.data as T;
    }
    this.cache.delete(key);
    return null;
  }

  private setCachedResult(key: string, data: any): void {
    this.cache.set(key, { data, timestamp: Date.now() });
  }

  /**
   * Analyze asset condition, maintenance needs, and performance using AI
   */
  async analyzeAsset(assetId: string, assetData: any): Promise<AssetAnalysis> {
    const cacheKey = `asset-analysis-${assetId}`;
    const cached = this.getCachedResult<AssetAnalysis>(cacheKey);
    if (cached) return cached;

    try {
      const { object } = await generateObject({
        model: google(this.config.google.model),
        schema: AssetAnalysisSchema,
        prompt: `
          Analyze the following asset data and provide comprehensive insights:
          
          Asset ID: ${assetId}
          Asset Data: ${JSON.stringify(assetData, null, 2)}
          
          Please analyze:
          1. Current condition and trend
          2. Maintenance requirements and priorities
          3. Utilization efficiency
          4. Risk assessment
          5. Actionable recommendations
          
          Base your analysis on the provided data and industry best practices for asset management.
        `,
        temperature: this.config.google.temperature,
      });

      this.setCachedResult(cacheKey, object);
      
      // Create insight from analysis
      await this.createInsight({
        type: "recommendation",
        title: `Asset Analysis Complete: ${assetId}`,
        description: `Comprehensive analysis completed with ${object.insights.length} insights and ${object.actionItems.length} action items`,
        confidence: object.analysis.condition.confidence,
        impact: object.actionItems.some(item => item.priority === "critical") ? "critical" : "medium",
        category: "asset_analysis",
        data: object,
        actionable: true,
        suggestedActions: object.actionItems.map(item => item.action),
        source: "ai_model"
      });

      return object;
    } catch (error) {
      console.error('Asset analysis failed:', error);
      throw new Error('Failed to analyze asset');
    }
  }

  /**
   * Generate predictive maintenance recommendations using AI
   */
  async generateMaintenancePredictions(assetId: string, historicalData?: any[]): Promise<MaintenancePrediction> {
    const cacheKey = `maintenance-prediction-${assetId}`;
    const cached = this.getCachedResult<MaintenancePrediction>(cacheKey);
    if (cached) return cached;

    try {
      const { object } = await generateObject({
        model: google(this.config.google.model),
        schema: MaintenancePredictionSchema,
        prompt: `
          Generate predictive maintenance analysis for asset ${assetId}.
          
          Historical Data: ${JSON.stringify(historicalData || [], null, 2)}
          
          Please provide:
          1. Component-specific failure predictions
          2. Maintenance type recommendations (preventive, corrective, predictive)
          3. Cost estimates and downtime projections
          4. Risk assessment and prioritization
          5. Actionable maintenance recommendations
          
          Consider industry standards, component lifecycles, and operational patterns.
        `,
        temperature: this.config.google.temperature,
      });

      this.setCachedResult(cacheKey, object);

      // Create predictive model entry
      const model: PredictiveModel = {
        id: `model-${Date.now()}`,
        name: `Maintenance Prediction - ${assetId}`,
        type: "maintenance",
        accuracy: 0.85,
        predictions: object.predictions.map(pred => ({
          id: `pred-${Date.now()}-${Math.random()}`,
          assetId,
          modelId: `model-${Date.now()}`,
          type: pred.maintenanceType,
          value: pred.failureProbability,
          confidence: pred.confidence,
          timeframe: pred.estimatedFailureDate,
          createdAt: new Date().toISOString(),
          metadata: { component: pred.component, estimatedCost: pred.estimatedCost }
        })),
        lastTrained: new Date().toISOString(),
        trainingData: {
          samples: historicalData?.length || 0,
          features: ['usage', 'age', 'maintenance_history', 'environmental_factors'],
          timeRange: {
            start: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString(),
            end: new Date().toISOString()
          }
        },
        performance: {
          precision: 0.82,
          recall: 0.78,
          f1Score: 0.80,
          mse: 0.15
        }
      };

      this.models.push(model);

      // Create insight
      await this.createInsight({
        type: "prediction",
        title: `Maintenance Predictions Generated`,
        description: `Generated ${object.predictions.length} maintenance predictions for asset ${assetId}`,
        confidence: Math.max(...object.predictions.map(p => p.confidence)),
        impact: object.totalRisk,
        category: "maintenance",
        data: object,
        actionable: true,
        suggestedActions: object.recommendations,
        source: "ai_model"
      });

      return object;
    } catch (error) {
      console.error('Maintenance prediction failed:', error);
      throw new Error('Failed to generate maintenance predictions');
    }
  }

  /**
   * Generate cost optimization recommendations using AI
   */
  async generateCostOptimization(assetData: any[], timeframe: string = "1 year"): Promise<CostOptimization> {
    const cacheKey = `cost-optimization-${timeframe}`;
    const cached = this.getCachedResult<CostOptimization>(cacheKey);
    if (cached) return cached;

    try {
      const { object } = await generateObject({
        model: google(this.config.google.model),
        schema: CostOptimizationSchema,
        prompt: `
          Analyze the following asset portfolio for cost optimization opportunities:
          
          Asset Data: ${JSON.stringify(assetData, null, 2)}
          Timeframe: ${timeframe}
          
          Please provide:
          1. Current vs optimized cost breakdown
          2. Specific optimization recommendations with impact analysis
          3. Implementation roadmap with effort estimates
          4. Risk assessment for proposed changes
          5. ROI calculations and payback periods
          
          Focus on maintenance efficiency, operational optimization, and lifecycle management.
        `,
        temperature: this.config.google.temperature,
      });

      this.setCachedResult(cacheKey, object);

      // Create insight
      await this.createInsight({
        type: "cost_analysis",
        title: "Cost Optimization Analysis Complete",
        description: `Identified potential savings of $${object.analysis.potentialSavings.toLocaleString()} with ${object.recommendations.length} optimization opportunities`,
        confidence: 0.85,
        impact: object.analysis.potentialSavings > 50000 ? "high" : "medium",
        category: "cost_optimization",
        data: object,
        actionable: true,
        suggestedActions: object.recommendations.map(rec => rec.action),
        source: "ai_model"
      });

      return object;
    } catch (error) {
      console.error('Cost optimization failed:', error);
      throw new Error('Failed to generate cost optimization');
    }
  }

  /**
   * Process natural language queries about assets using AI
   */
  async processNaturalLanguageQuery(query: string, context?: any): Promise<NLPQuery> {
    const startTime = Date.now();
    
    try {
      // Define tools for asset queries
      const assetTools = {
        searchAssets: tool({
          description: 'Search for assets based on criteria',
          parameters: z.object({
            criteria: z.string().describe('Search criteria'),
            category: z.string().optional().describe('Asset category filter'),
            status: z.string().optional().describe('Asset status filter')
          }),
          execute: async ({ criteria, category, status }) => {
            try {
              // Use real asset service to search assets
              const filter = {
                search: criteria,
                category,
                status: status as "active" | "maintenance" | "disposed" | undefined,
                includeRelations: false,
              };

              const pagination = {
                page: 1,
                limit: 10,
                sortBy: 'createdAt',
                sortOrder: 'desc' as const,
              };

              const result = await assetService.getAssetsWithFilter(filter, pagination);

              return result.assets.map(asset => ({
                id: asset.id,
                name: asset.name,
                category: asset.category,
                status: asset.status,
                location: asset.location,
                purchasePrice: asset.purchasePrice,
                department: asset.department,
              }));
            } catch (error) {
              console.error('Error searching assets in AI engine:', error);
              return [];
            }
          }
        }),
        getAssetDetails: tool({
          description: 'Get detailed information about a specific asset',
          parameters: z.object({
            assetId: z.string().describe('Asset ID to get details for')
          }),
          execute: async ({ assetId }) => {
            try {
              // Use real asset service to get asset details
              const asset = await assetService.getAssetById(assetId);

              if (!asset) {
                return {
                  error: 'Asset not found',
                  id: assetId
                };
              }

              return {
                id: asset.id,
                name: asset.name,
                category: asset.category,
                status: asset.status,
                purchaseDate: asset.purchaseDate.toISOString(),
                value: asset.purchasePrice,
                location: asset.location,
                department: asset.department,
                serialNumber: asset.serialNumber,
              };
            } catch (error) {
              console.error('Error getting asset details in AI engine:', error);
              return {
                error: 'Failed to fetch asset details',
                id: assetId
              };
            }
          }
        }),
        getMaintenanceSchedule: tool({
          description: 'Get maintenance schedule for assets',
          parameters: z.object({
            assetId: z.string().optional().describe('Specific asset ID, or all assets if not provided')
          }),
          execute: async ({ assetId }) => {
            // Mock maintenance schedule - replace with actual database query
            return [
              { assetId: assetId || '1', task: 'Software Update', dueDate: '2024-02-15', priority: 'Medium' },
              { assetId: assetId || '2', task: 'Oil Change', dueDate: '2024-02-10', priority: 'High' }
            ];
          }
        })
      };

      const result = await generateText({
        model: google(this.config.google.model),
        tools: assetTools,
        maxSteps: 3,
        prompt: `
          You are an AI assistant for an asset management system. Help the user with their query about assets, maintenance, or related topics.
          
          User Query: ${query}
          Context: ${JSON.stringify(context || {}, null, 2)}
          
          Provide helpful, accurate information and use the available tools when needed to fetch specific data.
        `,
        temperature: this.config.google.temperature,
      });

      const processingTime = Date.now() - startTime;

      // Extract entities from the query (simplified)
      const entities = this.extractEntities(query);

      const nlpResult: NLPQuery = {
        query,
        intent: this.classifyIntent(query),
        entities,
        confidence: 0.85,
        response: result.text,
        results: result.toolResults?.map(tr => tr.result) || [],
        processingTime,
        model: this.config.google.model,
        context
      };

      return nlpResult;
    } catch (error) {
      console.error('NLP query processing failed:', error);
      
      return {
        query,
        intent: "error",
        entities: [],
        confidence: 0,
        response: "I apologize, but I encountered an error processing your request. Please try again.",
        results: [],
        processingTime: Date.now() - startTime,
        model: this.config.google.model,
        context
      };
    }
  }

  /**
   * Detect anomalies in asset data using AI
   */
  async detectAnomalies(entityType: string, data: any[]): Promise<AIInsight[]> {
    const cacheKey = `anomalies-${entityType}`;
    const cached = this.getCachedResult<AIInsight[]>(cacheKey);
    if (cached) return cached;

    try {
      const { text } = await generateText({
        model: google(this.config.google.model),
        prompt: `
          Analyze the following ${entityType} data for anomalies and unusual patterns:
          
          Data: ${JSON.stringify(data, null, 2)}
          
          Identify:
          1. Statistical outliers
          2. Unusual trends or patterns
          3. Potential data quality issues
          4. Performance anomalies
          5. Cost or usage spikes
          
          For each anomaly found, provide:
          - Type and severity
          - Confidence level
          - Potential causes
          - Recommended actions
          
          Format your response as a structured analysis.
        `,
        temperature: this.config.google.temperature,
      });

      // Parse AI response and create insights
      const anomalies: AIInsight[] = [];
      
      // This is a simplified example - in production, you'd parse the AI response more sophisticatedly
      const anomalyInsight = await this.createInsight({
        type: "anomaly",
        title: `Anomaly Detection Complete - ${entityType}`,
        description: "AI-powered anomaly detection analysis completed",
        confidence: 0.75,
        impact: "medium",
        category: entityType,
        data: { analysis: text, dataPoints: data.length },
        actionable: true,
        suggestedActions: ["Review flagged items", "Investigate root causes", "Update monitoring thresholds"],
        source: "ai_model"
      });

      anomalies.push(anomalyInsight);
      this.setCachedResult(cacheKey, anomalies);

      return anomalies;
    } catch (error) {
      console.error('Anomaly detection failed:', error);
      throw new Error('Failed to detect anomalies');
    }
  }

  /**
   * Generate smart alerts based on AI analysis
   */
  async generateSmartAlert(
    entityId: string, 
    entityType: "asset" | "location" | "category" | "user",
    data: any
  ): Promise<SmartAlert> {
    try {
      const { text } = await generateText({
        model: google(this.config.google.model),
        prompt: `
          Analyze the following data and determine if a smart alert should be generated:
          
          Entity: ${entityType} (${entityId})
          Data: ${JSON.stringify(data, null, 2)}
          
          Consider:
          1. Severity and urgency
          2. Business impact
          3. Required actions
          4. Stakeholders to notify
          
          If an alert is warranted, specify the alert type, severity, and recommended actions.
        `,
        temperature: this.config.google.temperature,
      });

      const alert: SmartAlert = {
        id: `alert-${Date.now()}`,
        type: "predictive",
        severity: "warning",
        title: `Smart Alert: ${entityType} ${entityId}`,
        message: text,
        entityId,
        entityType,
        triggers: [{
          id: `trigger-${Date.now()}`,
          type: "pattern",
          condition: "AI analysis threshold exceeded",
          threshold: 0.8
        }],
        actions: [{
          id: `action-${Date.now()}`,
          type: "notification",
          config: { recipients: ["<EMAIL>"] },
          executed: false
        }],
        isActive: true,
        createdAt: new Date().toISOString(),
        metadata: { aiGenerated: true, model: this.config.google.model }
      };

      this.alerts.push(alert);
      return alert;
    } catch (error) {
      console.error('Smart alert generation failed:', error);
      throw new Error('Failed to generate smart alert');
    }
  }

  // Helper methods
  private extractEntities(query: string): any[] {
    // Simplified entity extraction - in production, use more sophisticated NLP
    const entities = [];
    const assetPattern = /asset\s+(\w+)/gi;
    const datePattern = /(\d{1,2}\/\d{1,2}\/\d{4}|\d{4}-\d{2}-\d{2})/g;
    
    let match;
    while ((match = assetPattern.exec(query)) !== null) {
      entities.push({
        type: 'asset',
        value: match[1],
        confidence: 0.8,
        start: match.index,
        end: match.index + match[0].length
      });
    }
    
    while ((match = datePattern.exec(query)) !== null) {
      entities.push({
        type: 'date',
        value: match[1],
        confidence: 0.9,
        start: match.index,
        end: match.index + match[0].length
      });
    }
    
    return entities;
  }

  private classifyIntent(query: string): string {
    const intents = {
      search: ['find', 'search', 'show', 'list', 'get'],
      count: ['how many', 'count', 'number of'],
      status: ['status', 'condition', 'state'],
      maintenance: ['maintenance', 'repair', 'service'],
      cost: ['cost', 'price', 'expense', 'budget']
    };

    const lowerQuery = query.toLowerCase();
    
    for (const [intent, keywords] of Object.entries(intents)) {
      if (keywords.some(keyword => lowerQuery.includes(keyword))) {
        return intent;
      }
    }
    
    return 'general';
  }

  private async createInsight(insight: Omit<AIInsight, "id" | "createdAt">): Promise<AIInsight> {
    const newInsight: AIInsight = {
      ...insight,
      id: `insight-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date().toISOString(),
    };

    this.insights.push(newInsight);
    return newInsight;
  }

  // Public getter methods
  getInsights(category?: string): AIInsight[] {
    return category 
      ? this.insights.filter((insight) => insight.category === category) 
      : this.insights;
  }

  getModels(): PredictiveModel[] {
    return this.models;
  }

  getAlerts(isActive?: boolean): SmartAlert[] {
    return isActive !== undefined 
      ? this.alerts.filter((alert) => alert.isActive === isActive) 
      : this.alerts;
  }

  // Configuration methods
  updateConfig(newConfig: Partial<AIServiceConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  getConfig(): AIServiceConfig {
    return { ...this.config };
  }

  // Cache management
  clearCache(): void {
    this.cache.clear();
  }

  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }
}
