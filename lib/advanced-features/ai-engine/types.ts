import { z } from 'zod';

// Core AI Insight Types
export interface AIInsight {
  id: string
  type: "prediction" | "recommendation" | "anomaly" | "optimization" | "maintenance" | "cost_analysis"
  title: string
  description: string
  confidence: number
  impact: "low" | "medium" | "high" | "critical"
  category: string
  data?: any
  actionable: boolean
  suggestedActions: string[]
  createdAt: string
  expiresAt?: string
  metadata?: Record<string, any>
  source: "ai_model" | "rule_based" | "hybrid"
}

// Enhanced Predictive Model
export interface PredictiveModel {
  id: string
  name: string
  type: "maintenance" | "depreciation" | "utilization" | "cost" | "lifecycle"
  accuracy: number
  predictions: Prediction[]
  lastTrained: string
  trainingData: {
    samples: number
    features: string[]
    timeRange: { start: string; end: string }
  }
  performance: {
    precision: number
    recall: number
    f1Score: number
    mse?: number
  }
}

export interface Prediction {
  id: string
  assetId: string
  modelId: string
  type: string
  value: number | string
  confidence: number
  timeframe: string
  createdAt: string
  metadata?: Record<string, any>
}

// Smart Alert System
export interface SmartAlert {
  id: string
  type: "threshold" | "predictive" | "anomaly" | "pattern"
  severity: "info" | "warning" | "error" | "critical"
  title: string
  message: string
  entityId: string
  entityType: "asset" | "location" | "category" | "user"
  triggers: AlertTrigger[]
  actions: AlertAction[]
  isActive: boolean
  createdAt: string
  acknowledgedAt?: string
  acknowledgedBy?: string
  resolvedAt?: string
  metadata?: Record<string, any>
}

export interface AlertTrigger {
  id: string
  type: "value" | "trend" | "pattern" | "time"
  condition: string
  threshold?: number
  timeWindow?: string
}

export interface AlertAction {
  id: string
  type: "email" | "webhook" | "task" | "notification"
  config: Record<string, any>
  executed: boolean
  executedAt?: string
}

// Natural Language Processing
export interface NLPQuery {
  query: string
  intent: string
  entities: NLPEntity[]
  confidence: number
  response: string
  results: any[]
  processingTime: number
  model: string
  context?: Record<string, any>
}

export interface NLPEntity {
  type: string
  value: string
  confidence: number
  start: number
  end: number
}

// AI Chat Interface
export interface AIChatMessage {
  id: string
  role: "user" | "assistant" | "system"
  content: string
  timestamp: string
  metadata?: Record<string, any>
  tools?: ToolInvocation[]
}

export interface ToolInvocation {
  id: string
  name: string
  parameters: Record<string, any>
  result?: any
  status: "pending" | "completed" | "failed"
  executedAt?: string
}

// Asset Analysis Schemas
export const AssetAnalysisSchema = z.object({
  assetId: z.string(),
  analysis: z.object({
    condition: z.object({
      current: z.enum(["excellent", "good", "fair", "poor"]),
      trend: z.enum(["improving", "stable", "declining"]),
      confidence: z.number().min(0).max(1)
    }),
    maintenance: z.object({
      nextDue: z.string().optional(),
      priority: z.enum(["low", "medium", "high", "critical"]),
      estimatedCost: z.number().optional(),
      recommendations: z.array(z.string())
    }),
    utilization: z.object({
      current: z.number().min(0).max(1),
      optimal: z.number().min(0).max(1),
      efficiency: z.number().min(0).max(1)
    }),
    risks: z.array(z.object({
      type: z.string(),
      probability: z.number().min(0).max(1),
      impact: z.enum(["low", "medium", "high", "critical"]),
      mitigation: z.string()
    }))
  }),
  insights: z.array(z.string()),
  actionItems: z.array(z.object({
    action: z.string(),
    priority: z.enum(["low", "medium", "high", "critical"]),
    estimatedCost: z.number().optional(),
    timeframe: z.string()
  }))
});

export const MaintenancePredictionSchema = z.object({
  assetId: z.string(),
  predictions: z.array(z.object({
    component: z.string(),
    failureProbability: z.number().min(0).max(1),
    estimatedFailureDate: z.string(),
    confidence: z.number().min(0).max(1),
    maintenanceType: z.enum(["preventive", "corrective", "predictive"]),
    estimatedCost: z.number(),
    downtime: z.object({
      estimated: z.number(),
      unit: z.enum(["hours", "days", "weeks"])
    })
  })),
  recommendations: z.array(z.string()),
  totalRisk: z.enum(["low", "medium", "high", "critical"])
});

export const CostOptimizationSchema = z.object({
  analysis: z.object({
    currentCosts: z.object({
      maintenance: z.number(),
      operation: z.number(),
      depreciation: z.number(),
      total: z.number()
    }),
    optimizedCosts: z.object({
      maintenance: z.number(),
      operation: z.number(),
      depreciation: z.number(),
      total: z.number()
    }),
    potentialSavings: z.number(),
    paybackPeriod: z.string()
  }),
  recommendations: z.array(z.object({
    category: z.string(),
    action: z.string(),
    impact: z.number(),
    implementation: z.object({
      effort: z.enum(["low", "medium", "high"]),
      timeframe: z.string(),
      resources: z.array(z.string())
    })
  })),
  riskAssessment: z.object({
    level: z.enum(["low", "medium", "high"]),
    factors: z.array(z.string())
  })
});

// AI Service Configuration
export interface AIServiceConfig {
  google: {
    apiKey: string
    model: string
    temperature: number
    maxTokens: number
  }
  features: {
    predictiveMaintenance: boolean
    costOptimization: boolean
    anomalyDetection: boolean
    nlpQueries: boolean
    smartAlerts: boolean
  }
  limits: {
    maxQueriesPerHour: number
    maxTokensPerQuery: number
    cacheTimeout: number
  }
}

// Export types for AI SDK integration
export type AssetAnalysis = z.infer<typeof AssetAnalysisSchema>;
export type MaintenancePrediction = z.infer<typeof MaintenancePredictionSchema>;
export type CostOptimization = z.infer<typeof CostOptimizationSchema>;
