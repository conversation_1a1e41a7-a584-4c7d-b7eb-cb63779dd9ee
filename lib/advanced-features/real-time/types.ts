export interface RealtimeEvent {
  id: string
  type: string
  channel: string
  data: any
  userId?: string
  timestamp: string
}

export interface Notification {
  id: string
  userId: string
  title: string
  message: string
  type: "info" | "warning" | "error" | "success"
  category: string
  read: boolean
  actionUrl?: string
  actionText?: string
  createdAt: string
}

export interface CollaborationSession {
  id: string
  entityId: string
  entityType: string
  participants: Array<{
    userId: string
    userName: string
    role: string
    joinedAt: string
    isActive: boolean
    leftAt?: string
  }>
  startedAt: string
  endedAt?: string
  activities: any[]
}

export interface Comment {
  id: string
  entityId: string
  entityType: string
  userId: string
  userName: string
  content: string
  mentions: string[]
  attachments: any[]
  replies: Comment[]
  parentId?: string
  createdAt: string
}
