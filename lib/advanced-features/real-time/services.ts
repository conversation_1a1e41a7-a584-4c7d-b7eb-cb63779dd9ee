import type { RealtimeEvent, Notification, CollaborationSession, Comment } from "./types"

export class RealtimeService {
  private static instance: RealtimeService
  private events: RealtimeEvent[] = []
  private notifications: Notification[] = []
  private sessions: CollaborationSession[] = []
  private comments: Comment[] = []
  private subscribers: Map<string, Function[]> = new Map()

  static getInstance(): RealtimeService {
    if (!RealtimeService.instance) {
      RealtimeService.instance = new RealtimeService()
    }
    return RealtimeService.instance
  }

  subscribe(channel: string, callback: Function): () => void {
    if (!this.subscribers.has(channel)) {
      this.subscribers.set(channel, [])
    }
    this.subscribers.get(channel)!.push(callback)

    return () => {
      const callbacks = this.subscribers.get(channel)
      if (callbacks) {
        const index = callbacks.indexOf(callback)
        if (index > -1) {
          callbacks.splice(index, 1)
        }
      }
    }
  }

  emit(event: Omit<RealtimeEvent, "id" | "timestamp">): void {
    const newEvent: RealtimeEvent = {
      ...event,
      id: `event-${Date.now()}`,
      timestamp: new Date().toISOString(),
    }

    this.events.push(newEvent)

    const callbacks = this.subscribers.get(event.channel)
    if (callbacks) {
      callbacks.forEach((callback) => callback(newEvent))
    }
  }

  async createNotification(notification: Omit<Notification, "id" | "createdAt" | "read">): Promise<Notification> {
    const newNotification: Notification = {
      ...notification,
      id: `notif-${Date.now()}`,
      read: false,
      createdAt: new Date().toISOString(),
    }

    this.notifications.push(newNotification)

    this.emit({
      type: "notification.created",
      channel: `user.${notification.userId}`,
      data: newNotification,
    })

    return newNotification
  }

  async startCollaborationSession(
    entityId: string,
    entityType: string,
    userId: string,
    userName: string,
  ): Promise<CollaborationSession> {
    const session: CollaborationSession = {
      id: `session-${Date.now()}`,
      entityId,
      entityType,
      participants: [
        {
          userId,
          userName,
          role: "owner",
          joinedAt: new Date().toISOString(),
          isActive: true,
        },
      ],
      startedAt: new Date().toISOString(),
      activities: [],
    }

    this.sessions.push(session)

    this.emit({
      type: "collaboration.session_started",
      channel: `entity.${entityType}.${entityId}`,
      data: session,
      userId,
    })

    return session
  }

  async addComment(comment: Omit<Comment, "id" | "createdAt" | "replies">): Promise<Comment> {
    const newComment: Comment = {
      ...comment,
      id: `comment-${Date.now()}`,
      replies: [],
      createdAt: new Date().toISOString(),
    }

    this.comments.push(newComment)

    this.emit({
      type: "comment.added",
      channel: `entity.${comment.entityType}.${comment.entityId}`,
      data: newComment,
      userId: comment.userId,
    })

    return newComment
  }

  getNotifications(userId: string, unreadOnly = false): Notification[] {
    let notifications = this.notifications.filter((n) => n.userId === userId)

    if (unreadOnly) {
      notifications = notifications.filter((n) => !n.read)
    }

    return notifications.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
  }

  getCollaborationSession(entityId: string, entityType: string): CollaborationSession | undefined {
    return this.sessions.find((s) => s.entityId === entityId && s.entityType === entityType && !s.endedAt)
  }

  getComments(entityId: string, entityType: string): Comment[] {
    return this.comments
      .filter((c) => c.entityId === entityId && c.entityType === entityType && !c.parentId)
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
  }
}
