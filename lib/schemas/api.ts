import { z } from "zod";

// Common API validation schemas

// Pagination schema
export const PaginationSchema = z.object({
  page: z.coerce.number().int().positive().default(1),
  limit: z.coerce.number().int().positive().max(100).default(10),
  sortBy: z.string().optional(),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

// Search and filter schemas
export const AssetFilterSchema = z.object({
  category: z.string().optional(),
  status: z.enum(["active", "maintenance", "disposed"]).optional(),
  department: z.string().optional(),
  location: z.string().optional(),
  assetTypeId: z.string().cuid().optional(),
  search: z.string().optional(),
  includeRelations: z.coerce.boolean().default(false),
  dateFrom: z.coerce.date().optional(),
  dateTo: z.coerce.date().optional(),
  priceMin: z.coerce.number().positive().optional(),
  priceMax: z.coerce.number().positive().optional(),
});

export const AssetRequestFilterSchema = z.object({
  status: z.enum(["pending", "approved", "rejected", "processing", "shipped", "delivered", "cancelled"]).optional(),
  priority: z.enum(["low", "normal", "high", "critical"]).optional(),
  userId: z.string().cuid().optional(),
  assetTypeId: z.string().cuid().optional(),
  department: z.string().optional(),
  dateFrom: z.coerce.date().optional(),
  dateTo: z.coerce.date().optional(),
  search: z.string().optional(),
});

export const SupportTicketFilterSchema = z.object({
  status: z.enum(["open", "in_progress", "waiting_response", "resolved", "closed"]).optional(),
  priority: z.enum(["low", "normal", "high", "critical"]).optional(),
  category: z.string().optional(),
  assignedTo: z.string().optional(),
  userId: z.string().cuid().optional(),
  dateFrom: z.coerce.date().optional(),
  dateTo: z.coerce.date().optional(),
  search: z.string().optional(),
});

export const MaintenanceTaskFilterSchema = z.object({
  status: z.enum(["scheduled", "in_progress", "completed", "cancelled", "overdue"]).optional(),
  priority: z.enum(["low", "medium", "high", "critical"]).optional(),
  type: z.string().optional(),
  assetId: z.string().cuid().optional(),
  assignedTo: z.string().optional(),
  dateFrom: z.coerce.date().optional(),
  dateTo: z.coerce.date().optional(),
  upcoming: z.coerce.boolean().optional(),
  overdue: z.coerce.boolean().optional(),
});

export const RequisitionFilterSchema = z.object({
  status: z.enum(["pending", "approved", "rejected", "fulfilled", "cancelled", "partially_fulfilled"]).optional(),
  priority: z.enum(["low", "normal", "high", "critical"]).optional(),
  requestorId: z.string().cuid().optional(),
  assetTypeId: z.string().cuid().optional(),
  department: z.string().optional(),
  dateFrom: z.coerce.date().optional(),
  dateTo: z.coerce.date().optional(),
  search: z.string().optional(),
});

// API Response schemas
export const ApiResponseSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  error: z.string().optional(),
  message: z.string().optional(),
});

export const PaginatedResponseSchema = z.object({
  success: z.boolean(),
  data: z.array(z.any()),
  pagination: z.object({
    page: z.number().int().positive(),
    limit: z.number().int().positive(),
    total: z.number().int().nonnegative(),
    totalPages: z.number().int().nonnegative(),
    hasNext: z.boolean(),
    hasPrev: z.boolean(),
  }),
  error: z.string().optional(),
  message: z.string().optional(),
});

// Statistics and metrics schemas
export const AssetStatisticsSchema = z.object({
  totalAssets: z.number().int().nonnegative(),
  activeAssets: z.number().int().nonnegative(),
  maintenanceAssets: z.number().int().nonnegative(),
  disposedAssets: z.number().int().nonnegative(),
  recentOperations: z.number().int().nonnegative(),
  assetsByCategory: z.array(z.object({
    category: z.string(),
    count: z.number().int().nonnegative(),
    percentage: z.number().min(0).max(100),
  })),
  assetsByStatus: z.array(z.object({
    status: z.string(),
    count: z.number().int().nonnegative(),
    percentage: z.number().min(0).max(100),
  })),
  assetsByLocation: z.array(z.object({
    location: z.string(),
    count: z.number().int().nonnegative(),
    percentage: z.number().min(0).max(100),
  })),
  totalValue: z.number().nonnegative(),
  averageAge: z.number().nonnegative(),
});

export const MaintenanceStatisticsSchema = z.object({
  totalTasks: z.number().int().nonnegative(),
  scheduledTasks: z.number().int().nonnegative(),
  inProgressTasks: z.number().int().nonnegative(),
  completedTasks: z.number().int().nonnegative(),
  overdueTasks: z.number().int().nonnegative(),
  upcomingTasks: z.number().int().nonnegative(),
  averageCompletionTime: z.number().nonnegative(),
  totalCost: z.number().nonnegative(),
  averageCost: z.number().nonnegative(),
  tasksByPriority: z.array(z.object({
    priority: z.string(),
    count: z.number().int().nonnegative(),
  })),
  tasksByType: z.array(z.object({
    type: z.string(),
    count: z.number().int().nonnegative(),
  })),
});

export const RequisitionStatisticsSchema = z.object({
  totalRequisitions: z.number().int().nonnegative(),
  pendingRequisitions: z.number().int().nonnegative(),
  approvedRequisitions: z.number().int().nonnegative(),
  rejectedRequisitions: z.number().int().nonnegative(),
  fulfilledRequisitions: z.number().int().nonnegative(),
  averageProcessingTime: z.number().nonnegative(),
  totalValue: z.number().nonnegative(),
  averageValue: z.number().nonnegative(),
  requisitionsByPriority: z.array(z.object({
    priority: z.string(),
    count: z.number().int().nonnegative(),
  })),
  requisitionsByDepartment: z.array(z.object({
    department: z.string(),
    count: z.number().int().nonnegative(),
  })),
});

// Bulk operation schemas
export const BulkAssetUpdateSchema = z.object({
  assetIds: z.array(z.string().cuid()).min(1, "At least one asset ID is required"),
  updates: z.object({
    status: z.enum(["active", "maintenance", "disposed"]).optional(),
    location: z.string().optional(),
    department: z.string().optional(),
  }).refine(data => Object.keys(data).length > 0, "At least one update field is required"),
});

export const BulkMaintenanceTaskUpdateSchema = z.object({
  taskIds: z.array(z.string().cuid()).min(1, "At least one task ID is required"),
  updates: z.object({
    status: z.enum(["scheduled", "in_progress", "completed", "cancelled", "overdue"]).optional(),
    assignedTo: z.string().optional(),
    priority: z.enum(["low", "medium", "high", "critical"]).optional(),
  }).refine(data => Object.keys(data).length > 0, "At least one update field is required"),
});

// File upload schemas
export const FileUploadSchema = z.object({
  file: z.any(),
  entityType: z.enum(["asset", "user", "ticket", "invoice"]),
  entityId: z.string().cuid(),
  description: z.string().optional(),
});

export const ImageUploadSchema = z.object({
  images: z.array(z.any()).min(1, "At least one image is required"),
  assetId: z.string().cuid(),
  description: z.string().optional(),
});

// Audit and logging schemas
export const AuditLogSchema = z.object({
  id: z.string().cuid(),
  entityType: z.string(),
  entityId: z.string().cuid(),
  action: z.enum(["create", "update", "delete", "view"]),
  userId: z.string().cuid(),
  userName: z.string(),
  changes: z.record(z.any()).optional(),
  metadata: z.record(z.any()).optional(),
  ipAddress: z.string().optional(),
  userAgent: z.string().optional(),
  timestamp: z.date().default(() => new Date()),
});

// Export TypeScript types
export type Pagination = z.infer<typeof PaginationSchema>;
export type AssetFilter = z.infer<typeof AssetFilterSchema>;
export type AssetRequestFilter = z.infer<typeof AssetRequestFilterSchema>;
export type SupportTicketFilter = z.infer<typeof SupportTicketFilterSchema>;
export type MaintenanceTaskFilter = z.infer<typeof MaintenanceTaskFilterSchema>;
export type RequisitionFilter = z.infer<typeof RequisitionFilterSchema>;

export type ApiResponse = z.infer<typeof ApiResponseSchema>;
export type PaginatedResponse = z.infer<typeof PaginatedResponseSchema>;

export type AssetStatistics = z.infer<typeof AssetStatisticsSchema>;
export type MaintenanceStatistics = z.infer<typeof MaintenanceStatisticsSchema>;
export type RequisitionStatistics = z.infer<typeof RequisitionStatisticsSchema>;

export type BulkAssetUpdate = z.infer<typeof BulkAssetUpdateSchema>;
export type BulkMaintenanceTaskUpdate = z.infer<typeof BulkMaintenanceTaskUpdateSchema>;

export type FileUpload = z.infer<typeof FileUploadSchema>;
export type ImageUpload = z.infer<typeof ImageUploadSchema>;

export type AuditLog = z.infer<typeof AuditLogSchema>;

// Validation helper functions
export function validatePagination(query: any): Pagination {
  return PaginationSchema.parse(query);
}

export function validateAssetFilter(query: any): AssetFilter {
  return AssetFilterSchema.parse(query);
}

export function validateAssetRequestFilter(query: any): AssetRequestFilter {
  return AssetRequestFilterSchema.parse(query);
}

export function validateSupportTicketFilter(query: any): SupportTicketFilter {
  return SupportTicketFilterSchema.parse(query);
}

export function validateMaintenanceTaskFilter(query: any): MaintenanceTaskFilter {
  return MaintenanceTaskFilterSchema.parse(query);
}

export function validateRequisitionFilter(query: any): RequisitionFilter {
  return RequisitionFilterSchema.parse(query);
}
