import type { Contact, Deal, Activity, Campaign, CRMMetrics, DealStage } from "./types"

export class CRMService {
  private static instance: CRMService
  private contacts: Contact[] = []
  private deals: Deal[] = []
  private activities: Activity[] = []
  private campaigns: Campaign[] = []
  private dealStages: DealStage[] = [
    { id: "1", name: "Qualification", order: 1, probability: 10, color: "#3B82F6" },
    { id: "2", name: "Needs Analysis", order: 2, probability: 25, color: "#8B5CF6" },
    { id: "3", name: "Proposal", order: 3, probability: 50, color: "#F59E0B" },
    { id: "4", name: "Negotiation", order: 4, probability: 75, color: "#EF4444" },
    { id: "5", name: "Closed Won", order: 5, probability: 100, color: "#10B981" },
  ]

  static getInstance(): CRMService {
    if (!CRMService.instance) {
      CRMService.instance = new CRMService()
    }
    return CRMService.instance
  }

  // Contact Management
  async createContact(contact: Omit<Contact, "id" | "createdAt" | "updatedAt">): Promise<Contact> {
    const newContact: Contact = {
      ...contact,
      id: `CNT-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }

    this.contacts.push(newContact)
    return newContact
  }

  async updateContact(id: string, updates: Partial<Contact>): Promise<Contact | null> {
    const index = this.contacts.findIndex((contact) => contact.id === id)
    if (index === -1) return null

    this.contacts[index] = {
      ...this.contacts[index],
      ...updates,
      updatedAt: new Date().toISOString(),
    }
    return this.contacts[index]
  }

  async deleteContact(id: string): Promise<boolean> {
    const index = this.contacts.findIndex((contact) => contact.id === id)
    if (index === -1) return false

    this.contacts.splice(index, 1)
    return true
  }

  async mergeContacts(primaryId: string, duplicateId: string): Promise<Contact | null> {
    const primary = this.contacts.find((c) => c.id === primaryId)
    const duplicate = this.contacts.find((c) => c.id === duplicateId)

    if (!primary || !duplicate) return null

    // Merge data from duplicate into primary
    primary.tags = [...new Set([...primary.tags, ...duplicate.tags])]
    primary.notes = [...primary.notes, ...duplicate.notes]
    primary.socialProfiles = [...primary.socialProfiles, ...duplicate.socialProfiles]

    // Update deals and activities to reference primary contact
    this.deals
      .filter((d) => d.contactId === duplicateId)
      .forEach((deal) => {
        deal.contactId = primaryId
        deal.contactName =
          primary.type === "Company" ? primary.companyName! : `${primary.firstName} ${primary.lastName}`
      })

    this.activities
      .filter((a) => a.contactId === duplicateId)
      .forEach((activity) => {
        activity.contactId = primaryId
      })

    // Remove duplicate
    await this.deleteContact(duplicateId)

    return primary
  }

  // Deal Management
  async createDeal(deal: Omit<Deal, "id" | "createdAt" | "updatedAt">): Promise<Deal> {
    const newDeal: Deal = {
      ...deal,
      id: `DEL-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }

    this.deals.push(newDeal)
    return newDeal
  }

  async updateDeal(id: string, updates: Partial<Deal>): Promise<Deal | null> {
    const index = this.deals.findIndex((deal) => deal.id === id)
    if (index === -1) return null

    this.deals[index] = {
      ...this.deals[index],
      ...updates,
      updatedAt: new Date().toISOString(),
    }
    return this.deals[index]
  }

  async moveDealToStage(dealId: string, stageId: string): Promise<boolean> {
    const deal = this.deals.find((d) => d.id === dealId)
    const stage = this.dealStages.find((s) => s.id === stageId)

    if (!deal || !stage) return false

    deal.stage = stage
    deal.probability = stage.probability
    deal.updatedAt = new Date().toISOString()

    if (stage.name === "Closed Won") {
      deal.status = "Won"
      deal.actualCloseDate = new Date().toISOString()
    }

    return true
  }

  async closeDeal(dealId: string, won: boolean, reason?: string): Promise<boolean> {
    const deal = this.deals.find((d) => d.id === dealId)
    if (!deal) return false

    deal.status = won ? "Won" : "Lost"
    deal.actualCloseDate = new Date().toISOString()
    deal.updatedAt = new Date().toISOString()

    if (!won && reason) {
      deal.lostReason = reason
    }

    return true
  }

  // Activity Management
  async createActivity(activity: Omit<Activity, "id" | "createdAt" | "updatedAt">): Promise<Activity> {
    const newActivity: Activity = {
      ...activity,
      id: `ACT-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }

    this.activities.push(newActivity)
    return newActivity
  }

  async completeActivity(id: string, outcome?: string, nextAction?: string): Promise<boolean> {
    const activity = this.activities.find((a) => a.id === id)
    if (!activity) return false

    activity.status = "Completed"
    activity.completedDate = new Date().toISOString()
    activity.outcome = outcome
    activity.nextAction = nextAction
    activity.updatedAt = new Date().toISOString()

    return true
  }

  async scheduleFollowUp(
    contactId: string,
    dealId: string,
    type: Activity["type"],
    subject: string,
    dueDate: string,
  ): Promise<Activity> {
    return this.createActivity({
      type,
      subject,
      description: `Follow-up activity for ${type.toLowerCase()}`,
      contactId,
      dealId,
      assignedTo: "current-user", // Would be dynamic
      dueDate,
      status: "Pending",
      priority: "Medium",
      attendees: [],
    })
  }

  // Campaign Management
  async createCampaign(campaign: Omit<Campaign, "id" | "createdAt" | "updatedAt">): Promise<Campaign> {
    const newCampaign: Campaign = {
      ...campaign,
      id: `CMP-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }

    this.campaigns.push(newCampaign)
    return newCampaign
  }

  async updateCampaignMetrics(campaignId: string, metrics: Partial<Campaign["metrics"]>): Promise<boolean> {
    const campaign = this.campaigns.find((c) => c.id === campaignId)
    if (!campaign) return false

    campaign.metrics = { ...campaign.metrics, ...metrics }
    campaign.updatedAt = new Date().toISOString()

    return true
  }

  // Analytics and Reporting
  async getCRMMetrics(): Promise<CRMMetrics> {
    const totalContacts = this.contacts.length
    const totalDeals = this.deals.length
    const wonDeals = this.deals.filter((d) => d.status === "Won")
    const totalDealValue = wonDeals.reduce((sum, deal) => sum + deal.value, 0)
    const averageDealSize = wonDeals.length > 0 ? totalDealValue / wonDeals.length : 0

    const closedDeals = this.deals.filter((d) => d.status === "Won" || d.status === "Lost")
    const winRate = closedDeals.length > 0 ? (wonDeals.length / closedDeals.length) * 100 : 0

    const customers = this.contacts.filter((c) => c.status === "Customer")
    const prospects = this.contacts.filter((c) => c.status === "Prospect")
    const conversionRate = prospects.length > 0 ? (customers.length / prospects.length) * 100 : 0

    return {
      totalContacts,
      totalDeals,
      totalDealValue,
      averageDealSize,
      winRate,
      salesCycleLength: this.calculateAverageSalesCycle(),
      conversionRate,
      customerLifetimeValue: this.calculateCustomerLifetimeValue(),
    }
  }

  private calculateAverageSalesCycle(): number {
    const wonDeals = this.deals.filter((d) => d.status === "Won" && d.actualCloseDate)
    if (wonDeals.length === 0) return 0

    const totalDays = wonDeals.reduce((sum, deal) => {
      const created = new Date(deal.createdAt)
      const closed = new Date(deal.actualCloseDate!)
      const days = Math.floor((closed.getTime() - created.getTime()) / (1000 * 60 * 60 * 24))
      return sum + days
    }, 0)

    return totalDays / wonDeals.length
  }

  private calculateCustomerLifetimeValue(): number {
    const customers = this.contacts.filter((c) => c.status === "Customer")
    if (customers.length === 0) return 0

    const customerDeals = this.deals.filter((d) => d.status === "Won" && customers.some((c) => c.id === d.contactId))

    const totalRevenue = customerDeals.reduce((sum, deal) => sum + deal.value, 0)
    return totalRevenue / customers.length
  }

  // Search and Filter
  searchContacts(query: string): Contact[] {
    const lowercaseQuery = query.toLowerCase()
    return this.contacts.filter(
      (contact) =>
        contact.firstName?.toLowerCase().includes(lowercaseQuery) ||
        contact.lastName?.toLowerCase().includes(lowercaseQuery) ||
        contact.companyName?.toLowerCase().includes(lowercaseQuery) ||
        contact.email.toLowerCase().includes(lowercaseQuery) ||
        contact.tags.some((tag) => tag.toLowerCase().includes(lowercaseQuery)),
    )
  }

  getContactsByStatus(status: Contact["status"]): Contact[] {
    return this.contacts.filter((c) => c.status === status)
  }

  getDealsByStage(stageId: string): Deal[] {
    return this.deals.filter((d) => d.stage.id === stageId)
  }

  getUpcomingActivities(days = 7): Activity[] {
    const futureDate = new Date()
    futureDate.setDate(futureDate.getDate() + days)

    return this.activities.filter(
      (activity) => activity.status === "Pending" && activity.dueDate && new Date(activity.dueDate) <= futureDate,
    )
  }

  // Getters
  getContacts(): Contact[] {
    return this.contacts
  }
  getDeals(): Deal[] {
    return this.deals
  }
  getActivities(): Activity[] {
    return this.activities
  }
  getCampaigns(): Campaign[] {
    return this.campaigns
  }
  getDealStages(): DealStage[] {
    return this.dealStages
  }
}
