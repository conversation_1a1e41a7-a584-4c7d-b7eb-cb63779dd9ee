export interface Contact {
  id: string
  type: "Individual" | "Company"
  firstName?: string
  lastName?: string
  companyName?: string
  title?: string
  email: string
  phone: string
  mobile?: string
  website?: string
  industry?: string
  address: Address
  socialProfiles: SocialProfile[]
  tags: string[]
  source: string
  assignedTo: string
  status: "Active" | "Inactive" | "Prospect" | "Customer" | "Partner"
  lastContactDate?: string
  nextFollowUp?: string
  notes: Note[]
  customFields: Record<string, any>
  createdAt: string
  updatedAt: string
}

export interface Address {
  street: string
  city: string
  state: string
  zipCode: string
  country: string
}

export interface SocialProfile {
  platform: "LinkedIn" | "Twitter" | "Facebook" | "Instagram"
  url: string
}

export interface Deal {
  id: string
  title: string
  contactId: string
  contactName: string
  value: number
  currency: string
  stage: DealStage
  probability: number
  expectedCloseDate: string
  actualCloseDate?: string
  source: string
  assignedTo: string
  description: string
  products: DealProduct[]
  activities: Activity[]
  documents: string[]
  competitors: string[]
  lostReason?: string
  status: "Open" | "Won" | "Lost" | "Cancelled"
  createdAt: string
  updatedAt: string
}

export interface DealStage {
  id: string
  name: string
  order: number
  probability: number
  color: string
}

export interface DealProduct {
  productId: string
  productName: string
  quantity: number
  unitPrice: number
  discount: number
  totalPrice: number
}

export interface Activity {
  id: string
  type: "Call" | "Email" | "Meeting" | "Task" | "Note" | "Demo"
  subject: string
  description: string
  contactId?: string
  dealId?: string
  assignedTo: string
  dueDate?: string
  completedDate?: string
  status: "Pending" | "Completed" | "Cancelled"
  priority: "Low" | "Medium" | "High"
  duration?: number
  location?: string
  attendees: string[]
  outcome?: string
  nextAction?: string
  createdAt: string
  updatedAt: string
}

export interface Note {
  id: string
  content: string
  author: string
  createdAt: string
  isPrivate: boolean
}

export interface Campaign {
  id: string
  name: string
  type: "Email" | "SMS" | "Social" | "Direct Mail" | "Event"
  status: "Draft" | "Active" | "Paused" | "Completed" | "Cancelled"
  startDate: string
  endDate: string
  budget: number
  targetAudience: string[]
  content: string
  metrics: CampaignMetrics
  createdAt: string
  updatedAt: string
}

export interface CampaignMetrics {
  sent: number
  delivered: number
  opened: number
  clicked: number
  converted: number
  bounced: number
  unsubscribed: number
  revenue: number
}

export interface CRMMetrics {
  totalContacts: number
  totalDeals: number
  totalDealValue: number
  averageDealSize: number
  winRate: number
  salesCycleLength: number
  conversionRate: number
  customerLifetimeValue: number
}
