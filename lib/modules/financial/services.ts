import type {
  FinancialAsset,
  TCOAnalysis,
  ROIAnalysis,
  LeaseVsBuyAnalysis,
  BudgetItem,
  TaxOptimization,
  FinancialMetrics,
  CostCenter,
  FinancialForecast,
} from "./types"

class FinancialService {
  private static instance: FinancialService
  private assets: FinancialAsset[] = []
  private tcoAnalyses: TCOAnalysis[] = []
  private roiAnalyses: ROIAnalysis[] = []
  private budgetItems: BudgetItem[] = []
  private costCenters: CostCenter[] = []

  private constructor() {
    this.initializeSampleData()
  }

  static getInstance(): FinancialService {
    if (!FinancialService.instance) {
      FinancialService.instance = new FinancialService()
    }
    return FinancialService.instance
  }

  private initializeSampleData(): void {
    // Sample financial assets
    this.assets = [
      {
        id: "1",
        name: "Manufacturing Equipment A",
        assetType: "Equipment",
        acquisitionCost: 250000,
        currentValue: 180000,
        depreciationMethod: "straight-line",
        usefulLife: 10,
        salvageValue: 25000,
        acquisitionDate: new Date("2022-01-15"),
        category: "Manufacturing",
        location: "Plant 1",
        status: "active",
      },
      {
        id: "2",
        name: "Office Building",
        assetType: "Real Estate",
        acquisitionCost: 2500000,
        currentValue: 2800000,
        depreciationMethod: "straight-line",
        usefulLife: 39,
        salvageValue: 500000,
        acquisitionDate: new Date("2020-06-01"),
        category: "Real Estate",
        location: "Headquarters",
        status: "active",
      },
      {
        id: "3",
        name: "Fleet Vehicle 001",
        assetType: "Vehicle",
        acquisitionCost: 45000,
        currentValue: 32000,
        depreciationMethod: "declining-balance",
        usefulLife: 5,
        salvageValue: 8000,
        acquisitionDate: new Date("2023-03-10"),
        category: "Transportation",
        location: "Fleet Depot",
        status: "active",
      },
    ]

    // Sample budget items
    this.budgetItems = [
      {
        id: "1",
        category: "Maintenance",
        subcategory: "Preventive Maintenance",
        budgetedAmount: 50000,
        actualAmount: 47500,
        variance: -2500,
        variancePercentage: -5.0,
        period: "2024-Q1",
        costCenter: "Manufacturing",
        status: "under-budget",
      },
      {
        id: "2",
        category: "Operations",
        subcategory: "Energy Costs",
        budgetedAmount: 25000,
        actualAmount: 28500,
        variance: 3500,
        variancePercentage: 14.0,
        period: "2024-Q1",
        costCenter: "Facilities",
        status: "over-budget",
      },
    ]

    // Sample cost centers
    this.costCenters = [
      {
        id: "1",
        name: "Manufacturing",
        department: "Operations",
        manager: "John Smith",
        budget: 500000,
        actualSpend: 475000,
        assets: ["1"],
        costAllocation: {
          direct: 400000,
          indirect: 75000,
          overhead: 25000,
        },
      },
      {
        id: "2",
        name: "Facilities",
        department: "Administration",
        manager: "Jane Doe",
        budget: 200000,
        actualSpend: 185000,
        assets: ["2"],
        costAllocation: {
          direct: 150000,
          indirect: 35000,
          overhead: 15000,
        },
      },
    ]
  }

  // TCO Analysis Methods
  calculateTCO(assetId: string): TCOAnalysis {
    const asset = this.assets.find((a) => a.id === assetId)
    if (!asset) throw new Error("Asset not found")

    // Sample TCO calculation
    const acquisitionCosts = {
      purchasePrice: asset.acquisitionCost,
      installation: asset.acquisitionCost * 0.05,
      training: asset.acquisitionCost * 0.02,
      other: asset.acquisitionCost * 0.01,
    }

    const annualOperationalCosts = {
      maintenance: asset.acquisitionCost * 0.08,
      energy: asset.acquisitionCost * 0.03,
      insurance: asset.acquisitionCost * 0.01,
      labor: asset.acquisitionCost * 0.15,
      supplies: asset.acquisitionCost * 0.05,
    }

    const disposalCosts = {
      removal: asset.acquisitionCost * 0.02,
      environmental: asset.acquisitionCost * 0.01,
      salvageValue: -asset.salvageValue,
    }

    const totalAcquisition = Object.values(acquisitionCosts).reduce((sum, cost) => sum + cost, 0)
    const totalOperational =
      Object.values(annualOperationalCosts).reduce((sum, cost) => sum + cost, 0) * asset.usefulLife
    const totalDisposal = Object.values(disposalCosts).reduce((sum, cost) => sum + cost, 0)

    const totalCostOfOwnership = totalAcquisition + totalOperational + totalDisposal

    return {
      assetId,
      acquisitionCosts,
      operationalCosts: annualOperationalCosts,
      disposalCosts,
      totalCostOfOwnership,
      costPerYear: totalCostOfOwnership / asset.usefulLife,
    }
  }

  // ROI Analysis Methods
  calculateROI(assetId: string, annualCashFlows: number[], discountRate = 0.08): ROIAnalysis {
    const asset = this.assets.find((a) => a.id === assetId)
    if (!asset) throw new Error("Asset not found")

    const initialInvestment = asset.acquisitionCost

    // Calculate NPV
    const netPresentValue = annualCashFlows.reduce((npv, cashFlow, year) => {
      return npv + cashFlow / Math.pow(1 + discountRate, year + 1)
    }, -initialInvestment)

    // Calculate IRR (simplified approximation)
    let irr = 0.1 // Starting guess
    for (let i = 0; i < 100; i++) {
      const npvAtIrr = annualCashFlows.reduce((npv, cashFlow, year) => {
        return npv + cashFlow / Math.pow(1 + irr, year + 1)
      }, -initialInvestment)

      if (Math.abs(npvAtIrr) < 0.01) break
      irr += npvAtIrr > 0 ? 0.001 : -0.001
    }

    // Calculate Payback Period
    let cumulativeCashFlow = -initialInvestment
    let paybackPeriod = 0
    for (let i = 0; i < annualCashFlows.length; i++) {
      cumulativeCashFlow += annualCashFlows[i]
      if (cumulativeCashFlow >= 0) {
        paybackPeriod = i + 1 - (cumulativeCashFlow - annualCashFlows[i]) / annualCashFlows[i]
        break
      }
    }

    const profitabilityIndex = (netPresentValue + initialInvestment) / initialInvestment
    const economicValueAdded = netPresentValue - initialInvestment * 0.1 // Assuming 10% cost of capital

    return {
      assetId,
      initialInvestment,
      annualCashFlows,
      discountRate,
      netPresentValue,
      internalRateOfReturn: irr,
      paybackPeriod,
      profitabilityIndex,
      economicValueAdded,
    }
  }

  // Lease vs Buy Analysis
  analyzeLeaseVsBuy(assetId: string): LeaseVsBuyAnalysis {
    const asset = this.assets.find((a) => a.id === assetId)
    if (!asset) throw new Error("Asset not found")

    const purchasePrice = asset.acquisitionCost
    const monthlyLeaseRate = purchasePrice * 0.025 // 2.5% of asset value per month
    const discountRate = 0.08

    // Purchase scenario
    const purchaseScenario = {
      initialCost: purchasePrice,
      annualCosts: Array(asset.usefulLife).fill(purchasePrice * 0.1), // 10% annual maintenance
      taxBenefits: Array(asset.usefulLife).fill((purchasePrice * 0.2) / asset.usefulLife), // Depreciation tax benefit
      residualValue: asset.salvageValue,
      netPresentValue: 0,
    }

    // Operating lease scenario
    const operatingLeaseScenario = {
      monthlyPayment: monthlyLeaseRate,
      annualCosts: Array(asset.usefulLife).fill(monthlyLeaseRate * 12),
      taxBenefits: Array(asset.usefulLife).fill(monthlyLeaseRate * 12 * 0.25), // Lease payment tax deduction
      netPresentValue: 0,
    }

    // Finance lease scenario
    const financeLeaseScenario = {
      downPayment: purchasePrice * 0.1,
      monthlyPayment: monthlyLeaseRate * 0.8,
      annualCosts: Array(asset.usefulLife).fill(monthlyLeaseRate * 0.8 * 12),
      taxBenefits: Array(asset.usefulLife).fill((purchasePrice * 0.2) / asset.usefulLife),
      residualValue: asset.salvageValue,
      netPresentValue: 0,
    }

    // Calculate NPVs (simplified)
    purchaseScenario.netPresentValue =
      -purchasePrice +
      purchaseScenario.annualCosts.reduce((npv, cost, year) => npv - cost / Math.pow(1 + discountRate, year + 1), 0) +
      purchaseScenario.taxBenefits.reduce(
        (npv, benefit, year) => npv + benefit / Math.pow(1 + discountRate, year + 1),
        0,
      ) +
      asset.salvageValue / Math.pow(1 + discountRate, asset.usefulLife)

    operatingLeaseScenario.netPresentValue =
      operatingLeaseScenario.annualCosts.reduce(
        (npv, cost, year) => npv - cost / Math.pow(1 + discountRate, year + 1),
        0,
      ) +
      operatingLeaseScenario.taxBenefits.reduce(
        (npv, benefit, year) => npv + benefit / Math.pow(1 + discountRate, year + 1),
        0,
      )

    financeLeaseScenario.netPresentValue =
      -financeLeaseScenario.downPayment +
      financeLeaseScenario.annualCosts.reduce(
        (npv, cost, year) => npv - cost / Math.pow(1 + discountRate, year + 1),
        0,
      ) +
      financeLeaseScenario.taxBenefits.reduce(
        (npv, benefit, year) => npv + benefit / Math.pow(1 + discountRate, year + 1),
        0,
      ) +
      asset.salvageValue / Math.pow(1 + discountRate, asset.usefulLife)

    // Determine recommendation
    const scenarios = {
      purchase: purchaseScenario,
      operatingLease: operatingLeaseScenario,
      financeLease: financeLeaseScenario,
    }
    const bestScenario = Object.entries(scenarios).reduce(
      (best, [key, scenario]) => (scenario.netPresentValue > best.scenario.netPresentValue ? { key, scenario } : best),
      { key: "purchase", scenario: purchaseScenario },
    )

    return {
      assetId,
      scenarios: {
        purchase: purchaseScenario,
        operatingLease: operatingLeaseScenario,
        financeLease: financeLeaseScenario,
      },
      recommendation: bestScenario.key as "purchase" | "operating-lease" | "finance-lease",
      savings: Math.abs(
        bestScenario.scenario.netPresentValue - Math.min(...Object.values(scenarios).map((s) => s.netPresentValue)),
      ),
      flexibilityScore: bestScenario.key === "operatingLease" ? 9 : bestScenario.key === "financeLease" ? 6 : 4,
    }
  }

  // Tax Optimization
  optimizeTaxStrategy(assetId: string): TaxOptimization {
    const asset = this.assets.find((a) => a.id === assetId)
    if (!asset) throw new Error("Asset not found")

    const taxRate = 0.25 // 25% corporate tax rate
    const acquisitionCost = asset.acquisitionCost

    const strategies = {
      straightLine: {
        annualDeduction: (acquisitionCost - asset.salvageValue) / asset.usefulLife,
        totalDeduction: acquisitionCost - asset.salvageValue,
        taxSavings: (acquisitionCost - asset.salvageValue) * taxRate,
      },
      accelerated: {
        annualDeduction: ((acquisitionCost - asset.salvageValue) * 2) / asset.usefulLife, // Double declining balance
        totalDeduction: acquisitionCost - asset.salvageValue,
        taxSavings: (acquisitionCost - asset.salvageValue) * taxRate * 1.2, // 20% more due to time value
      },
      section179: {
        eligibleAmount: Math.min(acquisitionCost, 1080000), // 2024 Section 179 limit
        deduction: Math.min(acquisitionCost, 1080000),
        taxSavings: Math.min(acquisitionCost, 1080000) * taxRate,
      },
      bonusDepreciation: {
        eligibleAmount: acquisitionCost,
        deduction: acquisitionCost * 0.8, // 80% bonus depreciation for 2024
        taxSavings: acquisitionCost * 0.8 * taxRate,
      },
    }

    // Determine best strategy
    const bestStrategy = Object.entries(strategies).reduce(
      (best, [key, strategy]) => (strategy.taxSavings > best.savings ? { key, savings: strategy.taxSavings } : best),
      { key: "straightLine", savings: strategies.straightLine.taxSavings },
    )

    return {
      assetId,
      depreciationStrategies: strategies,
      recommendedStrategy: bestStrategy.key,
      totalTaxSavings: bestStrategy.savings,
    }
  }

  // Financial Metrics
  getFinancialMetrics(): FinancialMetrics {
    const totalAssetValue = this.assets.reduce((sum, asset) => sum + asset.currentValue, 0)
    const totalAcquisitionCost = this.assets.reduce((sum, asset) => sum + asset.acquisitionCost, 0)

    // Calculate average TCO
    const tcoAnalyses = this.assets.map((asset) => this.calculateTCO(asset.id))
    const totalTCO = tcoAnalyses.reduce((sum, tco) => sum + tco.totalCostOfOwnership, 0)

    // Calculate average ROI (simplified)
    const averageROI = ((totalAssetValue - totalAcquisitionCost) / totalAcquisitionCost) * 100

    // Budget utilization
    const totalBudget = this.budgetItems.reduce((sum, item) => sum + item.budgetedAmount, 0)
    const totalActual = this.budgetItems.reduce((sum, item) => sum + item.actualAmount, 0)
    const budgetUtilization = (totalActual / totalBudget) * 100

    // Maintenance cost ratio
    const maintenanceCosts = this.budgetItems
      .filter((item) => item.category === "Maintenance")
      .reduce((sum, item) => sum + item.actualAmount, 0)
    const maintenanceCostRatio = (maintenanceCosts / totalAssetValue) * 100

    return {
      totalAssetValue,
      totalTCO,
      averageROI,
      budgetUtilization,
      maintenanceCostRatio,
      assetTurnover: totalAssetValue / totalAcquisitionCost,
      depreciationExpense: totalAcquisitionCost * 0.1, // Simplified
      taxSavings: totalAcquisitionCost * 0.05, // Simplified
      portfolioHealth: 85, // Calculated score
    }
  }

  // Getters
  getAssets(): FinancialAsset[] {
    // Enhance assets with additional financial information
    this.assets = this.assets.map(asset => {
      // Add maintenance information if not present
      if (!asset.maintenanceFrequency) {
        asset.maintenanceFrequency = "annual";
        
        // Calculate last maintenance date (random date in the past year)
        const lastMaintenanceDate = new Date();
        lastMaintenanceDate.setDate(lastMaintenanceDate.getDate() - Math.floor(Math.random() * 365));
        asset.lastMaintenanceDate = lastMaintenanceDate;
        
        // Calculate next maintenance date based on frequency
        const nextMaintenanceDate = new Date(lastMaintenanceDate);
        nextMaintenanceDate.setFullYear(nextMaintenanceDate.getFullYear() + 1);
        asset.nextMaintenanceDate = nextMaintenanceDate;
      }
      
      // Add replacement priority if not present
      if (!asset.replacementPriority) {
        // Calculate replacement priority based on asset age
        const acquisitionDate = new Date(asset.acquisitionDate);
        const currentDate = new Date();
        const ageInYears = (currentDate.getTime() - acquisitionDate.getTime()) / (1000 * 60 * 60 * 24 * 365);
        const lifePercentage = ageInYears / asset.usefulLife;
        
        if (lifePercentage > 0.9) {
          asset.replacementPriority = "critical";
        } else if (lifePercentage > 0.7) {
          asset.replacementPriority = "high";
        } else if (lifePercentage > 0.5) {
          asset.replacementPriority = "medium";
        } else {
          asset.replacementPriority = "low";
        }
      }
      
      // Add asset condition if not present
      if (!asset.assetCondition) {
        // Calculate condition based on age and value
        const acquisitionDate = new Date(asset.acquisitionDate);
        const currentDate = new Date();
        const ageInYears = (currentDate.getTime() - acquisitionDate.getTime()) / (1000 * 60 * 60 * 24 * 365);
        const lifePercentage = ageInYears / asset.usefulLife;
        const valueRatio = asset.currentValue / asset.acquisitionCost;
        
        if (lifePercentage < 0.3 && valueRatio > 0.8) {
          asset.assetCondition = "excellent";
        } else if (lifePercentage < 0.6 && valueRatio > 0.6) {
          asset.assetCondition = "good";
        } else if (lifePercentage < 0.8 && valueRatio > 0.4) {
          asset.assetCondition = "fair";
        } else {
          asset.assetCondition = "poor";
        }
      }
      
      return asset;
    });
    
    return [...this.assets]
  }

  getBudgetItems(): BudgetItem[] {
    return [...this.budgetItems]
  }

  getCostCenters(): CostCenter[] {
    return [...this.costCenters]
  }

  getAssetById(id: string): FinancialAsset | undefined {
    return this.assets.find((asset) => asset.id === id)
  }

  // Forecasting
  generateForecast(period: string): FinancialForecast {
    const baseMaintenanceCost = this.budgetItems
      .filter((item) => item.category === "Maintenance")
      .reduce((sum, item) => sum + item.actualAmount, 0)

    return {
      period,
      predictedCosts: {
        maintenance: baseMaintenanceCost * 1.05, // 5% increase
        operations: baseMaintenanceCost * 0.8,
        depreciation: this.assets.reduce((sum, asset) => sum + asset.acquisitionCost * 0.1, 0),
        total: baseMaintenanceCost * 1.95,
      },
      confidence: 0.85,
      factors: ["Historical trends", "Asset age", "Market conditions"],
    }
  }
}

export const financialService = FinancialService.getInstance()
