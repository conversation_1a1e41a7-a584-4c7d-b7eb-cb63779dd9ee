export interface FinancialAsset {
  id: string
  name: string
  assetType: string
  acquisitionCost: number
  currentValue: number
  depreciationMethod: "straight-line" | "declining-balance" | "sum-of-years" | "units-of-production" | "macrs"
  usefulLife: number
  salvageValue: number
  acquisitionDate: Date
  category: string
  location: string
  status: "active" | "disposed" | "impaired"
  costCenter?: string
  insuranceValue?: number
  warrantyExpiryDate?: Date
  maintenanceCost?: number
  maintenanceFrequency?: "monthly" | "quarterly" | "semi-annual" | "annual"
  lastMaintenanceDate?: Date
  nextMaintenanceDate?: Date
  financingType?: "owned" | "leased" | "financed"
  financingDetails?: FinancingDetails
  taxInformation?: AssetTaxInformation
  assetCondition?: "excellent" | "good" | "fair" | "poor"
  replacementPriority?: "low" | "medium" | "high" | "critical"
  replacementYear?: number
  customFields?: Record<string, any>
}

export interface TCOAnalysis {
  assetId: string
  acquisitionCosts: {
    purchasePrice: number
    installation: number
    training: number
    other: number
  }
  operationalCosts: {
    maintenance: number
    energy: number
    insurance: number
    labor: number
    supplies: number
  }
  disposalCosts: {
    removal: number
    environmental: number
    salvageValue: number
  }
  totalCostOfOwnership: number
  costPerYear: number
  costPerUnit?: number
}

export interface ROIAnalysis {
  assetId: string
  initialInvestment: number
  annualCashFlows: number[]
  discountRate: number
  netPresentValue: number
  internalRateOfReturn: number
  paybackPeriod: number
  profitabilityIndex: number
  economicValueAdded: number
}

export interface LeaseVsBuyAnalysis {
  assetId: string
  scenarios: {
    purchase: {
      initialCost: number
      annualCosts: number[]
      taxBenefits: number[]
      residualValue: number
      netPresentValue: number
    }
    operatingLease: {
      monthlyPayment: number
      annualCosts: number[]
      taxBenefits: number[]
      netPresentValue: number
    }
    financeLease: {
      downPayment: number
      monthlyPayment: number
      annualCosts: number[]
      taxBenefits: number[]
      residualValue: number
      netPresentValue: number
    }
  }
  recommendation: "purchase" | "operating-lease" | "finance-lease"
  savings: number
  flexibilityScore: number
}

export interface BudgetItem {
  id: string
  category: string
  subcategory: string
  budgetedAmount: number
  actualAmount: number
  variance: number
  variancePercentage: number
  period: string
  costCenter: string
  status: "on-track" | "over-budget" | "under-budget"
}

export interface TaxOptimization {
  assetId: string
  depreciationStrategies: {
    straightLine: {
      annualDeduction: number
      totalDeduction: number
      taxSavings: number
    }
    accelerated: {
      annualDeduction: number
      totalDeduction: number
      taxSavings: number
    }
    section179: {
      eligibleAmount: number
      deduction: number
      taxSavings: number
    }
    bonusDepreciation: {
      eligibleAmount: number
      deduction: number
      taxSavings: number
    }
  }
  recommendedStrategy: string
  totalTaxSavings: number
}

export interface FinancialMetrics {
  totalAssetValue: number
  totalTCO: number
  averageROI: number
  budgetUtilization: number
  maintenanceCostRatio: number
  assetTurnover: number
  depreciationExpense: number
  taxSavings: number
  portfolioHealth: number
}

export interface CostCenter {
  id: string
  name: string
  department: string
  manager: string
  budget: number
  actualSpend: number
  assets: string[]
  costAllocation: {
    direct: number
    indirect: number
    overhead: number
  }
}

export interface FinancialForecast {
  period: string
  predictedCosts: {
    maintenance: number
    operations: number
    depreciation: number
    total: number
  }
  confidence: number
  factors: string[]
}

export interface FinancingDetails {
  type: "loan" | "lease" | "cash" | "other"
  lender?: string
  agreementNumber?: string
  startDate: Date
  endDate?: Date
  originalAmount: number
  interestRate?: number
  monthlyPayment?: number
  remainingBalance?: number
  leaseType?: "operating" | "finance"
  buyoutOption?: boolean
  buyoutAmount?: number
}

export interface AssetTaxInformation {
  taxJurisdiction: string
  taxableStatus: "taxable" | "exempt" | "partially-exempt"
  exemptionReason?: string
  taxRate?: number
  lastAssessmentDate?: Date
  assessedValue?: number
  propertyTaxAmount?: number
  taxDepreciationMethod?: string
  taxDepreciationSchedule?: {
    year: number
    deduction: number
  }[]
  section179Eligible?: boolean
  bonusDepreciationEligible?: boolean
}

export interface AssetInsurance {
  id: string
  assetId: string
  policyNumber: string
  provider: string
  coverageType: string
  coverageAmount: number
  premium: number
  deductible: number
  startDate: Date
  endDate: Date
  status: "active" | "expired" | "cancelled"
  documents?: string[]
  claims?: InsuranceClaim[]
}

export interface InsuranceClaim {
  id: string
  assetInsuranceId: string
  claimNumber: string
  incidentDate: Date
  reportDate: Date
  description: string
  damageAmount: number
  settlementAmount?: number
  status: "filed" | "in-progress" | "settled" | "denied" | "closed"
  documents?: string[]
}

export interface AssetDisposal {
  id: string
  assetId: string
  disposalDate: Date
  disposalMethod: "sale" | "trade-in" | "donation" | "scrapped" | "stolen" | "destroyed"
  disposalReason: string
  salePrice?: number
  buyerInformation?: string
  disposalCosts: number
  netProceeds: number
  gainLoss: number
  taxImpact?: number
  approvedBy?: string
  documents?: string[]
}

export interface AssetTransfer {
  id: string
  assetId: string
  transferDate: Date
  fromDepartment: string
  toDepartment: string
  fromLocation: string
  toLocation: string
  fromCostCenter: string
  toCostCenter: string
  transferReason: string
  valueAtTransfer: number
  approvedBy: string
  status: "pending" | "approved" | "completed" | "cancelled"
}

export interface AssetRevaluation {
  id: string
  assetId: string
  revaluationDate: Date
  previousValue: number
  newValue: number
  revaluationMethod: "appraisal" | "market-comparison" | "income-approach" | "cost-approach"
  reason: string
  appraiser?: string
  approvedBy?: string
  documents?: string[]
  accountingEntries?: {
    account: string
    debit?: number
    credit?: number
  }[]
}

export interface AssetMaintenanceRecord {
  id: string
  assetId: string
  maintenanceDate: Date
  maintenanceType: "preventive" | "corrective" | "predictive" | "condition-based"
  description: string
  performedBy: string
  cost: number
  parts?: {
    name: string
    quantity: number
    cost: number
  }[]
  downtime?: number
  nextScheduledDate?: Date
  status: "scheduled" | "in-progress" | "completed" | "deferred"
  notes?: string
}

export interface AssetReplacement {
  id: string
  assetId: string
  replacementReason: string
  estimatedReplacementDate: Date
  estimatedReplacementCost: number
  replacementAssetType?: string
  budgetStatus: "not-budgeted" | "planned" | "budgeted" | "approved"
  budgetYear?: number
  replacementPriority: "low" | "medium" | "high" | "critical"
  businessImpact: string
  costBenefitAnalysis?: {
    continuedMaintenanceCost: number
    operationalSavings: number
    productivityImprovements: number
    paybackPeriod: number
  }
}
