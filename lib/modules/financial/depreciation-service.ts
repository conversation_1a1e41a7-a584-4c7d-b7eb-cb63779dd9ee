import type { FinancialAsset } from "./types";
import { DepreciationSchedule, DepreciationEntry, DepreciationMethod } from "./depreciation-types";

/**
 * Service for calculating and managing asset depreciation
 */
export class DepreciationService {
  private static instance: DepreciationService;

  private constructor() {}

  static getInstance(): DepreciationService {
    if (!DepreciationService.instance) {
      DepreciationService.instance = new DepreciationService();
    }
    return DepreciationService.instance;
  }

  /**
   * Calculate straight-line depreciation
   * Formula: (Cost - Salvage Value) / Useful Life
   */
  calculateStraightLine(
    asset: FinancialAsset,
    startDate: Date = new Date()
  ): DepreciationSchedule {
    const depreciableAmount = asset.acquisitionCost - asset.salvageValue;
    const annualDepreciation = depreciableAmount / asset.usefulLife;
    
    const schedule: DepreciationEntry[] = [];
    let remainingValue = asset.acquisitionCost;
    
    for (let year = 1; year <= asset.usefulLife; year++) {
      const periodEndDate = new Date(startDate);
      periodEndDate.setFullYear(startDate.getFullYear() + year);
      
      remainingValue -= annualDepreciation;
      
      schedule.push({
        period: year,
        periodEndDate: periodEndDate.toISOString().split('T')[0],
        depreciationAmount: annualDepreciation,
        accumulatedDepreciation: annualDepreciation * year,
        bookValue: Math.max(remainingValue, asset.salvageValue),
      });
    }
    
    return {
      assetId: asset.id,
      assetName: asset.name,
      method: "straight-line",
      acquisitionCost: asset.acquisitionCost,
      salvageValue: asset.salvageValue,
      usefulLife: asset.usefulLife,
      startDate: startDate.toISOString().split('T')[0],
      entries: schedule,
    };
  }

  /**
   * Calculate declining balance depreciation
   * Formula: Book Value × Depreciation Rate
   * @param rate - The depreciation rate (e.g., 2 for double declining)
   */
  calculateDecliningBalance(
    asset: FinancialAsset,
    rate: number = 2,
    startDate: Date = new Date()
  ): DepreciationSchedule {
    const straightLineRate = 1 / asset.usefulLife;
    const decliningRate = straightLineRate * rate;
    
    const schedule: DepreciationEntry[] = [];
    let remainingValue = asset.acquisitionCost;
    let accumulatedDepreciation = 0;
    
    for (let year = 1; year <= asset.usefulLife; year++) {
      const periodEndDate = new Date(startDate);
      periodEndDate.setFullYear(startDate.getFullYear() + year);
      
      // Switch to straight-line for remaining years if it gives higher depreciation
      const remainingYears = asset.usefulLife - year + 1;
      const decliningDepreciation = remainingValue * decliningRate;
      const straightLineDepreciation = (remainingValue - asset.salvageValue) / remainingYears;
      
      let depreciationAmount: number;
      
      if (remainingValue <= asset.salvageValue) {
        depreciationAmount = 0;
      } else if (straightLineDepreciation > decliningDepreciation) {
        depreciationAmount = straightLineDepreciation;
      } else {
        depreciationAmount = decliningDepreciation;
      }
      
      accumulatedDepreciation += depreciationAmount;
      remainingValue -= depreciationAmount;
      
      // Ensure book value doesn't go below salvage value
      remainingValue = Math.max(remainingValue, asset.salvageValue);
      
      schedule.push({
        period: year,
        periodEndDate: periodEndDate.toISOString().split('T')[0],
        depreciationAmount,
        accumulatedDepreciation,
        bookValue: remainingValue,
      });
    }
    
    return {
      assetId: asset.id,
      assetName: asset.name,
      method: "declining-balance",
      acquisitionCost: asset.acquisitionCost,
      salvageValue: asset.salvageValue,
      usefulLife: asset.usefulLife,
      startDate: startDate.toISOString().split('T')[0],
      entries: schedule,
    };
  }

  /**
   * Calculate sum-of-years-digits depreciation
   * Formula: (Remaining Life / SYD) × (Cost - Salvage Value)
   */
  calculateSumOfYearsDigits(
    asset: FinancialAsset,
    startDate: Date = new Date()
  ): DepreciationSchedule {
    const depreciableAmount = asset.acquisitionCost - asset.salvageValue;
    const sumOfYears = (asset.usefulLife * (asset.usefulLife + 1)) / 2;
    
    const schedule: DepreciationEntry[] = [];
    let remainingValue = asset.acquisitionCost;
    let accumulatedDepreciation = 0;
    
    for (let year = 1; year <= asset.usefulLife; year++) {
      const periodEndDate = new Date(startDate);
      periodEndDate.setFullYear(startDate.getFullYear() + year);
      
      const factor = (asset.usefulLife - year + 1) / sumOfYears;
      const depreciationAmount = depreciableAmount * factor;
      
      accumulatedDepreciation += depreciationAmount;
      remainingValue -= depreciationAmount;
      
      schedule.push({
        period: year,
        periodEndDate: periodEndDate.toISOString().split('T')[0],
        depreciationAmount,
        accumulatedDepreciation,
        bookValue: remainingValue,
      });
    }
    
    return {
      assetId: asset.id,
      assetName: asset.name,
      method: "sum-of-years",
      acquisitionCost: asset.acquisitionCost,
      salvageValue: asset.salvageValue,
      usefulLife: asset.usefulLife,
      startDate: startDate.toISOString().split('T')[0],
      entries: schedule,
    };
  }

  /**
   * Calculate units of production depreciation
   * Formula: (Number of units produced / Life in number of units) × (Cost - Salvage value)
   */
  calculateUnitsOfProduction(
    asset: FinancialAsset,
    totalEstimatedUnits: number,
    annualUnits: number[],
    startDate: Date = new Date()
  ): DepreciationSchedule {
    const depreciableAmount = asset.acquisitionCost - asset.salvageValue;
    const depreciationPerUnit = depreciableAmount / totalEstimatedUnits;
    
    const schedule: DepreciationEntry[] = [];
    let remainingValue = asset.acquisitionCost;
    let accumulatedDepreciation = 0;
    
    for (let year = 1; year <= annualUnits.length; year++) {
      const periodEndDate = new Date(startDate);
      periodEndDate.setFullYear(startDate.getFullYear() + year);
      
      const depreciationAmount = depreciationPerUnit * annualUnits[year - 1];
      
      accumulatedDepreciation += depreciationAmount;
      remainingValue -= depreciationAmount;
      
      // Ensure book value doesn't go below salvage value
      remainingValue = Math.max(remainingValue, asset.salvageValue);
      
      schedule.push({
        period: year,
        periodEndDate: periodEndDate.toISOString().split('T')[0],
        depreciationAmount,
        accumulatedDepreciation,
        bookValue: remainingValue,
        units: annualUnits[year - 1],
      });
    }
    
    return {
      assetId: asset.id,
      assetName: asset.name,
      method: "units-of-production",
      acquisitionCost: asset.acquisitionCost,
      salvageValue: asset.salvageValue,
      usefulLife: annualUnits.length,
      startDate: startDate.toISOString().split('T')[0],
      entries: schedule,
      totalEstimatedUnits,
    };
  }

  /**
   * Calculate MACRS (Modified Accelerated Cost Recovery System) depreciation
   * Uses predefined rates based on property class
   */
  calculateMACRS(
    asset: FinancialAsset,
    propertyClass: 3 | 5 | 7 | 10 | 15 | 20 | 27.5 | 39,
    startDate: Date = new Date()
  ): DepreciationSchedule {
    // MACRS rates for different property classes
    const macrsRates: Record<number, number[]> = {
      3: [0.3333, 0.4445, 0.1481, 0.0741],
      5: [0.2000, 0.3200, 0.1920, 0.1152, 0.1152, 0.0576],
      7: [0.1429, 0.2449, 0.1749, 0.1249, 0.0893, 0.0892, 0.0893, 0.0446],
      10: [0.1000, 0.1800, 0.1440, 0.1152, 0.0922, 0.0737, 0.0655, 0.0655, 0.0656, 0.0655, 0.0328],
      15: [0.0500, 0.0950, 0.0855, 0.0770, 0.0693, 0.0623, 0.0590, 0.0590, 0.0591, 0.0590, 0.0591, 0.0590, 0.0591, 0.0590, 0.0591, 0.0295],
      20: [0.0375, 0.0722, 0.0668, 0.0618, 0.0571, 0.0528, 0.0489, 0.0452, 0.0447, 0.0447, 0.0446, 0.0446, 0.0446, 0.0446, 0.0446, 0.0446, 0.0446, 0.0446, 0.0446, 0.0446, 0.0223],
      27.5: Array(28).fill(0).map((_, i) => i === 27 ? 0.0182 : 0.0364),
      39: Array(40).fill(0).map((_, i) => i === 39 ? 0.0128 : 0.0256),
    };
    
    const rates = macrsRates[propertyClass];
    if (!rates) {
      throw new Error(`Invalid MACRS property class: ${propertyClass}`);
    }
    
    const schedule: DepreciationEntry[] = [];
    let remainingValue = asset.acquisitionCost;
    let accumulatedDepreciation = 0;
    
    for (let year = 1; year <= rates.length; year++) {
      const periodEndDate = new Date(startDate);
      periodEndDate.setFullYear(startDate.getFullYear() + year);
      
      const depreciationAmount = asset.acquisitionCost * rates[year - 1];
      
      accumulatedDepreciation += depreciationAmount;
      remainingValue -= depreciationAmount;
      
      schedule.push({
        period: year,
        periodEndDate: periodEndDate.toISOString().split('T')[0],
        depreciationAmount,
        accumulatedDepreciation,
        bookValue: remainingValue,
      });
    }
    
    return {
      assetId: asset.id,
      assetName: asset.name,
      method: "macrs",
      acquisitionCost: asset.acquisitionCost,
      salvageValue: 0, // MACRS assumes zero salvage value
      usefulLife: rates.length,
      startDate: startDate.toISOString().split('T')[0],
      entries: schedule,
      propertyClass,
    };
  }

  /**
   * Calculate Section 179 deduction
   * Allows businesses to deduct the full purchase price of qualifying equipment
   */
  calculateSection179(
    asset: FinancialAsset,
    taxYear: number = new Date().getFullYear()
  ): {
    eligibleAmount: number;
    deduction: number;
    remainingBasis: number;
  } {
    // Section 179 limits by tax year
    const limits: Record<number, { deductionLimit: number; spendingCap: number }> = {
      2023: { deductionLimit: 1160000, spendingCap: 2890000 },
      2024: { deductionLimit: 1220000, spendingCap: 3050000 },
    };
    
    const yearLimits = limits[taxYear] || limits[2024]; // Default to 2024 if year not found
    
    // Check if asset is eligible (under spending cap)
    if (asset.acquisitionCost > yearLimits.spendingCap) {
      return {
        eligibleAmount: 0,
        deduction: 0,
        remainingBasis: asset.acquisitionCost,
      };
    }
    
    const eligibleAmount = Math.min(asset.acquisitionCost, yearLimits.deductionLimit);
    
    return {
      eligibleAmount,
      deduction: eligibleAmount,
      remainingBasis: asset.acquisitionCost - eligibleAmount,
    };
  }

  /**
   * Calculate bonus depreciation
   * Allows businesses to deduct a percentage of the purchase price in the first year
   */
  calculateBonusDepreciation(
    asset: FinancialAsset,
    taxYear: number = new Date().getFullYear()
  ): {
    bonusPercentage: number;
    bonusAmount: number;
    remainingBasis: number;
  } {
    // Bonus depreciation percentages by tax year
    const bonusPercentages: Record<number, number> = {
      2023: 0.80, // 80%
      2024: 0.60, // 60%
      2025: 0.40, // 40%
      2026: 0.20, // 20%
      2027: 0.00, // 0%
    };
    
    const percentage = bonusPercentages[taxYear] || 0;
    const bonusAmount = asset.acquisitionCost * percentage;
    
    return {
      bonusPercentage: percentage,
      bonusAmount,
      remainingBasis: asset.acquisitionCost - bonusAmount,
    };
  }

  /**
   * Compare different depreciation methods for an asset
   */
  compareDepreciationMethods(
    asset: FinancialAsset,
    startDate: Date = new Date()
  ): Record<DepreciationMethod, DepreciationSchedule> {
    return {
      "straight-line": this.calculateStraightLine(asset, startDate),
      "declining-balance": this.calculateDecliningBalance(asset, 2, startDate),
      "sum-of-years": this.calculateSumOfYearsDigits(asset, startDate),
      "units-of-production": this.calculateUnitsOfProduction(
        asset,
        asset.usefulLife * 2000, // Example: 2000 units per year
        Array(asset.usefulLife).fill(2000), // Assuming constant production
        startDate
      ),
    };
  }

  /**
   * Get the optimal depreciation method based on business goals
   * @param goal - The business goal for depreciation strategy
   */
  getOptimalDepreciationMethod(
    asset: FinancialAsset,
    goal: "tax-efficiency" | "financial-reporting" | "cash-flow" | "balanced" = "balanced"
  ): {
    recommendedMethod: DepreciationMethod;
    reasoning: string;
    schedule: DepreciationSchedule;
  } {
    switch (goal) {
      case "tax-efficiency":
        // For tax efficiency, accelerated methods are usually better
        return {
          recommendedMethod: "declining-balance",
          reasoning: "Accelerated depreciation provides higher deductions in early years, reducing taxable income when the asset is most valuable.",
          schedule: this.calculateDecliningBalance(asset, 2),
        };
        
      case "financial-reporting":
        // For financial reporting, straight-line is often preferred for consistency
        return {
          recommendedMethod: "straight-line",
          reasoning: "Straight-line depreciation provides consistent, predictable expenses that are easier to forecast and explain to stakeholders.",
          schedule: this.calculateStraightLine(asset),
        };
        
      case "cash-flow":
        // For cash flow optimization, accelerated methods can help with early tax savings
        return {
          recommendedMethod: "sum-of-years",
          reasoning: "Sum-of-years-digits provides accelerated depreciation that's less aggressive than double-declining, balancing tax benefits with financial reporting needs.",
          schedule: this.calculateSumOfYearsDigits(asset),
        };
        
      case "balanced":
      default:
        // For a balanced approach, compare methods and choose based on asset type
        if (asset.assetType === "Technology" || asset.assetType === "Equipment") {
          return {
            recommendedMethod: "declining-balance",
            reasoning: "Technology and equipment typically lose value faster in early years, making accelerated depreciation more accurate.",
            schedule: this.calculateDecliningBalance(asset, 2),
          };
        } else if (asset.assetType === "Real Estate" || asset.assetType === "Building") {
          return {
            recommendedMethod: "straight-line",
            reasoning: "Real estate and buildings typically depreciate more evenly over time, making straight-line depreciation appropriate.",
            schedule: this.calculateStraightLine(asset),
          };
        } else {
          return {
            recommendedMethod: "straight-line",
            reasoning: "Straight-line is the most widely accepted method and provides consistent expenses over the asset's life.",
            schedule: this.calculateStraightLine(asset),
          };
        }
    }
  }

  /**
   * Generate depreciation journal entries for accounting
   */
  generateJournalEntries(
    schedule: DepreciationSchedule,
    period: number
  ): {
    date: string;
    description: string;
    entries: Array<{
      account: string;
      debit?: number;
      credit?: number;
    }>;
  } {
    const entry = schedule.entries[period - 1];
    if (!entry) {
      throw new Error(`Period ${period} not found in depreciation schedule`);
    }
    
    return {
      date: entry.periodEndDate,
      description: `Depreciation expense for ${schedule.assetName} - Period ${period}`,
      entries: [
        {
          account: "Depreciation Expense",
          debit: entry.depreciationAmount,
        },
        {
          account: "Accumulated Depreciation",
          credit: entry.depreciationAmount,
        },
      ],
    };
  }

  /**
   * Calculate depreciation for a group of assets
   */
  calculateGroupDepreciation(
    assets: FinancialAsset[],
    method: DepreciationMethod = "straight-line"
  ): {
    totalAcquisitionCost: number;
    totalSalvageValue: number;
    totalDepreciationExpense: number;
    assetSchedules: DepreciationSchedule[];
  } {
    const assetSchedules: DepreciationSchedule[] = [];
    let totalAcquisitionCost = 0;
    let totalSalvageValue = 0;
    let totalDepreciationExpense = 0;
    
    for (const asset of assets) {
      let schedule: DepreciationSchedule;
      
      switch (method) {
        case "straight-line":
          schedule = this.calculateStraightLine(asset);
          break;
        case "declining-balance":
          schedule = this.calculateDecliningBalance(asset);
          break;
        case "sum-of-years":
          schedule = this.calculateSumOfYearsDigits(asset);
          break;
        case "units-of-production":
          schedule = this.calculateUnitsOfProduction(
            asset,
            asset.usefulLife * 2000, // Example: 2000 units per year
            Array(asset.usefulLife).fill(2000), // Assuming constant production
          );
          break;
        default:
          schedule = this.calculateStraightLine(asset);
      }
      
      assetSchedules.push(schedule);
      totalAcquisitionCost += asset.acquisitionCost;
      totalSalvageValue += asset.salvageValue;
      
      // Sum first year depreciation for all assets
      if (schedule.entries.length > 0) {
        totalDepreciationExpense += schedule.entries[0].depreciationAmount;
      }
    }
    
    return {
      totalAcquisitionCost,
      totalSalvageValue,
      totalDepreciationExpense,
      assetSchedules,
    };
  }

  /**
   * Generate a depreciation forecast for budget planning
   */
  generateDepreciationForecast(
    assets: FinancialAsset[],
    forecastYears: number = 5
  ): Array<{
    year: number;
    totalDepreciation: number;
    assetBreakdown: Record<string, number>;
  }> {
    const forecast: Array<{
      year: number;
      totalDepreciation: number;
      assetBreakdown: Record<string, number>;
    }> = [];
    
    for (let year = 1; year <= forecastYears; year++) {
      const yearForecast = {
        year,
        totalDepreciation: 0,
        assetBreakdown: {} as Record<string, number>,
      };
      
      for (const asset of assets) {
        let schedule: DepreciationSchedule;
        
        switch (asset.depreciationMethod) {
          case "straight-line":
            schedule = this.calculateStraightLine(asset);
            break;
          case "declining-balance":
            schedule = this.calculateDecliningBalance(asset);
            break;
          case "sum-of-years":
            schedule = this.calculateSumOfYearsDigits(asset);
            break;
          case "units-of-production":
            schedule = this.calculateUnitsOfProduction(
              asset,
              asset.usefulLife * 2000, // Example: 2000 units per year
              Array(asset.usefulLife).fill(2000), // Assuming constant production
            );
            break;
          case "macrs":
            schedule = this.calculateMACRS(asset, 5); // Using a default propertyClass for now
            break;
          default:
            schedule = this.calculateStraightLine(asset);
        }
        
        if (year <= schedule.entries.length) {
          const depreciationAmount = schedule.entries[year - 1].depreciationAmount;
          yearForecast.totalDepreciation += depreciationAmount;
          yearForecast.assetBreakdown[asset.id] = depreciationAmount;
        }
      }
      
      forecast.push(yearForecast);
    }
    
    return forecast;
  }
}

export const depreciationService = DepreciationService.getInstance();