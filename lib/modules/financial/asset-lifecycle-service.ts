import type {
  FinancialAsset,
  AssetDisposal,
  AssetTransfer,
  AssetRevaluation,
  AssetInsurance,
  InsuranceClaim,
  AssetMaintenanceRecord,
  AssetReplacement,
  FinancingDetails,
  AssetTaxInformation,
  TCOAnalysis,
  ROIAnalysis
} from "./types";
import { depreciationService } from "./depreciation-service";

/**
 * Service for managing the complete financial lifecycle of assets
 */
export class AssetLifecycleService {
  private static instance: AssetLifecycleService;
  private assets: FinancialAsset[] = [];
  private disposals: AssetDisposal[] = [];
  private transfers: AssetTransfer[] = [];
  private revaluations: AssetRevaluation[] = [];
  private insurancePolicies: AssetInsurance[] = [];
  private maintenanceRecords: AssetMaintenanceRecord[] = [];
  private replacementPlans: AssetReplacement[] = [];

  private constructor() {
    this.initializeSampleData();
  }

  static getInstance(): AssetLifecycleService {
    if (!AssetLifecycleService.instance) {
      AssetLifecycleService.instance = new AssetLifecycleService();
    }
    return AssetLifecycleService.instance;
  }

  private initializeSampleData(): void {
    // Sample assets are loaded from financial service
    // This will be populated when getAssets is called
  }

  /**
   * Get all assets
   */
  getAssets(): FinancialAsset[] {
    return this.assets;
  }

  /**
   * Set assets (used to sync with financial service)
   */
  setAssets(assets: FinancialAsset[]): void {
    this.assets = assets;
  }

  /**
   * Get asset by ID
   */
  getAssetById(id: string): FinancialAsset | undefined {
    return this.assets.find(asset => asset.id === id);
  }

  /**
   * Add financing details to an asset
   */
  addFinancingDetails(assetId: string, financingDetails: FinancingDetails): FinancialAsset | undefined {
    const asset = this.getAssetById(assetId);
    if (!asset) return undefined;

    asset.financingType = financingDetails.type === "loan" ? "financed" : 
                         financingDetails.type === "lease" ? "leased" : "owned";
    asset.financingDetails = financingDetails;

    return asset;
  }

  /**
   * Add tax information to an asset
   */
  addTaxInformation(assetId: string, taxInfo: AssetTaxInformation): FinancialAsset | undefined {
    const asset = this.getAssetById(assetId);
    if (!asset) return undefined;

    asset.taxInformation = taxInfo;
    return asset;
  }

  /**
   * Record asset disposal
   */
  recordDisposal(disposal: AssetDisposal): AssetDisposal {
    const asset = this.getAssetById(disposal.assetId);
    if (!asset) throw new Error(`Asset with ID ${disposal.assetId} not found`);

    // Calculate gain/loss if not provided
    if (disposal.gainLoss === undefined) {
      disposal.gainLoss = (disposal.salePrice || 0) - asset.currentValue;
    }

    // Calculate net proceeds if not provided
    if (disposal.netProceeds === undefined) {
      disposal.netProceeds = (disposal.salePrice || 0) - disposal.disposalCosts;
    }

    // Update asset status
    asset.status = "disposed";

    // Add to disposals
    this.disposals.push(disposal);

    return disposal;
  }

  /**
   * Get all disposals
   */
  getDisposals(): AssetDisposal[] {
    return this.disposals;
  }

  /**
   * Get disposals for a specific asset
   */
  getDisposalsByAssetId(assetId: string): AssetDisposal[] {
    return this.disposals.filter(disposal => disposal.assetId === assetId);
  }

  /**
   * Record asset transfer
   */
  recordTransfer(transfer: AssetTransfer): AssetTransfer {
    const asset = this.getAssetById(transfer.assetId);
    if (!asset) throw new Error(`Asset with ID ${transfer.assetId} not found`);

    // Update asset location and cost center
    if (transfer.status === "completed") {
      asset.location = transfer.toLocation;
      asset.costCenter = transfer.toCostCenter;
    }

    // Add to transfers
    this.transfers.push(transfer);

    return transfer;
  }

  /**
   * Get all transfers
   */
  getTransfers(): AssetTransfer[] {
    return this.transfers;
  }

  /**
   * Get transfers for a specific asset
   */
  getTransfersByAssetId(assetId: string): AssetTransfer[] {
    return this.transfers.filter(transfer => transfer.assetId === assetId);
  }

  /**
   * Record asset revaluation
   */
  recordRevaluation(revaluation: AssetRevaluation): AssetRevaluation {
    const asset = this.getAssetById(revaluation.assetId);
    if (!asset) throw new Error(`Asset with ID ${revaluation.assetId} not found`);

    // Update asset value
    asset.currentValue = revaluation.newValue;

    // Generate accounting entries if not provided
    if (!revaluation.accountingEntries) {
      const difference = revaluation.newValue - revaluation.previousValue;
      
      if (difference > 0) {
        // Upward revaluation
        revaluation.accountingEntries = [
          { account: "Asset", debit: difference },
          { account: "Revaluation Reserve", credit: difference }
        ];
      } else if (difference < 0) {
        // Downward revaluation
        revaluation.accountingEntries = [
          { account: "Revaluation Loss", debit: Math.abs(difference) },
          { account: "Asset", credit: Math.abs(difference) }
        ];
      }
    }

    // Add to revaluations
    this.revaluations.push(revaluation);

    return revaluation;
  }

  /**
   * Get all revaluations
   */
  getRevaluations(): AssetRevaluation[] {
    return this.revaluations;
  }

  /**
   * Get revaluations for a specific asset
   */
  getRevaluationsByAssetId(assetId: string): AssetRevaluation[] {
    return this.revaluations.filter(revaluation => revaluation.assetId === assetId);
  }

  /**
   * Add insurance policy to an asset
   */
  addInsurancePolicy(insurance: AssetInsurance): AssetInsurance {
    const asset = this.getAssetById(insurance.assetId);
    if (!asset) throw new Error(`Asset with ID ${insurance.assetId} not found`);

    // Update asset insurance value
    asset.insuranceValue = insurance.coverageAmount;

    // Add to insurance policies
    this.insurancePolicies.push(insurance);

    return insurance;
  }

  /**
   * Get all insurance policies
   */
  getInsurancePolicies(): AssetInsurance[] {
    return this.insurancePolicies;
  }

  /**
   * Get insurance policies for a specific asset
   */
  getInsurancePoliciesByAssetId(assetId: string): AssetInsurance[] {
    return this.insurancePolicies.filter(policy => policy.assetId === assetId);
  }

  /**
   * Record insurance claim
   */
  recordInsuranceClaim(policyId: string, claim: InsuranceClaim): InsuranceClaim {
    const policy = this.insurancePolicies.find(p => p.id === policyId);
    if (!policy) throw new Error(`Insurance policy with ID ${policyId} not found`);

    // Initialize claims array if it doesn't exist
    if (!policy.claims) {
      policy.claims = [];
    }

    // Add claim to policy
    policy.claims.push(claim);

    return claim;
  }

  /**
   * Record maintenance for an asset
   */
  recordMaintenance(maintenance: AssetMaintenanceRecord): AssetMaintenanceRecord {
    const asset = this.getAssetById(maintenance.assetId);
    if (!asset) throw new Error(`Asset with ID ${maintenance.assetId} not found`);

    // Update asset maintenance dates
    asset.lastMaintenanceDate = maintenance.maintenanceDate;
    
    if (maintenance.nextScheduledDate) {
      asset.nextMaintenanceDate = maintenance.nextScheduledDate;
    } else if (asset.maintenanceFrequency) {
      // Calculate next maintenance date based on frequency
      const nextDate = new Date(maintenance.maintenanceDate);
      switch (asset.maintenanceFrequency) {
        case "monthly":
          nextDate.setMonth(nextDate.getMonth() + 1);
          break;
        case "quarterly":
          nextDate.setMonth(nextDate.getMonth() + 3);
          break;
        case "semi-annual":
          nextDate.setMonth(nextDate.getMonth() + 6);
          break;
        case "annual":
          nextDate.setFullYear(nextDate.getFullYear() + 1);
          break;
      }
      asset.nextMaintenanceDate = nextDate;
    }

    // Add to maintenance records
    this.maintenanceRecords.push(maintenance);

    return maintenance;
  }

  /**
   * Get all maintenance records
   */
  getMaintenanceRecords(): AssetMaintenanceRecord[] {
    return this.maintenanceRecords;
  }

  /**
   * Get maintenance records for a specific asset
   */
  getMaintenanceRecordsByAssetId(assetId: string): AssetMaintenanceRecord[] {
    return this.maintenanceRecords.filter(record => record.assetId === assetId);
  }

  /**
   * Create replacement plan for an asset
   */
  createReplacementPlan(replacement: AssetReplacement): AssetReplacement {
    const asset = this.getAssetById(replacement.assetId);
    if (!asset) throw new Error(`Asset with ID ${replacement.assetId} not found`);

    // Update asset replacement information
    asset.replacementPriority = replacement.replacementPriority;
    asset.replacementYear = new Date(replacement.estimatedReplacementDate).getFullYear();

    // Add to replacement plans
    this.replacementPlans.push(replacement);

    return replacement;
  }

  /**
   * Get all replacement plans
   */
  getReplacementPlans(): AssetReplacement[] {
    return this.replacementPlans;
  }

  /**
   * Get replacement plan for a specific asset
   */
  getReplacementPlanByAssetId(assetId: string): AssetReplacement | undefined {
    return this.replacementPlans.find(plan => plan.assetId === assetId);
  }

  /**
   * Calculate remaining useful life of an asset
   */
  calculateRemainingUsefulLife(assetId: string): number {
    const asset = this.getAssetById(assetId);
    if (!asset) throw new Error(`Asset with ID ${assetId} not found`);

    const acquisitionDate = new Date(asset.acquisitionDate);
    const currentDate = new Date();
    
    // Calculate years elapsed since acquisition
    const yearsElapsed = (currentDate.getTime() - acquisitionDate.getTime()) / (1000 * 60 * 60 * 24 * 365.25);
    
    // Calculate remaining useful life
    const remainingLife = Math.max(0, asset.usefulLife - yearsElapsed);
    
    return parseFloat(remainingLife.toFixed(2));
  }

  /**
   * Calculate total cost of ownership (TCO) for an asset
   */
  calculateTCO(assetId: string): TCOAnalysis {
    const asset = this.getAssetById(assetId);
    if (!asset) throw new Error(`Asset with ID ${assetId} not found`);

    // Get maintenance costs
    const maintenanceCosts = this.getMaintenanceRecordsByAssetId(assetId)
      .reduce((total, record) => total + record.cost, 0);

    // Calculate acquisition costs
    const acquisitionCosts = {
      purchasePrice: asset.acquisitionCost,
      installation: asset.acquisitionCost * 0.05, // Estimated installation cost
      training: asset.acquisitionCost * 0.02,     // Estimated training cost
      other: asset.acquisitionCost * 0.01         // Other acquisition costs
    };

    // Calculate operational costs
    const operationalCosts = {
      maintenance: maintenanceCosts || asset.acquisitionCost * 0.08, // Use actual maintenance if available
      energy: asset.acquisitionCost * 0.03,       // Estimated energy cost
      insurance: asset.insuranceValue ? (asset.insuranceValue * 0.02) : (asset.acquisitionCost * 0.01), // Insurance premium
      labor: asset.acquisitionCost * 0.15,        // Estimated labor cost
      supplies: asset.acquisitionCost * 0.05      // Estimated supplies cost
    };

    // Calculate disposal costs
    const disposalCosts = {
      removal: asset.acquisitionCost * 0.02,      // Estimated removal cost
      environmental: asset.acquisitionCost * 0.01, // Estimated environmental cost
      salvageValue: -asset.salvageValue            // Negative because it reduces TCO
    };

    // Calculate totals
    const totalAcquisition = Object.values(acquisitionCosts).reduce((sum, cost) => sum + cost, 0);
    const totalOperational = Object.values(operationalCosts).reduce((sum, cost) => sum + cost, 0);
    const totalDisposal = Object.values(disposalCosts).reduce((sum, cost) => sum + cost, 0);

    const totalCostOfOwnership = totalAcquisition + totalOperational + totalDisposal;

    return {
      assetId,
      acquisitionCosts,
      operationalCosts,
      disposalCosts,
      totalCostOfOwnership,
      costPerYear: totalCostOfOwnership / asset.usefulLife
    };
  }

  /**
   * Calculate return on investment (ROI) for an asset
   */
  calculateROI(assetId: string, annualCashFlows?: number[]): ROIAnalysis {
    const asset = this.getAssetById(assetId);
    if (!asset) throw new Error(`Asset with ID ${assetId} not found`);

    const initialInvestment = asset.acquisitionCost;
    const discountRate = 0.08; // 8% discount rate

    // Generate estimated cash flows if not provided
    const cashFlows = annualCashFlows || this.estimateCashFlows(asset);

    // Calculate NPV
    const netPresentValue = cashFlows.reduce((npv, cashFlow, year) => {
      return npv + cashFlow / Math.pow(1 + discountRate, year + 1);
    }, -initialInvestment);

    // Calculate IRR (simplified approximation)
    let irr = 0.1; // Starting guess
    for (let i = 0; i < 100; i++) {
      const npvAtIrr = cashFlows.reduce((npv, cashFlow, year) => {
        return npv + cashFlow / Math.pow(1 + irr, year + 1);
      }, -initialInvestment);

      if (Math.abs(npvAtIrr) < 0.01) break;
      irr += npvAtIrr > 0 ? 0.001 : -0.001;
    }

    // Calculate Payback Period
    let cumulativeCashFlow = -initialInvestment;
    let paybackPeriod = 0;
    for (let i = 0; i < cashFlows.length; i++) {
      cumulativeCashFlow += cashFlows[i];
      if (cumulativeCashFlow >= 0) {
        paybackPeriod = i + 1 - (cumulativeCashFlow - cashFlows[i]) / cashFlows[i];
        break;
      }
    }

    const profitabilityIndex = (netPresentValue + initialInvestment) / initialInvestment;
    const economicValueAdded = netPresentValue - initialInvestment * 0.1; // Assuming 10% cost of capital

    return {
      assetId,
      initialInvestment,
      annualCashFlows: cashFlows,
      discountRate,
      netPresentValue,
      internalRateOfReturn: irr,
      paybackPeriod,
      profitabilityIndex,
      economicValueAdded
    };
  }

  /**
   * Estimate annual cash flows for an asset based on its type and value
   */
  private estimateCashFlows(asset: FinancialAsset): number[] {
    const cashFlows: number[] = [];
    
    // Different cash flow patterns based on asset type
    switch (asset.assetType) {
      case "Equipment":
      case "Machinery":
        // Equipment typically has higher returns early, then declining
        for (let year = 0; year < asset.usefulLife; year++) {
          const factor = 1 - (year / asset.usefulLife) * 0.5; // Declining factor
          cashFlows.push(asset.acquisitionCost * 0.25 * factor);
        }
        break;
        
      case "Vehicle":
        // Vehicles typically have steady returns with maintenance increasing
        for (let year = 0; year < asset.usefulLife; year++) {
          const maintenanceFactor = 1 + (year / asset.usefulLife) * 0.5; // Increasing maintenance
          cashFlows.push(asset.acquisitionCost * 0.2 - (asset.acquisitionCost * 0.02 * maintenanceFactor));
        }
        break;
        
      case "Real Estate":
      case "Building":
        // Real estate typically appreciates and has steady returns
        for (let year = 0; year < asset.usefulLife; year++) {
          const appreciationFactor = 1 + (year / asset.usefulLife) * 0.2; // Appreciation over time
          cashFlows.push(asset.acquisitionCost * 0.08 * appreciationFactor);
        }
        break;
        
      case "Technology":
      case "IT Equipment":
        // Technology typically has high returns early but becomes obsolete
        for (let year = 0; year < asset.usefulLife; year++) {
          const obsolescenceFactor = Math.exp(-0.3 * year); // Exponential decline
          cashFlows.push(asset.acquisitionCost * 0.4 * obsolescenceFactor);
        }
        break;
        
      default:
        // Default pattern for other asset types
        for (let year = 0; year < asset.usefulLife; year++) {
          cashFlows.push(asset.acquisitionCost * 0.15); // 15% return per year
        }
    }
    
    return cashFlows;
  }

  /**
   * Calculate asset performance metrics
   */
  calculateAssetPerformanceMetrics(assetId: string): {
    utilizationRate: number;
    returnOnAsset: number;
    maintenanceCostRatio: number;
    downtime: number;
    remainingUsefulLife: number;
    replacementUrgency: number;
  } {
    const asset = this.getAssetById(assetId);
    if (!asset) throw new Error(`Asset with ID ${assetId} not found`);

    // Get maintenance records
    const maintenanceRecords = this.getMaintenanceRecordsByAssetId(assetId);
    
    // Calculate total maintenance cost
    const totalMaintenanceCost = maintenanceRecords.reduce((total, record) => total + record.cost, 0);
    
    // Calculate total downtime (in hours)
    const totalDowntime = maintenanceRecords.reduce((total, record) => total + (record.downtime || 0), 0);
    
    // Calculate remaining useful life
    const remainingUsefulLife = this.calculateRemainingUsefulLife(assetId);
    
    // Calculate ROI
    const roi = this.calculateROI(assetId);
    
    // Calculate metrics
    const utilizationRate = 0.85 - (totalDowntime / (365 * 24) * 0.1); // Estimated utilization rate
    const returnOnAsset = roi.netPresentValue / asset.acquisitionCost;
    const maintenanceCostRatio = totalMaintenanceCost / asset.acquisitionCost;
    
    // Calculate replacement urgency (0-10 scale)
    let replacementUrgency = 0;
    
    if (remainingUsefulLife <= 0) {
      replacementUrgency = 10; // End of useful life
    } else if (remainingUsefulLife < asset.usefulLife * 0.2) {
      replacementUrgency = 8; // Less than 20% of useful life remaining
    } else if (remainingUsefulLife < asset.usefulLife * 0.4) {
      replacementUrgency = 6; // Less than 40% of useful life remaining
    } else if (maintenanceCostRatio > 0.5) {
      replacementUrgency = 7; // High maintenance cost ratio
    } else if (returnOnAsset < 0) {
      replacementUrgency = 5; // Negative return on asset
    }
    
    // Adjust based on asset condition if available
    if (asset.assetCondition) {
      switch (asset.assetCondition) {
        case "poor":
          replacementUrgency += 2;
          break;
        case "fair":
          replacementUrgency += 1;
          break;
        case "good":
          replacementUrgency -= 1;
          break;
        case "excellent":
          replacementUrgency -= 2;
          break;
      }
    }
    
    // Ensure replacement urgency is within 0-10 range
    replacementUrgency = Math.max(0, Math.min(10, replacementUrgency));
    
    return {
      utilizationRate,
      returnOnAsset,
      maintenanceCostRatio,
      downtime: totalDowntime,
      remainingUsefulLife,
      replacementUrgency
    };
  }

  /**
   * Generate financial reports for assets
   */
  generateFinancialReports(assetIds?: string[]): {
    assetValuationReport: {
      totalAcquisitionCost: number;
      totalCurrentValue: number;
      totalDepreciation: number;
      assetsByCategory: Record<string, { count: number; value: number }>;
      assetsByStatus: Record<string, { count: number; value: number }>;
    };
    depreciationReport: {
      totalDepreciationExpense: number;
      depreciationByMethod: Record<string, number>;
      depreciationByCategory: Record<string, number>;
    };
    maintenanceReport: {
      totalMaintenanceCost: number;
      maintenanceByType: Record<string, number>;
      maintenanceByAsset: Record<string, number>;
    };
    insuranceReport: {
      totalInsuranceValue: number;
      totalPremiums: number;
      policiesByStatus: Record<string, number>;
      claimsByStatus: Record<string, number>;
    };
  } {
    // Filter assets if assetIds is provided
    const assetsToAnalyze = assetIds 
      ? this.assets.filter(asset => assetIds.includes(asset.id))
      : this.assets;

    // Asset Valuation Report
    const assetValuationReport = {
      totalAcquisitionCost: 0,
      totalCurrentValue: 0,
      totalDepreciation: 0,
      assetsByCategory: {} as Record<string, { count: number; value: number }>,
      assetsByStatus: {} as Record<string, { count: number; value: number }>
    };

    // Depreciation Report
    const depreciationReport = {
      totalDepreciationExpense: 0,
      depreciationByMethod: {} as Record<string, number>,
      depreciationByCategory: {} as Record<string, number>
    };

    // Maintenance Report
    const maintenanceReport = {
      totalMaintenanceCost: 0,
      maintenanceByType: {} as Record<string, number>,
      maintenanceByAsset: {} as Record<string, number>
    };

    // Insurance Report
    const insuranceReport = {
      totalInsuranceValue: 0,
      totalPremiums: 0,
      policiesByStatus: {} as Record<string, number>,
      claimsByStatus: {} as Record<string, number>
    };

    // Process assets
    for (const asset of assetsToAnalyze) {
      // Asset Valuation Report
      assetValuationReport.totalAcquisitionCost += asset.acquisitionCost;
      assetValuationReport.totalCurrentValue += asset.currentValue;
      assetValuationReport.totalDepreciation += (asset.acquisitionCost - asset.currentValue);

      // Process by category
      if (!assetValuationReport.assetsByCategory[asset.category]) {
        assetValuationReport.assetsByCategory[asset.category] = { count: 0, value: 0 };
      }
      assetValuationReport.assetsByCategory[asset.category].count++;
      assetValuationReport.assetsByCategory[asset.category].value += asset.currentValue;

      // Process by status
      if (!assetValuationReport.assetsByStatus[asset.status]) {
        assetValuationReport.assetsByStatus[asset.status] = { count: 0, value: 0 };
      }
      assetValuationReport.assetsByStatus[asset.status].count++;
      assetValuationReport.assetsByStatus[asset.status].value += asset.currentValue;

      // Depreciation Report
      const annualDepreciation = (asset.acquisitionCost - asset.salvageValue) / asset.usefulLife;
      depreciationReport.totalDepreciationExpense += annualDepreciation;

      // Process by method
      if (!depreciationReport.depreciationByMethod[asset.depreciationMethod]) {
        depreciationReport.depreciationByMethod[asset.depreciationMethod] = 0;
      }
      depreciationReport.depreciationByMethod[asset.depreciationMethod] += annualDepreciation;

      // Process by category
      if (!depreciationReport.depreciationByCategory[asset.category]) {
        depreciationReport.depreciationByCategory[asset.category] = 0;
      }
      depreciationReport.depreciationByCategory[asset.category] += annualDepreciation;

      // Maintenance Report
      const assetMaintenanceRecords = this.getMaintenanceRecordsByAssetId(asset.id);
      const assetMaintenanceCost = assetMaintenanceRecords.reduce((total, record) => total + record.cost, 0);
      
      maintenanceReport.totalMaintenanceCost += assetMaintenanceCost;
      maintenanceReport.maintenanceByAsset[asset.id] = assetMaintenanceCost;

      // Process by type
      for (const record of assetMaintenanceRecords) {
        if (!maintenanceReport.maintenanceByType[record.maintenanceType]) {
          maintenanceReport.maintenanceByType[record.maintenanceType] = 0;
        }
        maintenanceReport.maintenanceByType[record.maintenanceType] += record.cost;
      }

      // Insurance Report
      const assetInsurancePolicies = this.getInsurancePoliciesByAssetId(asset.id);
      
      for (const policy of assetInsurancePolicies) {
        insuranceReport.totalInsuranceValue += policy.coverageAmount;
        insuranceReport.totalPremiums += policy.premium;

        // Process by status
        if (!insuranceReport.policiesByStatus[policy.status]) {
          insuranceReport.policiesByStatus[policy.status] = 0;
        }
        insuranceReport.policiesByStatus[policy.status]++;

        // Process claims by status
        if (policy.claims) {
          for (const claim of policy.claims) {
            if (!insuranceReport.claimsByStatus[claim.status]) {
              insuranceReport.claimsByStatus[claim.status] = 0;
            }
            insuranceReport.claimsByStatus[claim.status]++;
          }
        }
      }
    }

    return {
      assetValuationReport,
      depreciationReport,
      maintenanceReport,
      insuranceReport
    };
  }

  /**
   * Generate asset replacement forecast
   */
  generateReplacementForecast(years: number = 5): Array<{
    year: number;
    assetsToReplace: Array<{
      assetId: string;
      assetName: string;
      replacementCost: number;
      replacementPriority: string;
    }>;
    totalReplacementCost: number;
  }> {
    const currentYear = new Date().getFullYear();
    const forecast: Array<{
      year: number;
      assetsToReplace: Array<{
        assetId: string;
        assetName: string;
        replacementCost: number;
        replacementPriority: string;
      }>;
      totalReplacementCost: number;
    }> = [];

    // Initialize forecast years
    for (let i = 0; i < years; i++) {
      forecast.push({
        year: currentYear + i,
        assetsToReplace: [],
        totalReplacementCost: 0
      });
    }

    // Process assets
    for (const asset of this.assets) {
      // Skip disposed assets
      if (asset.status === "disposed") continue;

      // Determine replacement year
      let replacementYear: number;

      // If we have a replacement plan, use that
      const replacementPlan = this.getReplacementPlanByAssetId(asset.id);
      if (replacementPlan) {
        replacementYear = new Date(replacementPlan.estimatedReplacementDate).getFullYear();
      } else if (asset.replacementYear) {
        // If asset has a replacement year, use that
        replacementYear = asset.replacementYear;
      } else {
        // Otherwise, estimate based on acquisition date and useful life
        const acquisitionYear = new Date(asset.acquisitionDate).getFullYear();
        replacementYear = acquisitionYear + asset.usefulLife;
      }

      // Check if replacement year is within forecast period
      if (replacementYear >= currentYear && replacementYear < currentYear + years) {
        const forecastIndex = replacementYear - currentYear;
        
        // Determine replacement cost
        let replacementCost: number;
        if (replacementPlan && replacementPlan.estimatedReplacementCost) {
          replacementCost = replacementPlan.estimatedReplacementCost;
        } else {
          // Estimate replacement cost with inflation
          const yearsSinceAcquisition = replacementYear - new Date(asset.acquisitionDate).getFullYear();
          const inflationRate = 0.03; // 3% annual inflation
          replacementCost = asset.acquisitionCost * Math.pow(1 + inflationRate, yearsSinceAcquisition);
        }

        // Add to forecast
        forecast[forecastIndex].assetsToReplace.push({
          assetId: asset.id,
          assetName: asset.name,
          replacementCost,
          replacementPriority: asset.replacementPriority || "medium"
        });

        forecast[forecastIndex].totalReplacementCost += replacementCost;
      }
    }

    return forecast;
  }

  /**
   * Generate tax depreciation schedule for an asset
   */
  generateTaxDepreciationSchedule(assetId: string): {
    assetId: string;
    assetName: string;
    acquisitionCost: number;
    acquisitionDate: string;
    depreciationMethod: string;
    usefulLife: number;
    schedule: Array<{
      year: number;
      fiscalYear: number;
      depreciationBase: number;
      depreciationRate: number;
      depreciationAmount: number;
      accumulatedDepreciation: number;
      bookValue: number;
    }>;
    section179Deduction?: number;
    bonusDepreciation?: number;
  } {
    const asset = this.getAssetById(assetId);
    if (!asset) throw new Error(`Asset with ID ${assetId} not found`);

    const acquisitionYear = new Date(asset.acquisitionDate).getFullYear();
    const schedule: Array<{
      year: number;
      fiscalYear: number;
      depreciationBase: number;
      depreciationRate: number;
      depreciationAmount: number;
      accumulatedDepreciation: number;
      bookValue: number;
    }> = [];

    // Check for Section 179 and bonus depreciation eligibility
    let section179Deduction = 0;
    let bonusDepreciation = 0;
    let depreciationBase = asset.acquisitionCost;

    if (asset.taxInformation?.section179Eligible) {
      section179Deduction = Math.min(asset.acquisitionCost, 1080000); // 2023 Section 179 limit
      depreciationBase -= section179Deduction;
    }

    if (asset.taxInformation?.bonusDepreciationEligible && depreciationBase > 0) {
      // 2023 bonus depreciation rate is 80%
      bonusDepreciation = depreciationBase * 0.8;
      depreciationBase -= bonusDepreciation;
    }

    // Generate depreciation schedule
    let accumulatedDepreciation = section179Deduction + bonusDepreciation;
    let bookValue = asset.acquisitionCost - accumulatedDepreciation;

    for (let year = 1; year <= asset.usefulLife; year++) {
      const fiscalYear = acquisitionYear + year - 1;
      
      // Determine depreciation rate based on method
      let depreciationRate: number;
      let depreciationAmount: number;
      
      switch (asset.depreciationMethod) {
        case "straight-line":
          depreciationRate = 1 / asset.usefulLife;
          depreciationAmount = depreciationBase * depreciationRate;
          break;
          
        case "declining-balance":
          depreciationRate = 2 / asset.usefulLife; // Double declining
          
          // Switch to straight-line if it gives higher depreciation
          const straightLineRate = (depreciationBase - accumulatedDepreciation + section179Deduction + bonusDepreciation) / 
                                  (asset.usefulLife - year + 1);
          const decliningAmount = bookValue * depreciationRate;
          
          if (straightLineRate > decliningAmount) {
            depreciationAmount = straightLineRate;
          } else {
            depreciationAmount = decliningAmount;
          }
          break;
          
        case "sum-of-years":
          const sumOfYears = (asset.usefulLife * (asset.usefulLife + 1)) / 2;
          depreciationRate = (asset.usefulLife - year + 1) / sumOfYears;
          depreciationAmount = depreciationBase * depreciationRate;
          break;
          
        case "units-of-production":
          // For tax purposes, units of production is typically converted to straight-line
          depreciationRate = 1 / asset.usefulLife;
          depreciationAmount = depreciationBase * depreciationRate;
          break;
          
        default:
          depreciationRate = 1 / asset.usefulLife;
          depreciationAmount = depreciationBase * depreciationRate;
      }
      
      // Ensure we don't depreciate below salvage value
      if (bookValue - depreciationAmount < asset.salvageValue) {
        depreciationAmount = bookValue - asset.salvageValue;
      }
      
      // Update accumulated depreciation and book value
      accumulatedDepreciation += depreciationAmount;
      bookValue = asset.acquisitionCost - accumulatedDepreciation;
      
      // Add to schedule
      schedule.push({
        year,
        fiscalYear,
        depreciationBase,
        depreciationRate,
        depreciationAmount,
        accumulatedDepreciation,
        bookValue
      });
      
      // Stop if fully depreciated
      if (bookValue <= asset.salvageValue) {
        break;
      }
    }

    return {
      assetId,
      assetName: asset.name,
      acquisitionCost: asset.acquisitionCost,
      acquisitionDate: new Date(asset.acquisitionDate).toISOString().split('T')[0],
      depreciationMethod: asset.depreciationMethod,
      usefulLife: asset.usefulLife,
      schedule,
      section179Deduction: section179Deduction > 0 ? section179Deduction : undefined,
      bonusDepreciation: bonusDepreciation > 0 ? bonusDepreciation : undefined
    };
  }
}

export const assetLifecycleService = AssetLifecycleService.getInstance();