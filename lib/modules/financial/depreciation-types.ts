/**
 * Types for the depreciation module
 */

export type DepreciationMethod = 
  | "straight-line" 
  | "declining-balance" 
  | "sum-of-years" 
  | "units-of-production" 
  | "macrs";

export interface DepreciationEntry {
  period: number;
  periodEndDate: string;
  depreciationAmount: number;
  accumulatedDepreciation: number;
  bookValue: number;
  units?: number; // For units of production method
}

export interface DepreciationSchedule {
  assetId: string;
  assetName: string;
  method: string;
  acquisitionCost: number;
  salvageValue: number;
  usefulLife: number;
  startDate: string;
  entries: DepreciationEntry[];
  totalEstimatedUnits?: number; // For units of production method
  propertyClass?: number; // For MACRS method
}

export interface DepreciationReport {
  assetId: string;
  assetName: string;
  reportDate: string;
  fiscalYear: number;
  depreciationMethod: DepreciationMethod;
  currentBookValue: number;
  yearToDateDepreciation: number;
  remainingDepreciableAmount: number;
  remainingUsefulLife: number;
}

export interface DepreciationJournalEntry {
  date: string;
  description: string;
  entries: Array<{
    account: string;
    debit?: number;
    credit?: number;
  }>;
}

export interface DepreciationTaxBenefit {
  year: number;
  depreciationExpense: number;
  taxRate: number;
  taxSavings: number;
  presentValue: number; // Discounted value of tax savings
}

export interface DepreciationComparison {
  assetId: string;
  assetName: string;
  methods: {
    [key in DepreciationMethod]?: {
      firstYearExpense: number;
      totalExpense: number;
      presentValueOfExpenses: number;
      taxSavings: number;
    };
  };
  recommendedMethod: DepreciationMethod;
  recommendation: string;
}

export interface DepreciationForecast {
  year: number;
  totalDepreciation: number;
  assetBreakdown: Record<string, number>;
}

export interface AssetGroupDepreciation {
  groupId: string;
  groupName: string;
  assets: string[]; // Asset IDs
  totalAcquisitionCost: number;
  totalSalvageValue: number;
  depreciationMethod: DepreciationMethod;
  compositeLife: number; // Weighted average useful life
  annualDepreciation: number;
}

export interface DepreciationSettings {
  defaultMethod: DepreciationMethod;
  defaultSalvageValuePercent: number;
  defaultTaxRate: number;
  discountRate: number;
  fiscalYearStart: string; // MM-DD format
  assetCategoryDefaults: Record<string, {
    method: DepreciationMethod;
    usefulLife: number;
    salvageValuePercent: number;
  }>;
}

export interface DepreciationAdjustment {
  id: string;
  assetId: string;
  adjustmentDate: string;
  adjustmentType: "impairment" | "revaluation" | "useful-life-change" | "method-change";
  previousValue: number;
  newValue: number;
  reason: string;
  approvedBy?: string;
}

export interface DepreciationReconciliation {
  period: string;
  startDate: string;
  endDate: string;
  openingBalance: number;
  additions: number;
  disposals: number;
  depreciation: number;
  adjustments: number;
  closingBalance: number;
  status: "draft" | "pending" | "approved" | "rejected";
}

export interface DepreciationComplianceReport {
  reportDate: string;
  fiscalYear: number;
  standardsCompliance: {
    gaap: boolean;
    ifrs: boolean;
    taxCode: boolean;
  };
  issues: Array<{
    assetId: string;
    issue: string;
    severity: "high" | "medium" | "low";
    recommendation: string;
  }>;
  certifiedBy?: string;
}

export interface DepreciationDashboardMetrics {
  totalAssets: number;
  totalAcquisitionCost: number;
  totalAccumulatedDepreciation: number;
  totalNetBookValue: number;
  currentYearDepreciation: number;
  projectedNextYearDepreciation: number;
  assetsByCategory: Record<string, number>;
  depreciationByMethod: Record<DepreciationMethod, number>;
  fullyDepreciatedAssets: number;
  assetsNearingEndOfLife: number;
}