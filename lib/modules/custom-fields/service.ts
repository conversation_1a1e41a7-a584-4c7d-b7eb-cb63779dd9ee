"use client";

import { CustomField, FieldOption, FieldValidation, ConditionalLogic } from "../asset-types/types";
import { DEFAULT_FIELD } from "./constants";

export class CustomFieldsService {
  private static instance: CustomFieldsService;

  static getInstance(): CustomFieldsService {
    if (!CustomFieldsService.instance) {
      CustomFieldsService.instance = new CustomFieldsService();
    }
    return CustomFieldsService.instance;
  }

  createField(field: Omit<CustomField, "id">): CustomField {
    return {
      ...field,
      id: `CF-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
    };
  }

  validateField(field: CustomField): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!field.name) {
      errors.push("Field name is required");
    } else if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(field.name)) {
      errors.push("Field name must start with a letter and contain only letters, numbers, and underscores");
    }

    if (!field.label) {
      errors.push("Field label is required");
    }

    if (field.type === "select" || field.type === "multiselect") {
      if (!field.options || field.options.length === 0) {
        errors.push("Options are required for select and multiselect fields");
      } else {
        const optionValues = new Set<string>();
        field.options.forEach((option) => {
          if (!option.value) {
            errors.push("Option value cannot be empty");
          } else if (optionValues.has(option.value)) {
            errors.push(`Duplicate option value: ${option.value}`);
          } else {
            optionValues.add(option.value);
          }

          if (!option.label) {
            errors.push("Option label cannot be empty");
          }
        });
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  validateFieldValue(value: any, field: CustomField): { isValid: boolean; error?: string } {
    if (field.isRequired && (value === undefined || value === null || value === "")) {
      return { isValid: false, error: `${field.label} is required` };
    }

    // If not required and empty, it's valid
    if (!field.isRequired && (value === undefined || value === null || value === "")) {
      return { isValid: true };
    }

    const { validation } = field;

    switch (field.type) {
      case "text":
      case "textarea":
      case "email":
      case "url":
      case "phone":
        if (typeof value !== "string") {
          return { isValid: false, error: `${field.label} must be text` };
        }
        
        if (validation.minLength !== undefined && value.length < validation.minLength) {
          return { 
            isValid: false, 
            error: `${field.label} must be at least ${validation.minLength} characters` 
          };
        }
        
        if (validation.maxLength !== undefined && value.length > validation.maxLength) {
          return { 
            isValid: false, 
            error: `${field.label} must be no more than ${validation.maxLength} characters` 
          };
        }
        
        if (validation.pattern && !new RegExp(validation.pattern).test(value)) {
          return { 
            isValid: false, 
            error: validation.errorMessage || `${field.label} has an invalid format` 
          };
        }
        
        if (field.type === "email" && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
          return { isValid: false, error: `${field.label} must be a valid email address` };
        }
        
        if (field.type === "url" && !/^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w .-]*)*\/?$/.test(value)) {
          return { isValid: false, error: `${field.label} must be a valid URL` };
        }
        
        if (field.type === "phone" && !/^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/.test(value)) {
          return { isValid: false, error: `${field.label} must be a valid phone number` };
        }
        break;
        
      case "number":
      case "decimal":
      case "currency":
      case "percentage":
        const numValue = Number(value);
        
        if (isNaN(numValue)) {
          return { isValid: false, error: `${field.label} must be a number` };
        }
        
        if (validation.minValue !== undefined && numValue < validation.minValue) {
          return { 
            isValid: false, 
            error: `${field.label} must be at least ${validation.minValue}` 
          };
        }
        
        if (validation.maxValue !== undefined && numValue > validation.maxValue) {
          return { 
            isValid: false, 
            error: `${field.label} must be no more than ${validation.maxValue}` 
          };
        }
        break;
        
      case "date":
      case "datetime":
        try {
          const dateValue = new Date(value);
          
          if (isNaN(dateValue.getTime())) {
            return { isValid: false, error: `${field.label} must be a valid date` };
          }
          
          if (validation.minValue !== undefined) {
            const minDate = new Date(validation.minValue);
            if (dateValue < minDate) {
              return { 
                isValid: false, 
                error: `${field.label} must be on or after ${minDate.toLocaleDateString()}` 
              };
            }
          }
          
          if (validation.maxValue !== undefined) {
            const maxDate = new Date(validation.maxValue);
            if (dateValue > maxDate) {
              return { 
                isValid: false, 
                error: `${field.label} must be on or before ${maxDate.toLocaleDateString()}` 
              };
            }
          }
        } catch (e) {
          return { isValid: false, error: `${field.label} must be a valid date` };
        }
        break;
        
      case "boolean":
        if (typeof value !== "boolean") {
          return { isValid: false, error: `${field.label} must be a boolean value` };
        }
        break;
        
      case "select":
        if (!field.options?.some(opt => opt.value === value)) {
          return { isValid: false, error: `${field.label} must be a valid option` };
        }
        break;
        
      case "multiselect":
        if (!Array.isArray(value)) {
          return { isValid: false, error: `${field.label} must be a list of options` };
        }
        
        const validOptions = new Set(field.options?.map(opt => opt.value) || []);
        const invalidOptions = value.filter(v => !validOptions.has(v));
        
        if (invalidOptions.length > 0) {
          return { isValid: false, error: `${field.label} contains invalid options` };
        }
        
        if (validation.minLength !== undefined && value.length < validation.minLength) {
          return { 
            isValid: false, 
            error: `${field.label} must have at least ${validation.minLength} selections` 
          };
        }
        
        if (validation.maxLength !== undefined && value.length > validation.maxLength) {
          return { 
            isValid: false, 
            error: `${field.label} must have no more than ${validation.maxLength} selections` 
          };
        }
        break;
        
      case "file":
      case "image":
        // File validation would typically be handled during upload
        // This is a basic check for file size if maxLength is used to represent max file size in KB
        if (validation.maxLength !== undefined && value?.size > validation.maxLength * 1024) {
          return { 
            isValid: false, 
            error: `${field.label} must be smaller than ${validation.maxLength}KB` 
          };
        }
        break;
        
      case "json":
        if (typeof value === "string") {
          try {
            JSON.parse(value);
          } catch (e) {
            return { isValid: false, error: `${field.label} must be valid JSON` };
          }
        } else if (typeof value !== "object") {
          return { isValid: false, error: `${field.label} must be a valid JSON object` };
        }
        
        if (validation.customValidator) {
          try {
            // This would execute a custom validation function stored as a string
            // In a real implementation, this would need proper sandboxing
            const validatorFn = new Function('value', validation.customValidator);
            const result = validatorFn(value);
            
            if (result !== true) {
              return { 
                isValid: false, 
                error: typeof result === 'string' ? result : `${field.label} failed custom validation` 
              };
            }
          } catch (e) {
            console.error("Custom validator error:", e);
            return { isValid: false, error: `Error in custom validator for ${field.label}` };
          }
        }
        break;
    }

    return { isValid: true };
  }

  evaluateConditionalLogic(
    logic: ConditionalLogic[],
    values: Record<string, any>
  ): Record<string, { visible: boolean; required: boolean; disabled: boolean; value?: any }> {
    const result: Record<string, { visible: boolean; required: boolean; disabled: boolean; value?: any }> = {};

    logic.forEach((rule) => {
      const { condition, action, targetFieldId } = rule;
      const fieldValue = values[condition.fieldId];
      let conditionMet = false;

      // Evaluate the condition
      switch (condition.operator) {
        case "equals":
          conditionMet = fieldValue === condition.value;
          break;
        case "not_equals":
          conditionMet = fieldValue !== condition.value;
          break;
        case "contains":
          if (Array.isArray(fieldValue)) {
            conditionMet = fieldValue.includes(condition.value);
          } else if (typeof fieldValue === "string") {
            conditionMet = fieldValue.includes(String(condition.value));
          }
          break;
        case "greater_than":
          conditionMet = fieldValue > condition.value;
          break;
        case "less_than":
          conditionMet = fieldValue < condition.value;
          break;
        case "is_empty":
          conditionMet = fieldValue === undefined || fieldValue === null || fieldValue === "" || 
                        (Array.isArray(fieldValue) && fieldValue.length === 0);
          break;
        case "is_not_empty":
          conditionMet = !(fieldValue === undefined || fieldValue === null || fieldValue === "" || 
                          (Array.isArray(fieldValue) && fieldValue.length === 0));
          break;
      }

      // Initialize the target field state if not already done
      if (!result[targetFieldId]) {
        result[targetFieldId] = {
          visible: true,
          required: false,
          disabled: false,
        };
      }

      // Apply the action if condition is met
      if (conditionMet) {
        switch (action.type) {
          case "show":
            result[targetFieldId].visible = true;
            break;
          case "hide":
            result[targetFieldId].visible = false;
            break;
          case "require":
            result[targetFieldId].required = true;
            break;
          case "set_value":
            result[targetFieldId].value = action.value;
            break;
          case "disable":
            result[targetFieldId].disabled = true;
            break;
          case "enable":
            result[targetFieldId].disabled = false;
            break;
        }
      }
    });

    return result;
  }

  getDefaultValue(field: CustomField): any {
    if (field.defaultValue !== undefined) {
      return field.defaultValue;
    }

    switch (field.type) {
      case "text":
      case "textarea":
      case "email":
      case "url":
      case "phone":
        return "";
      case "number":
      case "decimal":
      case "currency":
      case "percentage":
        return null;
      case "date":
      case "datetime":
        return null;
      case "boolean":
        return false;
      case "select":
        return "";
      case "multiselect":
        return [];
      case "file":
      case "image":
        return null;
      case "json":
        return "{}";
      default:
        return null;
    }
  }

  exportFields(fields: CustomField[], format: 'json' | 'schema' | 'react' | 'html'): string {
    switch (format) {
      case 'json':
        return JSON.stringify(fields, null, 2);
      case 'schema':
        return this.generateJsonSchema(fields);
      case 'react':
        return this.generateReactCode(fields);
      case 'html':
        return this.generateHtmlForm(fields);
      default:
        return JSON.stringify(fields, null, 2);
    }
  }

  importFields(data: string, format: 'json' | 'schema'): CustomField[] {
    try {
      if (format === 'json') {
        const parsed = JSON.parse(data);
        if (Array.isArray(parsed)) {
          return parsed;
        }
      } else if (format === 'schema') {
        // Convert JSON schema to custom fields
        const schema = JSON.parse(data);
        return this.convertSchemaToFields(schema);
      }
      throw new Error('Invalid format or data structure');
    } catch (e) {
      console.error('Error importing fields:', e);
      throw new Error('Failed to import fields: ' + (e as Error).message);
    }
  }

  private generateJsonSchema(fields: CustomField[]): string {
    const properties: Record<string, any> = {};
    const required: string[] = [];

    fields.forEach(field => {
      if (field.isRequired) {
        required.push(field.name);
      }

      let property: Record<string, any> = {
        title: field.label,
        description: field.description || undefined,
      };

      switch (field.type) {
        case 'text':
        case 'textarea':
        case 'email':
        case 'url':
        case 'phone':
          property.type = 'string';
          if (field.validation.minLength !== undefined) {
            property.minLength = field.validation.minLength;
          }
          if (field.validation.maxLength !== undefined) {
            property.maxLength = field.validation.maxLength;
          }
          if (field.validation.pattern) {
            property.pattern = field.validation.pattern;
          }
          if (field.type === 'email') {
            property.format = 'email';
          }
          if (field.type === 'url') {
            property.format = 'uri';
          }
          break;
        case 'number':
        case 'decimal':
        case 'currency':
        case 'percentage':
          property.type = field.type === 'decimal' || field.type === 'currency' || field.type === 'percentage' 
            ? 'number' 
            : 'integer';
          if (field.validation.minValue !== undefined) {
            property.minimum = field.validation.minValue;
          }
          if (field.validation.maxValue !== undefined) {
            property.maximum = field.validation.maxValue;
          }
          break;
        case 'date':
        case 'datetime':
          property.type = 'string';
          property.format = field.type === 'date' ? 'date' : 'date-time';
          break;
        case 'boolean':
          property.type = 'boolean';
          break;
        case 'select':
          property.type = 'string';
          if (field.options && field.options.length > 0) {
            property.enum = field.options.map(opt => opt.value);
            property.enumNames = field.options.map(opt => opt.label);
          }
          break;
        case 'multiselect':
          property.type = 'array';
          property.items = {
            type: 'string',
            enum: field.options?.map(opt => opt.value) || [],
            enumNames: field.options?.map(opt => opt.label) || [],
          };
          if (field.validation.minLength !== undefined) {
            property.minItems = field.validation.minLength;
          }
          if (field.validation.maxLength !== undefined) {
            property.maxItems = field.validation.maxLength;
          }
          property.uniqueItems = true;
          break;
        case 'file':
        case 'image':
          property.type = 'string';
          property.format = 'binary';
          break;
        case 'json':
          property.type = 'object';
          break;
      }

      properties[field.name] = property;
    });

    const schema = {
      type: 'object',
      required: required.length > 0 ? required : undefined,
      properties
    };

    return JSON.stringify(schema, null, 2);
  }

  private convertSchemaToFields(schema: any): CustomField[] {
    const fields: CustomField[] = [];
    const properties = schema.properties || {};
    const required = schema.required || [];

    Object.entries(properties).forEach(([name, prop]: [string, any], index) => {
      const isRequired = required.includes(name);
      let field: CustomField = {
        id: `CF-${Date.now()}-${index}`,
        name,
        label: prop.title || name,
        description: prop.description || '',
        isRequired,
        isUnique: false,
        displayOrder: index,
        isActive: true,
        validation: {},
      } as CustomField;

      if (prop.type === 'string') {
        if (prop.format === 'email') {
          field.type = 'email';
        } else if (prop.format === 'uri') {
          field.type = 'url';
        } else if (prop.format === 'date') {
          field.type = 'date';
        } else if (prop.format === 'date-time') {
          field.type = 'datetime';
        } else if (prop.format === 'binary') {
          field.type = 'file';
        } else if (prop.enum) {
          field.type = 'select';
          field.options = prop.enum.map((value: string, i: number) => ({
            value,
            label: prop.enumNames?.[i] || value,
            isActive: true
          }));
        } else {
          field.type = prop.maxLength && prop.maxLength > 100 ? 'textarea' : 'text';
        }

        if (prop.minLength !== undefined) {
          field.validation.minLength = prop.minLength;
        }
        if (prop.maxLength !== undefined) {
          field.validation.maxLength = prop.maxLength;
        }
        if (prop.pattern) {
          field.validation.pattern = prop.pattern;
        }
      } else if (prop.type === 'integer' || prop.type === 'number') {
        field.type = prop.type === 'integer' ? 'number' : 'decimal';
        
        if (prop.minimum !== undefined) {
          field.validation.minValue = prop.minimum;
        }
        if (prop.maximum !== undefined) {
          field.validation.maxValue = prop.maximum;
        }
      } else if (prop.type === 'boolean') {
        field.type = 'boolean';
      } else if (prop.type === 'array') {
        field.type = 'multiselect';
        
        if (prop.items?.enum) {
          field.options = prop.items.enum.map((value: string, i: number) => ({
            value,
            label: prop.items.enumNames?.[i] || value,
            isActive: true
          }));
        }
        
        if (prop.minItems !== undefined) {
          field.validation.minLength = prop.minItems;
        }
        if (prop.maxItems !== undefined) {
          field.validation.maxLength = prop.maxItems;
        }
      } else if (prop.type === 'object') {
        field.type = 'json';
      }

      fields.push(field);
    });

    return fields;
  }

  private generateReactCode(fields: CustomField[]): string {
    let imports = `import { useState } from 'react';\nimport { z } from 'zod';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\n`;
    imports += `import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';\n`;
    imports += `import { Input } from '@/components/ui/input';\n`;
    imports += `import { Textarea } from '@/components/ui/textarea';\n`;
    imports += `import { Checkbox } from '@/components/ui/checkbox';\n`;
    imports += `import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\n`;
    imports += `import { Button } from '@/components/ui/button';\n`;
    imports += `import { Calendar } from '@/components/ui/calendar';\n`;
    imports += `import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\n`;
    imports += `import { cn } from '@/lib/utils';\n`;
    imports += `import { format } from 'date-fns';\n`;
    imports += `import { CalendarIcon } from 'lucide-react';\n\n`;

    // Generate Zod schema
    let zodSchema = `const formSchema = z.object({\n`;
    fields.forEach((field, index) => {
      let schema = '';
      switch (field.type) {
        case 'text':
        case 'textarea':
        case 'email':
        case 'url':
        case 'phone':
          schema = `z.string()`;
          if (field.isRequired) {
            schema += `.min(1, { message: '${field.label} is required' })`;
          } else {
            schema += `.optional()`;
          }
          if (field.validation.minLength) {
            schema += `.min(${field.validation.minLength}, { message: '${field.label} must be at least ${field.validation.minLength} characters' })`;
          }
          if (field.validation.maxLength) {
            schema += `.max(${field.validation.maxLength}, { message: '${field.label} must be at most ${field.validation.maxLength} characters' })`;
          }
          if (field.type === 'email') {
            schema += `.email({ message: 'Please enter a valid email address' })`;
          }
          if (field.type === 'url') {
            schema += `.url({ message: 'Please enter a valid URL' })`;
          }
          if (field.validation.pattern) {
            schema += `.regex(new RegExp('${field.validation.pattern}'), { message: '${field.validation.errorMessage || 'Invalid format'}' })`;
          }
          break;
        case 'number':
        case 'decimal':
        case 'currency':
        case 'percentage':
          schema = field.type === 'number' ? `z.number().int()` : `z.number()`;
          if (field.isRequired) {
            schema = `z.preprocess(val => Number(val), ${schema})`;
          } else {
            schema = `z.preprocess(val => val === '' ? undefined : Number(val), ${schema}.optional())`;
          }
          if (field.validation.minValue !== undefined) {
            schema += `.min(${field.validation.minValue}, { message: '${field.label} must be at least ${field.validation.minValue}' })`;
          }
          if (field.validation.maxValue !== undefined) {
            schema += `.max(${field.validation.maxValue}, { message: '${field.label} must be at most ${field.validation.maxValue}' })`;
          }
          break;
        case 'date':
        case 'datetime':
          schema = `z.date()`;
          if (!field.isRequired) {
            schema += `.optional()`;
          }
          break;
        case 'boolean':
          schema = field.isRequired ? `z.boolean().refine(val => val === true, { message: '${field.label} is required' })` : `z.boolean().optional()`;
          break;
        case 'select':
          schema = `z.string()`;
          if (field.isRequired) {
            schema += `.min(1, { message: '${field.label} is required' })`;
          } else {
            schema += `.optional()`;
          }
          break;
        case 'multiselect':
          schema = `z.array(z.string())`;
          if (field.isRequired) {
            schema += `.min(1, { message: 'Please select at least one option' })`;
          }
          if (field.validation.minLength) {
            schema += `.min(${field.validation.minLength}, { message: 'Please select at least ${field.validation.minLength} options' })`;
          }
          if (field.validation.maxLength) {
            schema += `.max(${field.validation.maxLength}, { message: 'Please select at most ${field.validation.maxLength} options' })`;
          }
          break;
        case 'file':
        case 'image':
          schema = field.isRequired ? `z.instanceof(File, { message: '${field.label} is required' })` : `z.instanceof(File).optional()`;
          break;
        case 'json':
          schema = `z.string().refine(val => { try { JSON.parse(val); return true; } catch { return false; } }, { message: 'Please enter valid JSON' })`;
          if (!field.isRequired) {
            schema += `.optional()`;
          }
          break;
      }
      zodSchema += `  ${field.name}: ${schema},${index < fields.length - 1 ? '\n' : ''}`;
    });
    zodSchema += `\n});\n\n`;

    // Generate form component
    let formComponent = `export function DynamicForm() {\n`;
    formComponent += `  const form = useForm({\n`;
    formComponent += `    resolver: zodResolver(formSchema),\n`;
    formComponent += `    defaultValues: {\n`;
    fields.forEach((field, index) => {
      let defaultValue = 'undefined';
      switch (field.type) {
        case 'text':
        case 'textarea':
        case 'email':
        case 'url':
        case 'phone':
        case 'select':
          defaultValue = field.defaultValue ? `"${field.defaultValue}"` : `""`;
          break;
        case 'number':
        case 'decimal':
        case 'currency':
        case 'percentage':
          defaultValue = field.defaultValue !== undefined ? field.defaultValue : 'undefined';
          break;
        case 'date':
        case 'datetime':
          defaultValue = field.defaultValue ? `new Date("${field.defaultValue}")` : 'undefined';
          break;
        case 'boolean':
          defaultValue = field.defaultValue ? 'true' : 'false';
          break;
        case 'multiselect':
          defaultValue = field.defaultValue ? JSON.stringify(field.defaultValue) : '[]';
          break;
        case 'json':
          defaultValue = field.defaultValue ? `\`${field.defaultValue}\`` : '"{}"';
          break;
        case 'file':
        case 'image':
          defaultValue = 'undefined';
          break;
      }
      formComponent += `      ${field.name}: ${defaultValue},${index < fields.length - 1 ? '\n' : ''}`;
    });
    formComponent += `\n    },\n`;
    formComponent += `  });\n\n`;

    formComponent += `  function onSubmit(values) {\n`;
    formComponent += `    console.log(values);\n`;
    formComponent += `    // Handle form submission\n`;
    formComponent += `  }\n\n`;

    formComponent += `  return (\n`;
    formComponent += `    <Form {...form}>\n`;
    formComponent += `      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">\n`;

    // Group fields by groupName
    const fieldGroups: Record<string, CustomField[]> = {};
    fields.forEach(field => {
      const group = field.groupName || 'default';
      if (!fieldGroups[group]) {
        fieldGroups[group] = [];
      }
      fieldGroups[group].push(field);
    });

    // Generate form fields by group
    Object.entries(fieldGroups).forEach(([groupName, groupFields]) => {
      if (groupName !== 'default') {
        formComponent += `        <div className="border rounded-md p-4">\n`;
        formComponent += `          <h3 className="text-lg font-medium mb-4">${groupName}</h3>\n`;
      }

      groupFields.forEach(field => {
        formComponent += `        <FormField\n`;
        formComponent += `          control={form.control}\n`;
        formComponent += `          name="${field.name}"\n`;
        formComponent += `          render={({ field: formField }) => (\n`;
        formComponent += `            <FormItem>\n`;
        formComponent += `              <FormLabel>${field.label}</FormLabel>\n`;

        switch (field.type) {
          case 'text':
          case 'email':
          case 'url':
          case 'phone':
          case 'currency':
          case 'percentage':
            formComponent += `              <FormControl>\n`;
            formComponent += `                <Input placeholder="${field.label}" {...formField} />\n`;
            formComponent += `              </FormControl>\n`;
            break;
          case 'textarea':
            formComponent += `              <FormControl>\n`;
            formComponent += `                <Textarea placeholder="${field.label}" {...formField} />\n`;
            formComponent += `              </FormControl>\n`;
            break;
          case 'number':
          case 'decimal':
            formComponent += `              <FormControl>\n`;
            formComponent += `                <Input type="number" placeholder="${field.label}" {...formField} />\n`;
            formComponent += `              </FormControl>\n`;
            break;
          case 'date':
          case 'datetime':
            formComponent += `              <Popover>\n`;
            formComponent += `                <PopoverTrigger asChild>\n`;
            formComponent += `                  <FormControl>\n`;
            formComponent += `                    <Button\n`;
            formComponent += `                      variant="outline"\n`;
            formComponent += `                      className={cn(\n`;
            formComponent += `                        "w-full pl-3 text-left font-normal",\n`;
            formComponent += `                        !formField.value && "text-muted-foreground"\n`;
            formComponent += `                      )}\n`;
            formComponent += `                    >\n`;
            formComponent += `                      {formField.value ? (\n`;
            formComponent += `                        format(formField.value, "${field.type === 'date' ? 'PPP' : 'PPP p'}")\n`;
            formComponent += `                      ) : (\n`;
            formComponent += `                        <span>Select ${field.type === 'date' ? 'date' : 'date and time'}</span>\n`;
            formComponent += `                      )}\n`;
            formComponent += `                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />\n`;
            formComponent += `                    </Button>\n`;
            formComponent += `                  </FormControl>\n`;
            formComponent += `                </PopoverTrigger>\n`;
            formComponent += `                <PopoverContent className="w-auto p-0" align="start">\n`;
            formComponent += `                  <Calendar\n`;
            formComponent += `                    mode="single"\n`;
            formComponent += `                    selected={formField.value}\n`;
            formComponent += `                    onSelect={formField.onChange}\n`;
            formComponent += `                    initialFocus\n`;
            formComponent += `                  />\n`;
            formComponent += `                </PopoverContent>\n`;
            formComponent += `              </Popover>\n`;
            break;
          case 'boolean':
            formComponent += `              <FormControl>\n`;
            formComponent += `                <Checkbox\n`;
            formComponent += `                  checked={formField.value}\n`;
            formComponent += `                  onCheckedChange={formField.onChange}\n`;
            formComponent += `                />\n`;
            formComponent += `              </FormControl>\n`;
            break;
          case 'select':
            formComponent += `              <Select onValueChange={formField.onChange} defaultValue={formField.value}>\n`;
            formComponent += `                <FormControl>\n`;
            formComponent += `                  <SelectTrigger>\n`;
            formComponent += `                    <SelectValue placeholder="Select ${field.label}" />\n`;
            formComponent += `                  </SelectTrigger>\n`;
            formComponent += `                </FormControl>\n`;
            formComponent += `                <SelectContent>\n`;
            if (field.options) {
              field.options.forEach(option => {
                formComponent += `                  <SelectItem value="${option.value}">${option.label}</SelectItem>\n`;
              });
            }
            formComponent += `                </SelectContent>\n`;
            formComponent += `              </Select>\n`;
            break;
          case 'multiselect':
            // This would need a custom multiselect component
            formComponent += `              <FormControl>\n`;
            formComponent += `                {/* Implement multiselect component */}\n`;
            formComponent += `                <div className="border rounded-md p-2">\n`;
            formComponent += `                  <div className="flex flex-wrap gap-2">\n`;
            formComponent += `                    {field.options?.map((option) => (\n`;
            formComponent += `                      <div key={option.value} className="flex items-center space-x-2">\n`;
            formComponent += `                        <Checkbox\n`;
            formComponent += `                          id={option.value}\n`;
            formComponent += `                          checked={formField.value?.includes(option.value)}\n`;
            formComponent += `                          onCheckedChange={(checked) => {\n`;
            formComponent += `                            const updatedValue = checked\n`;
            formComponent += `                              ? [...(formField.value || []), option.value]\n`;
            formComponent += `                              : (formField.value || []).filter((val) => val !== option.value);\n`;
            formComponent += `                            formField.onChange(updatedValue);\n`;
            formComponent += `                          }}\n`;
            formComponent += `                        />\n`;
            formComponent += `                        <label htmlFor={option.value}>{option.label}</label>\n`;
            formComponent += `                      </div>\n`;
            formComponent += `                    ))}\n`;
            formComponent += `                  </div>\n`;
            formComponent += `                </div>\n`;
            formComponent += `              </FormControl>\n`;
            break;
          case 'file':
          case 'image':
            formComponent += `              <FormControl>\n`;
            formComponent += `                <Input type="file" {...formField} value={undefined} onChange={(e) => {\n`;
            formComponent += `                  formField.onChange(e.target.files?.[0]);\n`;
            formComponent += `                }} />\n`;
            formComponent += `              </FormControl>\n`;
            break;
          case 'json':
            formComponent += `              <FormControl>\n`;
            formComponent += `                <Textarea\n`;
            formComponent += `                  placeholder="Enter JSON"\n`;
            formComponent += `                  className="font-mono"\n`;
            formComponent += `                  rows={5}\n`;
            formComponent += `                  {...formField}\n`;
            formComponent += `                />\n`;
            formComponent += `              </FormControl>\n`;
            break;
        }

        if (field.description) {
          formComponent += `              <FormDescription>${field.description}</FormDescription>\n`;
        }
        formComponent += `              <FormMessage />\n`;
        formComponent += `            </FormItem>\n`;
        formComponent += `          )}\n`;
        formComponent += `        />\n`;
      });

      if (groupName !== 'default') {
        formComponent += `        </div>\n`;
      }
    });

    formComponent += `        <Button type="submit">Submit</Button>\n`;
    formComponent += `      </form>\n`;
    formComponent += `    </Form>\n`;
    formComponent += `  );\n`;
    formComponent += `}\n`;

    return imports + zodSchema + formComponent;
  }

  private generateHtmlForm(fields: CustomField[]): string {
    let html = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Dynamic Form</title>
  <style>
    body { font-family: system-ui, sans-serif; line-height: 1.5; max-width: 800px; margin: 0 auto; padding: 1rem; }
    .form-group { margin-bottom: 1rem; }
    label { display: block; margin-bottom: 0.5rem; font-weight: 500; }
    input, select, textarea { width: 100%; padding: 0.5rem; border: 1px solid #ccc; border-radius: 4px; font-size: 1rem; }
    textarea { min-height: 100px; }
    .checkbox-group { display: flex; align-items: center; gap: 0.5rem; }
    .checkbox-group input { width: auto; }
    .checkbox-group label { margin-bottom: 0; font-weight: normal; }
    .form-description { font-size: 0.875rem; color: #666; margin-top: 0.25rem; }
    .form-error { font-size: 0.875rem; color: #e11d48; margin-top: 0.25rem; }
    button { background-color: #3b82f6; color: white; border: none; padding: 0.5rem 1rem; border-radius: 4px; font-size: 1rem; cursor: pointer; }
    button:hover { background-color: #2563eb; }
    fieldset { border: 1px solid #ddd; padding: 1rem; margin-bottom: 1rem; border-radius: 4px; }
    legend { padding: 0 0.5rem; font-weight: 500; }
    .option-group { margin-bottom: 0.5rem; }
  </style>
</head>
<body>
  <h1>Dynamic Form</h1>
  <form id="dynamicForm">
`;

    // Group fields by groupName
    const fieldGroups: Record<string, CustomField[]> = {};
    fields.forEach(field => {
      const group = field.groupName || 'default';
      if (!fieldGroups[group]) {
        fieldGroups[group] = [];
      }
      fieldGroups[group].push(field);
    });

    // Generate form fields by group
    Object.entries(fieldGroups).forEach(([groupName, groupFields]) => {
      if (groupName !== 'default') {
        html += `    <fieldset>\n`;
        html += `      <legend>${groupName}</legend>\n`;
      }

      groupFields.forEach(field => {
        html += `    <div class="form-group">\n`;
        html += `      <label for="${field.name}">${field.label}${field.isRequired ? ' *' : ''}</label>\n`;

        switch (field.type) {
          case 'text':
          case 'email':
          case 'url':
          case 'phone':
            html += `      <input type="${field.type === 'text' ? 'text' : field.type}" id="${field.name}" name="${field.name}" ${field.isRequired ? 'required' : ''} ${field.validation.minLength ? `minlength="${field.validation.minLength}"` : ''} ${field.validation.maxLength ? `maxlength="${field.validation.maxLength}"` : ''} ${field.validation.pattern ? `pattern="${field.validation.pattern}"` : ''} ${field.defaultValue ? `value="${field.defaultValue}"` : ''}>\n`;
            break;
          case 'textarea':
            html += `      <textarea id="${field.name}" name="${field.name}" ${field.isRequired ? 'required' : ''} ${field.validation.minLength ? `minlength="${field.validation.minLength}"` : ''} ${field.validation.maxLength ? `maxlength="${field.validation.maxLength}"` : ''}>${field.defaultValue || ''}</textarea>\n`;
            break;
          case 'number':
          case 'decimal':
            html += `      <input type="number" id="${field.name}" name="${field.name}" ${field.type === 'decimal' ? 'step="0.01"' : 'step="1"'} ${field.isRequired ? 'required' : ''} ${field.validation.minValue !== undefined ? `min="${field.validation.minValue}"` : ''} ${field.validation.maxValue !== undefined ? `max="${field.validation.maxValue}"` : ''} ${field.defaultValue !== undefined ? `value="${field.defaultValue}"` : ''}>\n`;
            break;
          case 'currency':
            html += `      <div style="position: relative;">\n`;
            html += `        <span style="position: absolute; left: 0.5rem; top: 0.5rem;">$</span>\n`;
            html += `        <input type="number" id="${field.name}" name="${field.name}" step="0.01" style="padding-left: 1.5rem;" ${field.isRequired ? 'required' : ''} ${field.validation.minValue !== undefined ? `min="${field.validation.minValue}"` : ''} ${field.validation.maxValue !== undefined ? `max="${field.validation.maxValue}"` : ''} ${field.defaultValue !== undefined ? `value="${field.defaultValue}"` : ''}>\n`;
            html += `      </div>\n`;
            break;
          case 'percentage':
            html += `      <div style="position: relative;">\n`;
            html += `        <input type="number" id="${field.name}" name="${field.name}" step="0.01" style="padding-right: 1.5rem;" ${field.isRequired ? 'required' : ''} ${field.validation.minValue !== undefined ? `min="${field.validation.minValue}"` : ''} ${field.validation.maxValue !== undefined ? `max="${field.validation.maxValue}"` : ''} ${field.defaultValue !== undefined ? `value="${field.defaultValue}"` : ''}>\n`;
            html += `        <span style="position: absolute; right: 0.5rem; top: 0.5rem;">%</span>\n`;
            html += `      </div>\n`;
            break;
          case 'date':
            html += `      <input type="date" id="${field.name}" name="${field.name}" ${field.isRequired ? 'required' : ''} ${field.validation.minValue ? `min="${field.validation.minValue}"` : ''} ${field.validation.maxValue ? `max="${field.validation.maxValue}"` : ''} ${field.defaultValue ? `value="${field.defaultValue}"` : ''}>\n`;
            break;
          case 'datetime':
            html += `      <input type="datetime-local" id="${field.name}" name="${field.name}" ${field.isRequired ? 'required' : ''} ${field.validation.minValue ? `min="${field.validation.minValue}"` : ''} ${field.validation.maxValue ? `max="${field.validation.maxValue}"` : ''} ${field.defaultValue ? `value="${field.defaultValue}"` : ''}>\n`;
            break;
          case 'boolean':
            html += `      <div class="checkbox-group">\n`;
            html += `        <input type="checkbox" id="${field.name}" name="${field.name}" ${field.defaultValue ? 'checked' : ''}>\n`;
            html += `        <label for="${field.name}">Yes</label>\n`;
            html += `      </div>\n`;
            break;
          case 'select':
            html += `      <select id="${field.name}" name="${field.name}" ${field.isRequired ? 'required' : ''}>\n`;
            html += `        <option value="">Select ${field.label}</option>\n`;
            if (field.options) {
              field.options.forEach(option => {
                html += `        <option value="${option.value}" ${field.defaultValue === option.value ? 'selected' : ''}>${option.label}</option>\n`;
              });
            }
            html += `      </select>\n`;
            break;
          case 'multiselect':
            html += `      <select id="${field.name}" name="${field.name}" multiple ${field.isRequired ? 'required' : ''} ${field.validation.minLength ? `data-min-options="${field.validation.minLength}"` : ''} ${field.validation.maxLength ? `data-max-options="${field.validation.maxLength}"` : ''}>\n`;
            if (field.options) {
              field.options.forEach(option => {
                const isSelected = Array.isArray(field.defaultValue) && field.defaultValue.includes(option.value);
                html += `        <option value="${option.value}" ${isSelected ? 'selected' : ''}>${option.label}</option>\n`;
              });
            }
            html += `      </select>\n`;
            break;
          case 'file':
            html += `      <input type="file" id="${field.name}" name="${field.name}" ${field.isRequired ? 'required' : ''}>\n`;
            break;
          case 'image':
            html += `      <input type="file" id="${field.name}" name="${field.name}" accept="image/*" ${field.isRequired ? 'required' : ''}>\n`;
            break;
          case 'json':
            html += `      <textarea id="${field.name}" name="${field.name}" class="json-input" ${field.isRequired ? 'required' : ''}>${field.defaultValue || '{}'}</textarea>\n`;
            break;
        }

        if (field.description) {
          html += `      <div class="form-description">${field.description}</div>\n`;
        }
        html += `      <div class="form-error" id="${field.name}-error"></div>\n`;
        html += `    </div>\n`;
      });

      if (groupName !== 'default') {
        html += `    </fieldset>\n`;
      }
    });

    html += `    <button type="submit">Submit</button>
  </form>

  <script>
    document.getElementById('dynamicForm').addEventListener('submit', function(e) {
      e.preventDefault();
      const formData = new FormData(this);
      const data = {};
      
      for (const [key, value] of formData.entries()) {
        data[key] = value;
      }
      
      console.log('Form data:', data);
      alert('Form submitted successfully!');
    });
    
    // Basic JSON validation for JSON fields
    document.querySelectorAll('.json-input').forEach(function(input) {
      input.addEventListener('blur', function() {
        try {
          JSON.parse(this.value);
          document.getElementById(this.id + '-error').textContent = '';
        } catch (e) {
          document.getElementById(this.id + '-error').textContent = 'Invalid JSON format';
        }
      });
    });
  </script>
</body>
</html>`;

    return html;
  }
}