import { FieldTypeOption } from "./types";
import { CustomFieldType } from "../asset-types/types";

export const FIELD_TYPES: FieldTypeOption[] = [
  {
    value: "text",
    label: "Text",
    description: "Single line text input",
    icon: "type",
  },
  {
    value: "textarea",
    label: "Text Area",
    description: "Multi-line text input",
    icon: "text",
  },
  {
    value: "number",
    label: "Number",
    description: "Integer values",
    icon: "hash",
  },
  {
    value: "decimal",
    label: "Decimal",
    description: "Decimal/float values",
    icon: "percent",
  },
  {
    value: "date",
    label: "Date",
    description: "Date picker",
    icon: "calendar",
  },
  {
    value: "datetime",
    label: "Date & Time",
    description: "Date and time picker",
    icon: "clock",
  },
  {
    value: "boolean",
    label: "Boolean",
    description: "Yes/No, True/False",
    icon: "toggle-left",
  },
  {
    value: "select",
    label: "Select",
    description: "Single option from dropdown",
    icon: "list",
  },
  {
    value: "multiselect",
    label: "Multi-Select",
    description: "Multiple options from dropdown",
    icon: "list-checks",
  },
  {
    value: "email",
    label: "Email",
    description: "Email address",
    icon: "mail",
  },
  {
    value: "url",
    label: "URL",
    description: "Web address",
    icon: "link",
  },
  {
    value: "phone",
    label: "Phone",
    description: "Phone number",
    icon: "phone",
  },
  {
    value: "currency",
    label: "Currency",
    description: "Monetary values",
    icon: "dollar-sign",
  },
  {
    value: "percentage",
    label: "Percentage",
    description: "Percentage values",
    icon: "percent",
  },
  {
    value: "file",
    label: "File",
    description: "File upload",
    icon: "file",
  },
  {
    value: "image",
    label: "Image",
    description: "Image upload",
    icon: "image",
  },
  {
    value: "json",
    label: "JSON",
    description: "JSON data structure",
    icon: "braces",
  },
];

export const DEFAULT_FIELD: Omit<CustomFieldType, "id"> = {
  name: "",
  label: "",
  type: "text",
  description: "",
  isRequired: false,
  isUnique: false,
  validation: {},
  displayOrder: 0,
  isActive: true,
};

export const VALIDATION_OPERATORS = [
  { value: "equals", label: "Equals" },
  { value: "not_equals", label: "Not Equals" },
  { value: "contains", label: "Contains" },
  { value: "greater_than", label: "Greater Than" },
  { value: "less_than", label: "Less Than" },
  { value: "is_empty", label: "Is Empty" },
  { value: "is_not_empty", label: "Is Not Empty" },
];

export const LOGIC_ACTIONS = [
  { value: "show", label: "Show Field" },
  { value: "hide", label: "Hide Field" },
  { value: "require", label: "Make Required" },
  { value: "set_value", label: "Set Value" },
  { value: "disable", label: "Disable Field" },
  { value: "enable", label: "Enable Field" },
];

export const FIELD_TYPE_VALIDATION_MAP: Record<CustomFieldType, string[]> = {
  text: ["minLength", "maxLength", "pattern"],
  textarea: ["minLength", "maxLength"],
  number: ["minValue", "maxValue"],
  decimal: ["minValue", "maxValue"],
  date: ["minValue", "maxValue"],
  datetime: ["minValue", "maxValue"],
  boolean: [],
  select: [],
  multiselect: ["minLength", "maxLength"],
  email: ["pattern"],
  url: ["pattern"],
  phone: ["pattern"],
  currency: ["minValue", "maxValue"],
  percentage: ["minValue", "maxValue"],
  file: ["maxLength"],
  image: ["maxLength"],
  json: ["customValidator"],
};