import { CustomField, CustomFieldType, FieldOption, FieldValidation, ConditionalLogic } from "../asset-types/types";

export interface CustomFieldEditorProps {
  field: CustomField | null;
  onSave: (field: CustomField) => void;
  onCancel: () => void;
}

export interface CustomFieldsListProps {
  fields: CustomField[];
  onEdit: (field: CustomField) => void;
  onDelete: (fieldId: string) => void;
  onAdd: () => void;
  onReorder: (reorderedFields: CustomField[]) => void;
}

export interface FormGeneratorProps {
  fields: CustomField[];
  values: Record<string, any>;
  onChange: (values: Record<string, any>) => void;
  readOnly?: boolean;
  showGroups?: boolean;
}

export interface FieldGroupProps {
  name: string;
  fields: CustomField[];
  values: Record<string, any>;
  onChange: (fieldId: string, value: any) => void;
  readOnly?: boolean;
}

export interface DynamicFieldProps {
  field: CustomField;
  value: any;
  onChange: (value: any) => void;
  error?: string;
  readOnly?: boolean;
}

export interface FieldTypeOption {
  value: CustomFieldType;
  label: string;
  description: string;
  icon?: string;
}

export interface ValidationRuleProps {
  validation: FieldValidation;
  fieldType: CustomFieldType;
  onChange: (validation: FieldValidation) => void;
}

export interface ConditionalLogicEditorProps {
  logic: ConditionalLogic[];
  availableFields: CustomField[];
  onChange: (logic: ConditionalLogic[]) => void;
}

export interface FieldOptionsEditorProps {
  options: FieldOption[];
  onChange: (options: FieldOption[]) => void;
}

export interface CustomFieldContext {
  fields: CustomField[];
  addField: (field: Omit<CustomField, "id">) => void;
  updateField: (id: string, updates: Partial<CustomField>) => void;
  deleteField: (id: string) => void;
  reorderFields: (reorderedFields: CustomField[]) => void;
}

export interface FormPreviewProps {
  fields: CustomField[];
}

export interface FormExportOptions {
  format: 'json' | 'schema' | 'react' | 'html';
  includeValidation: boolean;
  includeConditionalLogic: boolean;
}

export interface FormImportOptions {
  format: 'json' | 'schema';
  mergeStrategy: 'replace' | 'merge' | 'append';
}