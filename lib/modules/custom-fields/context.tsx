"use client";

import React, { createContext, useContext, useState, useCallback } from "react";
import { CustomField } from "../asset-types/types";
import { CustomFieldContext } from "./types";
import { CustomFieldsService } from "./service";

const CustomFieldsContext = createContext<CustomFieldContext | undefined>(undefined);

export function CustomFieldsProvider({
  initialFields = [],
  children,
}: {
  initialFields?: CustomField[];
  children: React.ReactNode;
}) {
  const [fields, setFields] = useState<CustomField[]>(initialFields);
  const service = CustomFieldsService.getInstance();

  const addField = useCallback((field: Omit<CustomField, "id">) => {
    const newField = service.createField(field);
    setFields((prev) => [...prev, newField]);
    return newField;
  }, [service]);

  const updateField = useCallback((id: string, updates: Partial<CustomField>) => {
    setFields((prev) =>
      prev.map((field) => (field.id === id ? { ...field, ...updates } : field))
    );
  }, []);

  const deleteField = useCallback((id: string) => {
    setFields((prev) => prev.filter((field) => field.id !== id));
  }, []);

  const reorderFields = useCallback((reorderedFields: CustomField[]) => {
    setFields(reorderedFields);
  }, []);

  const value = {
    fields,
    addField,
    updateField,
    deleteField,
    reorderFields,
  };

  return (
    <CustomFieldsContext.Provider value={value}>
      {children}
    </CustomFieldsContext.Provider>
  );
}

export function useCustomFields() {
  const context = useContext(CustomFieldsContext);
  if (context === undefined) {
    throw new Error("useCustomFields must be used within a CustomFieldsProvider");
  }
  return context;
}