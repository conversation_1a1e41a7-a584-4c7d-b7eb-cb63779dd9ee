export interface AssetType {
  id: string
  name: string
  code: string
  description: string
  category: AssetCategory
  subcategory?: string
  icon: string
  color: string
  isActive: boolean
  customFields: CustomField[]
  lifecycleStages: LifecycleStage[]
  maintenanceSchedules: MaintenanceSchedule[]
  depreciationSettings: DepreciationSettings
  tags: string[]
  createdAt: string
  updatedAt: string
  createdBy: string
  version: number
}

export interface AssetCategory {
  id: string
  name: string
  description: string
  parentId?: string
  level: number
  path: string
  isActive: boolean
}

export interface CustomField {
  id: string
  name: string
  label: string
  type: CustomFieldType
  description?: string
  isRequired: boolean
  isUnique: boolean
  defaultValue?: any
  validation: FieldValidation
  options?: FieldOption[]
  conditionalLogic?: ConditionalLogic[]
  displayOrder: number
  groupName?: string
  isActive: boolean
}

export type CustomFieldType =
  | "text"
  | "number"
  | "decimal"
  | "date"
  | "datetime"
  | "boolean"
  | "select"
  | "multiselect"
  | "textarea"
  | "email"
  | "url"
  | "phone"
  | "currency"
  | "percentage"
  | "file"
  | "image"
  | "json"

export interface FieldValidation {
  minLength?: number
  maxLength?: number
  minValue?: number
  maxValue?: number
  pattern?: string
  customValidator?: string
  errorMessage?: string
}

export interface FieldOption {
  value: string
  label: string
  description?: string
  isActive: boolean
  metadata?: Record<string, any>
}

export interface ConditionalLogic {
  condition: LogicCondition
  action: LogicAction
  targetFieldId: string
}

export interface LogicCondition {
  fieldId: string
  operator: "equals" | "not_equals" | "contains" | "greater_than" | "less_than" | "is_empty" | "is_not_empty"
  value: any
}

export interface LogicAction {
  type: "show" | "hide" | "require" | "set_value" | "disable" | "enable"
  value?: any
}

export interface LifecycleStage {
  id: string
  name: string
  code: string
  description: string
  order: number
  isInitial: boolean
  isFinal: boolean
  color: string
  icon: string
  allowedTransitions: string[]
  requiredFields: string[]
  automatedActions: AutomatedAction[]
  notifications: NotificationRule[]
  isActive: boolean
}

export interface AutomatedAction {
  id: string
  name: string
  type: "field_update" | "notification" | "workflow" | "integration" | "calculation"
  trigger: ActionTrigger
  conditions: ActionCondition[]
  actions: ActionStep[]
  isActive: boolean
}

export interface ActionTrigger {
  event: "stage_enter" | "stage_exit" | "field_change" | "time_based" | "manual"
  fieldId?: string
  schedule?: string
}

export interface ActionCondition {
  fieldId: string
  operator: string
  value: any
  logicalOperator?: "AND" | "OR"
}

export interface ActionStep {
  type: "update_field" | "send_notification" | "create_task" | "call_api" | "run_calculation"
  parameters: Record<string, any>
}

export interface NotificationRule {
  id: string
  name: string
  trigger: NotificationTrigger
  recipients: NotificationRecipient[]
  template: NotificationTemplate
  isActive: boolean
}

export interface NotificationTrigger {
  event: "stage_change" | "field_update" | "time_based" | "condition_met"
  conditions?: ActionCondition[]
  schedule?: string
}

export interface NotificationRecipient {
  type: "user" | "role" | "email" | "webhook"
  identifier: string
}

export interface NotificationTemplate {
  subject: string
  body: string
  format: "text" | "html" | "markdown"
  variables: string[]
}

export interface MaintenanceSchedule {
  id: string
  name: string
  description: string
  type: MaintenanceType
  frequency: MaintenanceFrequency
  priority: MaintenancePriority
  estimatedDuration: number
  estimatedCost: number
  requiredSkills: string[]
  requiredParts: RequiredPart[]
  instructions: string
  checklistItems: ChecklistItem[]
  triggers: MaintenanceTrigger[]
  isActive: boolean
}

export type MaintenanceType = "preventive" | "predictive" | "corrective" | "condition_based" | "time_based"

export interface MaintenanceFrequency {
  type: "days" | "weeks" | "months" | "years" | "hours" | "cycles" | "condition"
  interval: number
  startDate?: string
  endDate?: string
  conditions?: MaintenanceCondition[]
}

export interface MaintenanceCondition {
  metric: string
  operator: "greater_than" | "less_than" | "equals" | "between"
  value: number | [number, number]
  unit: string
}

export type MaintenancePriority = "low" | "medium" | "high" | "critical"

export interface RequiredPart {
  partId: string
  partName: string
  quantity: number
  estimatedCost: number
  isOptional: boolean
}

export interface ChecklistItem {
  id: string
  description: string
  isRequired: boolean
  order: number
  category?: string
}

export interface MaintenanceTrigger {
  type: "time" | "usage" | "condition" | "event"
  parameters: Record<string, any>
}

export interface DepreciationSettings {
  method: DepreciationMethod
  usefulLife: number
  usefulLifeUnit: "years" | "months" | "hours" | "cycles"
  salvageValue: number
  salvageValueType: "fixed" | "percentage"
  startDate: string
  customRates?: DepreciationRate[]
  acceleratedDepreciation?: AcceleratedDepreciation
  impairmentSettings?: ImpairmentSettings
  isActive: boolean
}

export type DepreciationMethod =
  | "straight_line"
  | "declining_balance"
  | "double_declining_balance"
  | "sum_of_years_digits"
  | "units_of_production"
  | "custom"

export interface DepreciationRate {
  year: number
  rate: number
  amount?: number
}

export interface AcceleratedDepreciation {
  isEnabled: boolean
  method: "bonus" | "section179" | "custom"
  percentage: number
  maxAmount?: number
  conditions?: string[]
}

export interface ImpairmentSettings {
  isEnabled: boolean
  triggers: ImpairmentTrigger[]
  testFrequency: "annual" | "quarterly" | "monthly" | "event_based"
}

export interface ImpairmentTrigger {
  condition: string
  threshold: number
  action: "flag" | "auto_adjust" | "notify"
}

export interface AssetTypeTemplate {
  id: string
  name: string
  description: string
  category: string
  assetType: Partial<AssetType>
  isPublic: boolean
  createdBy: string
  createdAt: string
  usageCount: number
  rating: number
  tags: string[]
}

export interface AssetTypeMetrics {
  totalTypes: number
  activeTypes: number
  totalAssets: number
  assetsByType: Record<string, number>
  customFieldsUsage: Record<string, number>
  lifecycleDistribution: Record<string, number>
  maintenanceScheduleCount: number
  depreciationMethods: Record<string, number>
}

export interface AssetTypeValidationResult {
  isValid: boolean
  errors: ValidationError[]
  warnings: ValidationWarning[]
}

export interface ValidationError {
  field: string
  message: string
  code: string
  severity: "error" | "warning"
}

export interface ValidationWarning {
  field: string
  message: string
  suggestion?: string
}
