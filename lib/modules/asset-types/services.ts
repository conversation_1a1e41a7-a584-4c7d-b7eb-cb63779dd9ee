import type {
  AssetType,
  AssetCategory,
  CustomField,
  LifecycleStage,
  MaintenanceSchedule,
  DepreciationSettings,
  AssetTypeTemplate,
  AssetTypeMetrics,
  AssetTypeValidationResult,
} from "./types"

export class AssetTypeService {
  private static instance: AssetTypeService
  private assetTypes: AssetType[] = []
  private categories: AssetCategory[] = []
  private templates: AssetTypeTemplate[] = []

  static getInstance(): AssetTypeService {
    if (!AssetTypeService.instance) {
      AssetTypeService.instance = new AssetTypeService()
      AssetTypeService.instance.initializeDefaultData()
    }
    return AssetTypeService.instance
  }

  private initializeDefaultData(): void {
    // Initialize default categories
    this.categories = [
      {
        id: "CAT-001",
        name: "IT Equipment",
        description: "Information Technology assets",
        level: 1,
        path: "/IT Equipment",
        isActive: true,
      },
      {
        id: "CAT-002",
        name: "Vehicles",
        description: "Transportation assets",
        level: 1,
        path: "/Vehicles",
        isActive: true,
      },
      {
        id: "CAT-003",
        name: "Machinery",
        description: "Industrial machinery and equipment",
        level: 1,
        path: "/Machinery",
        isActive: true,
      },
      {
        id: "CAT-004",
        name: "Furniture",
        description: "Office and facility furniture",
        level: 1,
        path: "/Furniture",
        isActive: true,
      },
    ]

    // Initialize sample asset types
    this.assetTypes = [
      {
        id: "AT-001",
        name: "Laptop Computer",
        code: "LAPTOP",
        description: "Portable computing devices for employees",
        category: this.categories[0],
        icon: "laptop",
        color: "#3B82F6",
        isActive: true,
        customFields: this.getDefaultLaptopFields(),
        lifecycleStages: this.getDefaultLifecycleStages(),
        maintenanceSchedules: this.getDefaultMaintenanceSchedules(),
        depreciationSettings: this.getDefaultDepreciationSettings(),
        tags: ["IT", "Computing", "Portable"],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: "system",
        version: 1,
      },
    ]
  }

  // Asset Type CRUD Operations
  async createAssetType(assetType: Omit<AssetType, "id" | "createdAt" | "updatedAt" | "version">): Promise<AssetType> {
    const newAssetType: AssetType = {
      ...assetType,
      id: `AT-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      version: 1,
    }

    const validation = await this.validateAssetType(newAssetType)
    if (!validation.isValid) {
      throw new Error(`Validation failed: ${validation.errors.map((e) => e.message).join(", ")}`)
    }

    this.assetTypes.push(newAssetType)
    return newAssetType
  }

  async updateAssetType(id: string, updates: Partial<AssetType>): Promise<AssetType | null> {
    const index = this.assetTypes.findIndex((at) => at.id === id)
    if (index === -1) return null

    const updatedAssetType = {
      ...this.assetTypes[index],
      ...updates,
      updatedAt: new Date().toISOString(),
      version: this.assetTypes[index].version + 1,
    }

    const validation = await this.validateAssetType(updatedAssetType)
    if (!validation.isValid) {
      throw new Error(`Validation failed: ${validation.errors.map((e) => e.message).join(", ")}`)
    }

    this.assetTypes[index] = updatedAssetType
    return updatedAssetType
  }

  async deleteAssetType(id: string): Promise<boolean> {
    const index = this.assetTypes.findIndex((at) => at.id === id)
    if (index === -1) return false

    // Check if asset type is in use
    const isInUse = await this.isAssetTypeInUse(id)
    if (isInUse) {
      throw new Error("Cannot delete asset type that is currently in use")
    }

    this.assetTypes.splice(index, 1)
    return true
  }

  getAssetType(id: string): AssetType | null {
    return this.assetTypes.find((at) => at.id === id) || null
  }

  getAssetTypes(filters?: {
    category?: string
    isActive?: boolean
    search?: string
  }): AssetType[] {
    let filtered = this.assetTypes

    if (filters?.category) {
      filtered = filtered.filter((at) => at.category.id === filters.category)
    }

    if (filters?.isActive !== undefined) {
      filtered = filtered.filter((at) => at.isActive === filters.isActive)
    }

    if (filters?.search) {
      const search = filters.search.toLowerCase()
      filtered = filtered.filter(
        (at) =>
          at.name.toLowerCase().includes(search) ||
          at.code.toLowerCase().includes(search) ||
          at.description.toLowerCase().includes(search) ||
          at.tags.some((tag) => tag.toLowerCase().includes(search)),
      )
    }

    return filtered.sort((a, b) => a.name.localeCompare(b.name))
  }

  // Custom Fields Management
  async addCustomField(assetTypeId: string, field: Omit<CustomField, "id">): Promise<CustomField | null> {
    const assetType = this.getAssetType(assetTypeId)
    if (!assetType) return null

    const newField: CustomField = {
      ...field,
      id: `CF-${Date.now()}`,
    }

    assetType.customFields.push(newField)
    assetType.updatedAt = new Date().toISOString()
    assetType.version += 1

    return newField
  }

  async updateCustomField(
    assetTypeId: string,
    fieldId: string,
    updates: Partial<CustomField>,
  ): Promise<CustomField | null> {
    const assetType = this.getAssetType(assetTypeId)
    if (!assetType) return null

    const fieldIndex = assetType.customFields.findIndex((f) => f.id === fieldId)
    if (fieldIndex === -1) return null

    assetType.customFields[fieldIndex] = {
      ...assetType.customFields[fieldIndex],
      ...updates,
    }

    assetType.updatedAt = new Date().toISOString()
    assetType.version += 1

    return assetType.customFields[fieldIndex]
  }

  async removeCustomField(assetTypeId: string, fieldId: string): Promise<boolean> {
    const assetType = this.getAssetType(assetTypeId)
    if (!assetType) return false

    const fieldIndex = assetType.customFields.findIndex((f) => f.id === fieldId)
    if (fieldIndex === -1) return false

    // Check if field is in use
    const isInUse = await this.isCustomFieldInUse(assetTypeId, fieldId)
    if (isInUse) {
      throw new Error("Cannot remove custom field that contains data")
    }

    assetType.customFields.splice(fieldIndex, 1)
    assetType.updatedAt = new Date().toISOString()
    assetType.version += 1

    return true
  }

  // Lifecycle Management
  async addLifecycleStage(assetTypeId: string, stage: Omit<LifecycleStage, "id">): Promise<LifecycleStage | null> {
    const assetType = this.getAssetType(assetTypeId)
    if (!assetType) return null

    const newStage: LifecycleStage = {
      ...stage,
      id: `LS-${Date.now()}`,
    }

    assetType.lifecycleStages.push(newStage)
    assetType.lifecycleStages.sort((a, b) => a.order - b.order)
    assetType.updatedAt = new Date().toISOString()
    assetType.version += 1

    return newStage
  }

  async updateLifecycleStage(
    assetTypeId: string,
    stageId: string,
    updates: Partial<LifecycleStage>,
  ): Promise<LifecycleStage | null> {
    const assetType = this.getAssetType(assetTypeId)
    if (!assetType) return null

    const stageIndex = assetType.lifecycleStages.findIndex((s) => s.id === stageId)
    if (stageIndex === -1) return null

    assetType.lifecycleStages[stageIndex] = {
      ...assetType.lifecycleStages[stageIndex],
      ...updates,
    }

    if (updates.order !== undefined) {
      assetType.lifecycleStages.sort((a, b) => a.order - b.order)
    }

    assetType.updatedAt = new Date().toISOString()
    assetType.version += 1

    return assetType.lifecycleStages[stageIndex]
  }

  // Maintenance Scheduling
  async addMaintenanceSchedule(
    assetTypeId: string,
    schedule: Omit<MaintenanceSchedule, "id">,
  ): Promise<MaintenanceSchedule | null> {
    const assetType = this.getAssetType(assetTypeId)
    if (!assetType) return null

    const newSchedule: MaintenanceSchedule = {
      ...schedule,
      id: `MS-${Date.now()}`,
    }

    assetType.maintenanceSchedules.push(newSchedule)
    assetType.updatedAt = new Date().toISOString()
    assetType.version += 1

    return newSchedule
  }

  async updateMaintenanceSchedule(
    assetTypeId: string,
    scheduleId: string,
    updates: Partial<MaintenanceSchedule>,
  ): Promise<MaintenanceSchedule | null> {
    const assetType = this.getAssetType(assetTypeId)
    if (!assetType) return null

    const scheduleIndex = assetType.maintenanceSchedules.findIndex((s) => s.id === scheduleId)
    if (scheduleIndex === -1) return null

    assetType.maintenanceSchedules[scheduleIndex] = {
      ...assetType.maintenanceSchedules[scheduleIndex],
      ...updates,
    }

    assetType.updatedAt = new Date().toISOString()
    assetType.version += 1

    return assetType.maintenanceSchedules[scheduleIndex]
  }

  // Depreciation Management
  async updateDepreciationSettings(
    assetTypeId: string,
    settings: DepreciationSettings,
  ): Promise<DepreciationSettings | null> {
    const assetType = this.getAssetType(assetTypeId)
    if (!assetType) return null

    assetType.depreciationSettings = settings
    assetType.updatedAt = new Date().toISOString()
    assetType.version += 1

    return settings
  }

  calculateDepreciation(
    assetValue: number,
    settings: DepreciationSettings,
    currentDate: string = new Date().toISOString(),
  ): {
    annualDepreciation: number
    accumulatedDepreciation: number
    bookValue: number
    remainingLife: number
  } {
    const startDate = new Date(settings.startDate)
    const current = new Date(currentDate)
    const yearsElapsed = (current.getTime() - startDate.getTime()) / (365.25 * 24 * 60 * 60 * 1000)

    const salvageValue =
      settings.salvageValueType === "percentage" ? assetValue * (settings.salvageValue / 100) : settings.salvageValue

    const depreciableAmount = assetValue - salvageValue

    let annualDepreciation = 0
    let accumulatedDepreciation = 0

    switch (settings.method) {
      case "straight_line":
        annualDepreciation = depreciableAmount / settings.usefulLife
        accumulatedDepreciation = Math.min(annualDepreciation * yearsElapsed, depreciableAmount)
        break

      case "declining_balance":
        const rate = 1 / settings.usefulLife
        annualDepreciation = (assetValue - accumulatedDepreciation) * rate
        accumulatedDepreciation = assetValue * (1 - Math.pow(1 - rate, yearsElapsed)) - salvageValue
        break

      case "double_declining_balance":
        const doubleRate = 2 / settings.usefulLife
        annualDepreciation = (assetValue - accumulatedDepreciation) * doubleRate
        accumulatedDepreciation = Math.min(assetValue * (1 - Math.pow(1 - doubleRate, yearsElapsed)), depreciableAmount)
        break

      case "sum_of_years_digits":
        const sumOfYears = (settings.usefulLife * (settings.usefulLife + 1)) / 2
        const currentYear = Math.floor(yearsElapsed) + 1
        const remainingYears = Math.max(settings.usefulLife - currentYear + 1, 0)
        annualDepreciation = (depreciableAmount * remainingYears) / sumOfYears

        let totalAccumulated = 0
        for (let year = 1; year <= Math.min(currentYear, settings.usefulLife); year++) {
          const yearFraction = (settings.usefulLife - year + 1) / sumOfYears
          totalAccumulated += depreciableAmount * yearFraction
        }
        accumulatedDepreciation = totalAccumulated
        break

      default:
        annualDepreciation = depreciableAmount / settings.usefulLife
        accumulatedDepreciation = Math.min(annualDepreciation * yearsElapsed, depreciableAmount)
    }

    const bookValue = assetValue - accumulatedDepreciation
    const remainingLife = Math.max(settings.usefulLife - yearsElapsed, 0)

    return {
      annualDepreciation: Math.max(annualDepreciation, 0),
      accumulatedDepreciation: Math.max(accumulatedDepreciation, 0),
      bookValue: Math.max(bookValue, salvageValue),
      remainingLife: Math.max(remainingLife, 0),
    }
  }

  // Category Management
  async createCategory(category: Omit<AssetCategory, "id">): Promise<AssetCategory> {
    const newCategory: AssetCategory = {
      ...category,
      id: `CAT-${Date.now()}`,
    }

    this.categories.push(newCategory)
    return newCategory
  }

  getCategories(parentId?: string): AssetCategory[] {
    return this.categories.filter((cat) => cat.parentId === parentId).sort((a, b) => a.name.localeCompare(b.name))
  }

  // Template Management
  async createTemplate(
    template: Omit<AssetTypeTemplate, "id" | "createdAt" | "usageCount" | "rating">,
  ): Promise<AssetTypeTemplate> {
    const newTemplate: AssetTypeTemplate = {
      ...template,
      id: `TPL-${Date.now()}`,
      createdAt: new Date().toISOString(),
      usageCount: 0,
      rating: 0,
    }

    this.templates.push(newTemplate)
    return newTemplate
  }

  getTemplates(category?: string): AssetTypeTemplate[] {
    let filtered = this.templates

    if (category) {
      filtered = filtered.filter((t) => t.category === category)
    }

    return filtered.sort((a, b) => b.rating - a.rating)
  }

  async applyTemplate(templateId: string, customizations?: Partial<AssetType>): Promise<Partial<AssetType> | null> {
    const template = this.templates.find((t) => t.id === templateId)
    if (!template) return null

    template.usageCount += 1

    return {
      ...template.assetType,
      ...customizations,
    }
  }

  // Analytics and Metrics
  async getMetrics(): Promise<AssetTypeMetrics> {
    const totalTypes = this.assetTypes.length
    const activeTypes = this.assetTypes.filter((at) => at.isActive).length

    // This would typically come from the asset service
    const totalAssets = 0 // Placeholder
    const assetsByType: Record<string, number> = {}

    const customFieldsUsage: Record<string, number> = {}
    this.assetTypes.forEach((at) => {
      at.customFields.forEach((field) => {
        customFieldsUsage[field.type] = (customFieldsUsage[field.type] || 0) + 1
      })
    })

    const lifecycleDistribution: Record<string, number> = {}
    this.assetTypes.forEach((at) => {
      at.lifecycleStages.forEach((stage) => {
        lifecycleDistribution[stage.name] = (lifecycleDistribution[stage.name] || 0) + 1
      })
    })

    const maintenanceScheduleCount = this.assetTypes.reduce((sum, at) => sum + at.maintenanceSchedules.length, 0)

    const depreciationMethods: Record<string, number> = {}
    this.assetTypes.forEach((at) => {
      const method = at.depreciationSettings.method
      depreciationMethods[method] = (depreciationMethods[method] || 0) + 1
    })

    return {
      totalTypes,
      activeTypes,
      totalAssets,
      assetsByType,
      customFieldsUsage,
      lifecycleDistribution,
      maintenanceScheduleCount,
      depreciationMethods,
    }
  }

  // Validation
  async validateAssetType(assetType: AssetType): Promise<AssetTypeValidationResult> {
    const errors: any[] = []
    const warnings: any[] = []

    // Basic validation
    if (!assetType.name?.trim()) {
      errors.push({
        field: "name",
        message: "Asset type name is required",
        code: "REQUIRED_FIELD",
        severity: "error",
      })
    }

    if (!assetType.code?.trim()) {
      errors.push({
        field: "code",
        message: "Asset type code is required",
        code: "REQUIRED_FIELD",
        severity: "error",
      })
    }

    // Check for duplicate codes
    const existingType = this.assetTypes.find((at) => at.code === assetType.code && at.id !== assetType.id)
    if (existingType) {
      errors.push({
        field: "code",
        message: "Asset type code must be unique",
        code: "DUPLICATE_CODE",
        severity: "error",
      })
    }

    // Validate custom fields
    const fieldNames = new Set<string>()
    assetType.customFields.forEach((field, index) => {
      if (!field.name?.trim()) {
        errors.push({
          field: `customFields[${index}].name`,
          message: "Custom field name is required",
          code: "REQUIRED_FIELD",
          severity: "error",
        })
      }

      if (fieldNames.has(field.name)) {
        errors.push({
          field: `customFields[${index}].name`,
          message: "Custom field names must be unique",
          code: "DUPLICATE_FIELD_NAME",
          severity: "error",
        })
      }
      fieldNames.add(field.name)
    })

    // Validate lifecycle stages
    const initialStages = assetType.lifecycleStages.filter((s) => s.isInitial)
    if (initialStages.length === 0) {
      warnings.push({
        field: "lifecycleStages",
        message: "No initial lifecycle stage defined",
        suggestion: "Add an initial stage for proper asset lifecycle tracking",
      })
    } else if (initialStages.length > 1) {
      errors.push({
        field: "lifecycleStages",
        message: "Only one initial lifecycle stage is allowed",
        code: "MULTIPLE_INITIAL_STAGES",
        severity: "error",
      })
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    }
  }

  // Helper methods
  private async isAssetTypeInUse(assetTypeId: string): Promise<boolean> {
    // This would check if any assets are using this asset type
    // For now, return false as placeholder
    return false
  }

  private async isCustomFieldInUse(assetTypeId: string, fieldId: string): Promise<boolean> {
    // This would check if any assets have data in this custom field
    // For now, return false as placeholder
    return false
  }

  private getDefaultLaptopFields(): CustomField[] {
    return [
      {
        id: "CF-001",
        name: "processor",
        label: "Processor",
        type: "text",
        description: "CPU model and specifications",
        isRequired: true,
        isUnique: false,
        validation: { maxLength: 100 },
        displayOrder: 1,
        groupName: "Hardware",
        isActive: true,
      },
      {
        id: "CF-002",
        name: "memory",
        label: "Memory (RAM)",
        type: "select",
        description: "System memory capacity",
        isRequired: true,
        isUnique: false,
        options: [
          { value: "4GB", label: "4 GB", isActive: true },
          { value: "8GB", label: "8 GB", isActive: true },
          { value: "16GB", label: "16 GB", isActive: true },
          { value: "32GB", label: "32 GB", isActive: true },
        ],
        validation: {},
        displayOrder: 2,
        groupName: "Hardware",
        isActive: true,
      },
      {
        id: "CF-003",
        name: "storage",
        label: "Storage",
        type: "text",
        description: "Storage capacity and type",
        isRequired: true,
        isUnique: false,
        validation: { maxLength: 50 },
        displayOrder: 3,
        groupName: "Hardware",
        isActive: true,
      },
      {
        id: "CF-004",
        name: "operatingSystem",
        label: "Operating System",
        type: "select",
        description: "Installed operating system",
        isRequired: true,
        isUnique: false,
        options: [
          { value: "windows11", label: "Windows 11", isActive: true },
          { value: "windows10", label: "Windows 10", isActive: true },
          { value: "macos", label: "macOS", isActive: true },
          { value: "linux", label: "Linux", isActive: true },
        ],
        validation: {},
        displayOrder: 4,
        groupName: "Software",
        isActive: true,
      },
    ]
  }

  private getDefaultLifecycleStages(): LifecycleStage[] {
    return [
      {
        id: "LS-001",
        name: "Requisition",
        code: "REQ",
        description: "Asset requested but not yet acquired",
        order: 1,
        isInitial: true,
        isFinal: false,
        color: "#FEF3C7",
        icon: "file-text",
        allowedTransitions: ["LS-002"],
        requiredFields: [],
        automatedActions: [],
        notifications: [],
        isActive: true,
      },
      {
        id: "LS-002",
        name: "Acquired",
        code: "ACQ",
        description: "Asset purchased and received",
        order: 2,
        isInitial: false,
        isFinal: false,
        color: "#DBEAFE",
        icon: "shopping-cart",
        allowedTransitions: ["LS-003"],
        requiredFields: ["purchaseDate", "purchasePrice"],
        automatedActions: [],
        notifications: [],
        isActive: true,
      },
      {
        id: "LS-003",
        name: "In Service",
        code: "SVC",
        description: "Asset deployed and actively used",
        order: 3,
        isInitial: false,
        isFinal: false,
        color: "#D1FAE5",
        icon: "check-circle",
        allowedTransitions: ["LS-004", "LS-005"],
        requiredFields: ["assignedTo", "location"],
        automatedActions: [],
        notifications: [],
        isActive: true,
      },
      {
        id: "LS-004",
        name: "Maintenance",
        code: "MNT",
        description: "Asset under maintenance or repair",
        order: 4,
        isInitial: false,
        isFinal: false,
        color: "#FED7AA",
        icon: "wrench",
        allowedTransitions: ["LS-003", "LS-005"],
        requiredFields: [],
        automatedActions: [],
        notifications: [],
        isActive: true,
      },
      {
        id: "LS-005",
        name: "Disposed",
        code: "DSP",
        description: "Asset retired and disposed of",
        order: 5,
        isInitial: false,
        isFinal: true,
        color: "#FEE2E2",
        icon: "trash-2",
        allowedTransitions: [],
        requiredFields: ["disposalDate", "disposalMethod"],
        automatedActions: [],
        notifications: [],
        isActive: true,
      },
    ]
  }

  private getDefaultMaintenanceSchedules(): MaintenanceSchedule[] {
    return [
      {
        id: "MS-001",
        name: "Quarterly System Check",
        description: "Comprehensive system health check and cleaning",
        type: "preventive",
        frequency: {
          type: "months",
          interval: 3,
        },
        priority: "medium",
        estimatedDuration: 60,
        estimatedCost: 50,
        requiredSkills: ["IT Support"],
        requiredParts: [],
        instructions: "Perform system diagnostics, clean hardware, update software",
        checklistItems: [
          {
            id: "CI-001",
            description: "Run hardware diagnostics",
            isRequired: true,
            order: 1,
            category: "Hardware",
          },
          {
            id: "CI-002",
            description: "Clean keyboard and screen",
            isRequired: true,
            order: 2,
            category: "Cleaning",
          },
          {
            id: "CI-003",
            description: "Update operating system",
            isRequired: true,
            order: 3,
            category: "Software",
          },
        ],
        triggers: [],
        isActive: true,
      },
    ]
  }

  private getDefaultDepreciationSettings(): DepreciationSettings {
    return {
      method: "straight_line",
      usefulLife: 3,
      usefulLifeUnit: "years",
      salvageValue: 10,
      salvageValueType: "percentage",
      startDate: new Date().toISOString(),
      isActive: true,
    }
  }
}
