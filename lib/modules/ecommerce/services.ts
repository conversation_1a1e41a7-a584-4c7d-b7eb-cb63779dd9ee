import type {
  EcommerceProduct,
  Customer,
  ShoppingCart,
  Order,
  ProductReview,
  Promotion,
  EcommerceMetrics,
  SupportTicket,
  CreditTransaction,
  CartItem,
} from "./types"

export class EcommerceService {
  private static instance: EcommerceService
  private products: EcommerceProduct[] = []
  private customers: Customer[] = []
  private carts: ShoppingCart[] = []
  private orders: Order[] = []
  private reviews: ProductReview[] = []
  private promotions: Promotion[] = []
  private supportTickets: SupportTicket[] = []

  static getInstance(): EcommerceService {
    if (!EcommerceService.instance) {
      EcommerceService.instance = new EcommerceService()
      EcommerceService.instance.initializeSampleData()
    }
    return EcommerceService.instance
  }

  private initializeSampleData(): void {
    // Sample products
    this.products = [
      {
        id: "prod-001",
        assetId: "AST-001",
        sku: "LAPTOP-XPS-001",
        name: "Dell XPS 15 Laptop",
        description:
          "High-performance laptop perfect for business and creative work. Features Intel i7 processor, 16GB RAM, and 512GB SSD.",
        shortDescription: "Dell XPS 15 - Intel i7, 16GB RAM, 512GB SSD",
        category: "Electronics",
        subcategory: "Laptops",
        brand: "Dell",
        model: "XPS 15",
        condition: "like-new",
        images: [
          {
            id: "img-001",
            url: "/placeholder.svg?height=400&width=400",
            alt: "Dell XPS 15 Front View",
            isPrimary: true,
            order: 1,
          },
        ],
        specifications: [
          { name: "Processor", value: "Intel Core i7-11800H", group: "Performance", order: 1 },
          { name: "Memory", value: "16GB DDR4", group: "Performance", order: 2 },
          { name: "Storage", value: "512GB NVMe SSD", group: "Storage", order: 1 },
          { name: "Display", value: '15.6" 4K OLED', group: "Display", order: 1 },
        ],
        pricing: {
          basePrice: 1899,
          creditPrice: 1519,
          creditDiscount: 20,
          bulkPricing: [
            { minQuantity: 5, discount: 5, discountType: "percentage" },
            { minQuantity: 10, discount: 10, discountType: "percentage" },
          ],
          priceHistory: [
            { price: 1999, creditPrice: 1599, date: "2024-01-01", reason: "Initial pricing" },
            { price: 1899, creditPrice: 1519, date: "2024-02-01", reason: "Promotional discount" },
          ],
        },
        inventory: {
          totalStock: 25,
          availableStock: 23,
          reservedStock: 2,
          lowStockThreshold: 5,
          trackInventory: true,
          allowBackorders: false,
        },
        seo: {
          metaTitle: "Dell XPS 15 Laptop - High Performance Business Laptop",
          metaDescription:
            "Shop the Dell XPS 15 laptop with Intel i7, 16GB RAM, and 512GB SSD. Perfect for business and creative professionals.",
          keywords: ["dell", "xps", "laptop", "business", "i7"],
          slug: "dell-xps-15-laptop",
        },
        status: "active",
        tags: ["business", "laptop", "dell", "high-performance"],
        weight: 2.1,
        dimensions: { length: 34.4, width: 23.0, height: 1.8, unit: "cm" },
        shippingInfo: {
          weight: 2.5,
          weightUnit: "kg",
          shippingClass: "standard",
          freeShippingThreshold: 1000,
          shippingCost: 25,
          handlingTime: 2,
          restrictions: [],
        },
        warranty: {
          duration: 12,
          durationType: "months",
          type: "manufacturer",
          description: "Dell manufacturer warranty",
          terms: "Standard Dell warranty terms apply",
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: "admin",
      },
    ]

    // Sample customers
    this.customers = [
      {
        id: "cust-001",
        email: "<EMAIL>",
        firstName: "John",
        lastName: "Doe",
        company: "Tech Solutions Inc",
        phone: "******-0123",
        addresses: [
          {
            id: "addr-001",
            type: "billing",
            isDefault: true,
            firstName: "John",
            lastName: "Doe",
            company: "Tech Solutions Inc",
            address1: "123 Business Ave",
            city: "New York",
            state: "NY",
            postalCode: "10001",
            country: "US",
            phone: "******-0123",
          },
        ],
        creditBalance: 5000,
        creditLimit: 10000,
        creditHistory: [
          {
            id: "credit-001",
            type: "bonus",
            amount: 5000,
            balance: 5000,
            description: "Welcome bonus",
            date: new Date().toISOString(),
            processedBy: "system",
          },
        ],
        orderHistory: [],
        preferences: {
          currency: "USD",
          language: "en",
          notifications: {
            email: true,
            sms: false,
            orderUpdates: true,
            promotions: true,
            newsletter: true,
          },
          paymentMethod: "credit",
        },
        status: "active",
        tier: "silver",
        registrationDate: new Date().toISOString(),
        notes: "",
      },
    ]

    // Sample orders
    this.orders = [
      {
        id: "order-001",
        orderNumber: "ORD-2024-001",
        customerId: "cust-001",
        customerInfo: {
          email: "<EMAIL>",
          firstName: "John",
          lastName: "Doe",
          company: "Tech Solutions Inc",
          phone: "******-0123",
        },
        items: [
          {
            id: "item-001",
            productId: "prod-001",
            productName: "Dell XPS 15 Laptop",
            sku: "LAPTOP-XPS-001",
            quantity: 1,
            unitPrice: 1899,
            unitCredits: 1519,
            totalPrice: 1899,
            totalCredits: 1519,
            status: "confirmed",
          },
        ],
        pricing: {
          subtotal: 1899,
          creditDiscount: 380,
          shippingCost: 0,
          taxAmount: 151.92,
          total: 1670.92,
          totalCredits: 1519,
          creditsUsed: 1519,
          cashAmount: 151.92,
        },
        shipping: {
          address: {
            id: "addr-001",
            type: "shipping",
            isDefault: true,
            firstName: "John",
            lastName: "Doe",
            company: "Tech Solutions Inc",
            address1: "123 Business Ave",
            city: "New York",
            state: "NY",
            postalCode: "10001",
            country: "US",
            phone: "******-0123",
          },
          method: "standard",
          cost: 0,
          estimatedDelivery: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
        },
        billing: {
          address: {
            id: "addr-001",
            type: "billing",
            isDefault: true,
            firstName: "John",
            lastName: "Doe",
            company: "Tech Solutions Inc",
            address1: "123 Business Ave",
            city: "New York",
            state: "NY",
            postalCode: "10001",
            country: "US",
            phone: "******-0123",
          },
          method: "credit",
          creditAmount: 1519,
          cashAmount: 151.92,
        },
        status: "confirmed",
        paymentStatus: "paid",
        fulfillmentStatus: "processing",
        timeline: [
          {
            id: "timeline-001",
            status: "pending",
            description: "Order placed",
            timestamp: new Date().toISOString(),
          },
          {
            id: "timeline-002",
            status: "confirmed",
            description: "Order confirmed and payment processed",
            timestamp: new Date().toISOString(),
          },
        ],
        notes: [],
        metadata: {},
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ]
  }

  // Product Management
  async createProduct(product: Omit<EcommerceProduct, "id" | "createdAt" | "updatedAt">): Promise<EcommerceProduct> {
    const newProduct: EcommerceProduct = {
      ...product,
      id: `prod-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }

    this.products.push(newProduct)
    return newProduct
  }

  async updateProduct(id: string, updates: Partial<EcommerceProduct>): Promise<EcommerceProduct | null> {
    const index = this.products.findIndex((p) => p.id === id)
    if (index === -1) return null

    this.products[index] = {
      ...this.products[index],
      ...updates,
      updatedAt: new Date().toISOString(),
    }

    return this.products[index]
  }

  async deleteProduct(id: string): Promise<boolean> {
    const index = this.products.findIndex((p) => p.id === id)
    if (index === -1) return false

    this.products.splice(index, 1)
    return true
  }

  // Customer Management
  async createCustomer(customer: Omit<Customer, "id" | "registrationDate">): Promise<Customer> {
    const newCustomer: Customer = {
      ...customer,
      id: `cust-${Date.now()}`,
      registrationDate: new Date().toISOString(),
    }

    this.customers.push(newCustomer)
    return newCustomer
  }

  async updateCustomerCredits(
    customerId: string,
    amount: number,
    type: CreditTransaction["type"],
    description: string,
  ): Promise<boolean> {
    const customer = this.customers.find((c) => c.id === customerId)
    if (!customer) return false

    const newBalance = customer.creditBalance + amount
    if (newBalance < 0) return false // Insufficient credits

    customer.creditBalance = newBalance
    customer.creditHistory.push({
      id: `credit-${Date.now()}`,
      type,
      amount,
      balance: newBalance,
      description,
      date: new Date().toISOString(),
      processedBy: "system",
    })

    return true
  }

  // Shopping Cart Management
  async getCart(customerId: string): Promise<ShoppingCart> {
    let cart = this.carts.find((c) => c.customerId === customerId)

    if (!cart) {
      cart = {
        id: `cart-${Date.now()}`,
        customerId,
        items: [],
        subtotal: 0,
        creditDiscount: 0,
        shippingCost: 0,
        taxAmount: 0,
        total: 0,
        totalCredits: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days
      }
      this.carts.push(cart)
    }

    return cart
  }

  async addToCart(customerId: string, productId: string, quantity: number): Promise<ShoppingCart> {
    const cart = await this.getCart(customerId)
    const product = this.products.find((p) => p.id === productId)

    if (!product) throw new Error("Product not found")
    if (product.inventory.availableStock < quantity) throw new Error("Insufficient stock")

    const existingItem = cart.items.find((item) => item.productId === productId)

    if (existingItem) {
      existingItem.quantity += quantity
      existingItem.totalPrice = existingItem.quantity * existingItem.unitPrice
      existingItem.totalCredits = existingItem.quantity * existingItem.unitCredits
    } else {
      const newItem: CartItem = {
        id: `item-${Date.now()}`,
        productId,
        quantity,
        unitPrice: product.pricing.basePrice,
        unitCredits: product.pricing.creditPrice,
        totalPrice: product.pricing.basePrice * quantity,
        totalCredits: product.pricing.creditPrice * quantity,
        selectedOptions: [],
      }
      cart.items.push(newItem)
    }

    return this.calculateCartTotals(cart)
  }

  async removeFromCart(customerId: string, itemId: string): Promise<ShoppingCart> {
    const cart = await this.getCart(customerId)
    cart.items = cart.items.filter((item) => item.id !== itemId)
    return this.calculateCartTotals(cart)
  }

  async updateCartItem(customerId: string, itemId: string, quantity: number): Promise<ShoppingCart> {
    const cart = await this.getCart(customerId)
    const item = cart.items.find((item) => item.id === itemId)

    if (!item) throw new Error("Cart item not found")

    const product = this.products.find((p) => p.id === item.productId)
    if (!product) throw new Error("Product not found")

    if (product.inventory.availableStock < quantity) throw new Error("Insufficient stock")

    item.quantity = quantity
    item.totalPrice = item.unitPrice * quantity
    item.totalCredits = item.unitCredits * quantity

    return this.calculateCartTotals(cart)
  }

  private calculateCartTotals(cart: ShoppingCart): ShoppingCart {
    cart.subtotal = cart.items.reduce((sum, item) => sum + item.totalPrice, 0)
    cart.totalCredits = cart.items.reduce((sum, item) => sum + item.totalCredits, 0)
    cart.creditDiscount = cart.subtotal - cart.totalCredits
    cart.taxAmount = cart.subtotal * 0.08 // 8% tax
    cart.shippingCost = cart.subtotal >= 1000 ? 0 : 25 // Free shipping over $1000
    cart.total = cart.subtotal + cart.taxAmount + cart.shippingCost
    cart.updatedAt = new Date().toISOString()

    return cart
  }

  // Order Management
  async createOrder(customerId: string, cartId: string): Promise<Order> {
    const cart = this.carts.find((c) => c.id === cartId && c.customerId === customerId)
    const customer = this.customers.find((c) => c.id === customerId)

    if (!cart || !customer) throw new Error("Cart or customer not found")
    if (cart.items.length === 0) throw new Error("Cart is empty")
    if (customer.creditBalance < cart.totalCredits) throw new Error("Insufficient credits")

    const order: Order = {
      id: `order-${Date.now()}`,
      orderNumber: `ORD-${new Date().getFullYear()}-${String(this.orders.length + 1).padStart(3, "0")}`,
      customerId,
      customerInfo: {
        email: customer.email,
        firstName: customer.firstName,
        lastName: customer.lastName,
        company: customer.company,
        phone: customer.phone,
      },
      items: cart.items.map((item) => ({
        id: `orderitem-${Date.now()}-${Math.random()}`,
        productId: item.productId,
        productName: this.products.find((p) => p.id === item.productId)?.name || "",
        sku: this.products.find((p) => p.id === item.productId)?.sku || "",
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        unitCredits: item.unitCredits,
        totalPrice: item.totalPrice,
        totalCredits: item.totalCredits,
        status: "pending",
      })),
      pricing: {
        subtotal: cart.subtotal,
        creditDiscount: cart.creditDiscount,
        shippingCost: cart.shippingCost,
        taxAmount: cart.taxAmount,
        total: cart.total,
        totalCredits: cart.totalCredits,
        creditsUsed: cart.totalCredits,
        cashAmount: cart.total - cart.totalCredits,
      },
      shipping: {
        address: customer.addresses.find((addr) => addr.type === "shipping") || customer.addresses[0],
        method: "standard",
        cost: cart.shippingCost,
        estimatedDelivery: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
      },
      billing: {
        address: customer.addresses.find((addr) => addr.type === "billing") || customer.addresses[0],
        method: "credit",
        creditAmount: cart.totalCredits,
        cashAmount: cart.total - cart.totalCredits,
      },
      status: "pending",
      paymentStatus: "pending",
      fulfillmentStatus: "pending",
      timeline: [
        {
          id: `timeline-${Date.now()}`,
          status: "pending",
          description: "Order placed",
          timestamp: new Date().toISOString(),
        },
      ],
      notes: [],
      metadata: {},
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }

    // Process payment (deduct credits)
    await this.updateCustomerCredits(customerId, -cart.totalCredits, "purchase", `Order ${order.orderNumber}`)

    // Update inventory
    for (const item of cart.items) {
      const product = this.products.find((p) => p.id === item.productId)
      if (product) {
        product.inventory.availableStock -= item.quantity
        product.inventory.reservedStock += item.quantity
      }
    }

    // Clear cart
    this.carts = this.carts.filter((c) => c.id !== cartId)

    // Add to customer order history
    customer.orderHistory.push(order.id)

    this.orders.push(order)
    return order
  }

  async updateOrderStatus(orderId: string, status: Order["status"], description?: string): Promise<Order | null> {
    const order = this.orders.find((o) => o.id === orderId)
    if (!order) return null

    order.status = status
    order.updatedAt = new Date().toISOString()

    order.timeline.push({
      id: `timeline-${Date.now()}`,
      status,
      description: description || `Order status updated to ${status}`,
      timestamp: new Date().toISOString(),
    })

    // Update fulfillment status based on order status
    if (status === "shipped") {
      order.fulfillmentStatus = "shipped"
    } else if (status === "delivered") {
      order.fulfillmentStatus = "delivered"
    } else if (status === "cancelled") {
      order.fulfillmentStatus = "cancelled"
      // Refund credits and restore inventory
      await this.refundOrder(orderId)
    }

    return order
  }

  private async refundOrder(orderId: string): Promise<void> {
    const order = this.orders.find((o) => o.id === orderId)
    if (!order) return

    // Refund credits
    await this.updateCustomerCredits(
      order.customerId,
      order.pricing.creditsUsed,
      "refund",
      `Refund for cancelled order ${order.orderNumber}`,
    )

    // Restore inventory
    for (const item of order.items) {
      const product = this.products.find((p) => p.id === item.productId)
      if (product) {
        product.inventory.availableStock += item.quantity
        product.inventory.reservedStock -= item.quantity
      }
    }
  }

  // Analytics and Metrics
  async getEcommerceMetrics(): Promise<EcommerceMetrics> {
    const totalProducts = this.products.length
    const activeProducts = this.products.filter((p) => p.status === "active").length
    const totalOrders = this.orders.length
    const totalRevenue = this.orders.reduce((sum, order) => sum + order.pricing.total, 0)
    const totalCreditsUsed = this.orders.reduce((sum, order) => sum + order.pricing.creditsUsed, 0)
    const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0
    const customerCount = this.customers.length
    const repeatCustomers = this.customers.filter((c) => c.orderHistory.length > 1).length
    const repeatCustomerRate = customerCount > 0 ? (repeatCustomers / customerCount) * 100 : 0

    // Top selling products
    const productSales = new Map<string, { name: string; units: number; revenue: number; credits: number }>()
    this.orders.forEach((order) => {
      order.items.forEach((item) => {
        const existing = productSales.get(item.productId) || {
          name: item.productName,
          units: 0,
          revenue: 0,
          credits: 0,
        }
        existing.units += item.quantity
        existing.revenue += item.totalPrice
        existing.credits += item.totalCredits
        productSales.set(item.productId, existing)
      })
    })

    const topSellingProducts = Array.from(productSales.entries())
      .map(([productId, data]) => ({
        productId,
        productName: data.name,
        unitsSold: data.units,
        revenue: data.revenue,
        creditsUsed: data.credits,
      }))
      .sort((a, b) => b.unitsSold - a.unitsSold)
      .slice(0, 10)

    return {
      totalProducts,
      activeProducts,
      totalOrders,
      totalRevenue,
      totalCreditsUsed,
      averageOrderValue,
      conversionRate: 0, // Would need visitor tracking
      customerCount,
      repeatCustomerRate,
      topSellingProducts,
      revenueByCategory: [], // Would calculate from orders
      customersByTier: [], // Would calculate from customers
    }
  }

  // Query methods
  getProducts(filters?: {
    category?: string
    status?: string
    search?: string
    minPrice?: number
    maxPrice?: number
  }): EcommerceProduct[] {
    let filtered = this.products

    if (filters?.category) {
      filtered = filtered.filter((p) => p.category === filters.category)
    }
    if (filters?.status) {
      filtered = filtered.filter((p) => p.status === filters.status)
    }
    if (filters?.search) {
      const search = filters.search.toLowerCase()
      filtered = filtered.filter(
        (p) =>
          p.name.toLowerCase().includes(search) ||
          p.description.toLowerCase().includes(search) ||
          p.sku.toLowerCase().includes(search),
      )
    }
    if (filters?.minPrice) {
      filtered = filtered.filter((p) => p.pricing.basePrice >= filters.minPrice!)
    }
    if (filters?.maxPrice) {
      filtered = filtered.filter((p) => p.pricing.basePrice <= filters.maxPrice!)
    }

    return filtered
  }

  getCustomers(): Customer[] {
    return [...this.customers]
  }

  getOrders(customerId?: string): Order[] {
    return customerId ? this.orders.filter((o) => o.customerId === customerId) : [...this.orders]
  }

  getProductById(id: string): EcommerceProduct | undefined {
    return this.products.find((p) => p.id === id)
  }

  getCustomerById(id: string): Customer | undefined {
    return this.customers.find((c) => c.id === id)
  }

  getOrderById(id: string): Order | undefined {
    return this.orders.find((o) => o.id === id)
  }
}

export const ecommerceService = EcommerceService.getInstance()
