export interface EcommerceProduct {
  id: string
  assetId: string
  sku: string
  name: string
  description: string
  shortDescription: string
  category: string
  subcategory: string
  brand: string
  model: string
  condition: "new" | "like-new" | "good" | "fair" | "refurbished"
  images: ProductImage[]
  specifications: ProductSpecification[]
  pricing: ProductPricing
  inventory: ProductInventory
  seo: ProductSEO
  status: "draft" | "active" | "inactive" | "out-of-stock"
  tags: string[]
  weight: number
  dimensions: ProductDimensions
  shippingInfo: ShippingInfo
  warranty: WarrantyInfo
  createdAt: string
  updatedAt: string
  createdBy: string
}

export interface ProductImage {
  id: string
  url: string
  alt: string
  isPrimary: boolean
  order: number
}

export interface ProductSpecification {
  name: string
  value: string
  group: string
  order: number
}

export interface ProductPricing {
  basePrice: number
  salePrice?: number
  creditPrice: number
  creditDiscount: number
  bulkPricing: BulkPricingTier[]
  priceHistory: PriceHistoryEntry[]
}

export interface BulkPricingTier {
  minQuantity: number
  maxQuantity?: number
  discount: number
  discountType: "percentage" | "fixed"
}

export interface PriceHistoryEntry {
  price: number
  creditPrice: number
  date: string
  reason: string
}

export interface ProductInventory {
  totalStock: number
  availableStock: number
  reservedStock: number
  lowStockThreshold: number
  trackInventory: boolean
  allowBackorders: boolean
  backorderLimit?: number
}

export interface ProductDimensions {
  length: number
  width: number
  height: number
  unit: "cm" | "in"
}

export interface ShippingInfo {
  weight: number
  weightUnit: "kg" | "lb"
  shippingClass: string
  freeShippingThreshold?: number
  shippingCost: number
  handlingTime: number
  restrictions: string[]
}

export interface WarrantyInfo {
  duration: number
  durationType: "days" | "months" | "years"
  type: "manufacturer" | "seller" | "extended"
  description: string
  terms: string
}

export interface ProductSEO {
  metaTitle: string
  metaDescription: string
  keywords: string[]
  slug: string
}

export interface Customer {
  id: string
  email: string
  firstName: string
  lastName: string
  company?: string
  phone?: string
  addresses: CustomerAddress[]
  creditBalance: number
  creditLimit: number
  creditHistory: CreditTransaction[]
  orderHistory: string[]
  preferences: CustomerPreferences
  status: "active" | "inactive" | "suspended"
  tier: "bronze" | "silver" | "gold" | "platinum"
  registrationDate: string
  lastLoginDate?: string
  notes: string
}

export interface CustomerAddress {
  id: string
  type: "billing" | "shipping"
  isDefault: boolean
  firstName: string
  lastName: string
  company?: string
  address1: string
  address2?: string
  city: string
  state: string
  postalCode: string
  country: string
  phone?: string
}

export interface CustomerPreferences {
  currency: string
  language: string
  notifications: {
    email: boolean
    sms: boolean
    orderUpdates: boolean
    promotions: boolean
    newsletter: boolean
  }
  paymentMethod: "credit" | "mixed"
}

export interface CreditTransaction {
  id: string
  type: "purchase" | "refund" | "adjustment" | "bonus" | "transfer"
  amount: number
  balance: number
  description: string
  orderId?: string
  date: string
  processedBy: string
}

export interface ShoppingCart {
  id: string
  customerId: string
  items: CartItem[]
  subtotal: number
  creditDiscount: number
  shippingCost: number
  taxAmount: number
  total: number
  totalCredits: number
  createdAt: string
  updatedAt: string
  expiresAt: string
}

export interface CartItem {
  id: string
  productId: string
  quantity: number
  unitPrice: number
  unitCredits: number
  totalPrice: number
  totalCredits: number
  selectedOptions: ProductOption[]
}

export interface ProductOption {
  name: string
  value: string
  priceModifier: number
  creditModifier: number
}

export interface Order {
  id: string
  orderNumber: string
  customerId: string
  customerInfo: OrderCustomerInfo
  items: OrderItem[]
  pricing: OrderPricing
  shipping: OrderShipping
  billing: OrderBilling
  status: OrderStatus
  paymentStatus: PaymentStatus
  fulfillmentStatus: FulfillmentStatus
  timeline: OrderTimeline[]
  notes: OrderNote[]
  metadata: Record<string, any>
  createdAt: string
  updatedAt: string
}

export interface OrderCustomerInfo {
  email: string
  firstName: string
  lastName: string
  company?: string
  phone?: string
}

export interface OrderItem {
  id: string
  productId: string
  productName: string
  sku: string
  quantity: number
  unitPrice: number
  unitCredits: number
  totalPrice: number
  totalCredits: number
  status: "pending" | "confirmed" | "shipped" | "delivered" | "cancelled"
}

export interface OrderPricing {
  subtotal: number
  creditDiscount: number
  shippingCost: number
  taxAmount: number
  total: number
  totalCredits: number
  creditsUsed: number
  cashAmount: number
}

export interface OrderShipping {
  address: CustomerAddress
  method: string
  cost: number
  estimatedDelivery: string
  trackingNumber?: string
  carrier?: string
}

export interface OrderBilling {
  address: CustomerAddress
  method: "credit" | "mixed"
  creditAmount: number
  cashAmount: number
}

export type OrderStatus = "pending" | "confirmed" | "processing" | "shipped" | "delivered" | "cancelled" | "refunded"
export type PaymentStatus = "pending" | "paid" | "partially-paid" | "failed" | "refunded"
export type FulfillmentStatus = "pending" | "processing" | "shipped" | "delivered" | "cancelled"

export interface OrderTimeline {
  id: string
  status: string
  description: string
  timestamp: string
  user?: string
  metadata?: Record<string, any>
}

export interface OrderNote {
  id: string
  type: "internal" | "customer"
  message: string
  author: string
  timestamp: string
}

export interface ProductReview {
  id: string
  productId: string
  customerId: string
  customerName: string
  rating: number
  title: string
  comment: string
  verified: boolean
  helpful: number
  notHelpful: number
  images: string[]
  createdAt: string
  status: "pending" | "approved" | "rejected"
}

export interface Promotion {
  id: string
  name: string
  description: string
  type: "percentage" | "fixed" | "credit-bonus" | "free-shipping"
  value: number
  conditions: PromotionCondition[]
  applicableProducts: string[]
  applicableCategories: string[]
  customerTiers: string[]
  startDate: string
  endDate: string
  usageLimit?: number
  usageCount: number
  isActive: boolean
  createdAt: string
}

export interface PromotionCondition {
  type: "min-order" | "min-quantity" | "customer-tier" | "first-order"
  value: number | string
}

export interface EcommerceMetrics {
  totalProducts: number
  activeProducts: number
  totalOrders: number
  totalRevenue: number
  totalCreditsUsed: number
  averageOrderValue: number
  conversionRate: number
  customerCount: number
  repeatCustomerRate: number
  topSellingProducts: ProductSalesData[]
  revenueByCategory: CategorySalesData[]
  customersByTier: CustomerTierData[]
}

export interface ProductSalesData {
  productId: string
  productName: string
  unitsSold: number
  revenue: number
  creditsUsed: number
}

export interface CategorySalesData {
  category: string
  revenue: number
  creditsUsed: number
  orderCount: number
}

export interface CustomerTierData {
  tier: string
  count: number
  totalSpent: number
  averageOrderValue: number
}

export interface SupportTicket {
  id: string
  customerId: string
  orderId?: string
  subject: string
  description: string
  category: "order" | "product" | "account" | "technical" | "billing"
  priority: "low" | "medium" | "high" | "urgent"
  status: "open" | "in-progress" | "waiting-customer" | "resolved" | "closed"
  assignedTo?: string
  messages: TicketMessage[]
  attachments: string[]
  createdAt: string
  updatedAt: string
  resolvedAt?: string
}

export interface TicketMessage {
  id: string
  author: string
  authorType: "customer" | "agent"
  message: string
  timestamp: string
  attachments: string[]
}
