export interface InventoryItem {
  id: string
  sku: string
  name: string
  description: string
  category: string
  subcategory?: string
  brand?: string
  model?: string
  barcode?: string
  qrCode?: string
  unitOfMeasure: string
  costPrice: number
  sellingPrice: number
  minStockLevel: number
  maxStockLevel: number
  reorderPoint: number
  reorderQuantity: number
  currentStock: number
  reservedStock: number
  availableStock: number
  location: StorageLocation
  supplier: Supplier
  status: "Active" | "Inactive" | "Discontinued"
  trackingMethod: "FIFO" | "LIFO" | "Average" | "Specific"
  serialNumbers: string[]
  batchNumbers: string[]
  expiryDate?: string
  weight?: number
  dimensions?: Dimensions
  images: string[]
  tags: string[]
  customFields: Record<string, any>
  createdAt: string
  updatedAt: string
}

export interface StorageLocation {
  warehouseId: string
  warehouseName: string
  zone?: string
  aisle?: string
  shelf?: string
  bin?: string
  coordinates?: string
}

export interface Dimensions {
  length: number
  width: number
  height: number
  unit: "cm" | "in" | "m" | "ft"
}

export interface StockMovement {
  id: string
  itemId: string
  itemName: string
  type: "IN" | "OUT" | "TRANSFER" | "ADJUSTMENT"
  reason: string
  quantity: number
  unitCost?: number
  totalCost?: number
  fromLocation?: StorageLocation
  toLocation?: StorageLocation
  referenceNumber?: string
  referenceType?: "Purchase Order" | "Sales Order" | "Transfer" | "Adjustment"
  batchNumber?: string
  serialNumbers?: string[]
  expiryDate?: string
  performedBy: string
  approvedBy?: string
  notes?: string
  createdAt: string
}

export interface PurchaseOrder {
  id: string
  orderNumber: string
  supplierId: string
  supplierName: string
  status: "Draft" | "Sent" | "Confirmed" | "Partial" | "Received" | "Cancelled"
  orderDate: string
  expectedDeliveryDate: string
  actualDeliveryDate?: string
  items: PurchaseOrderItem[]
  subtotal: number
  taxAmount: number
  shippingCost: number
  totalAmount: number
  currency: string
  paymentTerms: string
  deliveryAddress: string
  notes?: string
  attachments: string[]
  createdBy: string
  approvedBy?: string
  createdAt: string
  updatedAt: string
}

export interface PurchaseOrderItem {
  itemId: string
  itemName: string
  sku: string
  quantity: number
  unitPrice: number
  totalPrice: number
  receivedQuantity: number
  pendingQuantity: number
  specifications?: string
}

export interface Supplier {
  id: string
  name: string
  contactPerson: string
  email: string
  phone: string
  address: string
  paymentTerms: string
  leadTime: number
  rating: number
  status: "Active" | "Inactive" | "Blacklisted"
  categories: string[]
  notes?: string
  createdAt: string
  updatedAt: string
}

export interface StockAlert {
  id: string
  type: "Low Stock" | "Out of Stock" | "Overstock" | "Expiry Warning" | "Reorder Point"
  itemId: string
  itemName: string
  currentStock: number
  threshold: number
  severity: "Low" | "Medium" | "High" | "Critical"
  message: string
  acknowledged: boolean
  acknowledgedBy?: string
  acknowledgedAt?: string
  createdAt: string
}

export interface InventoryMetrics {
  totalItems: number
  totalValue: number
  lowStockItems: number
  outOfStockItems: number
  overstockItems: number
  turnoverRate: number
  averageLeadTime: number
  stockAccuracy: number
  carryingCost: number
}

export interface StockTakeRecord {
  id: string
  itemId: string
  itemName: string
  expectedQuantity: number
  actualQuantity: number
  variance: number
  varianceValue: number
  reason?: string
  countedBy: string
  verifiedBy?: string
  location: StorageLocation
  countDate: string
}

export interface StockTake {
  id: string
  name: string
  description?: string
  status: "Planned" | "In Progress" | "Completed" | "Cancelled"
  type: "Full" | "Partial" | "Cycle"
  scheduledDate: string
  startDate?: string
  completedDate?: string
  locations: StorageLocation[]
  items: string[] // Item IDs
  records: StockTakeRecord[]
  totalVariance: number
  totalVarianceValue: number
  createdBy: string
  assignedTo: string[]
  createdAt: string
  updatedAt: string
}
