import type {
  InventoryItem,
  StockMovement,
  PurchaseOrder,
  Supplier,
  StockAlert,
  InventoryMetrics,
  StockTake,
  StockTakeRecord,
} from "./types"

export class InventoryService {
  private static instance: InventoryService
  private items: InventoryItem[] = []
  private movements: StockMovement[] = []
  private purchaseOrders: PurchaseOrder[] = []
  private suppliers: Supplier[] = []
  private alerts: StockAlert[] = []
  private stockTakes: StockTake[] = []

  static getInstance(): InventoryService {
    if (!InventoryService.instance) {
      InventoryService.instance = new InventoryService()
    }
    return InventoryService.instance
  }

  // Item Management
  async createItem(
    item: Omit<InventoryItem, "id" | "createdAt" | "updatedAt" | "availableStock">,
  ): Promise<InventoryItem> {
    const newItem: InventoryItem = {
      ...item,
      id: `ITM-${Date.now()}`,
      availableStock: item.currentStock - item.reservedStock,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }

    this.items.push(newItem)
    await this.checkStockLevels(newItem.id)
    return newItem
  }

  async updateItem(id: string, updates: Partial<InventoryItem>): Promise<InventoryItem | null> {
    const index = this.items.findIndex((item) => item.id === id)
    if (index === -1) return null

    this.items[index] = {
      ...this.items[index],
      ...updates,
      availableStock:
        (updates.currentStock ?? this.items[index].currentStock) -
        (updates.reservedStock ?? this.items[index].reservedStock),
      updatedAt: new Date().toISOString(),
    }

    await this.checkStockLevels(id)
    return this.items[index]
  }

  async adjustStock(
    itemId: string,
    quantity: number,
    reason: string,
    performedBy: string,
    notes?: string,
  ): Promise<boolean> {
    const item = this.items.find((i) => i.id === itemId)
    if (!item) return false

    const oldStock = item.currentStock
    item.currentStock += quantity
    item.availableStock = item.currentStock - item.reservedStock
    item.updatedAt = new Date().toISOString()

    // Record movement
    await this.recordMovement({
      itemId,
      itemName: item.name,
      type: "ADJUSTMENT",
      reason,
      quantity: Math.abs(quantity),
      fromLocation: quantity < 0 ? item.location : undefined,
      toLocation: quantity > 0 ? item.location : undefined,
      performedBy,
      notes,
    })

    await this.checkStockLevels(itemId)
    return true
  }

  async transferStock(
    itemId: string,
    fromLocationId: string,
    toLocationId: string,
    quantity: number,
    performedBy: string,
  ): Promise<boolean> {
    const item = this.items.find((i) => i.id === itemId)
    if (!item || item.availableStock < quantity) return false

    // Record movement
    await this.recordMovement({
      itemId,
      itemName: item.name,
      type: "TRANSFER",
      reason: "Stock Transfer",
      quantity,
      fromLocation: item.location,
      toLocation: item.location, // Would be updated with actual location data
      performedBy,
    })

    return true
  }

  async reserveStock(itemId: string, quantity: number): Promise<boolean> {
    const item = this.items.find((i) => i.id === itemId)
    if (!item || item.availableStock < quantity) return false

    item.reservedStock += quantity
    item.availableStock -= quantity
    item.updatedAt = new Date().toISOString()

    return true
  }

  async releaseReservedStock(itemId: string, quantity: number): Promise<boolean> {
    const item = this.items.find((i) => i.id === itemId)
    if (!item || item.reservedStock < quantity) return false

    item.reservedStock -= quantity
    item.availableStock += quantity
    item.updatedAt = new Date().toISOString()

    return true
  }

  // Stock Movement Tracking
  private async recordMovement(movement: Omit<StockMovement, "id" | "createdAt">): Promise<StockMovement> {
    const newMovement: StockMovement = {
      ...movement,
      id: `MOV-${Date.now()}`,
      createdAt: new Date().toISOString(),
    }

    this.movements.push(newMovement)
    return newMovement
  }

  getMovementHistory(itemId?: string, days?: number): StockMovement[] {
    let filtered = this.movements

    if (itemId) {
      filtered = filtered.filter((m) => m.itemId === itemId)
    }

    if (days) {
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - days)
      filtered = filtered.filter((m) => new Date(m.createdAt) >= cutoffDate)
    }

    return filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
  }

  // Purchase Order Management
  async createPurchaseOrder(po: Omit<PurchaseOrder, "id" | "createdAt" | "updatedAt">): Promise<PurchaseOrder> {
    const newPO: PurchaseOrder = {
      ...po,
      id: `PO-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }

    this.purchaseOrders.push(newPO)
    return newPO
  }

  async receivePurchaseOrder(
    poId: string,
    itemReceipts: Array<{
      itemId: string
      receivedQuantity: number
      batchNumber?: string
      expiryDate?: string
    }>,
  ): Promise<boolean> {
    const po = this.purchaseOrders.find((p) => p.id === poId)
    if (!po) return false

    for (const receipt of itemReceipts) {
      const poItem = po.items.find((i) => i.itemId === receipt.itemId)
      if (!poItem) continue

      poItem.receivedQuantity += receipt.receivedQuantity
      poItem.pendingQuantity = poItem.quantity - poItem.receivedQuantity

      // Update inventory
      const item = this.items.find((i) => i.id === receipt.itemId)
      if (item) {
        item.currentStock += receipt.receivedQuantity
        item.availableStock = item.currentStock - item.reservedStock

        if (receipt.batchNumber) {
          item.batchNumbers.push(receipt.batchNumber)
        }

        if (receipt.expiryDate && !item.expiryDate) {
          item.expiryDate = receipt.expiryDate
        }

        // Record movement
        await this.recordMovement({
          itemId: receipt.itemId,
          itemName: item.name,
          type: "IN",
          reason: "Purchase Order Receipt",
          quantity: receipt.receivedQuantity,
          toLocation: item.location,
          referenceNumber: po.orderNumber,
          referenceType: "Purchase Order",
          batchNumber: receipt.batchNumber,
          expiryDate: receipt.expiryDate,
          performedBy: "system", // Would be current user
        })
      }
    }

    // Update PO status
    const allReceived = po.items.every((item) => item.receivedQuantity >= item.quantity)
    const partiallyReceived = po.items.some((item) => item.receivedQuantity > 0)

    if (allReceived) {
      po.status = "Received"
      po.actualDeliveryDate = new Date().toISOString()
    } else if (partiallyReceived) {
      po.status = "Partial"
    }

    po.updatedAt = new Date().toISOString()
    return true
  }

  // Supplier Management
  async createSupplier(supplier: Omit<Supplier, "id" | "createdAt" | "updatedAt">): Promise<Supplier> {
    const newSupplier: Supplier = {
      ...supplier,
      id: `SUP-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }

    this.suppliers.push(newSupplier)
    return newSupplier
  }

  async updateSupplierRating(supplierId: string, rating: number): Promise<boolean> {
    const supplier = this.suppliers.find((s) => s.id === supplierId)
    if (!supplier) return false

    supplier.rating = Math.max(1, Math.min(5, rating))
    supplier.updatedAt = new Date().toISOString()
    return true
  }

  // Stock Alerts
  private async checkStockLevels(itemId: string): Promise<void> {
    const item = this.items.find((i) => i.id === itemId)
    if (!item) return

    // Clear existing alerts for this item
    this.alerts = this.alerts.filter((a) => a.itemId !== itemId)

    // Check for low stock
    if (item.currentStock <= item.reorderPoint) {
      await this.createAlert({
        type: item.currentStock === 0 ? "Out of Stock" : "Low Stock",
        itemId: item.id,
        itemName: item.name,
        currentStock: item.currentStock,
        threshold: item.reorderPoint,
        severity: item.currentStock === 0 ? "Critical" : "High",
        message:
          item.currentStock === 0
            ? `${item.name} is out of stock`
            : `${item.name} is below reorder point (${item.currentStock}/${item.reorderPoint})`,
      })
    }

    // Check for overstock
    if (item.currentStock > item.maxStockLevel) {
      await this.createAlert({
        type: "Overstock",
        itemId: item.id,
        itemName: item.name,
        currentStock: item.currentStock,
        threshold: item.maxStockLevel,
        severity: "Medium",
        message: `${item.name} exceeds maximum stock level (${item.currentStock}/${item.maxStockLevel})`,
      })
    }

    // Check for expiry warning
    if (item.expiryDate) {
      const expiryDate = new Date(item.expiryDate)
      const warningDate = new Date()
      warningDate.setDate(warningDate.getDate() + 30) // 30 days warning

      if (expiryDate <= warningDate) {
        await this.createAlert({
          type: "Expiry Warning",
          itemId: item.id,
          itemName: item.name,
          currentStock: item.currentStock,
          threshold: 30,
          severity: expiryDate <= new Date() ? "Critical" : "High",
          message: `${item.name} expires on ${item.expiryDate}`,
        })
      }
    }
  }

  private async createAlert(alert: Omit<StockAlert, "id" | "acknowledged" | "createdAt">): Promise<StockAlert> {
    const newAlert: StockAlert = {
      ...alert,
      id: `ALT-${Date.now()}`,
      acknowledged: false,
      createdAt: new Date().toISOString(),
    }

    this.alerts.push(newAlert)
    return newAlert
  }

  async acknowledgeAlert(alertId: string, acknowledgedBy: string): Promise<boolean> {
    const alert = this.alerts.find((a) => a.id === alertId)
    if (!alert) return false

    alert.acknowledged = true
    alert.acknowledgedBy = acknowledgedBy
    alert.acknowledgedAt = new Date().toISOString()
    return true
  }

  // Stock Take Management
  async createStockTake(
    stockTake: Omit<StockTake, "id" | "records" | "totalVariance" | "totalVarianceValue" | "createdAt" | "updatedAt">,
  ): Promise<StockTake> {
    const newStockTake: StockTake = {
      ...stockTake,
      id: `ST-${Date.now()}`,
      records: [],
      totalVariance: 0,
      totalVarianceValue: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }

    this.stockTakes.push(newStockTake)
    return newStockTake
  }

  async recordStockCount(
    stockTakeId: string,
    record: Omit<StockTakeRecord, "id" | "variance" | "varianceValue">,
  ): Promise<boolean> {
    const stockTake = this.stockTakes.find((st) => st.id === stockTakeId)
    const item = this.items.find((i) => i.id === record.itemId)

    if (!stockTake || !item) return false

    const variance = record.actualQuantity - record.expectedQuantity
    const varianceValue = variance * item.costPrice

    const newRecord: StockTakeRecord = {
      ...record,
      id: `STR-${Date.now()}`,
      variance,
      varianceValue,
    }

    stockTake.records.push(newRecord)
    stockTake.totalVariance += Math.abs(variance)
    stockTake.totalVarianceValue += Math.abs(varianceValue)
    stockTake.updatedAt = new Date().toISOString()

    return true
  }

  async completeStockTake(stockTakeId: string, adjustInventory = true): Promise<boolean> {
    const stockTake = this.stockTakes.find((st) => st.id === stockTakeId)
    if (!stockTake) return false

    stockTake.status = "Completed"
    stockTake.completedDate = new Date().toISOString()

    if (adjustInventory) {
      for (const record of stockTake.records) {
        if (record.variance !== 0) {
          await this.adjustStock(
            record.itemId,
            record.variance,
            `Stock Take Adjustment - ${stockTake.name}`,
            record.countedBy,
            `Variance: ${record.variance} units`,
          )
        }
      }
    }

    return true
  }

  // Analytics and Reporting
  async getInventoryMetrics(): Promise<InventoryMetrics> {
    const totalItems = this.items.length
    const totalValue = this.items.reduce((sum, item) => sum + item.currentStock * item.costPrice, 0)
    const lowStockItems = this.items.filter((item) => item.currentStock <= item.reorderPoint).length
    const outOfStockItems = this.items.filter((item) => item.currentStock === 0).length
    const overstockItems = this.items.filter((item) => item.currentStock > item.maxStockLevel).length

    return {
      totalItems,
      totalValue,
      lowStockItems,
      outOfStockItems,
      overstockItems,
      turnoverRate: this.calculateTurnoverRate(),
      averageLeadTime: this.calculateAverageLeadTime(),
      stockAccuracy: this.calculateStockAccuracy(),
      carryingCost: totalValue * 0.25, // Assuming 25% carrying cost
    }
  }

  private calculateTurnoverRate(): number {
    // Calculate based on movements in the last 12 months
    const oneYearAgo = new Date()
    oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1)

    const outMovements = this.movements.filter((m) => m.type === "OUT" && new Date(m.createdAt) >= oneYearAgo)

    const totalOutQuantity = outMovements.reduce((sum, m) => sum + m.quantity, 0)
    const averageInventory = this.items.reduce((sum, item) => sum + item.currentStock, 0) / this.items.length

    return averageInventory > 0 ? totalOutQuantity / averageInventory : 0
  }

  private calculateAverageLeadTime(): number {
    const receivedPOs = this.purchaseOrders.filter((po) => po.status === "Received" && po.actualDeliveryDate)
    if (receivedPOs.length === 0) return 0

    const totalLeadTime = receivedPOs.reduce((sum, po) => {
      const orderDate = new Date(po.orderDate)
      const deliveryDate = new Date(po.actualDeliveryDate!)
      const leadTime = Math.floor((deliveryDate.getTime() - orderDate.getTime()) / (1000 * 60 * 60 * 24))
      return sum + leadTime
    }, 0)

    return totalLeadTime / receivedPOs.length
  }

  private calculateStockAccuracy(): number {
    const completedStockTakes = this.stockTakes.filter((st) => st.status === "Completed")
    if (completedStockTakes.length === 0) return 100

    const latestStockTake = completedStockTakes.sort(
      (a, b) => new Date(b.completedDate!).getTime() - new Date(a.completedDate!).getTime(),
    )[0]

    const totalRecords = latestStockTake.records.length
    const accurateRecords = latestStockTake.records.filter((r) => r.variance === 0).length

    return totalRecords > 0 ? (accurateRecords / totalRecords) * 100 : 100
  }

  // Reorder Management
  async generateReorderSuggestions(): Promise<
    Array<{
      item: InventoryItem
      suggestedQuantity: number
      estimatedCost: number
      supplier: Supplier
    }>
  > {
    const suggestions = []

    for (const item of this.items) {
      if (item.currentStock <= item.reorderPoint && item.status === "Active") {
        const supplier = this.suppliers.find((s) => s.id === item.supplier.id)
        if (supplier && supplier.status === "Active") {
          suggestions.push({
            item,
            suggestedQuantity: item.reorderQuantity,
            estimatedCost: item.reorderQuantity * item.costPrice,
            supplier,
          })
        }
      }
    }

    return suggestions
  }

  async createReorderPurchaseOrder(
    suggestions: Array<{
      item: InventoryItem
      quantity: number
    }>,
    supplierId: string,
  ): Promise<PurchaseOrder | null> {
    const supplier = this.suppliers.find((s) => s.id === supplierId)
    if (!supplier) return null

    const items = suggestions.map((suggestion) => ({
      itemId: suggestion.item.id,
      itemName: suggestion.item.name,
      sku: suggestion.item.sku,
      quantity: suggestion.quantity,
      unitPrice: suggestion.item.costPrice,
      totalPrice: suggestion.quantity * suggestion.item.costPrice,
      receivedQuantity: 0,
      pendingQuantity: suggestion.quantity,
    }))

    const subtotal = items.reduce((sum, item) => sum + item.totalPrice, 0)
    const taxAmount = subtotal * 0.1 // Assuming 10% tax
    const totalAmount = subtotal + taxAmount

    return this.createPurchaseOrder({
      orderNumber: `PO-${Date.now()}`,
      supplierId: supplier.id,
      supplierName: supplier.name,
      status: "Draft",
      orderDate: new Date().toISOString(),
      expectedDeliveryDate: new Date(Date.now() + supplier.leadTime * 24 * 60 * 60 * 1000).toISOString(),
      items,
      subtotal,
      taxAmount,
      shippingCost: 0,
      totalAmount,
      currency: "USD",
      paymentTerms: supplier.paymentTerms,
      deliveryAddress: "Main Warehouse", // Would be configurable
      createdBy: "system", // Would be current user
    })
  }

  // Query methods
  getItems(filters?: {
    category?: string
    status?: string
    lowStock?: boolean
    supplier?: string
  }): InventoryItem[] {
    let filtered = this.items

    if (filters?.category) {
      filtered = filtered.filter((item) => item.category === filters.category)
    }
    if (filters?.status) {
      filtered = filtered.filter((item) => item.status === filters.status)
    }
    if (filters?.lowStock) {
      filtered = filtered.filter((item) => item.currentStock <= item.reorderPoint)
    }
    if (filters?.supplier) {
      filtered = filtered.filter((item) => item.supplier.id === filters.supplier)
    }

    return filtered
  }

  searchItems(query: string): InventoryItem[] {
    const lowercaseQuery = query.toLowerCase()
    return this.items.filter(
      (item) =>
        item.name.toLowerCase().includes(lowercaseQuery) ||
        item.sku.toLowerCase().includes(lowercaseQuery) ||
        item.description.toLowerCase().includes(lowercaseQuery) ||
        item.barcode?.toLowerCase().includes(lowercaseQuery) ||
        item.tags.some((tag) => tag.toLowerCase().includes(lowercaseQuery)),
    )
  }

  getPurchaseOrders(status?: string): PurchaseOrder[] {
    return status ? this.purchaseOrders.filter((po) => po.status === status) : this.purchaseOrders
  }

  getSuppliers(status?: string): Supplier[] {
    return status ? this.suppliers.filter((s) => s.status === status) : this.suppliers
  }

  getAlerts(acknowledged?: boolean): StockAlert[] {
    return acknowledged !== undefined ? this.alerts.filter((a) => a.acknowledged === acknowledged) : this.alerts
  }

  getStockTakes(status?: string): StockTake[] {
    return status ? this.stockTakes.filter((st) => st.status === status) : this.stockTakes
  }
}
