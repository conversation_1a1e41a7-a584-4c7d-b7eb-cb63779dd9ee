import type { LeaseAgreement, PaymentSchedule, LeaseRenewal, LeaseMetrics } from "./types"

export class AssetLeasingService {
  private static instance: AssetLeasingService
  private leases: LeaseAgreement[] = []
  private payments: PaymentSchedule[] = []
  private renewals: LeaseRenewal[] = []

  static getInstance(): AssetLeasingService {
    if (!AssetLeasingService.instance) {
      AssetLeasingService.instance = new AssetLeasingService()
    }
    return AssetLeasingService.instance
  }

  // Lease Management
  async createLease(lease: Omit<LeaseAgreement, "id" | "createdAt" | "updatedAt">): Promise<LeaseAgreement> {
    const newLease: LeaseAgreement = {
      ...lease,
      id: `LSE-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }

    this.leases.push(newLease)
    await this.generatePaymentSchedule(newLease)
    return newLease
  }

  async updateLease(id: string, updates: Partial<LeaseAgreement>): Promise<LeaseAgreement | null> {
    const index = this.leases.findIndex((lease) => lease.id === id)
    if (index === -1) return null

    this.leases[index] = {
      ...this.leases[index],
      ...updates,
      updatedAt: new Date().toISOString(),
    }
    return this.leases[index]
  }

  async terminateLease(id: string, terminationDate: string, reason: string): Promise<boolean> {
    const lease = this.leases.find((l) => l.id === id)
    if (!lease) return false

    lease.status = "Terminated"
    lease.endDate = terminationDate
    lease.updatedAt = new Date().toISOString()

    // Cancel future payments
    this.payments
      .filter((p) => p.leaseId === id && new Date(p.dueDate) > new Date(terminationDate))
      .forEach((p) => (p.status = "Pending")) // Mark as cancelled

    return true
  }

  // Payment Management
  private async generatePaymentSchedule(lease: LeaseAgreement): Promise<void> {
    const startDate = new Date(lease.startDate)
    const endDate = new Date(lease.endDate)
    const monthlyPayment = lease.monthlyPayment

    const payments: PaymentSchedule[] = []
    const currentDate = new Date(startDate)
    let paymentNumber = 1

    while (currentDate <= endDate) {
      payments.push({
        id: `PAY-${lease.id}-${paymentNumber}`,
        leaseId: lease.id,
        dueDate: currentDate.toISOString().split("T")[0],
        amount: monthlyPayment,
        status: "Pending",
        paidAmount: 0,
        lateFee: 0,
      })

      currentDate.setMonth(currentDate.getMonth() + 1)
      paymentNumber++
    }

    this.payments.push(...payments)
  }

  async recordPayment(paymentId: string, amount: number, paymentMethod: string): Promise<boolean> {
    const payment = this.payments.find((p) => p.id === paymentId)
    if (!payment) return false

    payment.paidAmount += amount
    payment.paidDate = new Date().toISOString()
    payment.paymentMethod = paymentMethod
    payment.transactionId = `TXN-${Date.now()}`

    if (payment.paidAmount >= payment.amount) {
      payment.status = "Paid"
    } else {
      payment.status = "Partial"
    }

    return true
  }

  async calculateLateFees(): Promise<void> {
    const today = new Date()
    const overduePayments = this.payments.filter((p) => p.status === "Pending" && new Date(p.dueDate) < today)

    overduePayments.forEach((payment) => {
      const daysOverdue = Math.floor((today.getTime() - new Date(payment.dueDate).getTime()) / (1000 * 60 * 60 * 24))
      payment.lateFee = Math.min(payment.amount * 0.05, daysOverdue * 10) // 5% or $10/day max
      payment.status = "Overdue"
    })
  }

  // Renewal Management
  async proposeRenewal(leaseId: string, newTerms: string, rateAdjustment: number): Promise<LeaseRenewal> {
    const renewal: LeaseRenewal = {
      id: `REN-${Date.now()}`,
      originalLeaseId: leaseId,
      newLeaseId: "",
      renewalDate: new Date().toISOString(),
      newTerms,
      rateAdjustment,
      status: "Proposed",
    }

    this.renewals.push(renewal)
    return renewal
  }

  async processRenewal(renewalId: string, approved: boolean): Promise<boolean> {
    const renewal = this.renewals.find((r) => r.id === renewalId)
    if (!renewal) return false

    if (approved) {
      const originalLease = this.leases.find((l) => l.id === renewal.originalLeaseId)
      if (!originalLease) return false

      // Create new lease based on original
      const newLease = await this.createLease({
        ...originalLease,
        startDate: originalLease.endDate,
        endDate: new Date(
          new Date(originalLease.endDate).setFullYear(new Date(originalLease.endDate).getFullYear() + 1),
        ).toISOString(),
        monthlyPayment: originalLease.monthlyPayment * (1 + renewal.rateAdjustment / 100),
        status: "Active",
        paymentSchedule: [],
      })

      renewal.newLeaseId = newLease.id
      renewal.status = "Completed"

      // Update original lease
      originalLease.status = "Renewed"
    } else {
      renewal.status = "Rejected"
    }

    return true
  }

  // Analytics and Reporting
  async getLeaseMetrics(): Promise<LeaseMetrics> {
    const activeLeases = this.leases.filter((l) => l.status === "Active")
    const totalValue = activeLeases.reduce((sum, lease) => sum + lease.totalValue, 0)
    const monthlyRevenue = activeLeases.reduce((sum, lease) => sum + lease.monthlyPayment, 0)

    const renewedLeases = this.leases.filter((l) => l.status === "Renewed").length
    const expiredLeases = this.leases.filter((l) => l.status === "Expired").length
    const renewalRate = expiredLeases > 0 ? (renewedLeases / expiredLeases) * 100 : 0

    const overduePayments = this.payments.filter((p) => p.status === "Overdue").length
    const totalPayments = this.payments.length
    const defaultRate = totalPayments > 0 ? (overduePayments / totalPayments) * 100 : 0

    return {
      totalActiveLeases: activeLeases.length,
      totalLeaseValue: totalValue,
      monthlyRevenue,
      averageLeaseLength: this.calculateAverageLeaseLength(),
      renewalRate,
      defaultRate,
      utilizationRate: this.calculateUtilizationRate(),
    }
  }

  private calculateAverageLeaseLength(): number {
    if (this.leases.length === 0) return 0

    const totalMonths = this.leases.reduce((sum, lease) => {
      const start = new Date(lease.startDate)
      const end = new Date(lease.endDate)
      const months = (end.getFullYear() - start.getFullYear()) * 12 + (end.getMonth() - start.getMonth())
      return sum + months
    }, 0)

    return totalMonths / this.leases.length
  }

  private calculateUtilizationRate(): number {
    // This would typically integrate with asset management
    // For now, return a calculated value
    return 85
  }

  // Query methods
  getLeases(filters?: {
    status?: string
    lessorId?: string
    lesseeId?: string
    assetId?: string
  }): LeaseAgreement[] {
    let filtered = this.leases

    if (filters?.status) {
      filtered = filtered.filter((l) => l.status === filters.status)
    }
    if (filters?.lessorId) {
      filtered = filtered.filter((l) => l.lessorId === filters.lessorId)
    }
    if (filters?.lesseeId) {
      filtered = filtered.filter((l) => l.lesseeId === filters.lesseeId)
    }
    if (filters?.assetId) {
      filtered = filtered.filter((l) => l.assetId === filters.assetId)
    }

    return filtered
  }

  getPayments(leaseId?: string): PaymentSchedule[] {
    return leaseId ? this.payments.filter((p) => p.leaseId === leaseId) : this.payments
  }

  getRenewals(status?: string): LeaseRenewal[] {
    return status ? this.renewals.filter((r) => r.status === status) : this.renewals
  }
}
