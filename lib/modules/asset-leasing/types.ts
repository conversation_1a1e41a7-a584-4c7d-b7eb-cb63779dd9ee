export interface LeaseAgreement {
  id: string
  assetId: string
  assetName?: string // Joined from Asset
  lessorId: string
  lessorName: string
  lesseeId: string
  lesseeName: string
  leaseType: string // "Operating", "Finance", "Capital"
  startDate: string
  endDate: string
  monthlyPayment: number
  totalValue: number
  securityDeposit: number
  status: string // "Draft", "Active", "Expired", "Terminated", "Renewed"
  renewalOptions: number
  earlyTerminationClause: boolean
  maintenanceResponsibility: string // "Lessor", "Lessee", "Shared"
  insuranceRequirement: boolean
  terms?: string
  attachments: string[]
  createdAt: string
  updatedAt: string
  paymentSchedule?: PaymentSchedule[]
}

export interface PaymentSchedule {
  id: string
  leaseId: string
  dueDate: string
  amount: number
  status: string // "Pending", "Paid", "Overdue", "Partial", "Cancelled"
  paidAmount: number
  paidDate?: string
  paymentMethod?: string
  transactionId?: string
  lateFee: number
  createdAt?: string
  updatedAt?: string
}

export interface LeaseRenewal {
  id: string
  originalLeaseId: string
  newLeaseId?: string
  renewalDate: string
  newTerms?: string
  rateAdjustment: number
  status: string // "Proposed", "Approved", "Rejected", "Completed"
  createdAt?: string
  updatedAt?: string
}

export interface LeaseMetrics {
  totalActiveLeases: number
  totalLeaseValue?: number
  monthlyRevenue: number
  averageLeaseLength: number
  renewalRate: number
  defaultRate?: number
  utilizationRate: number
}
