import { 
  Client, 
  Storage,
  Functions,

  ID
} from "appwrite";

// Singleton pattern for Appwrite client
let client: Client;
let storage: Storage;
let functions: Functions;

function getClient() {
  if (!client) {
    client = new Client()
      .setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT!)
      .setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID!);
  }
  return client;
}

function getStorage() {
  if (!storage) {
    storage = new Storage(getClient());
  }
  return storage;
}



const BUCKET_ID = process.env.NEXT_PUBLIC_APPWRITE_BUCKET_ID!;

export interface FileUpload {
  id: string;
  filename: string;
  size: number;
  contentType: string;
  url: string;
}

export async function uploadFile(
  file: File,
  onProgress?: (progress: { loaded: number; total: number; percentage: number }) => void
): Promise<FileUpload> {
  try {
    if (file.size > 10 * 1024 * 1024) { // 10MB limit
      throw new Error("File size exceeds 10MB limit");
    }

    const uploadedFile = await getStorage().createFile(
      BUCKET_ID,
      ID.unique(),
      file,
      undefined, // permissions
      (progress) => {
        if (onProgress) {
          const percentage = Math.round((progress.loaded / progress.total) * 100);
          onProgress({ ...progress, percentage });
        }
      }
    );

    const fileUrl = getStorage().getFileView(BUCKET_ID, uploadedFile.$id);

    return {
      id: uploadedFile.$id,
      filename: uploadedFile.name,
      size: uploadedFile.sizeOriginal,
      contentType: uploadedFile.mimeType,
      url: fileUrl.href,
    };
  } catch (error) {
    console.error("Appwrite upload error:", error);
    throw new Error(
      error instanceof Error ? error.message : "Failed to upload file"
    );
  }
}

export async function deleteFile(fileId: string): Promise<void> {
  try {
    await getStorage().deleteFile(BUCKET_ID, fileId);
  } catch (error) {
    console.error("Appwrite delete error:", error);
    throw new Error("Failed to delete file");
  }
}
