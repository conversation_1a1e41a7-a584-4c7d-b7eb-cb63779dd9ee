import { z } from "zod"

// Asset validation schemas
export const assetSchema = z.object({
  name: z.string().min(1, "Asset name is required").max(100, "Asset name too long"),
  description: z.string().optional(),
  /* serialNumber: z.string().min(1, "Serial number is required"), */
  assetTypeId: z.string().min(1, "Asset type is required"),
  locationId: z.string().min(1, "Location is required"),
  departmentId: z.string().min(1, "Department is required"),
  purchasePrice: z.number().min(0, "Purchase price must be positive"),
  purchaseDate: z.string().refine((date) => !isNaN(Date.parse(date)), "Invalid date"),
  warrantyExpiration: z.string().optional(),
  status: z.enum(["active", "inactive", "maintenance", "disposed"]),
})

export const maintenanceSchema = z.object({
  assetId: z.string().min(1, "Asset ID is required"),
  type: z.enum(["preventive", "corrective", "emergency"]),
  title: z.string().min(1, "Title is required").max(200, "Title too long"),
  description: z.string().optional(),
  scheduledDate: z.string().refine((date) => !isNaN(Date.parse(date)), "Invalid date"),
  estimatedDuration: z.number().min(1, "Duration must be at least 1 hour"),
  assignedTo: z.string().min(1, "Technician assignment required"),
  priority: z.enum(["low", "medium", "high", "critical"]),
  cost: z.number().min(0, "Cost must be positive").optional(),
})

export const userSchema = z.object({
  email: z.string().email("Invalid email address"),
  firstName: z.string().min(1, "First name is required").max(50, "First name too long"),
  lastName: z.string().min(1, "Last name is required").max(50, "Last name too long"),
  role: z.enum(["admin", "manager", "technician", "viewer"]),
  department: z.string().min(1, "Department is required"),
  phone: z.string().optional(),
  isActive: z.boolean().default(true),
})

export const financialAssetSchema = z.object({
  assetId: z.string().min(1, "Asset ID is required"),
  acquisitionCost: z.number().min(0, "Acquisition cost must be positive"),
  currentBookValue: z.number().min(0, "Book value must be positive"),
  marketValue: z.number().min(0, "Market value must be positive"),
  depreciationMethod: z.enum(["straight_line", "declining_balance", "sum_of_years"]),
  usefulLife: z.number().min(1, "Useful life must be at least 1 year"),
  salvageValue: z.number().min(0, "Salvage value must be positive"),
})

// Validation helper functions
export const validateData = <T>(schema: z.ZodSchema<T>, data: unknown): T => {\
  try {\
    return schema.parse(data)
  } catch (error) {\
    if (error instanceof z.ZodError) {\
      const errorMessages = error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ')
      throw new Error(`Validation failed: ${errorMessages}`)
    }
    throw error
  }
}

export const sanitizeInput = (input: string): string => {\
  return input.trim().replace(/[<>]/g, '')
}

export const validateFileUpload = (file: File, allowedTypes: string[], maxSize: number) => {\
  if (!allowedTypes.includes(file.type)) {\
    throw new Error(`File type ${file.type} not allowed`)
  }
  
  if (file.size > maxSize) {\
    throw new Error(`File size exceeds ${maxSize / 1024 / 1024}MB limit`)
  }
  
  return true
}\
