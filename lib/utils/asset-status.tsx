import { Badge } from "@/components/ui/badge";
import { ASSET_STATUS_COLORS } from "@/lib/config/dashboard-config";

export function getStatusBadge(status: string) {
  const normalizedStatus = status.toLowerCase();
  
  switch (normalizedStatus) {
    case "active":
      return <Badge className="bg-green-100 text-green-800">Active</Badge>;
    case "maintenance":
      return <Badge className="bg-yellow-100 text-yellow-800">Maintenance</Badge>;
    case "disposed":
      return <Badge className="bg-red-100 text-red-800">Disposed</Badge>;
    case "inactive":
      return <Badge className="bg-gray-100 text-gray-800">Inactive</Badge>;
    default:
      return <Badge variant="secondary">{status}</Badge>;
  }
}

export function getStatusColor(status: string): string {
  const normalizedStatus = status.toLowerCase() as keyof typeof ASSET_STATUS_COLORS;
  return ASSET_STATUS_COLORS[normalizedStatus] || "gray";
}

export function getStatusText(status: string): string {
  switch (status.toLowerCase()) {
    case "active":
      return "Active";
    case "maintenance":
      return "Under Maintenance";
    case "disposed":
      return "Disposed";
    case "inactive":
      return "Inactive";
    default:
      return status;
  }
}

export const ASSET_STATUSES = [
  { value: "active", label: "Active", color: "green" },
  { value: "maintenance", label: "Maintenance", color: "yellow" },
  { value: "inactive", label: "Inactive", color: "gray" },
  { value: "disposed", label: "Disposed", color: "red" },
] as const;