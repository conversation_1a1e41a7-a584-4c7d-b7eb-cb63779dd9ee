import CryptoJS from "crypto-js"

// Security utilities for production
export class SecurityManager {
  private static readonly ENCRYPTION_KEY = process.env.NEXT_PUBLIC_ENCRYPTION_KEY || "default-key"

  // Encrypt sensitive data
  static encrypt(data: string): string {
    return CryptoJS.AES.encrypt(data, this.ENCRYPTION_KEY).toString()
  }

  // Decrypt sensitive data
  static decrypt(encryptedData: string): string {
    const bytes = CryptoJS.AES.decrypt(encryptedData, this.ENCRYPTION_KEY)
    return bytes.toString(CryptoJS.enc.Utf8)
  }

  // Generate secure random tokens
  static generateToken(length = 32): string {
    const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
    let result = ""
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }

  // Validate user permissions
  static hasPermission(userRole: string, requiredPermission: string): boolean {
    const rolePermissions: Record<string, string[]> = {
      admin: ["read", "write", "delete", "manage_users", "manage_system"],
      manager: ["read", "write", "manage_assets", "view_reports"],
      technician: ["read", "write", "manage_maintenance"],
      viewer: ["read"],
    }

    return rolePermissions[userRole]?.includes(requiredPermission) || false
  }

  // Sanitize user input to prevent XSS
  static sanitizeHtml(input: string): string {
    const map: Record<string, string> = {
      "&": "&amp;",
      "<": "&lt;",
      ">": "&gt;",
      '"': "&quot;",
      "'": "&#x27;",
      "/": "&#x2F;",
    }

    return input.replace(/[&<>"'/]/g, (s) => map[s])
  }

  // Rate limiting for API calls
  static createRateLimiter(maxRequests: number, windowMs: number) {
    const requests = new Map<string, number[]>()

    return (identifier: string): boolean => {
      const now = Date.now()
      const windowStart = now - windowMs

      if (!requests.has(identifier)) {
        requests.set(identifier, [])
      }

      const userRequests = requests.get(identifier)!
      const validRequests = userRequests.filter((time) => time > windowStart)

      if (validRequests.length >= maxRequests) {
        return false
      }

      validRequests.push(now)
      requests.set(identifier, validRequests)
      return true
    }
  }

  // Content Security Policy headers
  static getCSPHeaders(): Record<string, string> {
    return {
      "Content-Security-Policy": [
        "default-src 'self'",
        "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
        "style-src 'self' 'unsafe-inline'",
        "img-src 'self' data: https:",
        "font-src 'self'",
        "connect-src 'self'",
        "frame-ancestors 'none'",
      ].join("; "),
      "X-Frame-Options": "DENY",
      "X-Content-Type-Options": "nosniff",
      "Referrer-Policy": "strict-origin-when-cross-origin",
      "Permissions-Policy": "camera=(), microphone=(), geolocation=()",
    }
  }
}

// Session management
export class SessionManager {
  private static readonly SESSION_KEY = "wizeassets_session"
  private static readonly SESSION_TIMEOUT = 30 * 60 * 1000 // 30 minutes

  static createSession(userId: string, userRole: string) {
    const session = {
      userId,
      userRole,
      createdAt: Date.now(),
      lastActivity: Date.now(),
      token: SecurityManager.generateToken(),
    }

    localStorage.setItem(this.SESSION_KEY, JSON.stringify(session))
    return session
  }

  static getSession() {
    try {
      const sessionData = localStorage.getItem(this.SESSION_KEY)
      if (!sessionData) return null

      const session = JSON.parse(sessionData)

      // Check if session is expired
      if (Date.now() - session.lastActivity > this.SESSION_TIMEOUT) {
        this.clearSession()
        return null
      }

      // Update last activity
      session.lastActivity = Date.now()
      localStorage.setItem(this.SESSION_KEY, JSON.stringify(session))

      return session
    } catch {
      return null
    }
  }

  static clearSession() {
    localStorage.removeItem(this.SESSION_KEY)
  }

  static isAuthenticated(): boolean {
    return this.getSession() !== null
  }

  static hasRole(requiredRole: string): boolean {
    const session = this.getSession()
    return session?.userRole === requiredRole
  }
}
