import { Badge } from "@/components/ui/badge";
import { OPERATION_TYPE_COLORS } from "@/lib/config/dashboard-config";

export function getOperationTypeBadge(operationType: string) {
  switch (operationType) {
    case "asset.create":
      return <Badge className="bg-blue-100 text-blue-800">Created</Badge>;
    case "asset.update":
      return <Badge className="bg-purple-100 text-purple-800">Updated</Badge>;
    case "asset.transfer":
      return <Badge className="bg-indigo-100 text-indigo-800">Transferred</Badge>;
    case "asset.disposal":
      return <Badge className="bg-red-100 text-red-800">Disposed</Badge>;
    case "maintenance.log":
      return <Badge className="bg-orange-100 text-orange-800">Maintenance</Badge>;
    case "maintenance.schedule":
      return <Badge className="bg-yellow-100 text-yellow-800">Scheduled</Badge>;
    case "inventory.audit":
      return <Badge className="bg-green-100 text-green-800">Audited</Badge>;
    default:
      return <Badge variant="outline">{operationType.replace(/\./g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</Badge>;
  }
}

export function getOperationTypeColor(operationType: string): string {
  const normalizedType = operationType as keyof typeof OPERATION_TYPE_COLORS;
  return OPERATION_TYPE_COLORS[normalizedType] || "gray";
}

export function getOperationTypeLabel(operationType: string): string {
  switch (operationType) {
    case "asset.create":
      return "Asset Created";
    case "asset.update":
      return "Asset Updated";
    case "asset.transfer":
      return "Asset Transferred";
    case "asset.disposal":
      return "Asset Disposed";
    case "maintenance.log":
      return "Maintenance Logged";
    case "maintenance.schedule":
      return "Maintenance Scheduled";
    case "inventory.audit":
      return "Inventory Audited";
    default:
      return operationType.replace(/\./g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }
}

export const OPERATION_TYPES = [
  { value: "asset.create", label: "Asset Creation", color: "blue" },
  { value: "asset.update", label: "Asset Update", color: "purple" },
  { value: "asset.transfer", label: "Asset Transfer", color: "indigo" },
  { value: "asset.disposal", label: "Asset Disposal", color: "red" },
  { value: "maintenance.log", label: "Maintenance Log", color: "orange" },
  { value: "maintenance.schedule", label: "Maintenance Schedule", color: "yellow" },
  { value: "inventory.audit", label: "Inventory Audit", color: "green" },
] as const;