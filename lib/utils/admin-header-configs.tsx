import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Plus,
  Download,
  Upload,
  Settings,
  BarChart3,
  Brain,
  FileText,
  Wrench,
  Calculator,
  TrendingUp,
  Users,
  Package,
  Zap,
  Filter,
  Search,
  Eye,
  RefreshCw,
  Calendar,
  Target,
  Shield,
  Database,
  Workflow,
  CreditCard,
  Building,
  ShoppingCart,
  Layers,
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  DollarSign,
} from "lucide-react";

export interface HeaderConfig {
  title: string;
  description?: string;
  breadcrumbs?: { label: string; url: string }[];
  actions?: React.ReactNode[];
  variant?: 'default' | 'dashboard' | 'management' | 'analytics' | 'settings';
}

// Dashboard Header Configuration
export const getDashboardHeaderConfig = (): HeaderConfig => ({
  title: "Dashboard",
  description: "Welcome back! Here's what's happening with your assets today.",
  variant: 'dashboard',
  actions: [
    <Button variant="outline" size="sm" key="reports">
      <BarChart3 className="h-4 w-4 mr-2" />
      View Reports
    </Button>,
    <Button size="sm" key="ai-insights">
      <Brain className="h-4 w-4 mr-2" />
      AI Insights
    </Button>,
  ],
});

// Asset Management Header Configuration
export const getAssetManagementHeaderConfig = (
  isAddDialogOpen: boolean,
  setIsAddDialogOpen: (open: boolean) => void
): HeaderConfig => ({
  title: "Asset Management",
  breadcrumbs: [
    { label: "Admin", url: "/admin" },
    { label: "Assets", url: "/admin/assets" },
  ],
  variant: 'management',
  actions: [
    <Button variant="outline" key="import">
      <Upload className="mr-2 h-4 w-4" />
      Import
    </Button>,
    <Button variant="outline" key="export">
      <Download className="mr-2 h-4 w-4" />
      Export
    </Button>,
    <Button key="add-asset" onClick={() => setIsAddDialogOpen(true)}>
      <Plus className="mr-2 h-4 w-4" />
      Add Asset
    </Button>,
  ],
});

// Asset Types Header Configuration
export const getAssetTypesHeaderConfig = (
  setIsCreateDialogOpen: (open: boolean) => void
): HeaderConfig => ({
  title: "Asset Type Management",
  description: "Configure asset types with custom fields, lifecycle stages, and maintenance schedules",
  breadcrumbs: [
    { label: "Admin", url: "/admin" },
    { label: "Asset Types", url: "/admin/asset-types" },
  ],
  variant: 'management',
  actions: [
    <Button variant="outline" key="import">
      <Upload className="mr-2 h-4 w-4" />
      Import
    </Button>,
    <Button variant="outline" key="export">
      <Download className="mr-2 h-4 w-4" />
      Export
    </Button>,
    <Button key="create-type" onClick={() => setIsCreateDialogOpen(true)}>
      <Plus className="mr-2 h-4 w-4" />
      Create Asset Type
    </Button>,
  ],
});

// Financial Management Header Configuration
export const getFinancialHeaderConfig = (): HeaderConfig => ({
  title: "Financial Management",
  description: "Comprehensive financial analysis and asset valuation",
  breadcrumbs: [
    { label: "Admin", url: "/admin" },
    { label: "Financial", url: "/admin/financial" },
  ],
  variant: 'analytics',
  actions: [
    <Button variant="outline" key="export-financial">
      <Download className="mr-2 h-4 w-4" />
      Export Report
    </Button>,
    <Button variant="outline" key="calculate">
      <Calculator className="mr-2 h-4 w-4" />
      Calculate
    </Button>,
    <Button key="new-analysis">
      <TrendingUp className="mr-2 h-4 w-4" />
      New Analysis
    </Button>,
  ],
});

// Reports Header Configuration
export const getReportsHeaderConfig = (): HeaderConfig => ({
  title: "Reports & Analytics",
  description: "Generate and view comprehensive reports on your assets",
  breadcrumbs: [
    { label: "Admin", url: "/admin" },
    { label: "Reports", url: "/admin/reports" },
  ],
  variant: 'analytics',
  actions: [
    <Button variant="outline" key="schedule">
      <Calendar className="mr-2 h-4 w-4" />
      Schedule Report
    </Button>,
    <Button key="generate">
      <FileText className="mr-2 h-4 w-4" />
      Generate Report
    </Button>,
  ],
});

// Maintenance Header Configuration
export const getMaintenanceHeaderConfig = (): HeaderConfig => ({
  title: "Maintenance Management",
  description: "Schedule and track asset maintenance activities",
  breadcrumbs: [
    { label: "Admin", url: "/admin" },
    { label: "Maintenance", url: "/admin/maintenance" },
  ],
  variant: 'management',
  actions: [
    <Button variant="outline" key="schedule-maintenance">
      <Calendar className="mr-2 h-4 w-4" />
      Schedule
    </Button>,
    <Button key="new-task">
      <Wrench className="mr-2 h-4 w-4" />
      New Task
    </Button>,
  ],
});

// Settings Header Configuration
export const getSettingsHeaderConfig = (): HeaderConfig => ({
  title: "System Settings",
  description: "Configure system preferences and customizations",
  breadcrumbs: [
    { label: "Admin", url: "/admin" },
    { label: "Settings", url: "/admin/settings" },
  ],
  variant: 'settings',
  actions: [
    <Button variant="outline" key="backup">
      <Database className="mr-2 h-4 w-4" />
      Backup
    </Button>,
    <Button key="save-settings">
      <Settings className="mr-2 h-4 w-4" />
      Save Settings
    </Button>,
  ],
});

// Asset Automation Header Configuration
export const getAssetAutomationHeaderConfig = (): HeaderConfig => ({
  title: "Asset Automation",
  description: "Automate asset workflows and processes",
  breadcrumbs: [
    { label: "Admin", url: "/admin" },
    { label: "Asset Automation", url: "/admin/asset-automation" },
  ],
  variant: 'management',
  actions: [
    <Button variant="outline" key="templates">
      <Workflow className="mr-2 h-4 w-4" />
      Templates
    </Button>,
    <Button key="new-workflow">
      <Plus className="mr-2 h-4 w-4" />
      New Workflow
    </Button>,
  ],
});

// Customer Dashboard Header Configuration
export const getCustomerDashboardHeaderConfig = (): HeaderConfig => ({
  title: "Customer Dashboard",
  description: "Customer portal and asset access management",
  breadcrumbs: [
    { label: "Admin", url: "/admin" },
    { label: "Customer Dashboard", url: "/admin/customer-dashboard" },
  ],
  variant: 'management',
  actions: [
    <Button variant="outline" key="permissions">
      <Shield className="mr-2 h-4 w-4" />
      Permissions
    </Button>,
    <Button key="new-customer">
      <Users className="mr-2 h-4 w-4" />
      Add Customer
    </Button>,
  ],
});

// E-commerce Header Configuration
export const getEcommerceHeaderConfig = (): HeaderConfig => ({
  title: "E-commerce Integration",
  description: "Manage online asset sales and marketplace integration",
  breadcrumbs: [
    { label: "Admin", url: "/admin" },
    { label: "E-commerce", url: "/admin/ecommerce" },
  ],
  variant: 'management',
  actions: [
    <Button variant="outline" key="sync">
      <RefreshCw className="mr-2 h-4 w-4" />
      Sync
    </Button>,
    <Button key="new-listing">
      <ShoppingCart className="mr-2 h-4 w-4" />
      New Listing
    </Button>,
  ],
});

// Storefront Header Configuration
export const getStorefrontHeaderConfig = (): HeaderConfig => ({
  title: "Asset Storefront",
  description: "Manage your asset marketplace and listings",
  breadcrumbs: [
    { label: "Admin", url: "/admin" },
    { label: "Storefront", url: "/admin/storefront" },
  ],
  variant: 'management',
  actions: [
    <Button variant="outline" key="preview">
      <Eye className="mr-2 h-4 w-4" />
      Preview
    </Button>,
    <Button key="publish">
      <Building className="mr-2 h-4 w-4" />
      Publish
    </Button>,
  ],
});

// Advanced Features Header Configurations
export const getAdvancedAIHeaderConfig = (): HeaderConfig => ({
  title: "AI Insights",
  description: "Advanced AI-powered asset analytics and predictions",
  breadcrumbs: [
    { label: "Admin", url: "/admin" },
    { label: "Advanced", url: "/admin/advanced" },
    { label: "AI Insights", url: "/admin/advanced/ai-insights" },
  ],
  variant: 'analytics',
  actions: [
    <Button variant="outline" key="train-model">
      <Brain className="mr-2 h-4 w-4" />
      Train Model
    </Button>,
    <Button key="generate-insights">
      <Zap className="mr-2 h-4 w-4" />
      Generate Insights
    </Button>,
  ],
});

export const getAdvancedAutomationHeaderConfig = (): HeaderConfig => ({
  title: "Advanced Automation",
  description: "Complex automation workflows and integrations",
  breadcrumbs: [
    { label: "Admin", url: "/admin" },
    { label: "Advanced", url: "/admin/advanced" },
    { label: "Automation", url: "/admin/advanced/automation" },
  ],
  variant: 'management',
  actions: [
    <Button variant="outline" key="import-workflow">
      <Upload className="mr-2 h-4 w-4" />
      Import
    </Button>,
    <Button key="create-workflow">
      <Workflow className="mr-2 h-4 w-4" />
      Create Workflow
    </Button>,
  ],
});

export const getAdvancedRealTimeHeaderConfig = (): HeaderConfig => ({
  title: "Real-time Monitoring",
  description: "Live asset monitoring and alerts",
  breadcrumbs: [
    { label: "Admin", url: "/admin" },
    { label: "Advanced", url: "/admin/advanced" },
    { label: "Real-time", url: "/admin/advanced/real-time" },
  ],
  variant: 'analytics',
  actions: [
    <Button variant="outline" key="alerts">
      <AlertTriangle className="mr-2 h-4 w-4" />
      Alerts
    </Button>,
    <Button key="monitor">
      <Activity className="mr-2 h-4 w-4" />
      Monitor
    </Button>,
  ],
});

// Module Header Configurations
export const getAssetLeasingHeaderConfig = (): HeaderConfig => ({
  title: "Asset Leasing",
  description: "Manage asset leasing contracts and payments",
  breadcrumbs: [
    { label: "Admin", url: "/admin" },
    { label: "Modules", url: "/admin/modules" },
    { label: "Asset Leasing", url: "/admin/modules/asset-leasing" },
  ],
  variant: 'management',
  actions: [
    <Button variant="outline" key="contracts">
      <FileText className="mr-2 h-4 w-4" />
      Contracts
    </Button>,
    <Button key="new-lease">
      <CreditCard className="mr-2 h-4 w-4" />
      New Lease
    </Button>,
  ],
});

export const getCRMHeaderConfig = (): HeaderConfig => ({
  title: "Customer Relationship Management",
  description: "Manage customer relationships and interactions",
  breadcrumbs: [
    { label: "Admin", url: "/admin" },
    { label: "Modules", url: "/admin/modules" },
    { label: "CRM", url: "/admin/modules/crm" },
  ],
  variant: 'management',
  actions: [
    <Button variant="outline" key="import-contacts">
      <Upload className="mr-2 h-4 w-4" />
      Import
    </Button>,
    <Button key="new-contact">
      <Users className="mr-2 h-4 w-4" />
      Add Contact
    </Button>,
  ],
});

export const getInventoryHeaderConfig = (): HeaderConfig => ({
  title: "Inventory Management",
  description: "Track and manage asset inventory levels",
  breadcrumbs: [
    { label: "Admin", url: "/admin" },
    { label: "Modules", url: "/admin/modules" },
    { label: "Inventory", url: "/admin/modules/inventory" },
  ],
  variant: 'management',
  actions: [
    <Button variant="outline" key="stock-check">
      <CheckCircle className="mr-2 h-4 w-4" />
      Stock Check
    </Button>,
    <Button key="adjust-inventory">
      <Package className="mr-2 h-4 w-4" />
      Adjust Inventory
    </Button>,
  ],
});

export const getAutomationFlowHeaderConfig = (): HeaderConfig => ({
  title: "Automation Flow",
  description: "Design and manage automation workflows",
  breadcrumbs: [
    { label: "Admin", url: "/admin" },
    { label: "Modules", url: "/admin/modules" },
    { label: "Automation Flow", url: "/admin/modules/automation-flow" },
  ],
  variant: 'management',
  actions: [
    <Button variant="outline" key="templates">
      <Layers className="mr-2 h-4 w-4" />
      Templates
    </Button>,
    <Button key="new-flow">
      <Workflow className="mr-2 h-4 w-4" />
      New Flow
    </Button>,
  ],
});

// Settings Sub-pages
export const getCustomFieldsHeaderConfig = (): HeaderConfig => ({
  title: "Custom Fields",
  description: "Configure custom fields for assets and forms",
  breadcrumbs: [
    { label: "Admin", url: "/admin" },
    { label: "Settings", url: "/admin/settings" },
    { label: "Custom Fields", url: "/admin/settings/custom-fields" },
  ],
  variant: 'settings',
  actions: [
    <Button variant="outline" key="import-fields">
      <Upload className="mr-2 h-4 w-4" />
      Import
    </Button>,
    <Button key="new-field">
      <Plus className="mr-2 h-4 w-4" />
      Add Field
    </Button>,
  ],
});

export const getFormBuilderHeaderConfig = (): HeaderConfig => ({
  title: "Form Builder",
  description: "Create and manage dynamic forms",
  breadcrumbs: [
    { label: "Admin", url: "/admin" },
    { label: "Settings", url: "/admin/settings" },
    { label: "Form Builder", url: "/admin/settings/form-builder" },
  ],
  variant: 'settings',
  actions: [
    <Button variant="outline" key="templates">
      <FileText className="mr-2 h-4 w-4" />
      Templates
    </Button>,
    <Button key="new-form">
      <Plus className="mr-2 h-4 w-4" />
      New Form
    </Button>,
  ],
});

// Financial Sub-pages
export const getFinancialDashboardHeaderConfig = (): HeaderConfig => ({
  title: "Financial Dashboard",
  description: "Overview of financial metrics and KPIs",
  breadcrumbs: [
    { label: "Admin", url: "/admin" },
    { label: "Financial", url: "/admin/financial" },
    { label: "Dashboard", url: "/admin/financial/dashboard" },
  ],
  variant: 'analytics',
  actions: [
    <Button variant="outline" key="export-dashboard">
      <Download className="mr-2 h-4 w-4" />
      Export
    </Button>,
    <Button key="refresh">
      <RefreshCw className="mr-2 h-4 w-4" />
      Refresh
    </Button>,
  ],
});

export const getDepreciationHeaderConfig = (): HeaderConfig => ({
  title: "Depreciation Management",
  description: "Manage asset depreciation schedules and calculations",
  breadcrumbs: [
    { label: "Admin", url: "/admin" },
    { label: "Financial", url: "/admin/financial" },
    { label: "Depreciation", url: "/admin/financial/depreciation" },
  ],
  variant: 'analytics',
  actions: [
    <Button variant="outline" key="calculate-all">
      <Calculator className="mr-2 h-4 w-4" />
      Calculate All
    </Button>,
    <Button key="new-schedule">
      <Clock className="mr-2 h-4 w-4" />
      New Schedule
    </Button>,
  ],
});