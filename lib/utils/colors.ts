/**
 * Utility functions for consistent color usage across the application
 * These functions return CSS classes that use our theme variables
 */

export const statusColors = {
  // Success states
  success: {
    bg: 'bg-chart-2',
    text: 'text-chart-2',
    foreground: 'text-chart-2-foreground',
    border: 'border-chart-2',
    badge: 'bg-chart-2 text-chart-2-foreground'
  },
  
  // Error/Destructive states
  error: {
    bg: 'bg-destructive',
    text: 'text-destructive',
    foreground: 'text-destructive-foreground',
    border: 'border-destructive',
    badge: 'bg-destructive text-destructive-foreground'
  },
  
  // Warning states
  warning: {
    bg: 'bg-chart-4',
    text: 'text-chart-4',
    foreground: 'text-chart-4-foreground',
    border: 'border-chart-4',
    badge: 'bg-chart-4 text-chart-4-foreground'
  },
  
  // Info/Primary states
  info: {
    bg: 'bg-primary',
    text: 'text-primary',
    foreground: 'text-primary-foreground',
    border: 'border-primary',
    badge: 'bg-primary text-primary-foreground'
  },
  
  // Neutral states
  neutral: {
    bg: 'bg-muted',
    text: 'text-muted-foreground',
    foreground: 'text-muted-foreground',
    border: 'border-muted',
    badge: 'bg-muted text-muted-foreground'
  }
}

export const workflowStatusColors = {
  completed: statusColors.success,
  failed: statusColors.error,
  running: statusColors.info,
  queued: statusColors.warning,
  cancelled: statusColors.neutral,
  pending: statusColors.warning
}

export const assetStatusColors = {
  active: statusColors.success,
  operational: statusColors.success,
  maintenance: statusColors.warning,
  retired: statusColors.neutral,
  disposed: statusColors.error,
  'under-maintenance': statusColors.warning,
  'out-of-service': statusColors.error
}

export const priorityColors = {
  high: statusColors.error,
  medium: statusColors.warning,
  low: statusColors.info,
  critical: statusColors.error
}

export const connectionStatusColors = {
  connected: statusColors.success,
  disconnected: statusColors.error,
  connecting: statusColors.warning
}

/**
 * Get status badge classes for workflow execution status
 */
export function getWorkflowStatusBadge(status: string): string {
  const colors = workflowStatusColors[status as keyof typeof workflowStatusColors] || statusColors.neutral
  return colors.badge
}

/**
 * Get status icon color classes for workflow execution status
 */
export function getWorkflowStatusIcon(status: string): string {
  const colors = workflowStatusColors[status as keyof typeof workflowStatusColors] || statusColors.neutral
  return colors.text
}

/**
 * Get status badge classes for asset status
 */
export function getAssetStatusBadge(status: string): string {
  const colors = assetStatusColors[status as keyof typeof assetStatusColors] || statusColors.neutral
  return colors.badge
}

/**
 * Get priority badge classes
 */
export function getPriorityBadge(priority: string): string {
  const colors = priorityColors[priority as keyof typeof priorityColors] || statusColors.neutral
  return colors.badge
}

/**
 * Get connection status badge classes
 */
export function getConnectionStatusBadge(status: string): string {
  const colors = connectionStatusColors[status as keyof typeof connectionStatusColors] || statusColors.neutral
  return colors.badge
}

/**
 * Chart color palette using theme variables
 */
export const chartColors = {
  primary: 'hsl(var(--primary))',
  secondary: 'hsl(var(--chart-2))',
  tertiary: 'hsl(var(--chart-3))',
  quaternary: 'hsl(var(--chart-4))',
  quinary: 'hsl(var(--chart-5))',
  destructive: 'hsl(var(--destructive))',
  success: 'hsl(var(--chart-2))',
  warning: 'hsl(var(--chart-4))',
  info: 'hsl(var(--primary))'
}

/**
 * Get chart color array for multi-series charts
 */
export function getChartColorArray(): string[] {
  return [
    chartColors.primary,
    chartColors.secondary,
    chartColors.tertiary,
    chartColors.quaternary,
    chartColors.quinary,
    chartColors.destructive
  ]
}