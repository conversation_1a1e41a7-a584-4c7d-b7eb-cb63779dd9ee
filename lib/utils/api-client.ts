import { AppError, handleError, logError } from "./error-handler"
import { CacheManager } from "./cache"
import { PerformanceMonitor } from "./performance"

// Production-ready API client
export class ApiClient {
  private static instance: ApiClient
  private baseURL: string
  private cache: CacheManager
  private performance: PerformanceMonitor

  private constructor() {
    this.baseURL = process.env.NEXT_PUBLIC_API_URL || "/api"
    this.cache = CacheManager.getInstance()
    this.performance = PerformanceMonitor.getInstance()
  }

  static getInstance(): ApiClient {
    if (!ApiClient.instance) {
      ApiClient.instance = new ApiClient()
    }
    return ApiClient.instance
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
    useCache = false,
    cacheTTL: number = 5 * 60 * 1000,
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`
    const cacheKey = `${options.method || "GET"}:${url}:${JSON.stringify(options.body || {})}`

    // Check cache for GET requests
    if (useCache && (!options.method || options.method === "GET")) {
      const cached = this.cache.get<T>(cacheKey)
      if (cached) return cached
    }

    const stopTimer = this.performance.startTimer(`api:${endpoint}`)

    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          "Content-Type": "application/json",
          ...options.headers,
        },
      })

      stopTimer()

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new AppError(errorData.message || `HTTP ${response.status}: ${response.statusText}`, response.status)
      }

      const data = await response.json()

      // Cache successful GET requests
      if (useCache && (!options.method || options.method === "GET")) {
        this.cache.set(cacheKey, data, cacheTTL)
      }

      return data
    } catch (error) {
      stopTimer()
      const appError = handleError(error)
      logError(appError, { endpoint, options })
      throw appError
    }
  }

  // HTTP methods
  async get<T>(endpoint: string, useCache = true): Promise<T> {
    return this.request<T>(endpoint, { method: "GET" }, useCache)
  }

  async post<T>(endpoint: string, data: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: "POST",
      body: JSON.stringify(data),
    })
  }

  async put<T>(endpoint: string, data: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: "PUT",
      body: JSON.stringify(data),
    })
  }

  async patch<T>(endpoint: string, data: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: "PATCH",
      body: JSON.stringify(data),
    })
  }

  async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: "DELETE" })
  }

  // File upload
  async uploadFile<T>(endpoint: string, file: File, additionalData?: Record<string, any>): Promise<T> {
    const formData = new FormData()
    formData.append("file", file)

    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, String(value))
      })
    }

    return this.request<T>(endpoint, {
      method: "POST",
      body: formData,
      headers: {}, // Let browser set Content-Type for FormData
    })
  }

  // Batch requests
  async batch<T>(requests: Array<{ endpoint: string; method?: string; data?: any }>): Promise<T[]> {
    const promises = requests.map((req) =>
      this.request<T>(req.endpoint, {
        method: req.method || "GET",
        body: req.data ? JSON.stringify(req.data) : undefined,
      }),
    )

    return Promise.all(promises)
  }

  // Clear cache
  clearCache(pattern?: string): void {
    if (pattern) {
      this.cache.invalidatePattern(pattern)
    } else {
      this.cache.clear()
    }
  }
}

// React hooks for API calls
export const useApi = () => {
  const api = ApiClient.getInstance()

  return {
    get: api.get.bind(api),
    post: api.post.bind(api),
    put: api.put.bind(api),
    patch: api.patch.bind(api),
    delete: api.delete.bind(api),
    uploadFile: api.uploadFile.bind(api),
    batch: api.batch.bind(api),
    clearCache: api.clearCache.bind(api),
  }
}
