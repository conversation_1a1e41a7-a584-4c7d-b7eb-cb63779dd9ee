import { Badge } from "@/components/ui/badge";

export function getMaintenanceTypeBadge(type: string) {
  switch (type.toLowerCase()) {
    case "preventive":
      return <Badge className="bg-blue-100 text-blue-800">Preventive</Badge>;
    case "corrective":
      return <Badge className="bg-orange-100 text-orange-800">Corrective</Badge>;
    case "predictive":
      return <Badge className="bg-purple-100 text-purple-800">Predictive</Badge>;
    case "emergency":
      return <Badge className="bg-red-100 text-red-800">Emergency</Badge>;
    default:
      return <Badge variant="outline">{type}</Badge>;
  }
}

export function getMaintenanceStatusBadge(status: string) {
  switch (status.toLowerCase()) {
    case "scheduled":
      return <Badge className="bg-blue-100 text-blue-800">Scheduled</Badge>;
    case "in-progress":
      return <Badge className="bg-yellow-100 text-yellow-800">In Progress</Badge>;
    case "completed":
      return <Badge className="bg-green-100 text-green-800">Completed</Badge>;
    case "cancelled":
      return <Badge className="bg-gray-100 text-gray-800">Cancelled</Badge>;
    case "overdue":
      return <Badge className="bg-red-100 text-red-800">Overdue</Badge>;
    default:
      return <Badge variant="secondary">{status}</Badge>;
  }
}

export const MAINTENANCE_TYPES = [
  { value: "preventive", label: "Preventive", color: "blue" },
  { value: "corrective", label: "Corrective", color: "orange" },
  { value: "predictive", label: "Predictive", color: "purple" },
  { value: "emergency", label: "Emergency", color: "red" },
] as const;

export const MAINTENANCE_STATUSES = [
  { value: "scheduled", label: "Scheduled", color: "blue" },
  { value: "in-progress", label: "In Progress", color: "yellow" },
  { value: "completed", label: "Completed", color: "green" },
  { value: "cancelled", label: "Cancelled", color: "gray" },
  { value: "overdue", label: "Overdue", color: "red" },
] as const;