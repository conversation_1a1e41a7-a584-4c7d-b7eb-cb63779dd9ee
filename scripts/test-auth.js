#!/usr/bin/env node

/**
 * Test script for client authentication system
 * Run with: node scripts/test-auth.js
 */

const { PrismaClient } = require('@prisma/client');
const { hash } = require('bcryptjs');

const prisma = new PrismaClient();

async function testAuthSystem() {
  console.log('🧪 Testing Client Authentication System...\n');

  try {
    // Test 1: Create a test client user
    console.log('1. Creating test client user...');
    
    const testEmail = `test-client-${Date.now()}@example.com`;
    const hashedPassword = await hash('TestPassword123', 12);
    
    const testUser = await prisma.user.create({
      data: {
        name: 'Test Client User',
        email: testEmail,
        password: hashedPassword,
        role: 'client',
        status: 'active',
        phone: '******-123-4567',
        jobTitle: 'IT Manager',
        company: 'Test Company',
        clientId: `CLIENT-${Date.now()}`,
        emailVerified: new Date(),
        preferences: {
          marketingEmails: true,
          notifications: {
            email: true,
            requestUpdates: true,
            billingAlerts: true,
          },
        },
      },
    });
    
    console.log('✅ Test user created:', testUser.email);

    // Test 2: Create client profile
    console.log('\n2. Creating client profile...');
    
    const clientProfile = await prisma.clientProfile.create({
      data: {
        userId: testUser.id,
        companyName: 'Test Company Inc.',
        industry: 'Technology',
        companySize: '11-50',
        website: 'https://testcompany.com',
        subscriptionTier: 'professional',
        autoApprovalLimit: 1000,
      },
    });
    
    console.log('✅ Client profile created for:', clientProfile.companyName);

    // Test 3: Verify user can be retrieved with profile
    console.log('\n3. Testing user retrieval with profile...');
    
    const userWithProfile = await prisma.user.findUnique({
      where: { id: testUser.id },
      include: {
        clientProfile: true,
      },
    });
    
    if (userWithProfile && userWithProfile.clientProfile) {
      console.log('✅ User retrieved with profile successfully');
      console.log('   - User role:', userWithProfile.role);
      console.log('   - User status:', userWithProfile.status);
      console.log('   - Company:', userWithProfile.clientProfile.companyName);
      console.log('   - Subscription:', userWithProfile.clientProfile.subscriptionTier);
    } else {
      console.log('❌ Failed to retrieve user with profile');
    }

    // Test 4: Test role-based queries
    console.log('\n4. Testing role-based queries...');
    
    const clientUsers = await prisma.user.findMany({
      where: { role: 'client' },
      include: { clientProfile: true },
    });
    
    console.log(`✅ Found ${clientUsers.length} client users`);

    // Test 5: Test status filtering
    console.log('\n5. Testing status filtering...');
    
    const activeClients = await prisma.user.findMany({
      where: { 
        role: 'client',
        status: 'active'
      },
    });
    
    console.log(`✅ Found ${activeClients.length} active client users`);

    // Test 6: Create a test asset request
    console.log('\n6. Creating test asset request...');
    
    const assetRequest = await prisma.assetRequest.create({
      data: {
        requestNumber: `REQ-${Date.now()}`,
        userId: testUser.id,
        assetName: 'MacBook Pro 16"',
        quantity: 1,
        priority: 'normal',
        status: 'pending',
        justification: 'Need for development work',
        location: 'Main Office',
        estimatedCost: 2499.99,
      },
    });
    
    console.log('✅ Asset request created:', assetRequest.requestNumber);

    // Test 7: Create a test support ticket
    console.log('\n7. Creating test support ticket...');
    
    const supportTicket = await prisma.supportTicket.create({
      data: {
        ticketNumber: `TICKET-${Date.now()}`,
        userId: testUser.id,
        subject: 'Test Support Request',
        description: 'This is a test support ticket',
        category: 'technical',
        priority: 'normal',
        status: 'open',
      },
    });
    
    console.log('✅ Support ticket created:', supportTicket.ticketNumber);

    // Test 8: Create a test invoice
    console.log('\n8. Creating test invoice...');
    
    const invoice = await prisma.invoice.create({
      data: {
        invoiceNumber: `INV-${Date.now()}`,
        userId: testUser.id,
        amount: 2499.99,
        tax: 199.99,
        total: 2699.98,
        currency: 'USD',
        status: 'pending',
        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        description: 'MacBook Pro 16" - Asset Request',
        items: [
          {
            name: 'MacBook Pro 16"',
            quantity: 1,
            unitPrice: 2499.99,
            total: 2499.99,
          },
        ],
      },
    });
    
    console.log('✅ Invoice created:', invoice.invoiceNumber);

    // Test 9: Verify all relationships
    console.log('\n9. Testing relationships...');
    
    const userWithAllRelations = await prisma.user.findUnique({
      where: { id: testUser.id },
      include: {
        clientProfile: true,
        assetRequests: true,
        supportTickets: true,
        invoices: true,
      },
    });
    
    if (userWithAllRelations) {
      console.log('✅ All relationships working:');
      console.log(`   - Asset requests: ${userWithAllRelations.assetRequests.length}`);
      console.log(`   - Support tickets: ${userWithAllRelations.supportTickets.length}`);
      console.log(`   - Invoices: ${userWithAllRelations.invoices.length}`);
    }

    console.log('\n🎉 All tests passed! Client authentication system is working correctly.');
    
    // Cleanup (optional - comment out if you want to keep test data)
    console.log('\n🧹 Cleaning up test data...');
    
    await prisma.invoice.delete({ where: { id: invoice.id } });
    await prisma.supportTicket.delete({ where: { id: supportTicket.id } });
    await prisma.assetRequest.delete({ where: { id: assetRequest.id } });
    await prisma.clientProfile.delete({ where: { id: clientProfile.id } });
    await prisma.user.delete({ where: { id: testUser.id } });
    
    console.log('✅ Test data cleaned up');

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testAuthSystem().catch((error) => {
  console.error('❌ Test script failed:', error);
  process.exit(1);
});