import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';
import fs from 'fs';
import path from 'path';

// Import admin user data
const adminUserData = JSON.parse(
  fs.readFileSync(path.join(__dirname, 'admin-user-data.json'), 'utf8')
);

const prisma = new PrismaClient();

async function createAdminUser() {
  console.log('🚀 WizeAssets ERP - Admin User Creation');
  console.log('=====================================');
  console.log('');
  
  try {
    // Check database connection
    await prisma.$connect();
    console.log('✅ Database connection successful');
    
    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: adminUserData.email }
    });

    if (existingUser) {
      console.log('⚠️  User already exists with this email');
      console.log('📋 Current User Details:');
      console.table({
        'ID': existingUser.id,
        'Name': existingUser.name,
        'Email': existingUser.email,
        'Role': existingUser.role,
        'Department': existingUser.department,
        'Status': existingUser.status,
        'Company': existingUser.company,
        'Job Title': existingUser.jobTitle,
        'Phone': existingUser.phone,
        'Created At': existingUser.createdAt.toISOString().split('T')[0]
      });
      
      // Ask if user wants to update the existing record
      console.log('');
      console.log('💡 The user already exists. If you want to update it, you can:');
      console.log('   1. Delete the existing user first');
      console.log('   2. Update the password or other details manually');
      console.log('   3. Use a different email address');
      return;
    }

    // Hash the password
    console.log('🔒 Hashing password...');
    const hashedPassword = await bcrypt.hash(adminUserData.password, 10);

    // Create the admin user
    console.log('👤 Creating admin user...');
    const user = await prisma.user.create({
      data: {
        name: adminUserData.name,
        email: adminUserData.email,
        password: hashedPassword,
        role: adminUserData.role,
        department: adminUserData.department,
        status: adminUserData.status,
        phone: adminUserData.phone,
        address: adminUserData.address,
        company: adminUserData.company,
        jobTitle: adminUserData.jobTitle,
        joinDate: new Date(),
        lastActive: new Date(),
        emailVerified: new Date(), // Mark as verified
        avatarUrl: null
      }
    });

    console.log('✅ Admin user created successfully!');
    console.log('');
    console.log('📋 User Details:');
    console.table({
      'ID': user.id,
      'Name': user.name,
      'Email': user.email,
      'Role': user.role,
      'Department': user.department,
      'Status': user.status,
      'Company': user.company,
      'Job Title': user.jobTitle,
      'Phone': user.phone,
      'Email Verified': user.emailVerified ? 'Yes' : 'No'
    });
    
    console.log('');
    console.log('🔑 Login Credentials:');
    console.log(`   Email: ${adminUserData.email}`);
    console.log(`   Password: ${adminUserData.password}`);
    console.log('');
    console.log('🎉 You can now login to the admin dashboard!');

  } catch (error) {
    console.error('❌ Error creating admin user:', error);
    
    if (error.code === 'P1001') {
      console.log('');
      console.log('🔧 Database Connection Issue:');
      console.log('   The database server is not accessible.');
      console.log('   Please check your DATABASE_URL in .env file');
      console.log('   Current DATABASE_URL points to: ' + process.env.DATABASE_URL?.split('@')[1]);
      console.log('');
      console.log('🛠️  Alternative Solutions:');
      console.log('   1. Use the SQL script provided below');
      console.log('   2. Connect to the database directly and run the SQL');
      console.log('   3. Update the seeder and run it when the database is available');
      
      // Generate SQL as fallback
      await generateSQLFallback();
    }
  } finally {
    await prisma.$disconnect();
  }
}

async function generateSQLFallback() {
  console.log('');
  console.log('📝 SQL Script (Fallback Method):');
  console.log('================================');
  
  const hashedPassword = await bcrypt.hash(adminUserData.password, 10);
  const userId = 'clx' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  
  console.log('');
  console.log('-- Check if user exists:');
  console.log(`SELECT id, name, email, role FROM "User" WHERE email = '${adminUserData.email}';`);
  console.log('');
  console.log('-- Insert admin user:');
  console.log(`INSERT INTO "User" (`);
  console.log(`    id, name, email, password, department, role, status,`);
  console.log(`    phone, address, company, "jobTitle", "joinDate", "lastActive",`);
  console.log(`    "emailVerified", "createdAt", "updatedAt"`);
  console.log(`) VALUES (`);
  console.log(`    '${userId}',`);
  console.log(`    '${adminUserData.name}',`);
  console.log(`    '${adminUserData.email}',`);
  console.log(`    '${hashedPassword}',`);
  console.log(`    '${adminUserData.department}',`);
  console.log(`    '${adminUserData.role}',`);
  console.log(`    '${adminUserData.status}',`);
  console.log(`    '${adminUserData.phone}',`);
  console.log(`    '${adminUserData.address}',`);
  console.log(`    '${adminUserData.company}',`);
  console.log(`    '${adminUserData.jobTitle}',`);
  console.log(`    NOW(), NOW(), NOW(), NOW(), NOW()`);
  console.log(`) ON CONFLICT (email) DO NOTHING;`);
  console.log('');
  console.log('-- Verify user was created:');
  console.log(`SELECT id, name, email, role, department, status FROM "User" WHERE email = '${adminUserData.email}';`);
}

// Add command line argument handling
const args = process.argv.slice(2);
if (args.includes('--help') || args.includes('-h')) {
  console.log('🚀 WizeAssets ERP - Admin User Creation');
  console.log('=====================================');
  console.log('');
  console.log('Usage: npx tsx scripts/create-admin-user.ts [options]');
  console.log('');
  console.log('Options:');
  console.log('  --help, -h     Show this help message');
  console.log('  --sql-only     Generate SQL script only (no database connection)');
  console.log('');
  console.log('Description:');
  console.log('  Creates an admin user for the WizeAssets ERP system.');
  console.log('  User details are loaded from admin-user-data.json');
  console.log('');
  console.log('Examples:');
  console.log('  npx tsx scripts/create-admin-user.ts');
  console.log('  npx tsx scripts/create-admin-user.ts --sql-only');
  process.exit(0);
}

if (args.includes('--sql-only')) {
  console.log('🔧 Generating SQL script only...');
  generateSQLFallback();
} else {
  // Run the main function
  createAdminUser();
}