#!/usr/bin/env tsx

/**
 * Migration script to set up the enhanced Asset Type Management System
 * This script will:
 * 1. Run Prisma migrations to create new tables
 * 2. Seed default data for asset operations
 * 3. Create sample asset type configurations
 */

import { PrismaClient } from "@prisma/client";
import { ASSET_OPERATION_CONFIGS } from "../lib/types/asset-type-forms";

const prisma = new PrismaClient();

async function main() {
  console.log("🚀 Starting Asset Type Management System migration...");

  try {
    // Step 1: Verify database connection
    console.log("📡 Checking database connection...");
    await prisma.$connect();
    console.log("✅ Database connected successfully");

    // Step 2: Create sample asset categories if they don't exist
    console.log("📁 Setting up asset categories...");
    await setupAssetCategories();

    // Step 3: Create sample asset types
    console.log("🏷️ Creating sample asset types...");
    await createSampleAssetTypes();

    // Step 4: Create sample form definitions
    console.log("📝 Creating sample form definitions...");
    await createSampleFormDefinitions();

    // Step 5: Associate forms with asset types
    console.log("🔗 Associating forms with asset types...");
    await associateFormsWithAssetTypes();

    // Step 6: Create sample depreciation settings
    console.log("📉 Setting up depreciation configurations...");
    await setupDepreciationSettings();

    // Step 7: Create sample lifecycle stages
    console.log("🔄 Setting up lifecycle stages...");
    await setupLifecycleStages();

    // Step 8: Create sample maintenance schedules
    console.log("🔧 Setting up maintenance schedules...");
    await setupMaintenanceSchedules();

    console.log("🎉 Migration completed successfully!");

  } catch (error) {
    console.error("❌ Migration failed:", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

async function setupAssetCategories() {
  const categories = [
    {
      name: "IT Equipment",
      description: "Computers, servers, networking equipment",
      level: 1,
      path: "/it-equipment",
    },
    {
      name: "Office Furniture",
      description: "Desks, chairs, cabinets, and other office furniture",
      level: 1,
      path: "/office-furniture",
    },
    {
      name: "Vehicles",
      description: "Company cars, trucks, and other vehicles",
      level: 1,
      path: "/vehicles",
    },
    {
      name: "Manufacturing Equipment",
      description: "Production machinery and tools",
      level: 1,
      path: "/manufacturing-equipment",
    },
  ];

  for (const category of categories) {
    await prisma.assetCategory.upsert({
      where: { name: category.name },
      update: {},
      create: category,
    });
  }

  console.log(`✅ Created ${categories.length} asset categories`);
}

async function createSampleAssetTypes() {
  const categories = await prisma.assetCategory.findMany();
  const itCategory = categories.find(c => c.name === "IT Equipment");
  const furnitureCategory = categories.find(c => c.name === "Office Furniture");

  if (!itCategory || !furnitureCategory) {
    throw new Error("Required categories not found");
  }

  const assetTypes = [
    {
      name: "Laptop Computer",
      code: "LAPTOP",
      description: "Portable computers for employees",
      categoryId: itCategory.id,
      icon: "Laptop",
      color: "#3B82F6",
      tags: ["IT", "Portable", "Computing"],
      createdBy: "system",
    },
    {
      name: "Office Desk",
      code: "DESK",
      description: "Standard office desks",
      categoryId: furnitureCategory.id,
      icon: "Table",
      color: "#8B5CF6",
      tags: ["Furniture", "Office", "Workspace"],
      createdBy: "system",
    },
  ];

  for (const assetType of assetTypes) {
    await prisma.assetType.upsert({
      where: { code: assetType.code },
      update: {},
      create: assetType,
    });
  }

  console.log(`✅ Created ${assetTypes.length} asset types`);
}

async function createSampleFormDefinitions() {
  const forms = [
    {
      name: "Asset Creation Form",
      description: "Standard form for creating new assets",
      sections: JSON.stringify([
        {
          id: "basic-info",
          title: "Basic Information",
          description: "Essential asset details",
          columns: 2,
          fields: ["name", "category", "location", "department"],
        },
        {
          id: "financial-info",
          title: "Financial Information",
          description: "Purchase and cost details",
          columns: 2,
          fields: ["purchasePrice", "purchaseDate", "supplier"],
        },
      ]),
      settings: JSON.stringify({
        layout: "standard",
        labelPosition: "top",
        submitButtonText: "Create Asset",
        cancelButtonText: "Cancel",
        showProgressBar: true,
        allowSaveAsDraft: true,
      }),
      createdBy: "system",
    },
    {
      name: "Asset Transfer Form",
      description: "Form for transferring assets between locations",
      sections: JSON.stringify([
        {
          id: "transfer-details",
          title: "Transfer Details",
          description: "Transfer information",
          columns: 1,
          fields: ["newLocation", "newDepartment", "transferDate", "reason"],
        },
      ]),
      settings: JSON.stringify({
        layout: "standard",
        labelPosition: "top",
        submitButtonText: "Transfer Asset",
        cancelButtonText: "Cancel",
        showProgressBar: false,
        allowSaveAsDraft: false,
      }),
      createdBy: "system",
    },
    {
      name: "Maintenance Log Form",
      description: "Form for logging maintenance activities",
      sections: JSON.stringify([
        {
          id: "maintenance-details",
          title: "Maintenance Details",
          description: "Maintenance activity information",
          columns: 2,
          fields: ["type", "completedDate", "performedBy", "notes"],
        },
      ]),
      settings: JSON.stringify({
        layout: "standard",
        labelPosition: "top",
        submitButtonText: "Log Maintenance",
        cancelButtonText: "Cancel",
        showProgressBar: false,
        allowSaveAsDraft: true,
      }),
      createdBy: "system",
    },
  ];

  for (const form of forms) {
    await prisma.formDefinition.upsert({
      where: { name: form.name },
      update: {},
      create: form,
    });
  }

  console.log(`✅ Created ${forms.length} form definitions`);
}

async function associateFormsWithAssetTypes() {
  const assetTypes = await prisma.assetType.findMany();
  const forms = await prisma.formDefinition.findMany();

  const creationForm = forms.find(f => f.name === "Asset Creation Form");
  const transferForm = forms.find(f => f.name === "Asset Transfer Form");
  const maintenanceForm = forms.find(f => f.name === "Maintenance Log Form");

  if (!creationForm || !transferForm || !maintenanceForm) {
    throw new Error("Required forms not found");
  }

  const associations = [];

  for (const assetType of assetTypes) {
    associations.push(
      {
        assetTypeId: assetType.id,
        formId: creationForm.id,
        operationType: "asset.create",
        isDefault: true,
        createdBy: "system",
      },
      {
        assetTypeId: assetType.id,
        formId: transferForm.id,
        operationType: "asset.transfer",
        isDefault: true,
        createdBy: "system",
      },
      {
        assetTypeId: assetType.id,
        formId: maintenanceForm.id,
        operationType: "maintenance.log",
        isDefault: true,
        createdBy: "system",
      }
    );
  }

  for (const association of associations) {
    await prisma.assetTypeForm.upsert({
      where: {
        assetTypeId_operationType_isDefault: {
          assetTypeId: association.assetTypeId,
          operationType: association.operationType,
          isDefault: association.isDefault,
        },
      },
      update: {},
      create: association,
    });
  }

  console.log(`✅ Created ${associations.length} form associations`);
}

async function setupDepreciationSettings() {
  const assetTypes = await prisma.assetType.findMany();

  for (const assetType of assetTypes) {
    const settings = {
      assetTypeId: assetType.id,
      method: assetType.code === "LAPTOP" ? "double_declining_balance" : "straight_line",
      usefulLife: assetType.code === "LAPTOP" ? 3 : 7,
      usefulLifeUnit: "years",
      salvageValue: assetType.code === "LAPTOP" ? 10 : 5,
      salvageValueType: "percentage",
      startDate: new Date(),
      isActive: true,
    };

    await prisma.depreciationSettings.upsert({
      where: { assetTypeId: assetType.id },
      update: {},
      create: settings,
    });
  }

  console.log(`✅ Created depreciation settings for ${assetTypes.length} asset types`);
}

async function setupLifecycleStages() {
  const assetTypes = await prisma.assetType.findMany();

  const commonStages = [
    {
      name: "Requested",
      code: "REQUESTED",
      description: "Asset has been requested but not yet acquired",
      order: 1,
      isInitial: true,
      isFinal: false,
      color: "#F59E0B",
      icon: "Clock",
      allowedTransitions: JSON.stringify(["ORDERED", "CANCELLED"]),
      requiredFields: JSON.stringify(["requestedBy", "justification"]),
      isActive: true,
    },
    {
      name: "Ordered",
      code: "ORDERED",
      description: "Asset has been ordered from supplier",
      order: 2,
      isInitial: false,
      isFinal: false,
      color: "#3B82F6",
      icon: "ShoppingCart",
      allowedTransitions: JSON.stringify(["RECEIVED", "CANCELLED"]),
      requiredFields: JSON.stringify(["supplier", "orderDate"]),
      isActive: true,
    },
    {
      name: "Received",
      code: "RECEIVED",
      description: "Asset has been received and is being processed",
      order: 3,
      isInitial: false,
      isFinal: false,
      color: "#10B981",
      icon: "Package",
      allowedTransitions: JSON.stringify(["IN_USE", "IN_STORAGE"]),
      requiredFields: JSON.stringify(["receivedDate", "condition"]),
      isActive: true,
    },
    {
      name: "In Use",
      code: "IN_USE",
      description: "Asset is actively being used",
      order: 4,
      isInitial: false,
      isFinal: false,
      color: "#059669",
      icon: "Play",
      allowedTransitions: JSON.stringify(["MAINTENANCE", "IN_STORAGE", "DISPOSED"]),
      requiredFields: JSON.stringify(["assignedTo", "location"]),
      isActive: true,
    },
    {
      name: "Maintenance",
      code: "MAINTENANCE",
      description: "Asset is under maintenance",
      order: 5,
      isInitial: false,
      isFinal: false,
      color: "#F59E0B",
      icon: "Wrench",
      allowedTransitions: JSON.stringify(["IN_USE", "DISPOSED"]),
      requiredFields: JSON.stringify(["maintenanceType", "scheduledDate"]),
      isActive: true,
    },
    {
      name: "In Storage",
      code: "IN_STORAGE",
      description: "Asset is in storage, not actively used",
      order: 6,
      isInitial: false,
      isFinal: false,
      color: "#6B7280",
      icon: "Archive",
      allowedTransitions: JSON.stringify(["IN_USE", "DISPOSED"]),
      requiredFields: JSON.stringify(["storageLocation", "reason"]),
      isActive: true,
    },
    {
      name: "Disposed",
      code: "DISPOSED",
      description: "Asset has been disposed of",
      order: 7,
      isInitial: false,
      isFinal: true,
      color: "#EF4444",
      icon: "Trash2",
      allowedTransitions: JSON.stringify([]),
      requiredFields: JSON.stringify(["disposalMethod", "disposalDate"]),
      isActive: true,
    },
  ];

  for (const assetType of assetTypes) {
    for (const stage of commonStages) {
      await prisma.lifecycleStage.upsert({
        where: {
          assetTypeId_code: {
            assetTypeId: assetType.id,
            code: stage.code,
          },
        },
        update: {},
        create: {
          ...stage,
          assetTypeId: assetType.id,
        },
      });
    }
  }

  console.log(`✅ Created lifecycle stages for ${assetTypes.length} asset types`);
}

async function setupMaintenanceSchedules() {
  const assetTypes = await prisma.assetType.findMany();
  const laptopType = assetTypes.find(at => at.code === "LAPTOP");
  const deskType = assetTypes.find(at => at.code === "DESK");

  if (laptopType) {
    const laptopSchedules = [
      {
        assetTypeId: laptopType.id,
        name: "Software Updates",
        description: "Regular software and security updates",
        type: "preventive",
        frequency: JSON.stringify({ type: "months", interval: 1 }),
        priority: "medium",
        estimatedDuration: 30,
        estimatedCost: 0,
        requiredSkills: JSON.stringify(["IT Support"]),
        instructions: "Run all available software updates and security patches",
        checklistItems: JSON.stringify([
          "Check for OS updates",
          "Update antivirus software",
          "Update installed applications",
          "Restart if required",
        ]),
        isActive: true,
      },
      {
        assetTypeId: laptopType.id,
        name: "Hardware Inspection",
        description: "Physical inspection of laptop hardware",
        type: "preventive",
        frequency: JSON.stringify({ type: "months", interval: 6 }),
        priority: "low",
        estimatedDuration: 15,
        estimatedCost: 0,
        requiredSkills: JSON.stringify(["IT Support"]),
        instructions: "Inspect laptop for physical damage and wear",
        checklistItems: JSON.stringify([
          "Check screen for damage",
          "Test keyboard and trackpad",
          "Inspect ports and connections",
          "Check battery health",
        ]),
        isActive: true,
      },
    ];

    for (const schedule of laptopSchedules) {
      await prisma.maintenanceSchedule.upsert({
        where: {
          assetTypeId_name: {
            assetTypeId: schedule.assetTypeId,
            name: schedule.name,
          },
        },
        update: {},
        create: schedule,
      });
    }
  }

  if (deskType) {
    const deskSchedules = [
      {
        assetTypeId: deskType.id,
        name: "Annual Inspection",
        description: "Annual inspection of desk condition",
        type: "preventive",
        frequency: JSON.stringify({ type: "years", interval: 1 }),
        priority: "low",
        estimatedDuration: 10,
        estimatedCost: 0,
        requiredSkills: JSON.stringify(["Facilities"]),
        instructions: "Inspect desk for damage and stability",
        checklistItems: JSON.stringify([
          "Check for structural damage",
          "Test drawer functionality",
          "Inspect surface condition",
          "Check stability",
        ]),
        isActive: true,
      },
    ];

    for (const schedule of deskSchedules) {
      await prisma.maintenanceSchedule.upsert({
        where: {
          assetTypeId_name: {
            assetTypeId: schedule.assetTypeId,
            name: schedule.name,
          },
        },
        update: {},
        create: schedule,
      });
    }
  }

  console.log("✅ Created maintenance schedules for asset types");
}

// Run the migration
main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  });