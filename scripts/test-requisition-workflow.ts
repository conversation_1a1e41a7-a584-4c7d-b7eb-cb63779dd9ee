import prisma from "../lib/prisma";
import { RequisitionService } from "../lib/services/requisition-service";

async function testRequisitionWorkflow() {
  try {
    console.log("🧪 Testing Requisition Workflow...");

    // 1. Get a test asset type
    const assetType = await prisma.assetType.findFirst({
      where: { isActive: true },
    });

    if (!assetType) {
      throw new Error("No asset types found for testing");
    }

    console.log(`📦 Using asset type: ${assetType.name} (${assetType.code})`);

    // 2. Create a test user if not exists
    const testUser = await prisma.user.upsert({
      where: { email: "<EMAIL>" },
      update: {},
      create: {
        email: "<EMAIL>",
        name: "Test User",
        password: "hashed_password", // In real app, this would be properly hashed
        role: "user",
      },
    });

    console.log(`👤 Test user: ${testUser.name} (${testUser.email})`);

    // 3. Create a test manager
    const testManager = await prisma.user.upsert({
      where: { email: "<EMAIL>" },
      update: {},
      create: {
        email: "<EMAIL>",
        name: "Test Manager",
        password: "hashed_password",
        role: "manager",
      },
    });

    console.log(`👨‍💼 Test manager: ${testManager.name} (${testManager.email})`);

    // 4. Test requisition creation
    console.log("\n📝 Testing requisition creation...");
    const requisitionData = {
      requestorId: testUser.id,
      requestorName: testUser.name,
      assetTypeId: assetType.id,
      quantity: 2,
      priority: "normal" as const,
      justification: "Need laptops for new team members",
      businessCase: "Expanding development team requires additional hardware",
      location: "Office Building A, Floor 3",
      department: "Engineering",
      budgetCode: "ENG-2024-Q1",
      expectedDelivery: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      data: {
        specifications: "High-performance laptops with 16GB RAM",
        urgency: "normal",
      },
    };

    const createResult = await RequisitionService.createRequisition(requisitionData);
    
    if (!createResult.success) {
      throw new Error(`Failed to create requisition: ${createResult.error}`);
    }

    console.log(`✅ Requisition created: ${createResult.requisition!.id}`);
    console.log(`   Status: ${createResult.requisition!.status}`);

    // 5. Test requisition approval
    console.log("\n👍 Testing requisition approval...");
    const approvalData = {
      approverId: testManager.id,
      decision: "approved" as const,
      comments: "Approved for Q1 budget. Good business case.",
      conditions: "Must be delivered by end of month",
    };

    const approvalResult = await RequisitionService.processApproval(
      createResult.requisition!.id,
      approvalData
    );

    if (!approvalResult.success) {
      throw new Error(`Failed to approve requisition: ${approvalResult.error}`);
    }

    console.log(`✅ Requisition approved`);
    console.log(`   Status: ${approvalResult.requisition!.status}`);

    // Debug: Check the requisition status before fulfillment
    const preFullfillmentCheck = await prisma.requisition.findUnique({
      where: { id: createResult.requisition!.id },
      select: { id: true, status: true }
    });
    console.log(`🔍 Pre-fulfillment status check: ${preFullfillmentCheck?.status}`);

    // 6. Check auto-fulfillment result
    console.log("\n📦 Checking auto-fulfillment result...");
    
    if (preFullfillmentCheck?.status === "fulfilled") {
      console.log(`✅ Requisition was auto-fulfilled by the system`);
      console.log(`   This happened because available inventory was detected`);
    } else {
      // Manual fulfillment test - only if not auto-fulfilled
      console.log("\n📦 Testing manual requisition fulfillment...");
      
      // First, create some test assets for allocation
      const testAssets = [];
      for (let i = 0; i < 3; i++) {
        const asset = await prisma.asset.create({
          data: {
            name: `Test ${assetType.name} ${i + 1}`,
            serialNumber: `SN-TEST-${Date.now()}-${i + 1}`,
            assetTypeId: assetType.id,
            category: "IT Equipment",
            status: "active",
            location: "Warehouse",
            purchasePrice: 1000,
            purchaseDate: new Date(),
          },
        });
        testAssets.push(asset);
      }

      console.log(`📦 Created ${testAssets.length} test assets for allocation`);

      const fulfillmentData = {
        fulfillerId: testManager.id,
        method: "inventory" as const,
        assetIds: testAssets.slice(0, 2).map(a => a.id), // Allocate 2 assets
        deliveryDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3 days from now
        notes: "Allocated from existing inventory",
        trackingInfo: "TRACK-123456",
      };

      const fulfillmentResult = await RequisitionService.fulfillRequisition(
        createResult.requisition!.id,
        fulfillmentData
      );

      if (!fulfillmentResult.success) {
        throw new Error(`Failed to fulfill requisition: ${fulfillmentResult.error}`);
      }

      console.log(`✅ Requisition fulfilled`);
      console.log(`   Status: ${fulfillmentResult.requisition!.status}`);
      console.log(`   Method: ${fulfillmentData.method}`);
    }

    // 7. Verify final state
    console.log("\n🔍 Verifying final state...");
    const finalRequisition = await prisma.requisition.findUnique({
      where: { id: createResult.requisition!.id },
      include: {
        assetType: true,
        allocations: {
          include: {
            asset: true,
          },
        },
      },
    });

    console.log(`📊 Final requisition state:`);
    console.log(`   ID: ${finalRequisition!.id}`);
    console.log(`   Status: ${finalRequisition!.status}`);
    console.log(`   Asset Type: ${finalRequisition!.assetType.name}`);
    console.log(`   Quantity Requested: ${finalRequisition!.quantity}`);
    console.log(`   Allocated Assets: ${finalRequisition!.allocations?.length || 0}`);
    console.log(`   Approval History: ${Array.isArray(finalRequisition!.approvalHistory) ? finalRequisition!.approvalHistory.length : 0} entries`);

    // 8. Test rejection workflow
    console.log("\n❌ Testing requisition rejection...");
    const rejectionData = {
      requestorId: testUser.id,
      requestorName: testUser.name,
      assetTypeId: assetType.id,
      quantity: 10, // Large quantity to test rejection
      priority: "low" as const,
      justification: "Want more equipment",
      location: "Office",
      data: {},
    };

    const rejectReqResult = await RequisitionService.createRequisition(rejectionData);
    
    if (rejectReqResult.success) {
      const rejectionApproval = {
        approverId: testManager.id,
        decision: "rejected" as const,
        comments: "Insufficient budget for this quantity",
        alternativeOptions: "Consider requesting fewer units or wait for next quarter",
      };

      const rejectionResult = await RequisitionService.processApproval(
        rejectReqResult.requisition!.id,
        rejectionApproval
      );

      if (rejectionResult.success) {
        console.log(`✅ Requisition rejected successfully`);
        console.log(`   Status: ${rejectionResult.requisition!.status}`);
      }
    }

    // 9. Clean up test data
    console.log("\n🧹 Cleaning up test data...");
    
    // Delete test assets
    await prisma.asset.deleteMany({
      where: {
        id: { in: testAssets.map(a => a.id) },
      },
    });

    // Delete test requisitions
    await prisma.requisition.deleteMany({
      where: {
        requestorId: { in: [testUser.id] },
      },
    });

    console.log(`✅ Test data cleaned up`);

    console.log("\n🎉 All tests passed! Requisition workflow is working correctly.");

  } catch (error) {
    console.error("❌ Test failed:", error);
    throw error;
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testRequisitionWorkflow()
    .then(() => {
      console.log("🏁 Test completed successfully!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("💥 Test failed:", error);
      process.exit(1);
    });
}

export { testRequisitionWorkflow };