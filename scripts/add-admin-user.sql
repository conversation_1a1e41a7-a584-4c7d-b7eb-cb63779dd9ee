-- WizeAssets ERP - Admin User Creation SQL Script
-- =================================================
-- 
-- User: Thando Zondo
-- Email: <EMAIL>
-- Password: T3chn0l0gy@1
-- Role: admin
-- 
-- This script creates an admin user for the WizeAssets ERP system.
-- The password is already hashed using bcrypt with 10 salt rounds.

-- Check if the user already exists
SELECT id, name, email, role, department, status 
FROM "User" 
WHERE email = '<EMAIL>';

-- Insert the admin user (will be skipped if user already exists)
INSERT INTO "User" (
    id,
    name,
    email,
    password,
    department,
    role,
    status,
    phone,
    address,
    company,
    "jobTitle",
    "joinDate",
    "lastActive",
    "emailVerified",
    "avatarUrl",
    "createdAt",
    "updatedAt"
) VALUES (
    'clxh70j9z6uf9794nxtukjcsn',
    'Thando Zondo',
    '<EMAIL>',
    '$2b$10$GXbs4iJGCve51itbaq1QFeM1y939aOrhQo8As9JHrcAvVMQ1DMR8G',
    'IT',
    'admin',
    'active',
    '+27 11 123 4567',
    '123 Business Park, Johannesburg, South Africa',
    'SoImagine',
    'Chief Technology Officer',
    NOW(),
    NOW(),
    NOW(),
    NULL,
    NOW(),
    NOW()
) ON CONFLICT (email) DO NOTHING;

-- Verify the user was created successfully
SELECT id, name, email, role, department, status, phone, company, "jobTitle"
FROM "User" 
WHERE email = '<EMAIL>';

-- Show a summary of all admin users
SELECT 
    name,
    email,
    role,
    department,
    status,
    company,
    "jobTitle",
    "createdAt"
FROM "User" 
WHERE role = 'admin'
ORDER BY "createdAt" DESC;