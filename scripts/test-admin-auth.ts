#!/usr/bin/env tsx

import { PrismaClient } from '@prisma/client';
import { hashPassword } from '@/lib/utils';

const prisma = new PrismaClient();

async function main() {
  console.log('🔍 Testing admin authentication setup...');
  
  // Check if admin users exist
  const adminUsers = await prisma.user.findMany({
    where: {
      role: {
        in: ['admin', 'manager']
      }
    }
  });

  console.log(`Found ${adminUsers.length} admin/manager users:`);
  adminUsers.forEach(user => {
    console.log(`  - ${user.name} (${user.email}) - Role: ${user.role}, Status: ${user.status}`);
  });

  // Create default admin user if none exist
  if (adminUsers.length === 0) {
    console.log('\n🔧 Creating default admin user...');
    
    const adminUser = await prisma.user.create({
      data: {
        name: 'System Administrator',
        email: '<EMAIL>',
        password: await hashPassword('admin123'),
        role: 'admin',
        status: 'active',
        department: 'IT',
        joinDate: new Date(),
        lastActive: new Date(),
      }
    });

    console.log(`✅ Created admin user: ${adminUser.email}`);
    console.log(`🔑 Login credentials:`);
    console.log(`   Email: <EMAIL>`);
    console.log(`   Password: admin123`);
  } else {
    console.log(`\n🔑 Existing admin login credentials:`);
    console.log(`   Email: <EMAIL>`);
    console.log(`   Password: admin123`);
  }

  // Test database connection
  try {
    await prisma.$queryRaw`SELECT 1`;
    console.log('\n✅ Database connection successful');
  } catch (error) {
    console.error('\n❌ Database connection failed:', error);
  }

  console.log('\n🎯 Next steps:');
  console.log('1. Start your development server: npm run dev');
  console.log('2. Navigate to: http://localhost:3000/admin');
  console.log('3. You should be redirected to login page');
  console.log('4. Login with the credentials above');
  console.log('5. You should be redirected to the admin dashboard');
}

main()
  .catch((e) => {
    console.error('Error:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });