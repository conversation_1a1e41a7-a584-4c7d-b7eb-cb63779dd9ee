import prisma from "../lib/prisma";

const requisitionFormDefinition = {
  id: "requisition-asset-form",
  name: "Asset Requisition Form",
  description: "Form for requesting new assets",
  sections: [
    {
      id: "basic_info",
      title: "Basic Information",
      description: "Provide basic details about your asset request",
      fields: [
        {
          id: "quantity",
          type: "number",
          label: "Quantity",
          placeholder: "Enter quantity needed",
          required: true,
          validation: {
            min: 1,
            max: 100,
          },
        },
        {
          id: "priority",
          type: "select",
          label: "Priority",
          placeholder: "Select priority level",
          required: true,
          options: [
            { value: "low", label: "Low" },
            { value: "normal", label: "Normal" },
            { value: "high", label: "High" },
            { value: "critical", label: "Critical" },
          ],
          defaultValue: "normal",
        },
        {
          id: "expectedDelivery",
          type: "date",
          label: "Expected Delivery Date",
          placeholder: "When do you need this?",
          required: false,
        },
      ],
    },
    {
      id: "location_info",
      title: "Location & Department",
      description: "Specify where the assets will be used",
      fields: [
        {
          id: "location",
          type: "text",
          label: "Location",
          placeholder: "Building, floor, room, etc.",
          required: true,
        },
        {
          id: "department",
          type: "text",
          label: "Department",
          placeholder: "Your department name",
          required: false,
        },
        {
          id: "budgetCode",
          type: "text",
          label: "Budget Code",
          placeholder: "Budget code for this request",
          required: false,
        },
      ],
    },
    {
      id: "justification",
      title: "Justification",
      description: "Explain why you need these assets",
      fields: [
        {
          id: "justification",
          type: "textarea",
          label: "Justification",
          placeholder: "Explain why you need these assets...",
          required: true,
          validation: {
            minLength: 10,
            maxLength: 500,
          },
        },
        {
          id: "businessCase",
          type: "textarea",
          label: "Business Case",
          placeholder: "Provide business justification (optional)...",
          required: false,
          validation: {
            maxLength: 1000,
          },
        },
      ],
    },
  ],
  settings: {
    submitButtonText: "Submit Requisition",
    cancelButtonText: "Cancel",
    showProgressBar: true,
    allowSaveAsDraft: true,
    theme: "default",
  },
};

const approvalFormDefinition = {
  id: "requisition-approve-form",
  name: "Requisition Approval Form",
  description: "Form for approving or rejecting asset requisitions",
  sections: [
    {
      id: "decision",
      title: "Approval Decision",
      description: "Review and make a decision on this requisition",
      fields: [
        {
          id: "decision",
          type: "select",
          label: "Decision",
          placeholder: "Select your decision",
          required: true,
          options: [
            { value: "approved", label: "Approve" },
            { value: "rejected", label: "Reject" },
          ],
        },
        {
          id: "comments",
          type: "textarea",
          label: "Comments",
          placeholder: "Provide your decision rationale...",
          required: true,
          validation: {
            minLength: 10,
            maxLength: 500,
          },
        },
        {
          id: "conditions",
          type: "textarea",
          label: "Conditions (if approved)",
          placeholder: "Any conditions for approval...",
          required: false,
          validation: {
            maxLength: 500,
          },
        },
        {
          id: "alternativeOptions",
          type: "textarea",
          label: "Alternative Options (if rejected)",
          placeholder: "Suggest alternatives...",
          required: false,
          validation: {
            maxLength: 500,
          },
        },
      ],
    },
  ],
  settings: {
    submitButtonText: "Submit Decision",
    cancelButtonText: "Cancel",
    showProgressBar: false,
    allowSaveAsDraft: false,
    theme: "default",
  },
};

const fulfillmentFormDefinition = {
  id: "requisition-fulfill-form",
  name: "Requisition Fulfillment Form",
  description: "Form for fulfilling approved requisitions",
  sections: [
    {
      id: "fulfillment_method",
      title: "Fulfillment Method",
      description: "Choose how to fulfill this requisition",
      fields: [
        {
          id: "method",
          type: "select",
          label: "Fulfillment Method",
          placeholder: "Select fulfillment method",
          required: true,
          options: [
            { value: "inventory", label: "From Existing Inventory" },
            { value: "purchase", label: "Purchase Order" },
            { value: "lease", label: "Lease Agreement" },
          ],
        },
        {
          id: "deliveryDate",
          type: "date",
          label: "Delivery Date",
          placeholder: "When will this be delivered?",
          required: false,
        },
        {
          id: "notes",
          type: "textarea",
          label: "Notes",
          placeholder: "Additional notes about fulfillment...",
          required: false,
          validation: {
            maxLength: 500,
          },
        },
        {
          id: "trackingInfo",
          type: "text",
          label: "Tracking Information",
          placeholder: "Tracking number or reference",
          required: false,
        },
      ],
    },
  ],
  settings: {
    submitButtonText: "Complete Fulfillment",
    cancelButtonText: "Cancel",
    showProgressBar: false,
    allowSaveAsDraft: true,
    theme: "default",
  },
};

async function seedRequisitionForms() {
  try {
    console.log("🌱 Seeding requisition forms...");

    // Create form definitions
    const requisitionForm = await prisma.formDefinition.upsert({
      where: { id: "requisition-asset-form" },
      update: {
        name: requisitionFormDefinition.name,
        description: requisitionFormDefinition.description,
        sections: JSON.stringify(requisitionFormDefinition.sections),
        settings: JSON.stringify(requisitionFormDefinition.settings),
      },
      create: {
        id: "requisition-asset-form",
        name: requisitionFormDefinition.name,
        description: requisitionFormDefinition.description,
        sections: JSON.stringify(requisitionFormDefinition.sections),
        settings: JSON.stringify(requisitionFormDefinition.settings),
        isActive: true,
        createdBy: "system",
      },
    });

    const approvalForm = await prisma.formDefinition.upsert({
      where: { id: "requisition-approve-form" },
      update: {
        name: approvalFormDefinition.name,
        description: approvalFormDefinition.description,
        sections: JSON.stringify(approvalFormDefinition.sections),
        settings: JSON.stringify(approvalFormDefinition.settings),
      },
      create: {
        id: "requisition-approve-form",
        name: approvalFormDefinition.name,
        description: approvalFormDefinition.description,
        sections: JSON.stringify(approvalFormDefinition.sections),
        settings: JSON.stringify(approvalFormDefinition.settings),
        isActive: true,
        createdBy: "system",
      },
    });

    const fulfillmentForm = await prisma.formDefinition.upsert({
      where: { id: "requisition-fulfill-form" },
      update: {
        name: fulfillmentFormDefinition.name,
        description: fulfillmentFormDefinition.description,
        sections: JSON.stringify(fulfillmentFormDefinition.sections),
        settings: JSON.stringify(fulfillmentFormDefinition.settings),
      },
      create: {
        id: "requisition-fulfill-form",
        name: fulfillmentFormDefinition.name,
        description: fulfillmentFormDefinition.description,
        sections: JSON.stringify(fulfillmentFormDefinition.sections),
        settings: JSON.stringify(fulfillmentFormDefinition.settings),
        isActive: true,
        createdBy: "system",
      },
    });

    // Get all asset types
    const assetTypes = await prisma.assetType.findMany({
      where: { isActive: true },
    });

    console.log(`📋 Found ${assetTypes.length} asset types`);

    // Create form associations for each asset type
    for (const assetType of assetTypes) {
      // Requisition form
      await prisma.assetTypeForm.upsert({
        where: {
          assetTypeId_operationType_isDefault: {
            assetTypeId: assetType.id,
            operationType: "requisition.asset",
            isDefault: true,
          },
        },
        update: {
          formId: requisitionForm.id,
          isActive: true,
          isDefault: true,
        },
        create: {
          assetTypeId: assetType.id,
          formId: requisitionForm.id,
          operationType: "requisition.asset",
          version: 1,
          isDefault: true,
          isActive: true,
          createdBy: "system",
        },
      });

      // Approval form
      await prisma.assetTypeForm.upsert({
        where: {
          assetTypeId_operationType_isDefault: {
            assetTypeId: assetType.id,
            operationType: "requisition.approve",
            isDefault: true,
          },
        },
        update: {
          formId: approvalForm.id,
          isActive: true,
          isDefault: true,
        },
        create: {
          assetTypeId: assetType.id,
          formId: approvalForm.id,
          operationType: "requisition.approve",
          version: 1,
          isDefault: true,
          isActive: true,
          createdBy: "system",
        },
      });

      // Fulfillment form
      await prisma.assetTypeForm.upsert({
        where: {
          assetTypeId_operationType_isDefault: {
            assetTypeId: assetType.id,
            operationType: "requisition.fulfill",
            isDefault: true,
          },
        },
        update: {
          formId: fulfillmentForm.id,
          isActive: true,
          isDefault: true,
        },
        create: {
          assetTypeId: assetType.id,
          formId: fulfillmentForm.id,
          operationType: "requisition.fulfill",
          version: 1,
          isDefault: true,
          isActive: true,
          createdBy: "system",
        },
      });
    }

    console.log("✅ Requisition forms seeded successfully!");
    console.log(`📋 Created form associations for ${assetTypes.length} asset types`);

  } catch (error) {
    console.error("❌ Error seeding requisition forms:", error);
    throw error;
  }
}

// Run the seed function if this file is executed directly
if (require.main === module) {
  seedRequisitionForms()
    .then(() => {
      console.log("🎉 Seeding completed!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("💥 Seeding failed:", error);
      process.exit(1);
    });
}

export { seedRequisitionForms };