import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function addAdminUser() {
  try {
    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (existingUser) {
      console.log('❌ User <NAME_EMAIL> already exists');
      console.log('User details:', {
        id: existingUser.id,
        name: existingUser.name,
        email: existingUser.email,
        role: existingUser.role,
        department: existingUser.department,
        status: existingUser.status
      });
      return;
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash('T3chn0l0gy@1', 10);

    // Create the admin user
    const user = await prisma.user.create({
      data: {
        name: 'Thando Zondo',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'admin',
        department: 'IT',
        status: 'active',
        phone: '+27 11 123 4567',
        address: '123 Business Park, Johannesburg, South Africa',
        company: 'SoImagine',
        jobTitle: 'Chief Technology Officer',
        joinDate: new Date(),
        lastActive: new Date(),
        emailVerified: new Date(), // Mark as verified
        avatarUrl: null
      }
    });

    console.log('✅ Admin user created successfully!');
    console.log('User details:', {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      department: user.department,
      status: user.status,
      phone: user.phone,
      company: user.company,
      jobTitle: user.jobTitle
    });

  } catch (error) {
    console.error('❌ Error creating admin user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the function
addAdminUser();