import bcrypt from 'bcryptjs';

async function generateAdminUserSQL() {
  try {
    // Generate bcrypt hash for the password
    const password = 'T3chn0l0gy@1';
    const hashedPassword = await bcrypt.hash(password, 10);
    
    // Generate a cuid-like ID (for demonstration, in production you'd use proper cuid)
    const userId = 'clx' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    
    console.log('🔐 Generated Admin User SQL');
    console.log('============================');
    console.log('');
    console.log('-- Check if user already exists:');
    console.log(`SELECT id, name, email, role, department, status FROM "User" WHERE email = '<EMAIL>';`);
    console.log('');
    console.log('-- Insert new admin user:');
    console.log(`INSERT INTO "User" (`);
    console.log(`    id,`);
    console.log(`    name,`);
    console.log(`    email,`);
    console.log(`    password,`);
    console.log(`    department,`);
    console.log(`    role,`);
    console.log(`    status,`);
    console.log(`    phone,`);
    console.log(`    address,`);
    console.log(`    company,`);
    console.log(`    "jobTitle",`);
    console.log(`    "joinDate",`);
    console.log(`    "lastActive",`);
    console.log(`    "emailVerified",`);
    console.log(`    "avatarUrl",`);
    console.log(`    "createdAt",`);
    console.log(`    "updatedAt"`);
    console.log(`) VALUES (`);
    console.log(`    '${userId}',`);
    console.log(`    'Thando Zondo',`);
    console.log(`    '<EMAIL>',`);
    console.log(`    '${hashedPassword}',`);
    console.log(`    'IT',`);
    console.log(`    'admin',`);
    console.log(`    'active',`);
    console.log(`    '+27 11 123 4567',`);
    console.log(`    '123 Business Park, Johannesburg, South Africa',`);
    console.log(`    'SoImagine',`);
    console.log(`    'Chief Technology Officer',`);
    console.log(`    NOW(),`);
    console.log(`    NOW(),`);
    console.log(`    NOW(),`);
    console.log(`    NULL,`);
    console.log(`    NOW(),`);
    console.log(`    NOW()`);
    console.log(`) ON CONFLICT (email) DO NOTHING;`);
    console.log('');
    console.log('-- Verify user was created:');
    console.log(`SELECT id, name, email, role, department, status, phone, company, "jobTitle" FROM "User" WHERE email = '<EMAIL>';`);
    console.log('');
    console.log('📋 User Details:');
    console.log('================');
    console.log(`Name: Thando Zondo`);
    console.log(`Email: <EMAIL>`);
    console.log(`Password: ${password}`);
    console.log(`Role: admin`);
    console.log(`Department: IT`);
    console.log(`Company: SoImagine`);
    console.log(`Job Title: Chief Technology Officer`);
    console.log(`Phone: +27 11 123 4567`);
    console.log(`Address: 123 Business Park, Johannesburg, South Africa`);
    console.log('');
    console.log('🔑 Password Hash Generated:');
    console.log(hashedPassword);
    console.log('');
    console.log('📝 Instructions:');
    console.log('1. Connect to your PostgreSQL database');
    console.log('2. Run the SQL statements above');
    console.log('3. Verify the user was created successfully');
    console.log('4. The user can now login with the email and password provided');
    
  } catch (error) {
    console.error('❌ Error generating SQL:', error);
  }
}

// Run the function
generateAdminUserSQL();