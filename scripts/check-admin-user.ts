import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkAdminUser() {
  try {
    console.log('🔍 Checking admin user status...');
    console.log('================================');

    // Check if the user exists
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (!user) {
      console.log('❌ User not found in database');
      console.log('');
      console.log('💡 Solutions:');
      console.log('1. Create the user first using: npm run db:create-admin');
      console.log('2. Or run the seeder: npm run db:seed:dev');
      console.log('3. Or use the SQL script directly');
      return;
    }

    console.log('✅ User found in database');
    console.log('');
    console.log('📋 User Details:');
    console.table({
      'ID': user.id,
      'Name': user.name,
      'Email': user.email,
      'Role': user.role,
      'Department': user.department,
      'Status': user.status,
      'Email Verified': user.emailVerified ? 'Yes' : 'No',
      'Created At': user.createdAt.toISOString(),
      'Last Active': user.lastActive?.toISOString() || 'Never'
    });

    // Check for potential issues
    console.log('');
    console.log('🔍 Checking for potential issues:');
    
    if (user.status !== 'active') {
      console.log('⚠️  User status is not active:', user.status);
    } else {
      console.log('✅ User status is active');
    }

    if (user.role !== 'admin') {
      console.log('⚠️  User role is not admin:', user.role);
    } else {
      console.log('✅ User role is admin');
    }

    if (!user.emailVerified) {
      console.log('⚠️  Email is not verified');
    } else {
      console.log('✅ Email is verified');
    }

    // Test password hash format
    if (!user.password.startsWith('$2b$') && !user.password.startsWith('$2a$')) {
      console.log('⚠️  Password hash format looks incorrect');
    } else {
      console.log('✅ Password hash format looks correct');
    }

    console.log('');
    console.log('🔑 Login Credentials:');
    console.log('Email: <EMAIL>');
    console.log('Password: T3chn0l0gy@1');

  } catch (error) {
    console.error('❌ Error checking user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkAdminUser();