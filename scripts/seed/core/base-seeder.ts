import { PrismaClient } from '@prisma/client';
import { SeedConfig } from './seed-config';
import { Logger } from './logger';

export abstract class BaseSeeder {
  protected prisma: PrismaClient;
  protected config: SeedConfig;
  protected logger: Logger;

  constructor(prisma: PrismaClient, config: SeedConfig, logger: Logger) {
    this.prisma = prisma;
    this.config = config;
    this.logger = logger;
  }

  /**
   * Get the name of this seeder (for logging and identification)
   */
  abstract getName(): string;

  /**
   * Get the dependencies of this seeder (other seeders that must run first)
   */
  getDependencies(): string[] {
    return [];
  }

  /**
   * Execute the seeding process
   */
  abstract seed(): Promise<void>;

  /**
   * Clean up data created by this seeder
   */
  async clean(): Promise<void> {
    this.logger.info(`🧹 Cleaning ${this.getName()}...`);
    // Default implementation - can be overridden by subclasses
  }

  /**
   * Validate that dependencies are met
   */
  async validateDependencies(): Promise<void> {
    // Default implementation - can be overridden by subclasses
  }

  /**
   * Get the count of items to create based on config
   */
  protected getCount(baseCount: number): number {
    const multiplier = this.config.getSizeMultiplier();
    return Math.max(1, Math.floor(baseCount * multiplier));
  }

  /**
   * Create items in batches to avoid memory issues
   */
  protected async createInBatches<T>(
    items: T[],
    batchSize: number,
    createFn: (batch: T[]) => Promise<void>
  ): Promise<void> {
    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      await createFn(batch);
      
      if (i + batchSize < items.length) {
        // Add a small delay between batches to prevent overwhelming the database
        await new Promise(resolve => setTimeout(resolve, 10));
      }
    }
  }

  /**
   * Get a random subset of items
   */
  protected getRandomSubset<T>(items: T[], count: number): T[] {
    if (count >= items.length) return items;
    
    const shuffled = [...items].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
  }

  /**
   * Get a weighted random choice from options
   */
  protected getWeightedChoice<T>(options: { item: T; weight: number }[]): T {
    const totalWeight = options.reduce((sum, option) => sum + option.weight, 0);
    const random = Math.random() * totalWeight;
    
    let currentWeight = 0;
    for (const option of options) {
      currentWeight += option.weight;
      if (random <= currentWeight) {
        return option.item;
      }
    }
    
    // Fallback to first option
    return options[0].item;
  }

  /**
   * Generate a date within a specific range
   */
  protected getRandomDateInRange(startDate: Date, endDate: Date): Date {
    const startTime = startDate.getTime();
    const endTime = endDate.getTime();
    const randomTime = startTime + Math.random() * (endTime - startTime);
    return new Date(randomTime);
  }

  /**
   * Generate business days only (excluding weekends)
   */
  protected getRandomBusinessDate(startDate: Date, endDate: Date): Date {
    let date = this.getRandomDateInRange(startDate, endDate);
    
    // If it's a weekend, adjust to the next Monday
    while (date.getDay() === 0 || date.getDay() === 6) {
      date = new Date(date.getTime() + 24 * 60 * 60 * 1000); // Add one day
    }
    
    return date;
  }

  /**
   * Generate a realistic price based on category and type
   */
  protected generatePrice(min: number, max: number, trend: 'increasing' | 'decreasing' | 'stable' = 'stable'): number {
    let basePrice = min + Math.random() * (max - min);
    
    switch (trend) {
      case 'increasing':
        basePrice *= 1 + Math.random() * 0.2; // Up to 20% increase
        break;
      case 'decreasing':
        basePrice *= 0.8 + Math.random() * 0.2; // Up to 20% decrease
        break;
      case 'stable':
        basePrice *= 0.95 + Math.random() * 0.1; // ±5% variation
        break;
    }
    
    return Math.round(basePrice * 100) / 100; // Round to 2 decimal places
  }
}
