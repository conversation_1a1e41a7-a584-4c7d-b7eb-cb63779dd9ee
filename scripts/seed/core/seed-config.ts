export class SeedConfig {
  private environment: 'development' | 'staging' | 'production' = 'development';
  private size: 'small' | 'medium' | 'large' = 'medium';
  private seed: number = 12345; // Fixed seed for reproducible results

  // Size multipliers for different data sizes
  private sizeMultipliers = {
    small: 0.3,
    medium: 1.0,
    large: 3.0
  };

  // Base counts for different entity types
  private baseCounts = {
    users: 25,
    assetCategories: 10,
    assetTypes: 30,
    assets: 100,
    maintenanceTasks: 80,
    workflows: 15,
    purchaseOrders: 40,
    leases: 20,
    reports: 12,
    notifications: 60,
    workflowExecutions: 200,
    assetTransfers: 30,
    maintenanceSchedules: 50,
    invoices: 35,
    approvalRequests: 25,
    inventoryChecks: 15
  };

  // Environment-specific configurations
  private environmentConfigs = {
    development: {
      batchSize: 50,
      enableVerboseLogging: true,
      generateTestData: true
    },
    staging: {
      batchSize: 100,
      enableVerboseLogging: false,
      generateTestData: true
    },
    production: {
      batchSize: 200,
      enableVerboseLogging: false,
      generateTestData: false
    }
  };

  setEnvironment(environment: 'development' | 'staging' | 'production'): void {
    this.environment = environment;
  }

  setSize(size: 'small' | 'medium' | 'large'): void {
    this.size = size;
  }

  setSeed(seed: number): void {
    this.seed = seed;
  }

  getEnvironment(): string {
    return this.environment;
  }

  getSize(): string {
    return this.size;
  }

  getSeed(): number {
    return this.seed;
  }

  getSizeMultiplier(): number {
    return this.sizeMultipliers[this.size];
  }

  getBaseCount(entity: keyof typeof SeedConfig.prototype.baseCounts): number {
    return this.baseCounts[entity];
  }

  getCount(entity: keyof typeof SeedConfig.prototype.baseCounts): number {
    return Math.max(1, Math.floor(this.getBaseCount(entity) * this.getSizeMultiplier()));
  }

  getBatchSize(): number {
    return this.environmentConfigs[this.environment].batchSize;
  }

  isVerboseLoggingEnabled(): boolean {
    return this.environmentConfigs[this.environment].enableVerboseLogging;
  }

  shouldGenerateTestData(): boolean {
    return this.environmentConfigs[this.environment].generateTestData;
  }

  // Business-specific configurations
  getDepartments(): string[] {
    return [
      'IT',
      'Human Resources',
      'Finance',
      'Operations',
      'Marketing',
      'Sales',
      'Legal',
      'Facilities',
      'Security',
      'Research & Development'
    ];
  }

  getLocations(): string[] {
    return [
      'New York Office',
      'Los Angeles Office',
      'Chicago Office',
      'Houston Office',
      'Phoenix Office',
      'Philadelphia Office',
      'San Antonio Office',
      'San Diego Office',
      'Dallas Office',
      'San Jose Office',
      'Austin Office',
      'Jacksonville Office',
      'Fort Worth Office',
      'Columbus Office',
      'Charlotte Office',
      'Detroit Office',
      'El Paso Office',
      'Memphis Office',
      'Seattle Office',
      'Denver Office',
      'Washington DC Office',
      'Boston Office',
      'Nashville Office',
      'Baltimore Office',
      'Louisville Office',
      'Portland Office',
      'Oklahoma City Office',
      'Milwaukee Office',
      'Las Vegas Office',
      'Albuquerque Office'
    ];
  }

  getCompanyNames(): string[] {
    return [
      'TechCorp Industries',
      'Global Dynamics',
      'Innovative Solutions Inc',
      'Enterprise Systems Ltd',
      'Modern Business Co',
      'Advanced Technologies',
      'Strategic Partners LLC',
      'Professional Services Group',
      'Digital Enterprises',
      'Corporate Solutions'
    ];
  }

  getSuppliers(): string[] {
    return [
      'Dell Technologies',
      'HP Inc.',
      'Lenovo Group',
      'Apple Inc.',
      'Microsoft Corporation',
      'Cisco Systems',
      'IBM Corporation',
      'Oracle Corporation',
      'Intel Corporation',
      'AMD Inc.',
      'NVIDIA Corporation',
      'Samsung Electronics',
      'LG Electronics',
      'Sony Corporation',
      'Panasonic Corporation',
      'Canon Inc.',
      'Epson Corporation',
      'Brother Industries',
      'Xerox Holdings',
      'Logitech International'
    ];
  }

  getMaintenanceTypes(): string[] {
    return [
      'Preventive Maintenance',
      'Corrective Maintenance',
      'Predictive Maintenance',
      'Condition-based Maintenance',
      'Scheduled Inspection',
      'Emergency Repair',
      'Upgrade Installation',
      'Calibration',
      'Cleaning',
      'Replacement'
    ];
  }

  getAssetStatuses(): { status: string; weight: number }[] {
    return [
      { status: 'active', weight: 70 },
      { status: 'maintenance', weight: 20 },
      { status: 'disposed', weight: 10 }
    ];
  }

  getUserRoles(): { role: string; weight: number }[] {
    return [
      { role: 'user', weight: 70 },
      { role: 'manager', weight: 25 },
      { role: 'admin', weight: 5 }
    ];
  }

  getWorkflowTypes(): string[] {
    return [
      'asset-automation',
      'maintenance-workflow',
      'procurement-process',
      'approval-chain',
      'notification-system',
      'reporting-automation',
      'compliance-check',
      'inventory-management'
    ];
  }
}
