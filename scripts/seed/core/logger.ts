export class Logger {
  private colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    dim: '\x1b[2m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m'
  };

  private formatMessage(level: string, message: string, color: string): string {
    const timestamp = new Date().toISOString();
    const prefix = `${color}[${timestamp}] ${level}:${this.colors.reset}`;
    return `${prefix} ${message}`;
  }

  info(message: string, ...args: any[]): void {
    console.log(this.formatMessage('INFO', message, this.colors.blue), ...args);
  }

  success(message: string, ...args: any[]): void {
    console.log(this.formatMessage('SUCCESS', message, this.colors.green), ...args);
  }

  warn(message: string, ...args: any[]): void {
    console.warn(this.formatMessage('WARN', message, this.colors.yellow), ...args);
  }

  error(message: string, error?: any, ...args: any[]): void {
    console.error(this.formatMessage('ERROR', message, this.colors.red), ...args);
    if (error) {
      console.error(error);
    }
  }

  debug(message: string, ...args: any[]): void {
    if (process.env.NODE_ENV === 'development') {
      console.log(this.formatMessage('DEBUG', message, this.colors.dim), ...args);
    }
  }

  progress(current: number, total: number, label: string = 'Progress'): void {
    const percentage = Math.round((current / total) * 100);
    const barLength = 20;
    const filledLength = Math.round((barLength * current) / total);
    const bar = '█'.repeat(filledLength) + '░'.repeat(barLength - filledLength);
    
    process.stdout.write(`\r${this.colors.cyan}${label}: [${bar}] ${percentage}% (${current}/${total})${this.colors.reset}`);
    
    if (current === total) {
      process.stdout.write('\n');
    }
  }

  table(data: any[], title?: string): void {
    if (title) {
      console.log(`\n${this.colors.bright}${this.colors.cyan}${title}${this.colors.reset}`);
    }
    console.table(data);
  }

  section(title: string): void {
    const separator = '='.repeat(60);
    console.log(`\n${this.colors.bright}${this.colors.magenta}${separator}`);
    console.log(`${title.toUpperCase()}`);
    console.log(`${separator}${this.colors.reset}\n`);
  }

  subsection(title: string): void {
    const separator = '-'.repeat(40);
    console.log(`\n${this.colors.cyan}${separator}`);
    console.log(`${title}`);
    console.log(`${separator}${this.colors.reset}`);
  }
}
