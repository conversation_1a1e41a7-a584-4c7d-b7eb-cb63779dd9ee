import { faker } from '@faker-js/faker';
import { BaseSeeder } from '../core/base-seeder';
import bcrypt from 'bcryptjs';

export class UserSeeder extends BaseSeeder {
  getName(): string {
    return 'Users';
  }

  async seed(): Promise<void> {
    const count = this.getCount(this.config.getBaseCount('users'));
    const departments = this.config.getDepartments();
    const roles = this.config.getUserRoles();
    
    // Always create a few admin and manager users
    const adminUsers = await this.createAdminUsers();
    const managerUsers = await this.createManagerUsers(departments);
    const regularUsers = await this.createRegularUsers(count - adminUsers.length - managerUsers.length, departments);

    const allUsers = [...adminUsers, ...managerUsers, ...regularUsers];
    
    if (this.config.isVerboseLoggingEnabled()) {
      this.logger.table(
        allUsers.map(user => ({
          name: user.name,
          email: user.email,
          role: user.role,
          department: user.department
        })),
        'Created Users Summary'
      );
    }

    this.logger.info(`Created ${allUsers.length} users (${adminUsers.length} admins, ${managerUsers.length} managers, ${regularUsers.length} regular users)`);
  }

  private async createAdminUsers() {
    const adminUsers = [
      {
        name: 'Thando Zondo',
        email: '<EMAIL>',
        password: await bcrypt.hash('T3chn0l0gy@1', 10),
        department: 'IT',
        role: 'admin',
        status: 'active',
        phone: '+27 11 123 4567',
        address: '123 Business Park, Johannesburg, South Africa',
        company: 'SoImagine',
        jobTitle: 'Chief Technology Officer',
        joinDate: new Date(),
        lastActive: new Date(),
        emailVerified: new Date(),
        avatarUrl: null
      },
      {
        name: 'System Administrator',
        email: '<EMAIL>',
        password: await bcrypt.hash('admin123', 10),
        department: 'IT',
        role: 'admin',
        status: 'active',
        joinDate: faker.date.past({ years: 2 }),
        lastActive: faker.date.recent({ days: 1 }),
        avatarUrl: faker.image.avatarGitHub()
      },
      {
        name: 'IT Manager',
        email: '<EMAIL>',
        password: await bcrypt.hash('manager123', 10),
        department: 'IT',
        role: 'admin',
        status: 'active',
        joinDate: faker.date.past({ years: 3 }),
        lastActive: faker.date.recent({ days: 1 }),
        avatarUrl: faker.image.avatarGitHub()
      },
      {
        name: 'Asset Manager',
        email: '<EMAIL>',
        password: await bcrypt.hash('assets123', 10),
        department: 'Operations',
        role: 'admin',
        status: 'active',
        joinDate: faker.date.past({ years: 1 }),
        lastActive: faker.date.recent({ days: 2 }),
        avatarUrl: faker.image.avatarGitHub()
      }
    ];

    const createdUsers = await this.prisma.user.createMany({
      data: adminUsers
    });

    return adminUsers;
  }

  private async createManagerUsers(departments: string[]) {
    const managerUsers = departments.slice(0, 6).map((department, index) => ({
      name: faker.person.fullName(),
      email: `${department.toLowerCase().replace(/[^a-z0-9]/g, '')}.manager.${index}@company.com`,
      password: bcrypt.hashSync('manager123', 10),
      department,
      role: 'manager',
      status: faker.helpers.weightedArrayElement([
        { weight: 90, value: 'active' },
        { weight: 10, value: 'inactive' }
      ]),
      joinDate: faker.date.past({ years: faker.number.int({ min: 1, max: 5 }) }),
      lastActive: faker.date.recent({ days: faker.number.int({ min: 1, max: 30 }) }),
      avatarUrl: faker.image.avatarGitHub()
    }));

    await this.prisma.user.createMany({
      data: managerUsers
    });

    return managerUsers;
  }

  private async createRegularUsers(count: number, departments: string[]) {
    const users = [];
    
    for (let i = 0; i < count; i++) {
      const firstName = faker.person.firstName();
      const lastName = faker.person.lastName();
      const department = faker.helpers.arrayElement(departments);
      
      const user = {
        name: `${firstName} ${lastName}`,
        email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}@company.com`,
        password: bcrypt.hashSync('user123', 10),
        department,
        role: 'user',
        status: faker.helpers.weightedArrayElement([
          { weight: 85, value: 'active' },
          { weight: 10, value: 'inactive' },
          { weight: 5, value: 'suspended' }
        ]),
        joinDate: faker.date.past({ years: faker.number.int({ min: 1, max: 5 }) }),
        lastActive: faker.date.recent({ days: faker.number.int({ min: 1, max: 90 }) }),
        avatarUrl: Math.random() > 0.3 ? faker.image.avatarGitHub() : null
      };

      users.push(user);
    }

    // Create users in batches
    await this.createInBatches(
      users,
      this.config.getBatchSize(),
      async (batch) => {
        await this.prisma.user.createMany({
          data: batch
        });
      }
    );

    return users;
  }

  async clean(): Promise<void> {
    await this.prisma.user.deleteMany();
    this.logger.info('🧹 Cleaned all users');
  }
}
