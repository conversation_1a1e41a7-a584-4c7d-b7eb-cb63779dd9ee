import { faker } from '@faker-js/faker';
import { BaseSeeder } from '../core/base-seeder';

export class PurchaseOrderSeeder extends BaseSeeder {
  getName(): string {
    return 'Purchase Orders';
  }

  getDependencies(): string[] {
    return ['Users'];
  }

  async seed(): Promise<void> {
    const count = this.getCount(this.config.getBaseCount('purchaseOrders'));
    const suppliers = this.config.getSuppliers();
    const locations = this.config.getLocations();
    const users = await this.prisma.user.findMany();

    if (users.length === 0) {
      this.logger.warn('No users found for purchase order seeding');
      return;
    }

    const purchaseOrders = [];
    
    for (let i = 0; i < count; i++) {
      const items = this.generateOrderItems();
      const total = items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
      const requestedBy = faker.helpers.arrayElement(users).email;
      
      const purchaseOrder = {
        supplier: faker.helpers.arrayElement(suppliers),
        items: JSON.stringify(items),
        total,
        requestedBy,
        deliveryDate: faker.date.future({ years: 1 }),
        shippingAddress: faker.helpers.arrayElement(locations),
        status: faker.helpers.weightedArrayElement([
          { weight: 20, value: 'pending_approval' },
          { weight: 30, value: 'approved' },
          { weight: 10, value: 'rejected' },
          { weight: 40, value: 'completed' }
        ])
      };

      purchaseOrders.push(purchaseOrder);
    }

    await this.createInBatches(
      purchaseOrders,
      this.config.getBatchSize(),
      async (batch) => {
        await this.prisma.purchaseOrder.createMany({
          data: batch
        });
      }
    );

    // Create related invoices
    await this.createInvoices(purchaseOrders);
    
    // Create approval requests
    await this.createApprovalRequests(users);

    this.logger.info(`Created ${purchaseOrders.length} purchase orders`);
  }

  private generateOrderItems() {
    const itemCount = faker.number.int({ min: 1, max: 5 });
    const items = [];

    for (let i = 0; i < itemCount; i++) {
      items.push({
        name: faker.commerce.productName(),
        description: faker.commerce.productDescription(),
        quantity: faker.number.int({ min: 1, max: 10 }),
        unitPrice: faker.number.float({ min: 10, max: 1000, fractionDigits: 2 }),
        category: faker.commerce.department()
      });
    }

    return items;
  }

  private async createInvoices(purchaseOrders: any[]) {
    const invoiceCount = this.getCount(this.config.getBaseCount('invoices'));
    const suppliers = this.config.getSuppliers();
    const invoices = [];

    for (let i = 0; i < invoiceCount; i++) {
      const po = faker.helpers.arrayElement(purchaseOrders);
      const invoiceNumber = `INV-${faker.string.alphanumeric({ length: 8, casing: 'upper' })}`;
      
      const invoice = {
        invoiceNumber,
        supplier: faker.helpers.arrayElement(suppliers),
        amount: faker.number.float({ min: 100, max: 10000, fractionDigits: 2 }),
        purchaseOrderId: Math.random() > 0.3 ? po.id : null,
        items: JSON.stringify(this.generateOrderItems()),
        dueDate: faker.date.future({ years: 1 }),
        status: faker.helpers.weightedArrayElement([
          { weight: 25, value: 'pending_approval' },
          { weight: 30, value: 'approved' },
          { weight: 10, value: 'rejected' },
          { weight: 35, value: 'paid' }
        ])
      };

      invoices.push(invoice);
    }

    await this.createInBatches(
      invoices,
      this.config.getBatchSize(),
      async (batch) => {
        await this.prisma.invoice.createMany({
          data: batch
        });
      }
    );

    this.logger.info(`Created ${invoices.length} invoices`);
  }

  private async createApprovalRequests(users: any[]) {
    const approvalCount = this.getCount(this.config.getBaseCount('approvalRequests'));
    const approvals = [];

    for (let i = 0; i < approvalCount; i++) {
      const requestedBy = faker.helpers.arrayElement(users).email;
      const approver = faker.helpers.arrayElement(users.filter(u => u.role !== 'user')).email;
      
      const approval = {
        type: faker.helpers.arrayElement(['purchase', 'disposal', 'transfer', 'maintenance']),
        requestedBy,
        approver,
        items: JSON.stringify(this.generateOrderItems()),
        justification: faker.lorem.paragraph(),
        urgency: faker.helpers.weightedArrayElement([
          { weight: 40, value: 'normal' },
          { weight: 30, value: 'high' },
          { weight: 20, value: 'low' },
          { weight: 10, value: 'critical' }
        ]),
        status: faker.helpers.weightedArrayElement([
          { weight: 40, value: 'pending' },
          { weight: 35, value: 'approved' },
          { weight: 25, value: 'rejected' }
        ])
      };

      approvals.push(approval);
    }

    await this.createInBatches(
      approvals,
      this.config.getBatchSize(),
      async (batch) => {
        await this.prisma.approvalRequest.createMany({
          data: batch
        });
      }
    );

    this.logger.info(`Created ${approvals.length} approval requests`);
  }

  async clean(): Promise<void> {
    await this.prisma.approvalRequest.deleteMany();
    await this.prisma.invoice.deleteMany();
    await this.prisma.purchaseOrder.deleteMany();
    this.logger.info('🧹 Cleaned all purchase orders and related data');
  }
}
