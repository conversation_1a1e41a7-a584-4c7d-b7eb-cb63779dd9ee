import { BaseSeeder } from '../core/base-seeder';
import { BusinessDataGenerator } from '../generators/business-data';

export class AssetCategorySeeder extends BaseSeeder {
  getName(): string {
    return 'Asset Categories';
  }

  async seed(): Promise<void> {
    const categoryData = BusinessDataGenerator.generateAssetCategories();
    const createdCategories = [];

    for (const parentCategory of categoryData) {
      // Create parent category
      const parent = await this.prisma.assetCategory.create({
        data: {
          name: parentCategory.name,
          description: parentCategory.description,
          level: 0,
          path: parentCategory.name,
          isActive: true
        }
      });

      createdCategories.push(parent);

      // Create child categories
      for (const childData of parentCategory.categories) {
        const child = await this.prisma.assetCategory.create({
          data: {
            name: childData.name,
            description: childData.description,
            parentId: parent.id,
            level: 1,
            path: `${parent.name} > ${childData.name}`,
            isActive: true
          }
        });

        createdCategories.push(child);
      }
    }

    this.logger.info(`Created ${createdCategories.length} asset categories (${categoryData.length} parent, ${createdCategories.length - categoryData.length} child)`);

    if (this.config.isVerboseLoggingEnabled()) {
      this.logger.table(
        createdCategories.map(cat => ({
          name: cat.name,
          level: cat.level,
          path: cat.path,
          isActive: cat.isActive
        })),
        'Created Asset Categories'
      );
    }
  }

  async clean(): Promise<void> {
    await this.prisma.assetCategory.deleteMany();
    this.logger.info('🧹 Cleaned all asset categories');
  }
}
