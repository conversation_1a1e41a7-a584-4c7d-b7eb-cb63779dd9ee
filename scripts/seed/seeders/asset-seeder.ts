import { faker } from '@faker-js/faker';
import { BaseSeeder } from '../core/base-seeder';
import { BusinessDataGenerator } from '../generators/business-data';

export class AssetSeeder extends BaseSeeder {
  getName(): string {
    return 'Assets';
  }

  getDependencies(): string[] {
    return ['Asset Types'];
  }

  async seed(): Promise<void> {
    const count = this.getCount(this.config.getBaseCount('assets'));
    const departments = this.config.getDepartments();
    const locations = this.config.getLocations();
    const statuses = this.config.getAssetStatuses();

    // Get all asset types
    const assetTypes = await this.prisma.assetType.findMany({
      include: { category: true }
    });

    if (assetTypes.length === 0) {
      this.logger.warn('No asset types found. Please seed asset types first.');
      return;
    }

    const assets = [];
    
    for (let i = 0; i < count; i++) {
      const assetType = faker.helpers.arrayElement(assetTypes);
      const purchaseDate = faker.date.past({ years: 3 });
      const assetTemplate = BusinessDataGenerator.generateAssetTypes().find(t => t.name === assetType.name);
      
      const basePrice = assetTemplate ? 
        faker.number.float({ 
          min: assetTemplate.priceRange.min, 
          max: assetTemplate.priceRange.max,
          fractionDigits: 2
        }) : 
        faker.number.float({ min: 100, max: 10000, fractionDigits: 2 });

      const asset = {
        name: BusinessDataGenerator.generateAssetName(assetType.name),
        category: assetType.category?.name || 'General',
        purchaseDate,
        purchasePrice: basePrice,
        location: faker.helpers.arrayElement(locations),
        department: faker.helpers.arrayElement(departments),
        status: this.getWeightedChoice(statuses),
        serialNumber: BusinessDataGenerator.generateSerialNumber(assetType.name),
        assetTypeId: assetType.id,
        assetImages: this.generateAssetImages(assetType.name)
      };

      assets.push(asset);
    }

    // Create assets in batches
    await this.createInBatches(
      assets,
      this.config.getBatchSize(),
      async (batch) => {
        await this.prisma.asset.createMany({
          data: batch
        });
      }
    );

    this.logger.info(`Created ${assets.length} assets`);

    // Create related data for assets
    await this.createAssetRelatedData();

    if (this.config.isVerboseLoggingEnabled()) {
      const summary = await this.generateAssetSummary();
      this.logger.table(summary, 'Asset Summary by Type');
    }
  }

  private generateAssetImages(assetTypeName: string): string[] {
    // Generate 0-3 mock image URLs for assets
    const imageCount = faker.number.int({ min: 0, max: 3 });
    const images = [];
    
    for (let i = 0; i < imageCount; i++) {
      images.push(`https://picsum.photos/400/300?random=${faker.number.int({ min: 1, max: 1000 })}`);
    }
    
    return images;
  }

  private async createAssetRelatedData(): Promise<void> {
    const assets = await this.prisma.asset.findMany();
    
    // Create asset transfers for some assets
    await this.createAssetTransfers(assets);
    
    // Create depreciation records for some assets
    await this.createDepreciationRecords(assets);
    
    // Create disposal records for disposed assets
    await this.createDisposalRecords(assets);
  }

  private async createAssetTransfers(assets: any[]): Promise<void> {
    const transferAssets = this.getRandomSubset(assets, Math.floor(assets.length * 0.3));
    const locations = this.config.getLocations();
    const departments = this.config.getDepartments();

    const transfers = transferAssets.map(asset => {
      const newLocation = faker.helpers.arrayElement(locations.filter(l => l !== asset.location));
      const newDepartment = faker.helpers.arrayElement(departments.filter(d => d !== asset.department));
      
      return {
        assetId: asset.id,
        previousLocation: asset.location,
        newLocation,
        previousDepartment: asset.department,
        newDepartment,
        transferDate: faker.date.between({ 
          from: asset.purchaseDate, 
          to: new Date() 
        }),
        reason: faker.helpers.arrayElement([
          'Department reorganization',
          'Employee relocation',
          'Office renovation',
          'Equipment consolidation',
          'Branch closure',
          'New project requirements'
        ])
      };
    });

    await this.prisma.assetTransfer.createMany({
      data: transfers
    });

    this.logger.info(`Created ${transfers.length} asset transfers`);
  }

  private async createDepreciationRecords(assets: any[]): Promise<void> {
    const depreciationAssets = this.getRandomSubset(assets, Math.floor(assets.length * 0.8));
    
    const depreciations = depreciationAssets.map(asset => {
      const usefulLife = faker.number.int({ min: 2, max: 10 });
      const salvageValue = asset.purchasePrice * faker.number.float({ min: 0.05, max: 0.15 });
      
      return {
        assetId: asset.id,
        method: faker.helpers.arrayElement(['straight-line', 'double-declining', 'sum-of-years']),
        usefulLife,
        salvageValue,
        initialValue: asset.purchasePrice,
        schedule: JSON.stringify(this.generateDepreciationSchedule(asset.purchasePrice, salvageValue, usefulLife))
      };
    });

    await this.prisma.assetDepreciation.createMany({
      data: depreciations
    });

    this.logger.info(`Created ${depreciations.length} depreciation records`);
  }

  private async createDisposalRecords(assets: any[]): Promise<void> {
    const disposedAssets = assets.filter(asset => asset.status === 'disposed');
    
    if (disposedAssets.length === 0) return;

    const disposals = disposedAssets.map(asset => {
      const salePrice = asset.purchasePrice * faker.number.float({ min: 0.1, max: 0.4 });
      const bookValue = asset.purchasePrice * faker.number.float({ min: 0.2, max: 0.6 });
      
      return {
        assetId: asset.id,
        method: faker.helpers.arrayElement(['sale', 'donation', 'recycling', 'destruction']),
        disposalDate: faker.date.between({ 
          from: asset.purchaseDate, 
          to: new Date() 
        }),
        bookValue,
        salePrice,
        gainLoss: salePrice - bookValue,
        reason: faker.helpers.arrayElement([
          'End of useful life',
          'Obsolescence',
          'Damage beyond repair',
          'Upgrade to newer model',
          'Cost of maintenance too high'
        ])
      };
    });

    await this.prisma.assetDisposal.createMany({
      data: disposals
    });

    this.logger.info(`Created ${disposals.length} disposal records`);
  }

  private generateDepreciationSchedule(purchasePrice: number, salvageValue: number, usefulLife: number) {
    const annualDepreciation = (purchasePrice - salvageValue) / usefulLife;
    const schedule = [];
    
    for (let year = 1; year <= usefulLife; year++) {
      schedule.push({
        year,
        depreciation: annualDepreciation,
        cumulativeDepreciation: annualDepreciation * year,
        bookValue: purchasePrice - (annualDepreciation * year)
      });
    }
    
    return schedule;
  }

  private async generateAssetSummary() {
    const summary = await this.prisma.asset.groupBy({
      by: ['category'],
      _count: {
        id: true
      },
      _avg: {
        purchasePrice: true
      }
    });

    return summary.map(item => ({
      category: item.category,
      count: item._count.id,
      avgPrice: Math.round((item._avg.purchasePrice || 0) * 100) / 100
    }));
  }

  async clean(): Promise<void> {
    await this.prisma.assetDisposal.deleteMany();
    await this.prisma.assetDepreciation.deleteMany();
    await this.prisma.assetTransfer.deleteMany();
    await this.prisma.asset.deleteMany();
    this.logger.info('🧹 Cleaned all assets and related data');
  }
}
