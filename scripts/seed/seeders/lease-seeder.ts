import { faker } from '@faker-js/faker';
import { BaseSeeder } from '../core/base-seeder';

export class LeaseSeeder extends BaseSeeder {
  getName(): string {
    return 'Leases';
  }

  getDependencies(): string[] {
    return ['Assets'];
  }

  async seed(): Promise<void> {
    const count = this.getCount(this.config.getBaseCount('leases'));
    const assets = await this.prisma.asset.findMany();
    const companies = this.config.getCompanyNames();

    if (assets.length === 0) {
      this.logger.warn('No assets found for lease seeding');
      return;
    }

    const leases = [];
    
    for (let i = 0; i < count; i++) {
      const asset = faker.helpers.arrayElement(assets);
      const startDate = faker.date.past({ years: 2 });
      const endDate = faker.date.future({ years: 2, refDate: startDate });
      const monthlyPayment = faker.number.float({ min: 100, max: 5000, fractionDigits: 2 });
      const totalValue = monthlyPayment * 12; // Annual value

      const lease = {
        assetId: asset.id,
        lessorId: faker.string.uuid(),
        lessorName: faker.helpers.arrayElement(companies),
        lesseeId: faker.string.uuid(),
        lesseeName: faker.company.name(),
        leaseType: faker.helpers.arrayElement(['Operating', 'Finance', 'Capital']),
        startDate,
        endDate,
        monthlyPayment,
        totalValue,
        securityDeposit: monthlyPayment * faker.number.float({ min: 1, max: 3 }),
        status: faker.helpers.weightedArrayElement([
          { weight: 60, value: 'Active' },
          { weight: 20, value: 'Draft' },
          { weight: 10, value: 'Expired' },
          { weight: 10, value: 'Terminated' }
        ]),
        renewalOptions: faker.number.int({ min: 0, max: 3 }),
        earlyTerminationClause: faker.datatype.boolean(),
        maintenanceResponsibility: faker.helpers.arrayElement(['Lessor', 'Lessee', 'Shared']),
        insuranceRequirement: faker.datatype.boolean(),
        terms: faker.lorem.paragraph(),
        attachments: JSON.stringify([])
      };

      leases.push(lease);
    }

    await this.createInBatches(
      leases,
      this.config.getBatchSize(),
      async (batch) => {
        await this.prisma.leaseAgreement.createMany({
          data: batch
        });
      }
    );

    // Create payment schedules for active leases
    await this.createPaymentSchedules();

    this.logger.info(`Created ${leases.length} lease agreements`);
  }

  private async createPaymentSchedules() {
    const activeLeases = await this.prisma.leaseAgreement.findMany({
      where: { status: 'Active' }
    });

    const payments = [];

    for (const lease of activeLeases) {
      const startDate = new Date(lease.startDate);
      const endDate = new Date(lease.endDate);
      const monthlyPayment = lease.monthlyPayment;

      // Generate monthly payments
      let currentDate = new Date(startDate);
      while (currentDate <= endDate) {
        const isPaid = Math.random() > 0.2; // 80% payments are made
        const payment = {
          leaseId: lease.id,
          dueDate: new Date(currentDate),
          amount: monthlyPayment,
          status: isPaid ? 'Paid' : faker.helpers.arrayElement(['Pending', 'Overdue']),
          paidAmount: isPaid ? monthlyPayment : 0,
          paidDate: isPaid ? faker.date.recent({ days: 30 }) : null,
          paymentMethod: isPaid ? faker.helpers.arrayElement(['Bank Transfer', 'Check', 'ACH']) : null,
          transactionId: isPaid ? faker.string.alphanumeric({ length: 10, casing: 'upper' }) : null,
          lateFee: !isPaid && Math.random() > 0.7 ? faker.number.float({ min: 25, max: 100 }) : 0
        };

        payments.push(payment);

        // Move to next month
        currentDate.setMonth(currentDate.getMonth() + 1);
      }
    }

    await this.createInBatches(
      payments,
      this.config.getBatchSize(),
      async (batch) => {
        await this.prisma.paymentSchedule.createMany({
          data: batch
        });
      }
    );

    this.logger.info(`Created ${payments.length} payment schedule entries`);
  }

  async clean(): Promise<void> {
    await this.prisma.paymentSchedule.deleteMany();
    await this.prisma.leaseRenewal.deleteMany();
    await this.prisma.leaseAgreement.deleteMany();
    this.logger.info('🧹 Cleaned all lease data');
  }
}
