import { BaseSeeder } from '../core/base-seeder';
import { BusinessDataGenerator } from '../generators/business-data';

export class AssetTypeSeeder extends BaseSeeder {
  getName(): string {
    return 'Asset Types';
  }

  async seed(): Promise<void> {
    const count = this.getCount(this.config.getBaseCount('assetTypes'));
    const assetTypes = BusinessDataGenerator.generateAssetTypes();
    const createdTypes = [];
    
    if (this.config.isVerboseLoggingEnabled()) {
      this.logger.table(assetTypes, 'Asset Type Templates');
    }

    for (const assetType of assetTypes) {
      const { category } = assetType;
      const categoryFound = await this.prisma.assetCategory.findFirst({
        where: { name: category }
      });

      if (!categoryFound) {
        this.logger.warn(`Category not found for asset type: ${assetType.name}`);
        continue;
      }

      const createdType = await this.prisma.assetType.create({
        data: {
          name: assetType.name,
          code: assetType.code,
          description: assetType.description,
          categoryId: categoryFound.id,
          icon: assetType.icon,
          color: assetType.color,
          tags: [category],
          isActive: true,
          createdBy: 'system'
        }
      });

      this.logger.info(`Asset Type created: ${assetType.name}`);
      createdTypes.push(createdType);
    }

    this.logger.info(`Created ${createdTypes.length} asset types`);
  }

  async clean(): Promise<void> {
    await this.prisma.assetType.deleteMany();
    this.logger.info('🧹 Cleaned all asset types');
  }
}
