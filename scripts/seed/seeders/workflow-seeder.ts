import { faker } from '@faker-js/faker';
import { BaseSeeder } from '../core/base-seeder';
import { BusinessDataGenerator } from '../generators/business-data';

export class WorkflowSeeder extends BaseSeeder {
  getName(): string {
    return 'Workflows';
  }

  async seed(): Promise<void> {
    const count = this.getCount(this.config.getBaseCount('workflows'));
    const workflowTemplates = BusinessDataGenerator.generateWorkflowTemplates();
    const workflowTypes = this.config.getWorkflowTypes();

    const workflows = [];
    
    // Create template workflows first
    for (const template of workflowTemplates) {
      const workflow = {
        name: template.name,
        description: template.description,
        type: template.type,
        nodes: JSON.stringify(template.nodes),
        edges: JSON.stringify([]), // Simplified for seeding
        webhooks: JSON.stringify([]),
        createdBy: 'system',
        isActive: true,
        tags: [template.type]
      };
      workflows.push(workflow);
    }

    // Create additional random workflows
    for (let i = workflows.length; i < count; i++) {
      const type = faker.helpers.arrayElement(workflowTypes);
      const workflow = {
        name: `${faker.company.buzzVerb()} ${faker.company.buzzNoun()} Workflow`,
        description: faker.lorem.sentence(),
        type,
        nodes: JSON.stringify(this.generateRandomNodes()),
        edges: JSON.stringify([]),
        webhooks: JSON.stringify([]),
        createdBy: 'system',
        isActive: faker.datatype.boolean(),
        tags: [type, faker.helpers.arrayElement(['automated', 'manual', 'scheduled'])]
      };
      workflows.push(workflow);
    }

    await this.createInBatches(
      workflows,
      this.config.getBatchSize(),
      async (batch) => {
        await this.prisma.workflow.createMany({
          data: batch
        });
      }
    );

    // Create workflow executions
    await this.createWorkflowExecutions();

    this.logger.info(`Created ${workflows.length} workflows`);
  }

  private generateRandomNodes() {
    const nodeTypes = ['trigger', 'action', 'decision', 'approval', 'notification'];
    const nodeCount = faker.number.int({ min: 3, max: 8 });
    const nodes = [];

    for (let i = 0; i < nodeCount; i++) {
      nodes.push({
        id: `node_${i}`,
        type: faker.helpers.arrayElement(nodeTypes),
        label: faker.company.buzzPhrase(),
        config: {}
      });
    }

    return nodes;
  }

  private async createWorkflowExecutions() {
    const workflows = await this.prisma.workflow.findMany();
    const executionCount = this.getCount(this.config.getBaseCount('workflowExecutions'));
    const executions = [];

    for (let i = 0; i < executionCount; i++) {
      const workflow = faker.helpers.arrayElement(workflows);
      const startedAt = faker.date.past({ years: 1 });
      const isCompleted = Math.random() > 0.2; // 80% completed
      
      const execution = {
        workflowId: workflow.id,
        status: isCompleted ? 
          faker.helpers.arrayElement(['completed', 'failed']) : 
          faker.helpers.arrayElement(['queued', 'running']),
        input: JSON.stringify({ trigger: 'test_data' }),
        output: isCompleted ? JSON.stringify({ result: 'success' }) : null,
        startedAt,
        completedAt: isCompleted ? faker.date.soon({ days: 1, refDate: startedAt }) : null,
        context: JSON.stringify({ executionId: i, metadata: {} })
      };

      executions.push(execution);
    }

    await this.createInBatches(
      executions,
      this.config.getBatchSize(),
      async (batch) => {
        await this.prisma.workflowExecution.createMany({
          data: batch
        });
      }
    );

    this.logger.info(`Created ${executions.length} workflow executions`);
  }

  async clean(): Promise<void> {
    await this.prisma.workflowExecution.deleteMany();
    await this.prisma.workflow.deleteMany();
    this.logger.info('🧹 Cleaned all workflows');
  }
}
