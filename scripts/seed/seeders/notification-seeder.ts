import { faker } from '@faker-js/faker';
import { BaseSeeder } from '../core/base-seeder';

export class NotificationSeeder extends BaseSeeder {
  getName(): string {
    return 'Notifications';
  }

  getDependencies(): string[] {
    return ['Users'];
  }

  async seed(): Promise<void> {
    const count = this.getCount(this.config.getBaseCount('notifications'));
    const users = await this.prisma.user.findMany();

    if (users.length === 0) {
      this.logger.warn('No users found for notification seeding');
      return;
    }

    const notifications = [];
    
    for (let i = 0; i < count; i++) {
      const user = faker.helpers.arrayElement(users);
      const notificationType = faker.helpers.arrayElement([
        'system', 'asset', 'maintenance', 'approval', 'lease', 'financial'
      ]);
      
      const notification = {
        recipient: user.email,
        subject: this.generateNotificationSubject(notificationType),
        message: this.generateNotificationMessage(notificationType),
        priority: faker.helpers.weightedArrayElement([
          { weight: 50, value: 'normal' },
          { weight: 30, value: 'high' },
          { weight: 15, value: 'low' },
          { weight: 5, value: 'critical' }
        ]),
        type: notificationType,
        status: faker.helpers.weightedArrayElement([
          { weight: 80, value: 'delivered' },
          { weight: 15, value: 'read' },
          { weight: 5, value: 'sent' }
        ]),
        readAt: Math.random() > 0.6 ? faker.date.recent({ days: 7 }) : null
      };

      notifications.push(notification);
    }

    await this.createInBatches(
      notifications,
      this.config.getBatchSize(),
      async (batch) => {
        await this.prisma.notification.createMany({
          data: batch
        });
      }
    );

    this.logger.info(`Created ${notifications.length} notifications`);

    if (this.config.isVerboseLoggingEnabled()) {
      const summary = this.generateNotificationSummary(notifications);
      this.logger.table(summary, 'Notification Summary by Type');
    }

    // Create inventory checks
    await this.createInventoryChecks();
  }

  private generateNotificationSubject(type: string): string {
    const subjects = {
      system: [
        'System Maintenance Scheduled',
        'Database Backup Completed',
        'Security Update Available',
        'System Performance Alert'
      ],
      asset: [
        'New Asset Added to Inventory',
        'Asset Transfer Completed',
        'Asset Disposal Approved',
        'Asset Warranty Expiring Soon'
      ],
      maintenance: [
        'Maintenance Task Assigned',
        'Maintenance Overdue',
        'Preventive Maintenance Due',
        'Maintenance Task Completed'
      ],
      approval: [
        'Purchase Order Requires Approval',
        'Disposal Request Pending',
        'Budget Approval Needed',
        'Request Approved'
      ],
      lease: [
        'Lease Agreement Expiring',
        'Payment Due Reminder',
        'Lease Renewal Available',
        'Payment Overdue'
      ],
      financial: [
        'Monthly Report Generated',
        'Budget Threshold Exceeded',
        'Invoice Received',
        'Payment Processed'
      ]
    };

    const typeSubjects = subjects[type as keyof typeof subjects] || subjects.system;
    return faker.helpers.arrayElement(typeSubjects);
  }

  private generateNotificationMessage(type: string): string {
    const messages = {
      system: 'System notification: Please review the attached details and take necessary action.',
      asset: 'Asset management notification: An asset in your department requires attention.',
      maintenance: 'Maintenance notification: Please review the maintenance schedule and assignments.',
      approval: 'Approval required: A request is waiting for your approval in the system.',
      lease: 'Lease management: Please review the lease agreement details.',
      financial: 'Financial notification: New financial information is available for review.'
    };

    const baseMessage = messages[type as keyof typeof messages] || messages.system;
    return `${baseMessage} ${faker.lorem.sentence()}`;
  }

  private generateNotificationSummary(notifications: any[]) {
    const summary = notifications.reduce((acc, notification) => {
      const type = notification.type;
      const priority = notification.priority;
      
      if (!acc[type]) {
        acc[type] = { total: 0, critical: 0, high: 0, normal: 0, low: 0, read: 0 };
      }
      
      acc[type].total++;
      acc[type][priority as keyof typeof acc[typeof type]]++;
      
      if (notification.readAt) {
        acc[type].read++;
      }
      
      return acc;
    }, {} as any);

    return Object.entries(summary).map(([type, stats]: [string, any]) => ({
      type,
      total: stats.total,
      critical: stats.critical,
      high: stats.high,
      read: stats.read,
      readRate: `${Math.round((stats.read / stats.total) * 100)}%`
    }));
  }

  private async createInventoryChecks() {
    const checkCount = this.getCount(this.config.getBaseCount('inventoryChecks'));
    const locations = this.config.getLocations();
    const departments = this.config.getDepartments();
    const users = await this.prisma.user.findMany({
      where: { role: { in: ['manager', 'admin'] } }
    });

    const inventoryChecks = [];

    for (let i = 0; i < checkCount; i++) {
      const location = faker.helpers.arrayElement(locations);
      const department = faker.helpers.arrayElement(departments);
      const performedBy = faker.helpers.arrayElement(users).email;
      const assetsChecked = faker.number.int({ min: 10, max: 100 });
      const assetsFound = faker.number.int({ min: assetsChecked - 5, max: assetsChecked });
      const assetsMissing = assetsChecked - assetsFound;

      const check = {
        location,
        department,
        checkDate: faker.date.past({ years: 1 }),
        performedBy,
        assetsChecked,
        assetsFound,
        assetsMissing,
        missingAssets: JSON.stringify(
          assetsMissing > 0 ? 
            Array.from({ length: assetsMissing }, () => ({
              id: faker.string.uuid(),
              name: faker.commerce.productName(),
              serialNumber: faker.string.alphanumeric({ length: 8, casing: 'upper' })
            })) : 
            []
        )
      };

      inventoryChecks.push(check);
    }

    await this.createInBatches(
      inventoryChecks,
      this.config.getBatchSize(),
      async (batch) => {
        await this.prisma.inventoryCheck.createMany({
          data: batch
        });
      }
    );

    this.logger.info(`Created ${inventoryChecks.length} inventory checks`);
  }

  async clean(): Promise<void> {
    await this.prisma.inventoryCheck.deleteMany();
    await this.prisma.notification.deleteMany();
    this.logger.info('🧹 Cleaned all notifications and inventory checks');
  }
}
