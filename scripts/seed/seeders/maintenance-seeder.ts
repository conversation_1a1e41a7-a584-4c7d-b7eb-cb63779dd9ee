import { faker } from '@faker-js/faker';
import { BaseSeeder } from '../core/base-seeder';
import { BusinessDataGenerator } from '../generators/business-data';

export class MaintenanceSeeder extends BaseSeeder {
  getName(): string {
    return 'Maintenance';
  }

  getDependencies(): string[] {
    return ['Assets', 'Users'];
  }

  async seed(): Promise<void> {
    const assets = await this.prisma.asset.findMany({
      where: { status: { in: ['active', 'maintenance'] } }
    });

    const users = await this.prisma.user.findMany({
      where: { role: { in: ['user', 'manager'] } }
    });

    if (assets.length === 0 || users.length === 0) {
      this.logger.warn('No assets or users found for maintenance seeding');
      return;
    }

    // Create maintenance records
    await this.createMaintenanceRecords(assets, users);
    
    // Create maintenance tasks
    await this.createMaintenanceTasks(assets, users);

    this.logger.info(`Maintenance seeding completed`);
  }

  private async createMaintenanceRecords(assets: any[], users: any[]): Promise<void> {
    const maintenanceCount = this.getCount(Math.floor(assets.length * 0.6));
    const maintenanceTypes = this.config.getMaintenanceTypes();
    const maintenanceRecords = [];

    for (let i = 0; i < maintenanceCount; i++) {
      const asset = faker.helpers.arrayElement(assets);
      const assignedUser = faker.helpers.arrayElement(users);
      const isCompleted = Math.random() > 0.4; // 60% completed
      
      // If completed, use past dates; if not completed, use future dates
      let scheduledDate: Date;
      let completedDate: Date | null = null;
      
      if (isCompleted) {
        scheduledDate = faker.date.past({ years: 1 });
        completedDate = faker.date.between({ from: scheduledDate, to: new Date() });
      } else {
        scheduledDate = faker.date.future({ years: 1 });
        completedDate = null;
      }
      
      const record = {
        assetId: asset.id,
        type: faker.helpers.arrayElement(maintenanceTypes),
        scheduledDate,
        completedDate,
        assignedTo: assignedUser.email,
        notes: faker.lorem.sentence(),
        status: isCompleted ? 'completed' : faker.helpers.arrayElement(['scheduled', 'in_progress', 'cancelled'])
      };

      maintenanceRecords.push(record);
    }

    await this.createInBatches(
      maintenanceRecords,
      this.config.getBatchSize(),
      async (batch) => {
        await this.prisma.assetMaintenance.createMany({
          data: batch
        });
      }
    );

    this.logger.info(`Created ${maintenanceRecords.length} maintenance records`);
  }

  private async createMaintenanceTasks(assets: any[], users: any[]): Promise<void> {
    const taskCount = this.getCount(this.config.getBaseCount('maintenanceTasks'));
    const maintenanceTasks = [];

    for (let i = 0; i < taskCount; i++) {
      const asset = faker.helpers.arrayElement(assets);
      const assignedUser = faker.helpers.arrayElement(users);
      const scheduledDate = faker.date.future({ years: 1 });
      const dueDate = faker.date.soon({ days: 30, refDate: scheduledDate });
      
      const task = {
        assetId: asset.id,
        title: `${faker.helpers.arrayElement(['Routine', 'Preventive', 'Emergency', 'Scheduled'])} Maintenance`,
        description: faker.lorem.sentences(2),
        type: faker.helpers.arrayElement(['preventive', 'predictive', 'corrective']),
        priority: faker.helpers.arrayElement(['low', 'medium', 'high', 'critical']),
        status: faker.helpers.arrayElement(['scheduled', 'in_progress', 'completed', 'cancelled']),
        scheduledDate,
        dueDate,
        completedDate: Math.random() > 0.5 ? faker.date.recent({ days: 10 }) : null,
        assignedTo: assignedUser.email,
        estimatedDuration: faker.number.int({ min: 30, max: 480 }), // 30 minutes to 8 hours
        estimatedCost: faker.number.float({ min: 50, max: 2000, fractionDigits: 2 }),
        instructions: faker.lorem.paragraph(),
        checklistItems: JSON.stringify(BusinessDataGenerator.generateMaintenanceTasks(asset.category))
      };

      maintenanceTasks.push(task);
    }

    await this.createInBatches(
      maintenanceTasks,
      this.config.getBatchSize(),
      async (batch) => {
        await this.prisma.maintenanceTask.createMany({
          data: batch
        });
      }
    );

    this.logger.info(`Created ${maintenanceTasks.length} maintenance tasks`);
  }

  async clean(): Promise<void> {
    await this.prisma.maintenanceTask.deleteMany();
    await this.prisma.assetMaintenance.deleteMany();
    this.logger.info('🧹 Cleaned all maintenance data');
  }
}
