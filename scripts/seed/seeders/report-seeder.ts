import { faker } from '@faker-js/faker';
import { BaseSeeder } from '../core/base-seeder';

export class ReportSeeder extends BaseSeeder {
  getName(): string {
    return 'Reports';
  }

  async seed(): Promise<void> {
    const count = this.getCount(this.config.getBaseCount('reports'));
    const reportTypes = [
      'asset-inventory',
      'depreciation',
      'maintenance-summary',
      'financial-overview',
      'asset-utilization',
      'compliance-audit',
      'vendor-performance',
      'lease-expiration',
      'disposal-report',
      'purchase-analysis'
    ];

    const reports = [];
    
    for (let i = 0; i < count; i++) {
      const type = faker.helpers.arrayElement(reportTypes);
      const report = {
        type,
        name: this.generateReportName(type),
        parameters: JSON.stringify(this.generateReportParameters(type)),
        format: faker.helpers.arrayElement(['PDF', 'CSV', 'XLSX', 'JSON']),
        delivery: faker.helpers.arrayElement(['download', 'email', 'api']),
        status: faker.helpers.weightedArrayElement([
          { weight: 70, value: 'completed' },
          { weight: 20, value: 'pending' },
          { weight: 10, value: 'failed' }
        ]),
        url: Math.random() > 0.3 ? `https://reports.company.com/${faker.string.uuid()}.pdf` : null
      };

      reports.push(report);
    }

    await this.createInBatches(
      reports,
      this.config.getBatchSize(),
      async (batch) => {
        await this.prisma.report.createMany({
          data: batch
        });
      }
    );

    this.logger.info(`Created ${reports.length} reports`);

    if (this.config.isVerboseLoggingEnabled()) {
      const summary = this.generateReportSummary(reports);
      this.logger.table(summary, 'Report Summary by Type');
    }
  }

  private generateReportName(type: string): string {
    const nameTemplates = {
      'asset-inventory': 'Asset Inventory Report',
      'depreciation': 'Depreciation Analysis',
      'maintenance-summary': 'Maintenance Summary',
      'financial-overview': 'Financial Overview',
      'asset-utilization': 'Asset Utilization Report',
      'compliance-audit': 'Compliance Audit',
      'vendor-performance': 'Vendor Performance Analysis',
      'lease-expiration': 'Lease Expiration Report',
      'disposal-report': 'Asset Disposal Report',
      'purchase-analysis': 'Purchase Analysis'
    };

    const baseName = nameTemplates[type as keyof typeof nameTemplates] || 'General Report';
    const period = faker.helpers.arrayElement(['Monthly', 'Quarterly', 'Annual', 'Weekly']);
    const year = faker.date.recent({ days: 365 }).getFullYear();
    
    return `${period} ${baseName} - ${year}`;
  }

  private generateReportParameters(type: string): any {
    const baseParams = {
      dateRange: {
        start: faker.date.past({ years: 1 }),
        end: faker.date.recent({ days: 30 })
      },
      generatedBy: 'system',
      timezone: 'UTC'
    };

    const typeSpecificParams = {
      'asset-inventory': {
        ...baseParams,
        departments: faker.helpers.arrayElements(this.config.getDepartments(), { min: 1, max: 3 }),
        assetTypes: ['IT Equipment', 'Office Equipment'],
        includeDisposed: false
      },
      'depreciation': {
        ...baseParams,
        method: faker.helpers.arrayElement(['straight-line', 'double-declining']),
        categories: ['IT Equipment', 'Vehicles'],
        includeProjections: true
      },
      'maintenance-summary': {
        ...baseParams,
        maintenanceTypes: ['Preventive', 'Corrective'],
        includeScheduled: true,
        includeCosts: true
      },
      'financial-overview': {
        ...baseParams,
        currency: 'USD',
        includeROI: true,
        includeForecast: true
      }
    };

    return typeSpecificParams[type as keyof typeof typeSpecificParams] || baseParams;
  }

  private generateReportSummary(reports: any[]) {
    const summary = reports.reduce((acc, report) => {
      const type = report.type;
      if (!acc[type]) {
        acc[type] = { count: 0, completed: 0, pending: 0, failed: 0 };
      }
      acc[type].count++;
      acc[type][report.status as keyof typeof acc[typeof type]]++;
      return acc;
    }, {} as any);

    return Object.entries(summary).map(([type, stats]: [string, any]) => ({
      type,
      total: stats.count,
      completed: stats.completed,
      pending: stats.pending,
      failed: stats.failed
    }));
  }

  async clean(): Promise<void> {
    await this.prisma.report.deleteMany();
    this.logger.info('🧹 Cleaned all reports');
  }
}
