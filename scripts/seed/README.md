# Database Seeding System

A comprehensive, modular database seeding system for the WizeAssets ERP application. This system generates realistic demo data for properly showcasing all application features.

## Features

- 🏗️ **Modular Architecture**: Each entity has its own seeder with clear dependencies
- 🎯 **Realistic Data**: Business-appropriate data that demonstrates real-world scenarios
- ⚙️ **Configurable**: Multiple environment and size configurations
- 🔄 **Reproducible**: Uses fixed seeds for consistent results
- 🧹 **Clean**: Built-in cleanup capabilities
- 📊 **Progress Tracking**: Detailed logging and progress indicators
- 🚀 **Scalable**: Batch processing for large datasets

## Quick Start

### Basic Usage

```bash
# Seed with default settings (development, medium size)
pnpm db:seed

# Seed with clean database first
pnpm db:seed:dev

# Different sizes
pnpm db:seed:small   # ~30% of base data
pnpm db:seed:large   # ~300% of base data
```

### Advanced Usage

```bash
# Custom environment and size
npx tsx scripts/seed/index.ts --environment staging --size large

# Seed specific entities only
npx tsx scripts/seed/index.ts --specific users,assets,maintenance

# Clean database before seeding
npx tsx scripts/seed/index.ts --clean --environment development

# Get help
npx tsx scripts/seed/index.ts --help
```

## Seeding Modules

### Core Modules

1. **Users** - Employee accounts with different roles and departments
2. **Asset Categories** - Hierarchical categorization system
3. **Asset Types** - Detailed asset type definitions with metadata
4. **Assets** - Individual assets with realistic specifications and history

### Business Modules

5. **Maintenance** - Tasks, schedules, and maintenance records
6. **Workflows** - Automation workflows and execution history
7. **Purchase Orders** - Procurement data with invoices and approvals
8. **Leases** - Asset leasing agreements with payment schedules
9. **Reports** - Generated business reports with various parameters
10. **Notifications** - System notifications and inventory checks

## Data Characteristics

### Realistic Business Data

- **IT Equipment**: Computers, servers, networking gear with proper specifications
- **Office Equipment**: Furniture, printers, communication devices
- **Vehicles**: Fleet management with maintenance schedules
- **Manufacturing**: Production equipment with complex maintenance needs
- **Facilities**: HVAC, security systems, building infrastructure

### Business Relationships

- Assets linked to appropriate asset types and categories
- Maintenance tasks assigned to qualified users
- Purchase orders connected to actual asset acquisitions
- Workflows triggered by realistic business events
- Financial data with proper depreciation calculations

### Temporal Consistency

- Purchase dates that make sense for asset ages
- Maintenance schedules aligned with asset lifecycles
- Financial data reflecting realistic depreciation
- User activity patterns matching business hours
- Seasonal variations in procurement and maintenance

## Configuration

### Environment Settings

- **Development**: Verbose logging, test data generation
- **Staging**: Production-like but with test markers
- **Production**: Minimal logging, realistic data only

### Size Multipliers

- **Small**: 30% of base counts (good for testing)
- **Medium**: 100% of base counts (demo environments)
- **Large**: 300% of base counts (stress testing)

### Base Counts (Medium Size)

```typescript
users: 25
assetCategories: 10
assetTypes: 30
assets: 100
maintenanceTasks: 80
workflows: 15
purchaseOrders: 40
leases: 20
reports: 12
notifications: 60
```

## Architecture

### Core Classes

- **SeedOrchestrator**: Main coordination and CLI interface
- **BaseSeeder**: Abstract base class for all seeders
- **SeedConfig**: Configuration management and business constants
- **Logger**: Colored logging with progress indicators
- **BusinessDataGenerator**: Realistic business data templates

### Design Patterns

- **Dependency Injection**: All seeders receive shared dependencies
- **Template Method**: BaseSeeder provides common functionality
- **Factory Pattern**: BusinessDataGenerator creates data templates
- **Command Pattern**: CLI interface with argument parsing

## Data Examples

### Users
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "role": "manager",
  "department": "IT",
  "status": "active"
}
```

### Assets
```json
{
  "name": "Dell OptiPlex A7X9 (2023)",
  "category": "Computers",
  "purchasePrice": 1250.00,
  "location": "New York Office",
  "serialNumber": "IT24A7X9F8",
  "status": "active"
}
```

### Maintenance Tasks
```json
{
  "title": "Routine Maintenance",
  "type": "preventive",
  "priority": "medium",
  "estimatedDuration": 120,
  "checklistItems": [
    "Clean internal components and fans",
    "Update operating system and software",
    "Check hardware components for failures"
  ]
}
```

## Extension Guide

### Adding New Seeders

1. Create a new seeder class extending `BaseSeeder`
2. Implement required methods: `getName()` and `seed()`
3. Add dependencies in `getDependencies()` if needed
4. Register in `SeedOrchestrator.initializeSeeders()`

```typescript
export class MySeeder extends BaseSeeder {
  getName(): string {
    return 'My Entity';
  }

  getDependencies(): string[] {
    return ['Users']; // Optional
  }

  async seed(): Promise<void> {
    const count = this.getCount(50); // Base count
    // Implementation here
  }
}
```

### Adding Business Data Templates

Add new templates to `BusinessDataGenerator`:

```typescript
static generateMyEntityTypes() {
  return [
    {
      name: 'My Entity Type',
      category: 'My Category',
      // ... other properties
    }
  ];
}
```

## Best Practices

### Performance
- Use batch operations for large datasets
- Implement proper progress tracking
- Add delays between batches to prevent database overload

### Data Quality
- Use realistic business relationships
- Implement proper data validation
- Ensure temporal consistency

### Maintainability
- Keep seeders focused on single entities
- Use configuration for business constants
- Implement comprehensive logging

### Testing
- Test with different size configurations
- Verify data relationships are correct
- Ensure cleanup works properly

## Troubleshooting

### Common Issues

**Memory errors with large datasets**
```bash
# Use smaller batch sizes or reduce data size
npx tsx scripts/seed/index.ts --size small
```

**Foreign key constraint errors**
```bash
# Check seeder dependencies and order
npx tsx scripts/seed/index.ts --clean
```

**Performance issues**
```bash
# Monitor batch sizes and add delays
# Check database connection pool settings
```

### Debug Mode

Enable verbose logging for troubleshooting:

```bash
NODE_ENV=development npx tsx scripts/seed/index.ts --environment development
```

## Contributing

When adding new features:

1. Follow the existing patterns and conventions
2. Add appropriate documentation and examples
3. Test with different configurations
4. Update this README with new capabilities

## License

This seeding system is part of the WizeAssets ERP application.
