import { faker } from '@faker-js/faker';

export class BusinessDataGenerator {
  
  /**
   * Generate realistic asset categories with hierarchical structure
   */
  static generateAssetCategories() {
    return [
      {
        name: 'IT Equipment',
        description: 'Information technology hardware and software assets',
        categories: [
          { name: 'Computers', description: 'Desktop computers, laptops, and workstations' },
          { name: 'Servers', description: 'Server hardware and related equipment' },
          { name: 'Networking', description: 'Routers, switches, and networking equipment' },
          { name: 'Storage', description: 'Data storage devices and systems' },
          { name: 'Software', description: 'Software licenses and applications' },
          { name: 'Mobile Devices', description: 'Smartphones, tablets, and mobile equipment' }
        ]
      },
      {
        name: 'Office Equipment',
        description: 'General office and administrative equipment',
        categories: [
          { name: 'Furniture', description: 'Desks, chairs, and office furniture' },
          { name: 'Printers', description: 'Printers, scanners, and imaging equipment' },
          { name: 'Communication', description: 'Phones, video conferencing equipment' },
          { name: 'Office Supplies', description: 'General office equipment and supplies' }
        ]
      },
      {
        name: 'Vehicles',
        description: 'Company vehicles and transportation assets',
        categories: [
          { name: 'Fleet Vehicles', description: 'Company cars and trucks' },
          { name: 'Specialty Vehicles', description: 'Specialized transportation equipment' }
        ]
      },
      {
        name: 'Manufacturing Equipment',
        description: 'Production and manufacturing machinery',
        categories: [
          { name: 'Production Machinery', description: 'Manufacturing and production equipment' },
          { name: 'Quality Control', description: 'Testing and quality assurance equipment' },
          { name: 'Safety Equipment', description: 'Safety and protective equipment' }
        ]
      },
      {
        name: 'Facilities',
        description: 'Building and facility-related assets',
        categories: [
          { name: 'HVAC Systems', description: 'Heating, ventilation, and air conditioning' },
          { name: 'Security Systems', description: 'Security cameras, access control systems' },
          { name: 'Building Infrastructure', description: 'Elevators, lighting, and building systems' }
        ]
      }
    ];
  }

  /**
   * Generate realistic asset types with detailed specifications
   */
  static generateAssetTypes() {
    return [
      // IT Equipment
      {
        name: 'Desktop Computer',
        code: 'IT-DESK',
        category: 'Computers',
        description: 'Standard desktop computer for office use',
        icon: '💻',
        color: '#3b82f6',
        priceRange: { min: 800, max: 2500 },
        usefulLife: 4,
        maintenanceFrequency: 'quarterly'
      },
      {
        name: 'Laptop Computer',
        code: 'IT-LAPTOP',
        category: 'Computers',
        description: 'Portable laptop computer for mobile work',
        icon: '💻',
        color: '#6366f1',
        priceRange: { min: 1000, max: 3000 },
        usefulLife: 3,
        maintenanceFrequency: 'semi-annually'
      },
      {
        name: 'Server',
        code: 'IT-SERVER',
        category: 'Servers',
        description: 'Enterprise server for data processing',
        icon: '🖥️',
        color: '#8b5cf6',
        priceRange: { min: 5000, max: 25000 },
        usefulLife: 5,
        maintenanceFrequency: 'monthly'
      },
      {
        name: 'Network Switch',
        code: 'IT-SWITCH',
        category: 'Networking',
        description: 'Network switching equipment',
        icon: '🌐',
        color: '#06b6d4',
        priceRange: { min: 500, max: 5000 },
        usefulLife: 7,
        maintenanceFrequency: 'quarterly'
      },
      {
        name: 'Smartphone',
        code: 'IT-PHONE',
        category: 'Mobile Devices',
        description: 'Corporate smartphone device',
        icon: '📱',
        color: '#10b981',
        priceRange: { min: 300, max: 1200 },
        usefulLife: 2,
        maintenanceFrequency: 'annually'
      },
      {
        name: 'Tablet',
        code: 'IT-TABLET',
        category: 'Mobile Devices',
        description: 'Business tablet device',
        icon: '📱',
        color: '#84cc16',
        priceRange: { min: 400, max: 1500 },
        usefulLife: 3,
        maintenanceFrequency: 'annually'
      },

      // Office Equipment
      {
        name: 'Office Desk',
        code: 'OFF-DESK',
        category: 'Furniture',
        description: 'Standard office desk',
        icon: '🪑',
        color: '#f59e0b',
        priceRange: { min: 200, max: 800 },
        usefulLife: 10,
        maintenanceFrequency: 'annually'
      },
      {
        name: 'Office Chair',
        code: 'OFF-CHAIR',
        category: 'Furniture',
        description: 'Ergonomic office chair',
        icon: '🪑',
        color: '#f97316',
        priceRange: { min: 150, max: 600 },
        usefulLife: 7,
        maintenanceFrequency: 'annually'
      },
      {
        name: 'Printer',
        code: 'OFF-PRINT',
        category: 'Printers',
        description: 'Office printer/scanner combo',
        icon: '🖨️',
        color: '#ef4444',
        priceRange: { min: 300, max: 2000 },
        usefulLife: 5,
        maintenanceFrequency: 'quarterly'
      },

      // Vehicles
      {
        name: 'Company Car',
        code: 'VEH-CAR',
        category: 'Fleet Vehicles',
        description: 'Standard company vehicle',
        icon: '🚗',
        color: '#8b5cf6',
        priceRange: { min: 25000, max: 50000 },
        usefulLife: 5,
        maintenanceFrequency: 'monthly'
      },
      {
        name: 'Delivery Truck',
        code: 'VEH-TRUCK',
        category: 'Fleet Vehicles',
        description: 'Commercial delivery truck',
        icon: '🚚',
        color: '#7c3aed',
        priceRange: { min: 40000, max: 80000 },
        usefulLife: 8,
        maintenanceFrequency: 'monthly'
      },

      // Manufacturing Equipment
      {
        name: 'Production Line Equipment',
        code: 'MFG-PROD',
        category: 'Production Machinery',
        description: 'Automated production line machinery',
        icon: '⚙️',
        color: '#6b7280',
        priceRange: { min: 50000, max: 200000 },
        usefulLife: 15,
        maintenanceFrequency: 'weekly'
      },

      // Facilities
      {
        name: 'HVAC Unit',
        code: 'FAC-HVAC',
        category: 'HVAC Systems',
        description: 'Heating, ventilation, and air conditioning unit',
        icon: '🌡️',
        color: '#0891b2',
        priceRange: { min: 3000, max: 15000 },
        usefulLife: 12,
        maintenanceFrequency: 'quarterly'
      },
      {
        name: 'Security Camera',
        code: 'FAC-CAM',
        category: 'Security Systems',
        description: 'Digital security surveillance camera',
        icon: '📹',
        color: '#dc2626',
        priceRange: { min: 200, max: 1000 },
        usefulLife: 6,
        maintenanceFrequency: 'annually'
      }
    ];
  }

  /**
   * Generate realistic workflow templates
   */
  static generateWorkflowTemplates() {
    return [
      {
        name: 'Asset Purchase Approval',
        type: 'approval-chain',
        description: 'Multi-level approval process for asset purchases',
        nodes: [
          { id: 'start', type: 'trigger', label: 'Purchase Request' },
          { id: 'manager', type: 'approval', label: 'Manager Approval', threshold: 5000 },
          { id: 'finance', type: 'approval', label: 'Finance Approval', threshold: 10000 },
          { id: 'procurement', type: 'action', label: 'Create Purchase Order' },
          { id: 'end', type: 'complete', label: 'Process Complete' }
        ]
      },
      {
        name: 'Maintenance Scheduling',
        type: 'maintenance-workflow',
        description: 'Automated maintenance task scheduling and tracking',
        nodes: [
          { id: 'trigger', type: 'schedule', label: 'Maintenance Due' },
          { id: 'assign', type: 'action', label: 'Assign Technician' },
          { id: 'notify', type: 'notification', label: 'Send Notification' },
          { id: 'complete', type: 'action', label: 'Mark Complete' }
        ]
      },
      {
        name: 'Asset Disposal Process',
        type: 'asset-automation',
        description: 'End-of-life asset disposal and documentation',
        nodes: [
          { id: 'evaluate', type: 'decision', label: 'Evaluate Asset Condition' },
          { id: 'approve', type: 'approval', label: 'Disposal Approval' },
          { id: 'sanitize', type: 'action', label: 'Data Sanitization' },
          { id: 'dispose', type: 'action', label: 'Physical Disposal' },
          { id: 'document', type: 'action', label: 'Update Records' }
        ]
      },
      {
        name: 'Inventory Audit',
        type: 'inventory-management',
        description: 'Periodic inventory verification and reconciliation',
        nodes: [
          { id: 'schedule', type: 'trigger', label: 'Audit Schedule' },
          { id: 'scan', type: 'action', label: 'Scan Assets' },
          { id: 'reconcile', type: 'decision', label: 'Reconcile Differences' },
          { id: 'report', type: 'action', label: 'Generate Report' }
        ]
      }
    ];
  }

  /**
   * Generate realistic asset names and specifications
   */
  static generateAssetName(assetType: string): string {
    const namePatterns = {
      'Desktop Computer': [
        'Dell OptiPlex', 'HP EliteDesk', 'Lenovo ThinkCentre', 'ASUS ExpertCenter'
      ],
      'Laptop Computer': [
        'Dell Latitude', 'HP EliteBook', 'Lenovo ThinkPad', 'ASUS ExpertBook'
      ],
      'Server': [
        'Dell PowerEdge', 'HP ProLiant', 'Lenovo ThinkSystem', 'IBM Power'
      ],
      'Network Switch': [
        'Cisco Catalyst', 'HP Aruba', 'Juniper EX', 'Netgear ProSafe'
      ],
      'Smartphone': [
        'iPhone', 'Samsung Galaxy', 'Google Pixel', 'OnePlus'
      ],
      'Office Desk': [
        'Executive Desk', 'Standing Desk', 'Corner Desk', 'Computer Desk'
      ],
      'Office Chair': [
        'Ergonomic Chair', 'Executive Chair', 'Task Chair', 'Conference Chair'
      ],
      'Company Car': [
        'Toyota Camry', 'Honda Accord', 'Ford Fusion', 'Chevrolet Malibu'
      ]
    };

    const patterns = namePatterns[assetType as keyof typeof namePatterns] || ['Standard'];
    const pattern = faker.helpers.arrayElement(patterns);
    const model = faker.string.alphanumeric({ length: { min: 3, max: 5 }, casing: 'upper' });
    const year = faker.date.recent({ days: 365 * 3 }).getFullYear();
    
    return `${pattern} ${model} (${year})`;
  }

  /**
   * Generate realistic serial numbers
   */
  static generateSerialNumber(assetType: string): string {
    const prefixes = {
      'IT': 'IT',
      'Office': 'OF',
      'Vehicle': 'VH',
      'Manufacturing': 'MF',
      'Facility': 'FC'
    };
    
    const category = Object.keys(prefixes).find(key => 
      assetType.toLowerCase().includes(key.toLowerCase())
    ) || 'GN';
    
    const prefix = prefixes[category as keyof typeof prefixes] || 'GN';
    const year = new Date().getFullYear().toString().slice(-2);
    const sequential = faker.string.alphanumeric({ length: 6, casing: 'upper' });
    
    return `${prefix}${year}${sequential}`;
  }

  /**
   * Generate realistic maintenance tasks
   */
  static generateMaintenanceTasks(assetType: string) {
    const taskTemplates = {
      'Desktop Computer': [
        'Clean internal components and fans',
        'Update operating system and software',
        'Check hardware components for failures',
        'Backup critical data',
        'Run antivirus scan'
      ],
      'Server': [
        'Check server logs for errors',
        'Monitor temperature and cooling',
        'Update firmware and security patches',
        'Verify backup systems',
        'Test disaster recovery procedures'
      ],
      'Vehicle': [
        'Oil change and fluid check',
        'Tire rotation and pressure check',
        'Brake inspection',
        'Engine diagnostic scan',
        'Interior and exterior cleaning'
      ],
      'Printer': [
        'Clean print heads and rollers',
        'Replace toner cartridges',
        'Calibrate print quality',
        'Check paper path for jams',
        'Update printer drivers'
      ]
    };

    const category = Object.keys(taskTemplates).find(key => 
      assetType.includes(key)
    ) || 'General';
    
    const tasks = taskTemplates[category as keyof typeof taskTemplates] || [
      'Visual inspection',
      'Performance check',
      'Clean equipment',
      'Check connections',
      'Update documentation'
    ];

    return faker.helpers.arrayElements(tasks, { min: 2, max: 4 });
  }

  /**
   * Generate realistic vendor information
   */
  static generateVendorInfo() {
    const vendors = [
      { name: 'Dell Technologies', specialties: ['Computers', 'Servers'], reliability: 0.95 },
      { name: 'HP Inc.', specialties: ['Computers', 'Printers'], reliability: 0.93 },
      { name: 'Lenovo Group', specialties: ['Computers', 'Mobile'], reliability: 0.91 },
      { name: 'Cisco Systems', specialties: ['Networking'], reliability: 0.96 },
      { name: 'Microsoft Corporation', specialties: ['Software'], reliability: 0.94 },
      { name: 'Herman Miller', specialties: ['Furniture'], reliability: 0.88 },
      { name: 'Toyota Motor Corp', specialties: ['Vehicles'], reliability: 0.97 },
      { name: 'Carrier Global', specialties: ['HVAC'], reliability: 0.89 }
    ];

    return faker.helpers.arrayElement(vendors);
  }

  /**
   * Generate realistic financial data with trends
   */
  static generateFinancialData(basePrice: number, ageInMonths: number) {
    // Calculate depreciation
    const monthlyDepreciation = basePrice * 0.02; // 2% per month
    const currentValue = Math.max(basePrice * 0.1, basePrice - (monthlyDepreciation * ageInMonths));
    
    // Calculate maintenance costs (increases with age)
    const baseMaintenance = basePrice * 0.05; // 5% of purchase price annually
    const ageMultiplier = 1 + (ageInMonths / 12) * 0.1; // 10% increase per year
    const annualMaintenanceCost = baseMaintenance * ageMultiplier;
    
    return {
      purchasePrice: basePrice,
      currentValue: Math.round(currentValue * 100) / 100,
      annualMaintenanceCost: Math.round(annualMaintenanceCost * 100) / 100,
      depreciationRate: ((basePrice - currentValue) / basePrice) * 100,
      roi: faker.number.float({ min: -10, max: 25, fractionDigits: 2 })
    };
  }
}
