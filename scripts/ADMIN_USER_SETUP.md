# Admin User Setup Guide

This guide explains how to add the admin user **Thando Zondo** to the WizeAssets ERP system.

## User Details

- **Name**: Thando Zondo
- **Email**: <EMAIL>
- **Password**: T3chn0l0gy@1
- **Role**: admin
- **Department**: IT
- **Company**: SoImagine
- **Job Title**: Chief Technology Officer
- **Phone**: +27 11 123 4567
- **Address**: 123 Business Park, Johannesburg, South Africa

## Method 1: Using Node.js Script (Recommended)

If you have access to the database and Prisma is working:

```bash
# Create admin user
npm run db:create-admin

# Or run directly
npx tsx scripts/create-admin-user.ts
```

## Method 2: Generate SQL Script Only

If you need to run the SQL manually:

```bash
# Generate SQL script
npm run db:admin-sql

# Or run directly
npx tsx scripts/create-admin-user.ts --sql-only
```

## Method 3: Direct SQL Execution

If you prefer to run SQL directly against the database:

```bash
# Connect to your PostgreSQL database and run:
psql -h your-host -p your-port -U your-username -d your-database -f scripts/add-admin-user.sql
```

## Method 4: Using Database Seeder

The user has been added to the seeder, so when you run the seeder, it will be included:

```bash
# Run the full seeder (includes the admin user)
npm run db:seed:dev
```

## Method 5: Manual SQL

Copy and paste this SQL into your database client:

```sql
-- Check if user exists
SELECT id, name, email, role FROM "User" WHERE email = '<EMAIL>';

-- Insert admin user
INSERT INTO "User" (
    id, name, email, password, department, role, status,
    phone, address, company, "jobTitle", "joinDate", "lastActive",
    "emailVerified", "createdAt", "updatedAt"
) VALUES (
    'clxh70j9z6uf9794nxtukjcsn',
    'Thando Zondo',
    '<EMAIL>',
    '$2b$10$GXbs4iJGCve51itbaq1QFeM1y939aOrhQo8As9JHrcAvVMQ1DMR8G',
    'IT',
    'admin',
    'active',
    '+27 11 123 4567',
    '123 Business Park, Johannesburg, South Africa',
    'SoImagine',
    'Chief Technology Officer',
    NOW(), NOW(), NOW(), NOW(), NOW()
) ON CONFLICT (email) DO NOTHING;

-- Verify user was created
SELECT id, name, email, role, department, status FROM "User" WHERE email = '<EMAIL>';
```

## Files Created

1. **scripts/create-admin-user.ts** - Main Node.js script with database connection
2. **scripts/add-admin-user.sql** - Ready-to-use SQL script
3. **scripts/admin-user-data.json** - User data configuration
4. **scripts/generate-admin-user-sql.ts** - SQL generator script
5. **scripts/add-admin-user.ts** - Simple database insertion script

## User Seeder Update

The user seeder (`scripts/seed/seeders/user-seeder.ts`) has been updated to include Thando Zondo as the first admin user.

## Verification

After creating the user, you can verify it was created successfully:

1. **Via SQL**:
   ```sql
   SELECT id, name, email, role, department, status, company, "jobTitle" 
   FROM "User" 
   WHERE email = '<EMAIL>';
   ```

2. **Via the application**:
   - Navigate to the login page
   - Use email: `<EMAIL>`
   - Use password: `T3chn0l0gy@1`
   - Should successfully log in as admin

## Troubleshooting

### Database Connection Issues

If you get connection errors, try:
1. Check your `DATABASE_URL` in `.env`
2. Ensure the database server is running
3. Use the SQL script method instead

### User Already Exists

If the user already exists, the scripts will:
- Skip creation (SQL uses `ON CONFLICT DO NOTHING`)
- Show existing user details
- Suggest next steps

### Permission Issues

Ensure the database user has permissions to:
- INSERT into the User table
- SELECT from the User table
- Use NOW() function

## Security Notes

- The password is properly hashed using bcrypt with 10 salt rounds
- The user is marked as email verified
- All timestamps are set to current time
- The user is created with active status

## Next Steps

After creating the admin user:
1. Test login functionality
2. Set up additional admin users if needed
3. Configure user roles and permissions
4. Set up email verification if required
5. Review and update user profile as needed