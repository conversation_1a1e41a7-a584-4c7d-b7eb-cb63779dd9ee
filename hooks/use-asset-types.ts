"use client"

import { useState, useEffect, useCallback } from "react"
import type {
  AssetType,
  AssetCategory,
  AssetTypeTemplate,
  AssetTypeMetrics,
  CustomField,
  LifecycleStage,
  MaintenanceSchedule,
  DepreciationSettings,
} from "@/lib/modules/asset-types/types"

interface UseAssetTypesFilters {
  category?: string
  isActive?: boolean
  search?: string
}

interface UseAssetTypesReturn {
  assetTypes: AssetType[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  createAssetType: (assetType: Omit<AssetType, "id" | "createdAt" | "updatedAt" | "version">) => Promise<AssetType>
  updateAssetType: (id: string, updates: Partial<AssetType>) => Promise<AssetType | null>
  deleteAssetType: (id: string) => Promise<boolean>
}

export function useAssetTypes(filters?: UseAssetTypesFilters): UseAssetTypesReturn {
  const [assetTypes, setAssetTypes] = useState<AssetType[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchAssetTypes = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams()
      if (filters?.category) params.append("category", filters.category)
      if (filters?.isActive !== undefined) params.append("isActive", filters.isActive.toString())
      if (filters?.search) params.append("search", filters.search)

      const response = await fetch(`/api/asset-types?${params.toString()}`)
      if (!response.ok) {
        throw new Error("Failed to fetch asset types")
      }

      const data = await response.json()
      setAssetTypes(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
    } finally {
      setLoading(false)
    }
  }, [filters])

  useEffect(() => {
    fetchAssetTypes()
  }, [fetchAssetTypes])

  const createAssetType = async (
    assetType: Omit<AssetType, "id" | "createdAt" | "updatedAt" | "version">
  ): Promise<AssetType> => {
    const response = await fetch("/api/asset-types", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(assetType),
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || "Failed to create asset type")
    }

    const newAssetType = await response.json()
    setAssetTypes((prev) => [...prev, newAssetType])
    return newAssetType
  }

  const updateAssetType = async (id: string, updates: Partial<AssetType>): Promise<AssetType | null> => {
    const response = await fetch(`/api/asset-types/${id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(updates),
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || "Failed to update asset type")
    }

    const updatedAssetType = await response.json()
    setAssetTypes((prev) => prev.map((at) => (at.id === id ? updatedAssetType : at)))
    return updatedAssetType
  }

  const deleteAssetType = async (id: string): Promise<boolean> => {
    const response = await fetch(`/api/asset-types/${id}`, {
      method: "DELETE",
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || "Failed to delete asset type")
    }

    setAssetTypes((prev) => prev.filter((at) => at.id !== id))
    return true
  }

  return {
    assetTypes,
    loading,
    error,
    refetch: fetchAssetTypes,
    createAssetType,
    updateAssetType,
    deleteAssetType,
  }
}

interface UseAssetTypeReturn {
  assetType: AssetType | null
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
}

export function useAssetType(id: string): UseAssetTypeReturn {
  const [assetType, setAssetType] = useState<AssetType | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchAssetType = useCallback(async () => {
    if (!id) return

    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/asset-types/${id}`)
      if (!response.ok) {
        throw new Error("Failed to fetch asset type")
      }

      const data = await response.json()
      setAssetType(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
    } finally {
      setLoading(false)
    }
  }, [id])

  useEffect(() => {
    fetchAssetType()
  }, [fetchAssetType])

  return {
    assetType,
    loading,
    error,
    refetch: fetchAssetType,
  }
}

interface UseAssetCategoriesReturn {
  categories: AssetCategory[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  createCategory: (category: Omit<AssetCategory, "id">) => Promise<AssetCategory>
}

export function useAssetCategories(parentId?: string): UseAssetCategoriesReturn {
  const [categories, setCategories] = useState<AssetCategory[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchCategories = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams()
      if (parentId) params.append("parentId", parentId)

      const response = await fetch(`/api/asset-types/categories?${params.toString()}`)
      if (!response.ok) {
        throw new Error("Failed to fetch categories")
      }

      const data = await response.json()
      setCategories(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
    } finally {
      setLoading(false)
    }
  }, [parentId])

  useEffect(() => {
    fetchCategories()
  }, [fetchCategories])

  const createCategory = async (category: Omit<AssetCategory, "id">): Promise<AssetCategory> => {
    const response = await fetch("/api/asset-types/categories", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(category),
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || "Failed to create category")
    }

    const newCategory = await response.json()
    setCategories((prev) => [...prev, newCategory])
    return newCategory
  }

  return {
    categories,
    loading,
    error,
    refetch: fetchCategories,
    createCategory,
  }
}

interface UseAssetTypeTemplatesReturn {
  templates: AssetTypeTemplate[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  createTemplate: (template: Omit<AssetTypeTemplate, "id" | "createdAt" | "usageCount" | "rating">) => Promise<AssetTypeTemplate>
}

export function useAssetTypeTemplates(category?: string): UseAssetTypeTemplatesReturn {
  const [templates, setTemplates] = useState<AssetTypeTemplate[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchTemplates = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams()
      if (category) params.append("category", category)

      const response = await fetch(`/api/asset-types/templates?${params.toString()}`)
      if (!response.ok) {
        throw new Error("Failed to fetch templates")
      }

      const data = await response.json()
      setTemplates(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
    } finally {
      setLoading(false)
    }
  }, [category])

  useEffect(() => {
    fetchTemplates()
  }, [fetchTemplates])

  const createTemplate = async (
    template: Omit<AssetTypeTemplate, "id" | "createdAt" | "usageCount" | "rating">
  ): Promise<AssetTypeTemplate> => {
    const response = await fetch("/api/asset-types/templates", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(template),
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || "Failed to create template")
    }

    const newTemplate = await response.json()
    setTemplates((prev) => [...prev, newTemplate])
    return newTemplate
  }

  return {
    templates,
    loading,
    error,
    refetch: fetchTemplates,
    createTemplate,
  }
}

interface UseAssetTypeMetricsReturn {
  metrics: AssetTypeMetrics | null
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
}

export function useAssetTypeMetrics(): UseAssetTypeMetricsReturn {
  const [metrics, setMetrics] = useState<AssetTypeMetrics | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchMetrics = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch("/api/asset-types/metrics")
      if (!response.ok) {
        throw new Error("Failed to fetch metrics")
      }

      const data = await response.json()
      setMetrics(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchMetrics()
  }, [fetchMetrics])

  return {
    metrics,
    loading,
    error,
    refetch: fetchMetrics,
  }
}

interface UseCustomFieldsReturn {
  addCustomField: (assetTypeId: string, field: Omit<CustomField, "id">) => Promise<CustomField | null>
  updateCustomField: (assetTypeId: string, fieldId: string, updates: Partial<CustomField>) => Promise<CustomField | null>
  removeCustomField: (assetTypeId: string, fieldId: string) => Promise<boolean>
}

export function useCustomFields(): UseCustomFieldsReturn {
  const addCustomField = async (assetTypeId: string, field: Omit<CustomField, "id">): Promise<CustomField | null> => {
    const response = await fetch(`/api/asset-types/${assetTypeId}/custom-fields`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(field),
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || "Failed to add custom field")
    }

    return response.json()
  }

  const updateCustomField = async (
    assetTypeId: string,
    fieldId: string,
    updates: Partial<CustomField>
  ): Promise<CustomField | null> => {
    const response = await fetch(`/api/asset-types/${assetTypeId}/custom-fields/${fieldId}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(updates),
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || "Failed to update custom field")
    }

    return response.json()
  }

  const removeCustomField = async (assetTypeId: string, fieldId: string): Promise<boolean> => {
    const response = await fetch(`/api/asset-types/${assetTypeId}/custom-fields/${fieldId}`, {
      method: "DELETE",
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || "Failed to remove custom field")
    }

    return true
  }

  return {
    addCustomField,
    updateCustomField,
    removeCustomField,
  }
}