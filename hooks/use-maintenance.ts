import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';

export interface MaintenanceTask {
  id: string;
  assetId: string;
  scheduleId?: string;
  title: string;
  description?: string;
  type: string;
  priority: "low" | "medium" | "high" | "critical";
  status: "scheduled" | "in_progress" | "completed" | "cancelled" | "overdue";
  scheduledDate: string;
  dueDate: string;
  completedDate?: string;
  assignedTo?: string;
  estimatedDuration?: number;
  actualDuration?: number;
  estimatedCost?: number;
  actualCost?: number;
  instructions?: string;
  checklistItems?: Record<string, any>;
  completionNotes?: string;
  createdAt: string;
  updatedAt: string;
  asset?: {
    id: string;
    name: string;
    category: string;
    location: string;
  };
}

export interface MaintenanceTaskFilter {
  status?: string;
  priority?: string;
  type?: string;
  assetId?: string;
  assignedTo?: string;
  dateFrom?: string;
  dateTo?: string;
  upcoming?: boolean;
  overdue?: boolean;
}

export interface MaintenanceStatistics {
  totalTasks: number;
  scheduledTasks: number;
  inProgressTasks: number;
  completedTasks: number;
  overdueTasks: number;
  upcomingTasks: number;
  averageCompletionTime: number;
  totalCost: number;
  averageCost: number;
  tasksByPriority: Array<{
    priority: string;
    count: number;
  }>;
  tasksByType: Array<{
    type: string;
    count: number;
  }>;
}

export function useMaintenance() {
  const { data: session } = useSession();
  const [tasks, setTasks] = useState<MaintenanceTask[]>([]);
  const [statistics, setStatistics] = useState<MaintenanceStatistics | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchTasks = useCallback(async (filter?: MaintenanceTaskFilter) => {
    if (!session?.user) return;

    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: '1',
        limit: '50',
        sortBy: 'scheduledDate',
        sortOrder: 'asc',
        ...filter,
      });

      const response = await fetch(`/api/maintenance/tasks?${params}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch maintenance tasks');
      }
      
      setTasks(result.data || []);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch maintenance tasks';
      setError(errorMessage);
      console.error('Error fetching maintenance tasks:', err);
    } finally {
      setLoading(false);
    }
  }, [session]);

  const fetchStatistics = useCallback(async () => {
    if (!session?.user) return;

    try {
      const response = await fetch('/api/maintenance/statistics');
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch maintenance statistics');
      }
      
      setStatistics(result.data);
    } catch (err) {
      console.error('Error fetching maintenance statistics:', err);
    }
  }, [session]);

  const createTask = useCallback(async (taskData: Partial<MaintenanceTask>) => {
    if (!session?.user) {
      throw new Error('User not authenticated');
    }

    // Only admin and manager users can create maintenance tasks
    if (!["admin", "manager"].includes(session.user.role)) {
      throw new Error('Insufficient permissions to create maintenance tasks');
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/maintenance/tasks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(taskData),
      });
      
      if (!response.ok) {
        const errorResult = await response.json();
        throw new Error(errorResult.error || `HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to create maintenance task');
      }
      
      // Refresh the tasks list
      await fetchTasks();
      await fetchStatistics();
      
      return result.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create maintenance task';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [session, fetchTasks, fetchStatistics]);

  const updateTask = useCallback(async (id: string, updateData: Partial<MaintenanceTask>) => {
    if (!session?.user) {
      throw new Error('User not authenticated');
    }

    // Only admin and manager users can update maintenance tasks
    if (!["admin", "manager"].includes(session.user.role)) {
      throw new Error('Insufficient permissions to update maintenance tasks');
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/maintenance/tasks', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id, ...updateData }),
      });
      
      if (!response.ok) {
        const errorResult = await response.json();
        throw new Error(errorResult.error || `HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to update maintenance task');
      }
      
      // Refresh the tasks list
      await fetchTasks();
      await fetchStatistics();
      
      return result.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update maintenance task';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [session, fetchTasks, fetchStatistics]);

  const completeTask = useCallback(async (id: string, completionData: {
    completionNotes?: string;
    actualDuration?: number;
    actualCost?: number;
  }) => {
    return updateTask(id, {
      status: "completed",
      completedDate: new Date().toISOString(),
      ...completionData,
    });
  }, [updateTask]);

  const getUpcomingTasks = useCallback(async (days: number = 30) => {
    return fetchTasks({ upcoming: true });
  }, [fetchTasks]);

  const getOverdueTasks = useCallback(async () => {
    return fetchTasks({ overdue: true });
  }, [fetchTasks]);

  // Load initial data
  useEffect(() => {
    if (session?.user) {
      fetchTasks();
      fetchStatistics();
    }
  }, [session, fetchTasks, fetchStatistics]);

  return {
    tasks,
    statistics,
    loading,
    error,
    fetchTasks,
    fetchStatistics,
    createTask,
    updateTask,
    completeTask,
    getUpcomingTasks,
    getOverdueTasks,
  };
}
