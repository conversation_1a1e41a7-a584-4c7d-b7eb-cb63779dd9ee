import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';

export interface FinancialMetrics {
  totalAssetValue: number;
  totalDepreciation: number;
  currentValue: number;
  monthlyDepreciation: number;
  averageAge: number;
  assetsByCategory: Array<{
    category: string;
    value: number;
    count: number;
    percentage: number;
  }>;
  depreciationByCategory: Array<{
    category: string;
    originalValue: number;
    currentValue: number;
    depreciationAmount: number;
    depreciationPercentage: number;
  }>;
  valueByLocation: Array<{
    location: string;
    value: number;
    count: number;
  }>;
  ageDistribution: Array<{
    ageRange: string;
    count: number;
    value: number;
  }>;
}

export interface AssetValuation {
  id: string;
  name: string;
  category: string;
  originalValue: number;
  currentValue: number;
  depreciationAmount: number;
  depreciationPercentage: number;
  ageInMonths: number;
  estimatedRemainingLife: number;
}

export interface TCOAnalysis {
  assetId: string;
  assetName: string;
  acquisitionCost: number;
  operatingCosts: number;
  maintenanceCosts: number;
  disposalCosts: number;
  totalCostOfOwnership: number;
  costPerYear: number;
  costPerMonth: number;
}

export interface ROIAnalysis {
  assetId: string;
  assetName: string;
  initialInvestment: number;
  annualBenefits: number;
  cumulativeBenefits: number;
  netPresentValue: number;
  returnOnInvestment: number;
  paybackPeriod: number;
}

export function useFinancialData() {
  const { data: session } = useSession();
  const [metrics, setMetrics] = useState<FinancialMetrics | null>(null);
  const [valuations, setValuations] = useState<AssetValuation[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchMetrics = useCallback(async () => {
    if (!session?.user) return;

    // Only admin and manager users can access financial data
    if (!["admin", "manager"].includes(session.user.role)) {
      setError("Insufficient permissions to access financial data");
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/financial/metrics');
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch financial metrics');
      }
      
      setMetrics(result.data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch financial metrics';
      setError(errorMessage);
      console.error('Error fetching financial metrics:', err);
    } finally {
      setLoading(false);
    }
  }, [session]);

  const fetchValuations = useCallback(async () => {
    if (!session?.user) return;

    // Only admin and manager users can access financial data
    if (!["admin", "manager"].includes(session.user.role)) {
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/financial/valuations');
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch asset valuations');
      }
      
      setValuations(result.data || []);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch asset valuations';
      setError(errorMessage);
      console.error('Error fetching asset valuations:', err);
    } finally {
      setLoading(false);
    }
  }, [session]);

  const calculateTCO = useCallback(async (assetId: string): Promise<TCOAnalysis | null> => {
    if (!session?.user) {
      throw new Error('User not authenticated');
    }

    // Only admin and manager users can access financial data
    if (!["admin", "manager"].includes(session.user.role)) {
      throw new Error('Insufficient permissions to access financial data');
    }

    try {
      const response = await fetch(`/api/financial/tco/${assetId}`);
      
      if (!response.ok) {
        const errorResult = await response.json();
        throw new Error(errorResult.error || `HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to calculate TCO');
      }
      
      return result.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to calculate TCO';
      setError(errorMessage);
      throw err;
    }
  }, [session]);

  const calculateROI = useCallback(async (assetId: string, annualBenefits: number): Promise<ROIAnalysis | null> => {
    if (!session?.user) {
      throw new Error('User not authenticated');
    }

    // Only admin and manager users can access financial data
    if (!["admin", "manager"].includes(session.user.role)) {
      throw new Error('Insufficient permissions to access financial data');
    }

    try {
      const response = await fetch(`/api/financial/roi/${assetId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ annualBenefits }),
      });
      
      if (!response.ok) {
        const errorResult = await response.json();
        throw new Error(errorResult.error || `HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to calculate ROI');
      }
      
      return result.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to calculate ROI';
      setError(errorMessage);
      throw err;
    }
  }, [session]);

  const refreshData = useCallback(async () => {
    await Promise.all([
      fetchMetrics(),
      fetchValuations(),
    ]);
  }, [fetchMetrics, fetchValuations]);

  // Load initial data
  useEffect(() => {
    if (session?.user && ["admin", "manager"].includes(session.user.role)) {
      refreshData();
    }
  }, [session, refreshData]);

  return {
    metrics,
    valuations,
    loading,
    error,
    fetchMetrics,
    fetchValuations,
    calculateTCO,
    calculateROI,
    refreshData,
  };
}
