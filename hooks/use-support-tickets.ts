import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';

export interface SupportTicket {
  id: string;
  ticketNumber: string;
  userId: string;
  subject: string;
  description: string;
  category: string;
  priority: "low" | "normal" | "high" | "critical";
  status: "open" | "in_progress" | "waiting_response" | "resolved" | "closed";
  assignedTo?: string;
  resolution?: string;
  createdAt: string;
  updatedAt: string;
  user?: {
    id: string;
    name: string;
    email: string;
    department?: string;
  };
  messages?: SupportMessage[];
}

export interface SupportMessage {
  id: string;
  ticketId: string;
  content: string;
  sender: string;
  senderType: "client" | "support" | "system";
  attachments: string[];
  createdAt: string;
}

export interface SupportTicketFilter {
  status?: string;
  priority?: string;
  category?: string;
  assignedTo?: string;
  userId?: string;
  dateFrom?: string;
  dateTo?: string;
  search?: string;
}

export interface SupportTicketStatistics {
  totalTickets: number;
  openTickets: number;
  inProgressTickets: number;
  resolvedTickets: number;
  closedTickets: number;
}

export function useSupportTickets() {
  const { data: session } = useSession();
  const [tickets, setTickets] = useState<SupportTicket[]>([]);
  const [statistics, setStatistics] = useState<SupportTicketStatistics | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchTickets = useCallback(async (filter?: SupportTicketFilter) => {
    if (!session?.user) return;

    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: '1',
        limit: '50',
        sortBy: 'createdAt',
        sortOrder: 'desc',
        ...filter,
      });

      const response = await fetch(`/api/support-tickets?${params}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch support tickets');
      }
      
      setTickets(result.data || []);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch support tickets';
      setError(errorMessage);
      console.error('Error fetching support tickets:', err);
    } finally {
      setLoading(false);
    }
  }, [session]);

  const fetchTicketById = useCallback(async (id: string): Promise<SupportTicket | null> => {
    if (!session?.user) return null;

    try {
      const response = await fetch(`/api/support-tickets/${id}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch support ticket');
      }
      
      return result.data;
    } catch (err) {
      console.error('Error fetching support ticket:', err);
      return null;
    }
  }, [session]);

  const createTicket = useCallback(async (ticketData: Partial<SupportTicket>) => {
    if (!session?.user) {
      throw new Error('User not authenticated');
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/support-tickets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(ticketData),
      });
      
      if (!response.ok) {
        const errorResult = await response.json();
        throw new Error(errorResult.error || `HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to create support ticket');
      }
      
      // Refresh the tickets list
      await fetchTickets();
      
      return result.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create support ticket';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [session, fetchTickets]);

  const updateTicket = useCallback(async (id: string, updateData: Partial<SupportTicket>) => {
    if (!session?.user) {
      throw new Error('User not authenticated');
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/support-tickets/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });
      
      if (!response.ok) {
        const errorResult = await response.json();
        throw new Error(errorResult.error || `HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to update support ticket');
      }
      
      // Refresh the tickets list
      await fetchTickets();
      
      return result.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update support ticket';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [session, fetchTickets]);

  const addMessage = useCallback(async (ticketId: string, content: string, attachments: string[] = []) => {
    if (!session?.user) {
      throw new Error('User not authenticated');
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/support-tickets/${ticketId}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ content, attachments }),
      });
      
      if (!response.ok) {
        const errorResult = await response.json();
        throw new Error(errorResult.error || `HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to add message');
      }
      
      // Refresh the tickets list to get updated messages
      await fetchTickets();
      
      return result.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to add message';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [session, fetchTickets]);

  // Load initial data
  useEffect(() => {
    if (session?.user) {
      fetchTickets();
    }
  }, [session, fetchTickets]);

  return {
    tickets,
    statistics,
    loading,
    error,
    fetchTickets,
    fetchTicketById,
    createTicket,
    updateTicket,
    addMessage,
  };
}
