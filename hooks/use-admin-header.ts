import { useEffect, useCallback } from 'react';
import { useAppHeaderStore } from '@/store/app-header-store';
import type { HeaderConfig } from '@/lib/utils/admin-header-configs';

/**
 * Custom hook for managing admin page headers
 * Provides a clean API for setting up and managing page headers
 */
export function useAdminHeader(config: HeaderConfig | (() => HeaderConfig)) {
  // Set header content on mount
  useEffect(() => {
    const { setHeaderContent, resetHeaderContent } = useAppHeaderStore.getState();
    const headerConfig = typeof config === 'function' ? config() : config;
    setHeaderContent(headerConfig);

    // Cleanup on unmount
    return () => {
      resetHeaderContent();
    };
  }, [config]);

  // Utility functions for dynamic header updates
  const updateHeaderActions = useCallback((actions: React.ReactNode[]) => {
    const { updateActions } = useAppHeaderStore.getState();
    updateActions(actions);
  }, []);

  const setHeaderLoading = useCallback((loading: boolean) => {
    const { setLoading } = useAppHeaderStore.getState();
    setLoading(loading);
  }, []);

  const updateHeaderConfig = useCallback((newConfig: Partial<HeaderConfig>) => {
    const { setHeaderContent } = useAppHeaderStore.getState();
    const currentConfig = typeof config === 'function' ? config() : config;
    setHeaderContent({ ...currentConfig, ...newConfig });
  }, [config]);

  return {
    updateHeaderActions,
    setHeaderLoading,
    updateHeaderConfig,
  };
}

/**
 * Hook for pages that need dynamic header updates based on state
 */
export function useDynamicAdminHeader() {
  const setHeader = useCallback((config: HeaderConfig) => {
    const { setHeaderContent } = useAppHeaderStore.getState();
    setHeaderContent(config);
  }, []);

  const resetHeader = useCallback(() => {
    const { resetHeaderContent } = useAppHeaderStore.getState();
    resetHeaderContent();
  }, []);

  const updateHeaderActions = useCallback((actions: React.ReactNode[]) => {
    const { updateActions } = useAppHeaderStore.getState();
    updateActions(actions);
  }, []);

  const setHeaderLoading = useCallback((loading: boolean) => {
    const { setLoading } = useAppHeaderStore.getState();
    setLoading(loading);
  }, []);

  const changeVariant = useCallback((variant: 'default' | 'dashboard' | 'management' | 'analytics' | 'settings') => {
    const { setVariant } = useAppHeaderStore.getState();
    setVariant(variant);
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      const { resetHeaderContent } = useAppHeaderStore.getState();
      resetHeaderContent();
    };
  }, []);

  return {
    setHeader,
    resetHeader,
    updateHeaderActions,
    setHeaderLoading,
    changeVariant,
  };
}