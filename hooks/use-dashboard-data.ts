import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';

export interface DashboardMetrics {
  totalAssets: number;
  activeAssets: number;
  maintenanceAssets: number;
  disposedAssets: number;
  pendingMaintenanceTasks: number;
  overdueTasks: number;
  completedTasksThisMonth: number;
  totalAssetValue: number;
  monthlyDepreciation: number;
  costSavings: number;
  systemHealth: number;
  pendingRequests: number;
  openTickets: number;
  recentOperations: number;
}

export interface RecentActivity {
  id: string;
  type: 'maintenance' | 'alert' | 'completion' | 'assignment' | 'request' | 'ticket';
  message: string;
  time: string;
  status: 'pending' | 'critical' | 'completed' | 'info' | 'warning';
  entityId?: string;
  entityType?: string;
}

export interface DashboardData {
  metrics: DashboardMetrics;
  recentActivities: RecentActivity[];
  trends: {
    assetsGrowth: number;
    maintenanceReduction: number;
    costSavingsIncrease: number;
    systemHealthImprovement: number;
  };
}

export function useDashboardData() {
  const { data: session } = useSession();
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchDashboardData = useCallback(async () => {
    if (!session?.user) return;

    try {
      setLoading(true);
      setError(null);

      // Fetch data from multiple APIs in parallel
      const [
        assetStatsResponse,
        maintenanceStatsResponse,
        financialMetricsResponse,
        assetRequestStatsResponse,
        supportTicketStatsResponse,
      ] = await Promise.allSettled([
        fetch('/api/assets/statistics?includeValueTrends=true'),
        fetch('/api/maintenance/statistics'),
        fetch('/api/financial/metrics'),
        fetch('/api/asset-requests/statistics'),
        fetch('/api/support-tickets/statistics'),
      ]);

      // Process asset statistics
      let assetStats = {
        totalAssets: 0,
        activeAssets: 0,
        maintenanceAssets: 0,
        disposedAssets: 0,
        recentOperations: 0,
      };

      if (assetStatsResponse.status === 'fulfilled' && assetStatsResponse.value.ok) {
        const result = await assetStatsResponse.value.json();
        if (result.success) {
          assetStats = result.data;
        }
      }

      // Process maintenance statistics
      let maintenanceStats = {
        totalTasks: 0,
        scheduledTasks: 0,
        inProgressTasks: 0,
        completedTasks: 0,
        overdueTasks: 0,
        upcomingTasks: 0,
        averageCompletionTime: 0,
        totalCost: 0,
        averageCost: 0,
      };

      if (maintenanceStatsResponse.status === 'fulfilled' && maintenanceStatsResponse.value.ok) {
        const result = await maintenanceStatsResponse.value.json();
        if (result.success) {
          maintenanceStats = result.data;
        }
      }

      // Process financial metrics
      let financialMetrics = {
        totalAssetValue: 0,
        totalDepreciation: 0,
        currentValue: 0,
        monthlyDepreciation: 0,
        averageAge: 0,
      };

      if (financialMetricsResponse.status === 'fulfilled' && financialMetricsResponse.value.ok) {
        const result = await financialMetricsResponse.value.json();
        if (result.success) {
          financialMetrics = result.data;
        }
      }

      // Process asset request statistics
      let requestStats = {
        totalRequests: 0,
        pendingRequests: 0,
        approvedRequests: 0,
        rejectedRequests: 0,
      };

      if (assetRequestStatsResponse.status === 'fulfilled' && assetRequestStatsResponse.value.ok) {
        const result = await assetRequestStatsResponse.value.json();
        if (result.success) {
          requestStats = result.data;
        }
      }

      // Process support ticket statistics
      let ticketStats = {
        totalTickets: 0,
        openTickets: 0,
        inProgressTickets: 0,
        resolvedTickets: 0,
        closedTickets: 0,
      };

      if (supportTicketStatsResponse.status === 'fulfilled' && supportTicketStatsResponse.value.ok) {
        const result = await supportTicketStatsResponse.value.json();
        if (result.success) {
          ticketStats = result.data;
        }
      }

      // Calculate derived metrics
      const systemHealth = assetStats.totalAssets > 0 
        ? ((assetStats.activeAssets / assetStats.totalAssets) * 100)
        : 100;

      const costSavings = financialMetrics.totalDepreciation * 0.1; // Estimate 10% savings from proper management

      // Fetch recent activities
      const recentActivities = await fetchRecentActivities();

      // Calculate trends (simplified - in real app, you'd compare with historical data)
      const trends = {
        assetsGrowth: 12.5, // This would come from historical comparison
        maintenanceReduction: maintenanceStats.overdueTasks > 0 ? -8.2 : 5.3,
        costSavingsIncrease: 18.7,
        systemHealthImprovement: systemHealth > 90 ? 2.1 : -1.5,
      };

      const metrics: DashboardMetrics = {
        totalAssets: assetStats.totalAssets,
        activeAssets: assetStats.activeAssets,
        maintenanceAssets: assetStats.maintenanceAssets,
        disposedAssets: assetStats.disposedAssets,
        pendingMaintenanceTasks: maintenanceStats.scheduledTasks + maintenanceStats.inProgressTasks,
        overdueTasks: maintenanceStats.overdueTasks,
        completedTasksThisMonth: maintenanceStats.completedTasks,
        totalAssetValue: financialMetrics.totalAssetValue,
        monthlyDepreciation: financialMetrics.monthlyDepreciation,
        costSavings,
        systemHealth,
        pendingRequests: requestStats.pendingRequests,
        openTickets: ticketStats.openTickets,
        recentOperations: assetStats.recentOperations,
      };

      setDashboardData({
        metrics,
        recentActivities,
        trends,
      });

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch dashboard data';
      setError(errorMessage);
      console.error('Error fetching dashboard data:', err);
    } finally {
      setLoading(false);
    }
  }, [session]);

  const fetchRecentActivities = async (): Promise<RecentActivity[]> => {
    const activities: RecentActivity[] = [];

    try {
      // Fetch recent maintenance tasks
      const maintenanceResponse = await fetch('/api/maintenance/tasks?limit=5&sortBy=createdAt&sortOrder=desc');
      if (maintenanceResponse.ok) {
        const result = await maintenanceResponse.json();
        if (result.success && result.data) {
          result.data.forEach((task: any) => {
            activities.push({
              id: task.id,
              type: 'maintenance',
              message: `${task.status === 'completed' ? 'Completed' : 'Scheduled'} maintenance: ${task.title}`,
              time: formatTimeAgo(new Date(task.createdAt)),
              status: task.status === 'completed' ? 'completed' : 
                     task.status === 'overdue' ? 'critical' : 'pending',
              entityId: task.id,
              entityType: 'maintenance_task',
            });
          });
        }
      }

      // Fetch recent asset requests
      const requestsResponse = await fetch('/api/asset-requests?limit=3&sortBy=createdAt&sortOrder=desc');
      if (requestsResponse.ok) {
        const result = await requestsResponse.json();
        if (result.success && result.data) {
          result.data.forEach((request: any) => {
            activities.push({
              id: request.id,
              type: 'request',
              message: `Asset request: ${request.assetName}`,
              time: formatTimeAgo(new Date(request.createdAt)),
              status: request.status === 'approved' ? 'completed' :
                     request.status === 'rejected' ? 'critical' : 'pending',
              entityId: request.id,
              entityType: 'asset_request',
            });
          });
        }
      }

      // Fetch recent support tickets
      const ticketsResponse = await fetch('/api/support-tickets?limit=3&sortBy=createdAt&sortOrder=desc');
      if (ticketsResponse.ok) {
        const result = await ticketsResponse.json();
        if (result.success && result.data) {
          result.data.forEach((ticket: any) => {
            activities.push({
              id: ticket.id,
              type: 'ticket',
              message: `Support ticket: ${ticket.subject}`,
              time: formatTimeAgo(new Date(ticket.createdAt)),
              status: ticket.status === 'resolved' ? 'completed' :
                     ticket.priority === 'critical' ? 'critical' : 'info',
              entityId: ticket.id,
              entityType: 'support_ticket',
            });
          });
        }
      }

    } catch (error) {
      console.error('Error fetching recent activities:', error);
    }

    // Sort by time and return top 10
    return activities
      .sort((a, b) => new Date(b.time).getTime() - new Date(a.time).getTime())
      .slice(0, 10);
  };

  const formatTimeAgo = (date: Date): string => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 60) {
      return `${diffInMinutes} minutes ago`;
    } else if (diffInMinutes < 1440) {
      const hours = Math.floor(diffInMinutes / 60);
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else {
      const days = Math.floor(diffInMinutes / 1440);
      return `${days} day${days > 1 ? 's' : ''} ago`;
    }
  };

  const refreshData = useCallback(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  // Load initial data
  useEffect(() => {
    if (session?.user) {
      fetchDashboardData();
    }
  }, [session, fetchDashboardData]);

  return {
    dashboardData,
    loading,
    error,
    refreshData,
  };
}
