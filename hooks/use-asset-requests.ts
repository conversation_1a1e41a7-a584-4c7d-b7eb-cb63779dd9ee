import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';

export interface AssetRequest {
  id: string;
  requestNumber: string;
  userId: string;
  assetTypeId?: string;
  assetName: string;
  quantity: number;
  status: "pending" | "approved" | "rejected" | "processing" | "shipped" | "delivered" | "cancelled";
  priority: "low" | "normal" | "high" | "critical";
  justification: string;
  businessCase?: string;
  specifications?: string;
  estimatedCost?: number;
  actualCost?: number;
  budgetCode?: string;
  department?: string;
  location: string;
  expectedDelivery?: string;
  actualDelivery?: string;
  approvedBy?: string;
  approvedAt?: string;
  rejectionReason?: string;
  trackingNumber?: string;
  notes?: string;
  attachments: string[];
  createdAt: string;
  updatedAt: string;
  user?: {
    id: string;
    name: string;
    email: string;
    department?: string;
  };
}

export interface AssetRequestFilter {
  status?: string;
  priority?: string;
  userId?: string;
  assetTypeId?: string;
  department?: string;
  dateFrom?: string;
  dateTo?: string;
  search?: string;
}

export interface AssetRequestStatistics {
  totalRequests: number;
  pendingRequests: number;
  approvedRequests: number;
  rejectedRequests: number;
  processingRequests: number;
  deliveredRequests: number;
}

export function useAssetRequests() {
  const { data: session } = useSession();
  const [requests, setRequests] = useState<AssetRequest[]>([]);
  const [statistics, setStatistics] = useState<AssetRequestStatistics | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchRequests = useCallback(async (filter?: AssetRequestFilter) => {
    if (!session?.user) return;

    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: '1',
        limit: '50',
        sortBy: 'createdAt',
        sortOrder: 'desc',
        ...filter,
      });

      const response = await fetch(`/api/asset-requests?${params}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch asset requests');
      }
      
      setRequests(result.data || []);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch asset requests';
      setError(errorMessage);
      console.error('Error fetching asset requests:', err);
    } finally {
      setLoading(false);
    }
  }, [session]);

  const fetchStatistics = useCallback(async () => {
    if (!session?.user) return;

    try {
      const response = await fetch('/api/asset-requests/statistics');
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch statistics');
      }
      
      setStatistics(result.data);
    } catch (err) {
      console.error('Error fetching asset request statistics:', err);
    }
  }, [session]);

  const createRequest = useCallback(async (requestData: Partial<AssetRequest>) => {
    if (!session?.user) {
      throw new Error('User not authenticated');
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/asset-requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });
      
      if (!response.ok) {
        const errorResult = await response.json();
        throw new Error(errorResult.error || `HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to create asset request');
      }
      
      // Refresh the requests list
      await fetchRequests();
      await fetchStatistics();
      
      return result.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create asset request';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [session, fetchRequests, fetchStatistics]);

  const updateRequest = useCallback(async (id: string, updateData: Partial<AssetRequest>) => {
    if (!session?.user) {
      throw new Error('User not authenticated');
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/asset-requests/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });
      
      if (!response.ok) {
        const errorResult = await response.json();
        throw new Error(errorResult.error || `HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to update asset request');
      }
      
      // Refresh the requests list
      await fetchRequests();
      await fetchStatistics();
      
      return result.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update asset request';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [session, fetchRequests, fetchStatistics]);

  const deleteRequest = useCallback(async (id: string) => {
    if (!session?.user) {
      throw new Error('User not authenticated');
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/asset-requests/${id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const errorResult = await response.json();
        throw new Error(errorResult.error || `HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to delete asset request');
      }
      
      // Refresh the requests list
      await fetchRequests();
      await fetchStatistics();
      
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete asset request';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [session, fetchRequests, fetchStatistics]);

  const approveRequest = useCallback(async (id: string, notes?: string) => {
    if (!session?.user) {
      throw new Error('User not authenticated');
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/asset-requests/${id}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ notes }),
      });
      
      if (!response.ok) {
        const errorResult = await response.json();
        throw new Error(errorResult.error || `HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to approve asset request');
      }
      
      // Refresh the requests list
      await fetchRequests();
      await fetchStatistics();
      
      return result.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to approve asset request';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [session, fetchRequests, fetchStatistics]);

  const rejectRequest = useCallback(async (id: string, rejectionReason: string) => {
    if (!session?.user) {
      throw new Error('User not authenticated');
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/asset-requests/${id}/reject`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ rejectionReason }),
      });
      
      if (!response.ok) {
        const errorResult = await response.json();
        throw new Error(errorResult.error || `HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to reject asset request');
      }
      
      // Refresh the requests list
      await fetchRequests();
      await fetchStatistics();
      
      return result.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to reject asset request';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [session, fetchRequests, fetchStatistics]);

  // Load initial data
  useEffect(() => {
    if (session?.user) {
      fetchRequests();
      fetchStatistics();
    }
  }, [session, fetchRequests, fetchStatistics]);

  return {
    requests,
    statistics,
    loading,
    error,
    fetchRequests,
    fetchStatistics,
    createRequest,
    updateRequest,
    deleteRequest,
    approveRequest,
    rejectRequest,
  };
}
