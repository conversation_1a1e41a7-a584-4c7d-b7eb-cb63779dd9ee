"use client";

import { useState, useEffect, useCallback } from 'react';
import { AIEngineService } from '@/lib/advanced-features/ai-engine/services';
import type { 
  AIInsight, 
  PredictiveModel, 
  SmartAlert, 
  NLPQuery,
  AssetAnalysis,
  MaintenancePrediction,
  CostOptimization
} from '@/lib/advanced-features/ai-engine/types';

interface UseAIEngineReturn {
  // Data
  insights: AIInsight[];
  models: PredictiveModel[];
  alerts: SmartAlert[];
  
  // Loading states
  isLoading: boolean;
  isAnalyzing: boolean;
  isPredicting: boolean;
  isOptimizing: boolean;
  isQuerying: boolean;
  
  // Error states
  error: string | null;
  
  // Actions
  analyzeAsset: (assetId: string, assetData: any) => Promise<AssetAnalysis | null>;
  generateMaintenancePredictions: (assetId: string, historicalData?: any[]) => Promise<MaintenancePrediction | null>;
  generateCostOptimization: (assetData: any[], timeframe?: string) => Promise<CostOptimization | null>;
  processNLPQuery: (query: string, context?: any) => Promise<NLPQuery | null>;
  detectAnomalies: (entityType: string, data: any[]) => Promise<AIInsight[] | null>;
  generateSmartAlert: (entityId: string, entityType: "asset" | "location" | "category" | "user", data: any) => Promise<SmartAlert | null>;
  
  // Utility functions
  refreshData: () => Promise<void>;
  clearError: () => void;
  getInsightsByCategory: (category: string) => AIInsight[];
  getActiveAlerts: () => SmartAlert[];
  getCacheStats: () => { size: number; keys: string[] };
  clearCache: () => void;
}

export function useAIEngine(): UseAIEngineReturn {
  const [insights, setInsights] = useState<AIInsight[]>([]);
  const [models, setModels] = useState<PredictiveModel[]>([]);
  const [alerts, setAlerts] = useState<SmartAlert[]>([]);
  
  const [isLoading, setIsLoading] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isPredicting, setIsPredicting] = useState(false);
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [isQuerying, setIsQuerying] = useState(false);
  
  const [error, setError] = useState<string | null>(null);

  const aiService = AIEngineService.getInstance();

  // Load initial data
  useEffect(() => {
    refreshData();
  }, []);

  const refreshData = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const currentInsights = aiService.getInsights();
      const currentModels = aiService.getModels();
      const currentAlerts = aiService.getAlerts(true);
      
      setInsights(currentInsights);
      setModels(currentModels);
      setAlerts(currentAlerts);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load AI data');
    } finally {
      setIsLoading(false);
    }
  }, [aiService]);

  const analyzeAsset = useCallback(async (assetId: string, assetData: any): Promise<AssetAnalysis | null> => {
    setIsAnalyzing(true);
    setError(null);
    
    try {
      const analysis = await aiService.analyzeAsset(assetId, assetData);
      await refreshData(); // Refresh to get new insights
      return analysis;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to analyze asset';
      setError(errorMessage);
      return null;
    } finally {
      setIsAnalyzing(false);
    }
  }, [aiService, refreshData]);

  const generateMaintenancePredictions = useCallback(async (
    assetId: string, 
    historicalData?: any[]
  ): Promise<MaintenancePrediction | null> => {
    setIsPredicting(true);
    setError(null);
    
    try {
      const prediction = await aiService.generateMaintenancePredictions(assetId, historicalData);
      await refreshData(); // Refresh to get new insights and models
      return prediction;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate maintenance predictions';
      setError(errorMessage);
      return null;
    } finally {
      setIsPredicting(false);
    }
  }, [aiService, refreshData]);

  const generateCostOptimization = useCallback(async (
    assetData: any[], 
    timeframe: string = "1 year"
  ): Promise<CostOptimization | null> => {
    setIsOptimizing(true);
    setError(null);
    
    try {
      const optimization = await aiService.generateCostOptimization(assetData, timeframe);
      await refreshData(); // Refresh to get new insights
      return optimization;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate cost optimization';
      setError(errorMessage);
      return null;
    } finally {
      setIsOptimizing(false);
    }
  }, [aiService, refreshData]);

  const processNLPQuery = useCallback(async (
    query: string, 
    context?: any
  ): Promise<NLPQuery | null> => {
    setIsQuerying(true);
    setError(null);
    
    try {
      const result = await aiService.processNaturalLanguageQuery(query, context);
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to process query';
      setError(errorMessage);
      return null;
    } finally {
      setIsQuerying(false);
    }
  }, [aiService]);

  const detectAnomalies = useCallback(async (
    entityType: string, 
    data: any[]
  ): Promise<AIInsight[] | null> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const anomalies = await aiService.detectAnomalies(entityType, data);
      await refreshData(); // Refresh to get new insights
      return anomalies;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to detect anomalies';
      setError(errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [aiService, refreshData]);

  const generateSmartAlert = useCallback(async (
    entityId: string,
    entityType: "asset" | "location" | "category" | "user",
    data: any
  ): Promise<SmartAlert | null> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const alert = await aiService.generateSmartAlert(entityId, entityType, data);
      await refreshData(); // Refresh to get new alerts
      return alert;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate smart alert';
      setError(errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [aiService, refreshData]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const getInsightsByCategory = useCallback((category: string): AIInsight[] => {
    return aiService.getInsights(category);
  }, [aiService]);

  const getActiveAlerts = useCallback((): SmartAlert[] => {
    return aiService.getAlerts(true);
  }, [aiService]);

  const getCacheStats = useCallback(() => {
    return aiService.getCacheStats();
  }, [aiService]);

  const clearCache = useCallback(() => {
    aiService.clearCache();
  }, [aiService]);

  return {
    // Data
    insights,
    models,
    alerts,
    
    // Loading states
    isLoading,
    isAnalyzing,
    isPredicting,
    isOptimizing,
    isQuerying,
    
    // Error state
    error,
    
    // Actions
    analyzeAsset,
    generateMaintenancePredictions,
    generateCostOptimization,
    processNLPQuery,
    detectAnomalies,
    generateSmartAlert,
    
    // Utility functions
    refreshData,
    clearError,
    getInsightsByCategory,
    getActiveAlerts,
    getCacheStats,
    clearCache
  };
}